<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.17.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.jiuji.oa</groupId>
    <artifactId>oa-after</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>oa-after-service</module>
        <module>oa-after-vo</module>
        <module>after-cloud</module>
    </modules>


    <properties>
        <jiuji.common.version>0.0.9-SNAPSHOT</jiuji.common.version>
        <jiuji.foundation.version>0.0.8-SNAPSHOT</jiuji.foundation.version>
        <mybatisplus.version>3.1.0</mybatisplus.version>
        <mybatis.version>1.3.1</mybatis.version>
        <spring-cloud-version>Greenwich.SR4</spring-cloud-version>
        <swagger.version>2.7.0</swagger.version>
        <lombok.version>1.18.10</lombok.version>
        <fastjson.version>1.2.83</fastjson.version>
        <mapstruct.version>1.3.1.Final</mapstruct.version>
        <delay.queue.version>1.3.0-RELEASE</delay.queue.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <artifactId>common-vo</artifactId>
                <groupId>com.jiuji.tc</groupId>
                <version>${jiuji.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.9.10.8</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.21</version>
            </dependency>
            <dependency>
                <groupId>commons-jxpath</groupId>
                <artifactId>commons-jxpath</artifactId>
                <version>1.4.0</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.9.10.8</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.18</version>
            </dependency>
            <dependency>
                <groupId>commons-jxpath</groupId>
                <artifactId>commons-jxpath</artifactId>
                <version>1.4.0</version>
            </dependency>

            <dependency>
                <artifactId>common-utils</artifactId>
                <groupId>com.jiuji.tc</groupId>
                <version>${jiuji.common.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!-- Log4j2 异步支持 -->
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>3.3.6</version>
            </dependency>

            <!-- mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.dubasdey</groupId>
                <artifactId>log4j2-jsonevent-layout</artifactId>
                <version>0.0.4</version>
            </dependency>
            <!--日志  log4j2 升级-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>2.17.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>2.17.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>2.17.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jul</artifactId>
                <version>2.17.1</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>2.0</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.5.13</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.2</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>2.9.7</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.oa</groupId>
                <artifactId>nc-segments-vo</artifactId>
                <version>1.7-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.oa</groupId>
                <artifactId>oa-after-vo</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.cloud</groupId>
                <artifactId>after-cloud</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.oa</groupId>
                <artifactId>oa-order-stub</artifactId>
                <version>1.0.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.oa</groupId>
                <artifactId>nc-segments-stub</artifactId>
                <version>1.7-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.oa</groupId>
                <artifactId>loginfo-stub</artifactId>
                <version>1.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.oa</groupId>
                <artifactId>oa-finance-stub</artifactId>
                <version>1.1-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>com.jiuji.oa</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>org.springframework.cloud</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>com.github.dubasdey</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>com.github.java-json-tools</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>com.jiuji.tc</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jiuji.oa</groupId>
                <artifactId>oa-finance-vo</artifactId>
                <version>1.1-SNAPSHOT</version>
            </dependency>
            <dependency>
            <groupId>com.jiuji.oa</groupId>
            <artifactId>oa-pay-sdk</artifactId>
            <version>1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

            <dependency>
                <artifactId>foundation-db-starter</artifactId>
                <groupId>com.jiuji.tc</groupId>
                <version>${jiuji.foundation.version}</version>
            </dependency>

            <dependency>
                <artifactId>foundation-message-starter</artifactId>
                <groupId>com.jiuji.tc</groupId>
                <version>${jiuji.foundation.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ch999.common</groupId>
                <artifactId>utils</artifactId>
                <version>1.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>3.17</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>3.17</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis</artifactId>
                <version>2.5.14</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis-lettuce</artifactId>
                <version>2.5.11</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>9.0.54</version>
                <exclusions>
                    <exclusion>
                        <artifactId>tomcat-annotations-api</artifactId>
                        <groupId>org.apache.tomcat</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>9.0.54</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>9.0.54</version>
                <exclusions>
                    <exclusion>
                        <artifactId>tomcat-embed-core</artifactId>
                        <groupId>org.apache.tomcat.embed</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <artifactId>orginfo-stub</artifactId>
                <groupId>com.jiuji.oa</groupId>
                <version>1.2.47-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>3.8.2</version>
            </dependency>
            <dependency>
                <artifactId>foundation-rabbitmq-starter</artifactId>
                <groupId>com.jiuji.tc</groupId>
                <version>${jiuji.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.1.10</version>
            </dependency>

            <!--增加swagger-bootstrap-ui-->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-ui</artifactId>
                <version>3.0.2</version>
            </dependency>


            <!--hutool java工具类库-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.5.7</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-mapper-asl</artifactId>
                <version>1.8.0</version>
            </dependency>

            <!--prometheus JVM监控数据上报-->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-core</artifactId>
                <version>1.5.1</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>1.5.1</version>
            </dependency>

            <!--中通开放平台SDK-->
            <dependency>
                <groupId>com.zto.zop</groupId>
                <artifactId>zopsdk</artifactId>
                <version>0.6</version>
            </dependency>
            <dependency>
                <artifactId>oa-office-stub</artifactId>
                <groupId>com.jiuji.oa.office</groupId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.huishou</groupId>
                <artifactId>huishou-cloud</artifactId>
                <version>0.0.8-SNAPSHOT</version>
            </dependency>
            <dependency>
                <artifactId>oa-stock-cloud</artifactId>
                <groupId>com.jiuji.stock</groupId>
                <version>1.2.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.web</groupId>
                <artifactId>extends-cloud</artifactId>
                <version>1.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.baomidou</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mybatis.spring.boot</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tomcat.embed</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jiuji.oaapi</groupId>
                <version>0.1.7-SNAPSHOT</version>
                <artifactId>oaapi-cloud</artifactId>
            </dependency>

            <dependency>
                <artifactId>oa-stock-cloud</artifactId>
                <groupId>com.jiuji.stock</groupId>
                <version>1.2.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>18.0</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>2.0.0</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.jiuji.infra</groupId>
                <artifactId>common-delay-queue</artifactId>
                <version>${delay.queue.version}</version>
            </dependency>
            <!-- easyexcel 依赖 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>2.2.6</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.cloud</groupId>
                <artifactId>org-cloud</artifactId>
                <version>0.0.18-SNAPSHOT</version>
            </dependency>
        </dependencies>

    </dependencyManagement>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                    <configuration>
                        <attach>true</attach>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.flywaydb</groupId>
                    <artifactId>flyway-maven-plugin</artifactId>
                    <version>6.3.2</version>
                    <dependencies>
                        <dependency>
                            <groupId>mysql</groupId>
                            <artifactId>mysql-connector-java</artifactId>
                            <version>8.0.19</version>
                        </dependency>
                    </dependencies>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <pluginRepositories>
        <pluginRepository>
            <id>jiuji-group</id>
            <url>http://nexus.ch999.cn:8081/nexus/repository/jiuji-group/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <repositories>
        <repository>
            <id>jiuji-group</id>
            <url>http://nexus.ch999.cn:8081/nexus/repository/jiuji-group/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>jiuji-releases</id>
            <url>http://nexus.ch999.cn:8081/nexus/repository/jiuji-release/</url>
        </repository>
        <snapshotRepository>
            <id>jiuji-snapshots</id>
            <url>http://nexus.ch999.cn:8081/nexus/repository/jiuji-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
