package com.jiuji.oa.afterservice.shouhou.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jiuji.tc.utils.jackson.LocalDateTimeDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 九机服务展示实体类
 *
 * <AUTHOR>
 * @description
 * @since 2021/5/7 15:12
 */
@Data
@ApiModel("九机服务外层")
@Accessors(chain = true)
public class ServiceInfoVO {
    /**
     * 产品ID
     */
    @ApiModelProperty("产品ID")
    private Integer productId;
    /**
     * ppid
     */
    @ApiModelProperty("ppid")
    private Integer ppriceId;
    /**
     * 大件商品库存主键
     */
    @ApiModelProperty("大件商品库存主键")
    private Integer mkcId;
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String productName;
    /**
     * 产品颜色规格
     */
    @ApiModelProperty("产品颜色规格")
    private String productColor;
    /**
     * 下单时间
     */
    @ApiModelProperty("下单时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime subDate;
    /**
     * 出库时间
     */
    @ApiModelProperty("出库时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime tradeDate;
    /**
     * 订单完成时间
     */
    @ApiModelProperty("订单完成时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime transactionDate;
    /**
     * 原始串号
     */
    @ApiModelProperty("原始串号")
    private String originImei;
    /**
     * 串号
     */
    @ApiModelProperty("串号")
    private String imei;
    /**
     * 库存门店代码
     */
    @ApiModelProperty("库存区域代码")
    private String mkcArea;
    /**
     * 库存门店ID
     */
    @ApiModelProperty("库存区域ID")
    private Integer mkcAreaId;
    /**
     * 订单电话
     */
    @ApiModelProperty("订单电话")
    private String subMobile;
    /**
     * 下单用户ID 非当前
     */
    @ApiModelProperty("下单用户ID 非当前")
    private Integer userId;
    /**
     * 下单用户名称
     */
    @ApiModelProperty("下单用户名称")
    private String username;
    /**
     * 根据用户编号获取1年内折价退换数量
     */
    @ApiModelProperty("根据用户编号获取1年内折价退换数量")
    private Integer afterServicesDiscount;
    /**
     * 下单用户等级
     */
    @ApiModelProperty("下单用户等级")
    private Integer userClass;
    /**
     * 下单用户等级名称
     */
    @ApiModelProperty("下单用户等级名称")
    private String userClassName;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private Integer xtenant;
    /**
     * 租户类型: 0 当前租户订单 1 非当前租户订单
     */
    @ApiModelProperty("租户类型: 0 当前租户订单 1 非当前租户订单")
    private Integer xtenantType;
    /**
     * 是否黑名单
     */
    @ApiModelProperty("是否黑名单")
    private Boolean blacklist;
    /**
     * 品牌ID
     */
    @ApiModelProperty("品牌ID")
    private Integer brandId;
    /**
     * 商品分类ID
     */
    @ApiModelProperty("商品分类ID")
    private Integer cid;
    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private BigDecimal price;
    /**
     * 订单ID
     */
    @ApiModelProperty("订单ID")
    private Integer subId;
    /**
     * 订单详情ID
     */
    @ApiModelProperty("订单详情ID")
    private Integer basketId;
    /**
     * 商品销售类型 6 拍卖
     */
    @ApiModelProperty("商品销售类型 6 拍卖")
    private Integer basketType;

    /**
     * 是否大件
     */
    @ApiModelProperty("是否大件")
    private Boolean isMobile;

    /**
     * 订单类型
     */
    @ApiModelProperty("订单类型")
    private Integer subType;
    /**
     * 销售类型
     */
    @ApiModelProperty("销售类型")
    private Integer saleType;
    /**
     * 收货人
     */
    @ApiModelProperty("收货人")
    private String subTo;
    /**
     * 额外业务提示信息
     */
    @ApiModelProperty("额外业务提示信息")
    private String msg;
    /**
     * 服务套餐
     */
    @ApiModelProperty("服务套餐")
    private BoughtPlusVO boughtPlus;
    /**
     * 是否瑕疵机
     */
    @ApiModelProperty("是否瑕疵机")
    private Boolean xcMkc;
    /**
     * 瑕疵机描述
     */
    @ApiModelProperty("瑕疵机描述")
    private String xcMkcInfo = "未查询到商品来源";
    /**
     * 是否回收
     */
    @ApiModelProperty("是否回收")
    private Boolean huishou;
    /**
     * 订单类型
     * @see com.jiuji.oa.afterservice.bigpro.bo.externalrepair.SimpleServiceSubInfoBo.OrderTypeEnum
     */
    @ApiModelProperty("订单类型 1 新机 2 良品 3 外修 4 历史记录")
    private Integer orderType;
    /**
     * 订单类型
     */
    @ApiModelProperty("订单状态 3 已完成 其他状态 需要结合订单类型")
    private Integer subCheck;
    /**
     * 是否已归档
     */
    @ApiModelProperty("是否已归档")
    private Boolean isHistory;
    /**
     * 最后一次是回收标识
     */
    @ApiModelProperty("最后一次是回收标识")
    private Boolean lastRecoverFlag;
    /**历史维修次数*/
    @ApiModelProperty("历史维修次数")
    private Integer shouhouTimes;
    /**
     * 所有服务集合
     */
    @ApiModelProperty("所有服务集合")
    private List<ServiceVO> serviceVos;
    /**
     * 串号查询日志
     */
    @ApiModelProperty("串号查询日志")
    private List<?> imeilist;
    /**
     * 是否保修
     */
    @ApiModelProperty("是否保修")
    private Boolean baoXiu;

    public ServiceInfoVO() {
        blacklist = Boolean.FALSE;
        xcMkc = Boolean.FALSE;
        huishou = Boolean.FALSE;
        lastRecoverFlag = Boolean.FALSE;
        baoXiu = Boolean.FALSE;

    }

    /**
     * 绑定的套餐
     */
    @Data
    @ApiModel("绑定的套餐")
    @Accessors(chain = true)
    public static class BoughtPlusVO{
        /**编号*/
        @ApiModelProperty("主键")
        private Integer id;
        /**编号*/
        @ApiModelProperty("编号")
        private Integer code;
        /**套餐名称*/
        @ApiModelProperty("套餐名称")
        private String name;
        @ApiModelProperty("商品ppid")
        private Integer ppriceid;
        /**套餐年限*/
        @ApiModelProperty("服务年限")
        private BigDecimal years;
        /**
         * 服务是否有效
         * @see ServiceVO.DetailInfo.EffectiveEnum
         */
        @ApiModelProperty("服务是否有效 1有效 2未生效 3已过期 4已使用")
        private Integer effective;
        /**服务是否有效描述*/
        @ApiModelProperty("服务是否有效描述")
        private String effectiveDes;
        /**
         * 购买时间
         */
        @ApiModelProperty("购买时间")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime tradeDate;
        /**服务生效日期*/
        @ApiModelProperty("服务生效日期")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime startTime;
        /**服务截止日期*/
        @ApiModelProperty("服务截止日期")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime endTime;
        /**价格*/
        @ApiModelProperty("价格")
        private BigDecimal price;
        /**费用*/
        @ApiModelProperty("费用")
        private BigDecimal feiyong;
    }

}
