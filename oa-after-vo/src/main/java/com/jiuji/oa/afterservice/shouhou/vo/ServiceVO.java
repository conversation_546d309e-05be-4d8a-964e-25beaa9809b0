package com.jiuji.oa.afterservice.shouhou.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import com.jiuji.tc.utils.jackson.LocalDateTimeDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 九机服务展示服务
 *
 * <AUTHOR>
 * @description
 * @since 2021/5/7 15:12
 */
@Data
@ApiModel("服务")
public class ServiceVO {
    /**服务名称*/
    @ApiModelProperty("服务名称")
    private String name;
    /**父级类型*/
    @ApiModelProperty("父级类型")
    private String parentType;
    /**服务类型*/
    @ApiModelProperty("服务类型")
    private String type;
    /**服务类型代码*/
    @ApiModelProperty("服务类型代码")
    private Integer typeCode;
    /**
     * 是否前端展示
     */
    @ApiModelProperty("是否前端展示")
    @JSONField(serialize = false,deserialize = false)
    private Boolean display;
    /**服务购买详情*/
    @ApiModelProperty("服务购买详情")
    private DetailInfo detailInfo;

    /**
     * 已购买,详细信息
     * <AUTHOR>
     * @description
     * @since 2021/5/7 15:12
     */
    @Data
    @ApiModel("服务购买详情")
    public static class DetailInfo{
        /**服务ID*/
        @ApiModelProperty("服务ID")
        private Integer id;
        /**服务ID*/
        @ApiModelProperty("绑定套餐id")
        private Integer boughtPlusId;
        /**服务类型*/
        @ApiModelProperty("服务类型")
        private String type;
        /**服务类型代码*/
        @ApiModelProperty("服务类型代码")
        private Integer typeCode;
        @ApiModelProperty("商品ppid")
        private Integer ppriceid;
        /**服务版本号*/
        @ApiModelProperty("服务版本号")
        private String version;
        /**服务版本描述*/
        @ApiModelProperty("服务版本描述")
        private String versionDes;
        /**
         * 服务是否有效
         * @see EffectiveEnum
         */
        @ApiModelProperty("服务是否有效 1有效 2未生效 3已过期 4已使用")
        private Integer effective;
        /**服务是否有效描述*/
        @ApiModelProperty("服务是否有效描述")
        private String effectiveDes;
        /**交易日期*/
        @ApiModelProperty("交易日期")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime tradeDate;
        /**服务生效日期*/
        @ApiModelProperty("服务生效日期")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime startTime;
        /**服务截止日期*/
        @ApiModelProperty("服务截止日期")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime endTime;
        /**服务年限*/
        @ApiModelProperty("服务年限")
        private BigDecimal years;
        /**服务是否已使用*/
        @ApiModelProperty("服务是否已使用")
        private Boolean use;
        /**停止服务订单类型
         * @see com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum
         */
        @ApiModelProperty("枚举类: BusinessTypeEnum")
        private Integer stopSubType = BusinessTypeEnum.REPAIR_ORDER.getCode();
        /**停止服务售后ID (如:有效期内 不在保的售后)*/
        @ApiModelProperty("停止服务售后ID (如: 不在保的售后)")
        private Integer stopShouhouId;
        /**服务截止日期*/
        @ApiModelProperty("有效期内 停止服务的时间")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime stopShouhouTime;
        /**价格*/
        @ApiModelProperty("价格")
        private BigDecimal price;
        /**费用*/
        @ApiModelProperty("费用")
        private BigDecimal feiyong;

        /**
         * 剩余费用 price- feiyong
         */
        private BigDecimal remainingCost;
        /**
         * 服务分类
         * @see ServiceClassificationEnum
         */
        @ApiModelProperty(value = "枚举类: ServiceClassificationEnum 服务分类 (1 自营 2 九讯)")
        private Integer classification;

        /**
         * 服务记录详情
         */
        private List<ServiceDetailVO> recordAppearingList;

        /**
         * 生效枚举类
         * <AUTHOR>
         * @description
         * @since 2021/7/29 15:12
         */
        @Getter
        @AllArgsConstructor
        public enum EffectiveEnum implements CodeMessageEnumInterface {
            UNKNOWN(0,"未知"),
            ACTIVE(1,"在保"),
            NOT_ACTIVE(2,"未生效"),
            INVALID(3,"已过期"),
            USED(4,"已使用"),
            NOT_REGISTER(5,"未注册"),
            NOT_PASS(6,"审核未通过")
            ;
            /**
             * 编码
             */
            private Integer code;
            /**
             * 名称
             */
            private String message;
        }

        public static Optional<EffectiveEnum> getEffectiveValue(LocalDateTime today, LocalDateTime startTime, LocalDateTime endTime){
            if(startTime == null){
                //未知状态
                return Optional.of(EffectiveEnum.UNKNOWN);
            }
            if(startTime.isAfter(today)){
                //未生效
                return Optional.of(EffectiveEnum.NOT_ACTIVE);
            }
            if(endTime == null){
                return Optional.of(EffectiveEnum.UNKNOWN);
            }
            if (today.isAfter(endTime)){
                //已过期
                return Optional.of(EffectiveEnum.INVALID);
            }
            //有效
            return Optional.of(EffectiveEnum.ACTIVE);
        }
    }

    public ServiceVO copy(){
        ServiceVO target = new ServiceVO();
        target.setName(this.getName());
        target.setParentType(this.getParentType());
        target.setType(this.getType());
        target.setTypeCode(this.getTypeCode());
        return target;
    }
}
