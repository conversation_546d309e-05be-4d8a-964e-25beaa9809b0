package com.jiuji.oa.afterservice.shouhou.vo;



import lombok.Data;

import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 服务记录详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Data
@Accessors(chain = true)
public class ServiceDetailVO   {


    private Integer id;



    private Integer shouhouId;


    private Integer serviceRecordId;

    private BigDecimal price;


    private LocalDateTime createTime;

}
