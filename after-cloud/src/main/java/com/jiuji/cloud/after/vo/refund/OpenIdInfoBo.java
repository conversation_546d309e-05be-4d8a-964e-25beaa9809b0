package com.jiuji.cloud.after.vo.refund;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

/**
 * 第三方用户验证bo实体
 * <AUTHOR>
 * @since 2021/11/1 17:05
 */
@Data
@Accessors(chain = true)
@ApiModel("第三方用户验证实体Bo")
public class OpenIdInfoBo {
    @ApiModelProperty(value = "主键")
    private Integer id;
    @ApiModelProperty("应用类型")
    private Integer type;
    @ApiModelProperty(value = "应用用户id")
    private String userId;
    @ApiModelProperty(value = "头像地址")
    private String userAvatar;
    @ApiModelProperty(value = "昵称")
    private String nickName;
    @ApiModelProperty("应用名称")
    private String name;

    /**
     * 第三方用户验证地址bo实体
     * <AUTHOR>
     * @since 2021/11/1 17:05
     */
    @Data
    @Accessors(chain = true)
    @ApiModel("第三方用户验证地址实体Bo")
    public static class OpenIdUrlBo {
        /**
         * @see OpenType#getMessage()
         */
        @ApiModelProperty("应用名称")
        private String name;
        @ApiModelProperty("应用类型")
        private Integer type;
        @ApiModelProperty("地址类型 1 验证地址 2 验证二维码地址")
        private Integer urlType;
        @ApiModelProperty("接口类型 0 验证 1 绑定")
        private Integer apiType;
        @ApiModelProperty("地址")
        private String url;
    }

    /**
     * 三方类型
     */
    @Getter
    @AllArgsConstructor
    public enum OpenType implements CodeMessageEnumInterface {
        WE_CHAT(1,2,"微信")
        ,ALIPAY(2,1,"支付宝")
        ,BANK_PAY(3,1,"银企直连转账")
        ;

        /**
         * 编码
         */
        private Integer code;
        /**
         * 地址类型 1 验证地址 2 验证二维码地址
         */
        private Integer urlType;
        /**
         * 编码对应信息
         */
        private String message;
    }
}
