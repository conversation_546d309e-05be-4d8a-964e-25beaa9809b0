package com.jiuji.cloud.after.service;

import com.jiuji.cloud.after.service.fallback.ApiAfterClientBackFactory;
import com.jiuji.cloud.after.vo.req.PersonStatisticsAppReq;
import com.jiuji.cloud.after.vo.res.ChangeInfoRes;
import com.jiuji.cloud.after.vo.res.PersonStatisticsAppVO;
import com.jiuji.cloud.after.vo.res.ReceiveSendResVO;
import com.jiuji.oa.afterservice.shouhou.vo.req.ListOrganizationReq;
import com.jiuji.oa.afterservice.shouhou.vo.req.SelectConfigToWebReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.ListOrganizationRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.SelectConfigToWebRes;
import com.jiuji.tc.common.vo.R;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "AFTERSERVICE", path = "afterservice",
        fallbackFactory = ApiAfterClientBackFactory.class)
public interface ApiAfterClient {
    /**
     * 售后个人业绩统计
     * @return
     */
    @PostMapping(value = "/api/after/statistics/person/app/v1")
    R<PersonStatisticsAppVO> personStatisticsApp(@RequestBody PersonStatisticsAppReq req);



    @PostMapping(value = "/api/ListOrganization/updateMobile")
    R<ListOrganizationRes> updateMobile(@RequestBody ListOrganizationReq req);


    @PostMapping("/api/SmallproConfig/selectConfigToWeb")
    R<SelectConfigToWebRes> selectConfigToWeb(@RequestBody SelectConfigToWebReq req);

    @GetMapping("/api/smallpro/recover/listReceiveSendCount")
    R<ReceiveSendResVO> listReceiveSendCount(@RequestParam(value = "areaId") Integer areaId);

    /**
     * 小件配置查询提供给网站
     * @param req
     * @return
     */
    @GetMapping("/api/SmallproConfig/selectChangeInfoRes")
    R<List<ChangeInfoRes>> selectChangeInfoRes(@RequestParam(value = "smallProId")Integer smallProId,
                                               @RequestHeader("Authorization") String authorization);
}
