package com.jiuji.cloud.after.vo.refund;



import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 接口包
 * <AUTHOR>
 * @since 2022/7/29 11:53
 */
public interface Refund<T extends Refund> {
    /**
     * 分组代码code字段名称
     */
    String ID_KEY = "id";
    /**
     * 时间字段
     */
    String DTIME_KEY = "dtime";
    /**
     * 分组代码code字段名称
     */
    String GROUP_CODE_KEY = "groupCode";
    /**
     * 当前退款金额字段名称
     */
    String REFUND_PRICE_KEY = "refundPrice";
    /**
     * 运营商返现
     */
    String REFUND_BUSINESS_TYPE="refundBusinessType";
    /**
     * 实际支付金额key
     */
    String ACTUAL_PAY_PRICE_KEY ="actualPayPrice";
    /**
     * json转换的缓存
     */
    String JSON_TO_MY_CLASS_CACHE_KEY = "__json_to_my_class_cache_key__";

    T setId(Integer id);


    /**
     * 获取分组code
     * @return
     */
     Integer getGroupCode();
    /**
     * 获取记录时间
     * @return
     */
     LocalDateTime getDtime();

    /**
     * 设置分组信息
     * @return
     */
    T setGroupCode(Integer groupCode);
    /**
     * 退款方式名称
     * @return
     */
    String getReturnWayName();

    /**
     * 退款方式名称
     * @return
     */
    T setReturnWayName(String returnWayName);
    /**
     * 获取实际支付金额
     * @return
     */
     BigDecimal getActualPayPrice();
    /**
     * 获取退款金额
     * @return
     */
     BigDecimal getRefundPrice();
    /**
     * 退款的业务类型 0和null是普通退款 1 运营商返现
     * @see RefundBusinessEnum
     * @return refundBusinessType
     */
    Integer getRefundBusinessType();

    /**
     * 退款的业务类型 0和null是普通退款 1 运营商返现
     * @return
     */
    T setRefundBusinessType(Integer refundBusinessType);

    /**
     * 设置退款金额
     * @param refundPrice
     * @return
     */
     T setRefundPrice(BigDecimal refundPrice);
}
