package com.jiuji.cloud.after.vo.baoxiu;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/10/30 16:41
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "计算保修参数")
public class BaoXiuParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private Long xtenant;

    /**
     * 包退时间（天）
     */
    @ApiModelProperty(value = "包退时间")
    private Integer refundDay;

    /**
     * 包换时间（天）
     */
    @ApiModelProperty(value = "包换时间")
    private Integer replaceDay;

    /**
     * 保修时间（天）
     */
    @ApiModelProperty(value = "保修时间")
    private Integer repairDay;

    /**
     * 以换代修 1：是；0：否
     */
    @ApiModelProperty(value = "以换代修")
    private Integer isReplace;

    /**
     * 订单类型
     * OrderTypeEnum
     */
    @ApiModelProperty(value = "订单类型")
    @NotNull(message = "订单类型不能为空")
    private OrderTypeEnum orderTypeEnum;

    /**
     * 是否为大件
     */
    private Boolean isMobile;
    // 交易完成时间/下单时间/出库时间至少有一个不为空
    // 交易完成时间 tradeDate1
    @ApiModelProperty(value = "交易完成时间")
    private LocalDateTime tradeCompleteTime;
    // 下单时间 subDate
    @ApiModelProperty(value = "下单时间")
    private LocalDateTime subDateTime;
    // 出库时间 tradeDate
    @ApiModelProperty(value = "出库时间")
    private LocalDateTime outStockTime;

    /**
     * 订单类型
     */
    @Getter
    @AllArgsConstructor
    public enum OrderTypeEnum implements CodeMessageEnumInterface{
        NEW_MACHINE(1, "新机"),
        EXCELLENT_PRODUCT(2, "优品"),
        GOOD_PRODUCT(3, "良品")
        ;
        /**
         * 编码
         */
        private Integer code;
        /**
         * 名称
         */
        private String message;
    }
}
