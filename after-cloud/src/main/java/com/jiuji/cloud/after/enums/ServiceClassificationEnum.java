package com.jiuji.cloud.after.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/11/17 13:59
 */
@Getter
@AllArgsConstructor
public enum ServiceClassificationEnum implements CodeMessageEnumInterface {
    SELF(1,"自营"),
    YADING(2,"九讯"),
    HUI_JI_BAO(3,"汇机保"),
    LIMIT(4,"额度"),
    ;

    /**
     * 状态
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;
}
