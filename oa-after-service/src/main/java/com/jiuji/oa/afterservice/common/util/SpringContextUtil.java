package com.jiuji.oa.afterservice.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.system.HostInfo;
import cn.hutool.system.SystemUtil;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.tc.utils.common.TraceIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.ApplicationContext;
import org.springframework.context.i18n.LocaleContext;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @Author: qiweiqing
 * @Date: 2020/10/13/19:24
 * @Description:
 */
@Component
@Slf4j
public class SpringContextUtil extends SpringUtil {

//    public static List<String> getActiveProfile(){
//        String[] profiles = SpringUtil.getApplicationContext().getEnvironment().getActiveProfiles();
//        if(profiles.length != 0){
//            return Arrays.asList(profiles);
//        }
//        return Collections.emptyList();
//    }

    public static List<String> getActiveProfileList(){
        String[] profiles = SpringUtil.getApplicationContext().getEnvironment().getActiveProfiles();
        if(profiles.length != 0){
            return Arrays.asList(profiles);
        }
        return Collections.emptyList();
    }


    public static ApplicationContext getContext(){
        return SpringUtil.getApplicationContext();
    }

    public static Optional<HttpServletRequest> getRequest(){
        return getServletRequestAttributes().map(ra->ra.getRequest());
    }

    /**
     * 添加错误消息
     * @param errorTemplate
     * @param params
     */
    public static void addRequestErrorMsg(String errorTemplate,Object... params) {
       addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_ERROR,errorTemplate,params);
    }

    /**
     * 添加错误消息
     * @param msgTemplate
     * @param params
     */
    public static void addRequestKeyMsg(String attKey, String msgTemplate, Object... params) {
        getRequest().ifPresent(req -> {
            Set<String> msgSet = (Set<String>) req.getAttribute(attKey);
            if (msgSet == null) {
                msgSet = new LinkedHashSet<>();
                req.setAttribute(attKey, msgSet);
            }
            msgSet.add(StrUtil.format(msgTemplate,params));
        });
    }


    /**
     * 添加日志到lambda,后面执行
     * @param runnable
     */
    public static void addRequestLambda(Runnable runnable) {
        String attKey = RequestAttrKeys.REQUEST_ATTR_SHOUHOU_LOG_LAMBDA;
        Optional<HttpServletRequest> request = getRequest();
        if(!request.isPresent()){
            //没有request对象,直接执行,并返回
            runnable.run();
            return;
        }
        request.ifPresent(req -> {
            Set<Runnable> msgSet = (Set<Runnable>) req.getAttribute(attKey);
            if (msgSet == null) {
                msgSet = new LinkedHashSet<>();
                req.setAttribute(attKey, msgSet);
            }
            msgSet.add(runnable);
        });

    }


    /**
     * 运行lambda
     */
    public static void runAndRemoveRequestLambda() {
        String attKey = RequestAttrKeys.REQUEST_ATTR_SHOUHOU_LOG_LAMBDA;
        getRequest().ifPresent(req -> {
            Set<Runnable> msgSet = (Set<Runnable>) req.getAttribute(attKey);
            if (msgSet != null) {
                msgSet.forEach(run -> {
                    try {
                        run.run();
                    } catch (Exception e) {
                        // 所有异常都不影响后续的执行
                        RRExceptionHandler.logError("运行lambda异常",null,e,SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
                    }
                });
                msgSet.clear();
            }
        });
    }

    /**
     * 获取错误消息
     */
    public static List<String> getRequestErrorMsg() {
        return getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_ERROR);
    }

    /**
     * 获取错误消息
     */
    public static List<String> getRequestKeyListMsg(String attrKey) {
        return getRequest().map(req -> (Set<String>) req.getAttribute(attrKey)).map(s -> s.stream().collect(Collectors.toList())).orElse(Collections.emptyList());
    }

    public static Optional<HttpServletResponse> getResponse(){
        return getServletRequestAttributes().map(ra->ra.getResponse());
    }

    public static Optional<ServletRequestAttributes> getServletRequestAttributes(){
        return Optional.ofNullable((ServletRequestAttributes) RequestContextHolder.getRequestAttributes());

    }

    /**
     * 获取当前服务的ip和端口号
     * @return
     */
    public static Optional<String> getServerIpPort() {
        Environment env = SpringContextUtil.getContext().getEnvironment();
        Optional<String> ipPort = Optional.ofNullable(SystemUtil.getHostInfo()).map(HostInfo::getAddress)
                .map(add -> SpringContextUtil.getRequest().map(req -> Convert.toStr(req.getServerPort()))
                        .map(sp -> StrUtil.format("{}:{}", add, ObjectUtil.defaultIfBlank(env.getProperty("server.port"),
                                ObjectUtil.defaultIfBlank(env.getProperty("spring.cloud.consul.discovery.ip-address"), sp)))).orElse(null));
        return ipPort;
    }

    /**
     * 是否为生产环境
     * @return
     */
    public static boolean isProduce(){
        return CollUtil.intersection(SpringContextUtil.getActiveProfileList(),Arrays.asList("dev","test","10050")).isEmpty();
    }

    /**
     * 请求级别的缓存
     * @param callback
     * @param <T>
     * @return
     */
    public static <T> T reqCache(Supplier<T> callback, String format,Object ...args){
        //缓存到请求属性中
        String cacheKey = StrUtil.format(RequestAttrKeys.REQUEST_CACHE_PREV, StrUtil.format(format, args));
        log.debug("请求级别缓存key: {}", cacheKey);
        Optional<HttpServletRequest> reqOpt = getRequest();
        return reqOpt.map(req -> (T)req.getAttribute(cacheKey)).orElseGet(()->{
            T t = callback.get();
            reqOpt.ifPresent(req -> {
                synchronized (req){
                    req.setAttribute(cacheKey, t);
                }
            });
            return t;
        });
    }

    /**
     * 移除所有attr属性
     * @return
     */
    public static boolean removeReqAttr(String ...prevAttrKeys){
        List<String> removePreList = Arrays.stream(prevAttrKeys).map(pak -> ReUtil.delAll("[_{}]+$", pak)).collect(Collectors.toList());
        //需要清理下请求级别的缓存
        getRequest().ifPresent(req ->{
            Enumeration<String> attributeNames = req.getAttributeNames();
            while (attributeNames.hasMoreElements()){
                String attr = attributeNames.nextElement();

                if(removePreList.stream().anyMatch(prevAttrKey -> StrUtil.startWith(attr, prevAttrKey))){
                    try {
                        req.removeAttribute(attr);
                    } catch (Exception e) {
                        //请求对象在线程间传递, 有可能会发生异常, 但是对象中的属性实际上已经移除
                        log.warn("移除请求[]属性", attr, e);
                    }
                }
            }
        });
        return true;
    }

    /**
     * 带上下文执行
     * @param supplier
     * @return
     */
    public static <U> Supplier<U> supplierWithContext(Supplier<U> supplier) {
        // 传递请求的上线
        // 将上下文设置到子线程中
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        LocaleContext localeContext = LocaleContextHolder.getLocaleContext();
        // 传递租户编号
        Integer xtenant = XtenantEnum.getXtenant();
        // 传递traceId
        String traceId = MDC.get(TraceIdUtil.TRACE_ID_KEY);
        Thread currentThread = Thread.currentThread();
        return () -> {
            // 如果是同一线程则直接执行
            if (currentThread == Thread.currentThread()) {
                return supplier.get();
            }
            try {
                RequestContextHolder.setRequestAttributes(requestAttributes);
                LocaleContextHolder.setLocaleContext(localeContext);
                XtenantEnum.setXtenant(xtenant);
                MDC.put(TraceIdUtil.TRACE_ID_KEY, traceId);
                return supplier.get();
            } finally {
                RequestContextHolder.resetRequestAttributes();
                LocaleContextHolder.resetLocaleContext();
                MDC.remove(TraceIdUtil.TRACE_ID_KEY);
            }
        };
    }



    /**
     * 带上下文执行
     * @param runnable
     * @return
     */
    public static Runnable runnableWithContext(Runnable runnable) {
        // 传递请求的上线
        // 将上下文设置到子线程中
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        LocaleContext localeContext = LocaleContextHolder.getLocaleContext();
        // 传递租户编号
        Integer xtenant = XtenantEnum.getXtenant();
        // 传递traceId
        String traceId = MDC.get(TraceIdUtil.TRACE_ID_KEY);
        Thread currentThread = Thread.currentThread();
        return () -> {
            // 如果是同一线程则直接执行
            if (currentThread == Thread.currentThread()) {
                runnable.run();
                return;
            }
            try {
                RequestContextHolder.setRequestAttributes(requestAttributes);
                LocaleContextHolder.setLocaleContext(localeContext);
                XtenantEnum.setXtenant(xtenant);
                MDC.put(TraceIdUtil.TRACE_ID_KEY, traceId);
                runnable.run();
            } finally {
                RequestContextHolder.resetRequestAttributes();
                LocaleContextHolder.resetLocaleContext();
                MDC.remove(TraceIdUtil.TRACE_ID_KEY);
            }
        };
    }
}
