package com.jiuji.oa.afterservice.smallpro.util;

import cn.hutool.core.util.ObjectUtil;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.smallpro.enums.SourceTypeEnum;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;


/**
 * description: <贴膜损耗工具类>
 *
 * <AUTHOR>
 * @date 2024-07-30
 * @since 1.0.0
 */
public class FilmLossUtil {

    /**
     * 判断是否开启贴膜损耗功能
     * 开启条件：九机租户 或者 小件单来源类型为销售单贴膜损耗
     *
     * @param smallpro 小件单对象
     * @return true-开启贴膜损耗功能，false-关闭贴膜损耗功能
     */
    public static boolean isFilmLossEnabled(Smallpro smallpro) {
        // 先判空，避免空指针异常
        if (ObjectUtil.isNull(smallpro)) {
            return false;
        }
        
        // 九机租户开关
        boolean isJiujiTenant = XtenantEnum.isJiujiXtenant();
        
        // 销售单贴膜损耗来源类型开关
        boolean isSaleOrderFilmLoss = ObjectUtil.equals(SourceTypeEnum.SALE_ORDER_FILM_LOSS.getCode(), smallpro.getSourceType());
        
        // 任一条件满足即开启贴膜损耗功能
        return isJiujiTenant || isSaleOrderFilmLoss;
    }

}
