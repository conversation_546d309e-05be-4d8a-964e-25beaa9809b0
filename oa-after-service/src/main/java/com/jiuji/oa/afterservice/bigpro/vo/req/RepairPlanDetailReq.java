package com.jiuji.oa.afterservice.bigpro.vo.req;

import com.jiuji.oa.afterservice.bigpro.vo.RepairFaultVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 维修方案详情VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@ApiModel(value = "RepairPlanDetailVO对象", description = "维修方案详情VO")
public class RepairPlanDetailReq   {

    @NotNull(message = "售后单ID不能为空")
    private Integer shouHouId;


    /**
     * 提供给网站接口需要鉴权
     */
    private Integer userId;

} 