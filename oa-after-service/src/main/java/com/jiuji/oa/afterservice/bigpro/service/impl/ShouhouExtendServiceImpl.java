package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.log.StaticLog;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.jiuji.cloud.after.enums.BaoXiuTypeEnum;
import com.jiuji.cloud.after.enums.ServiceEnum;
import com.jiuji.cloud.huishou.service.ProductSupportCloud;
import com.jiuji.cloud.huishou.vo.Result;
import com.jiuji.cloud.huishou.vo.response.SalfGoodsUsageVO;
import com.jiuji.oa.afterservice.api.bo.Ch999UserBasicBO;
import com.jiuji.oa.afterservice.api.service.OaApiService;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.bo.shouhou.MemberUserCodeRes;
import com.jiuji.oa.afterservice.bigpro.bo.shouhou.ScanCodeAddSub;
import com.jiuji.oa.afterservice.bigpro.dao.BbsxpUsersMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouHouPjMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouExMapper;
import com.jiuji.oa.afterservice.bigpro.enums.*;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.repository.RecoverMkcNewLogsRepository;
import com.jiuji.oa.afterservice.bigpro.repository.document.RecoverMkcNewLogs;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.MemberSubVO;
import com.jiuji.oa.afterservice.bigpro.vo.RepairOrderLogVO;
import com.jiuji.oa.afterservice.bigpro.vo.WeixiuzuKindVo;
import com.jiuji.oa.afterservice.bigpro.vo.req.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.bo.SubPushMsgBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.rabbimq.RabbitMqConfig;
import com.jiuji.oa.afterservice.common.constant.*;
import com.jiuji.oa.afterservice.common.enums.*;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.vo.res.ListBean;
import com.jiuji.oa.afterservice.other.bo.AreaBelongsDcHqD1AreaId;
import com.jiuji.oa.afterservice.other.bo.ShouhouTuiHuanInfo;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import com.jiuji.oa.afterservice.stock.po.MkcDellogs;
import com.jiuji.oa.afterservice.stock.service.IRecoverMkcService;
import com.jiuji.oa.afterservice.stock.service.MkcDellogsService;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.res.BbsxpUserSimpleRes;
import com.jiuji.oa.orginfo.member.res.MemberBasicRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.PlateFormConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.ch999.common.util.vo.Result.SUCCESS;

/**
 * @Description:
 * @author: Li Quan
 * @date: 2020/5/12 9:53
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ShouhouExtendServiceImpl extends ServiceImpl<ShouhouExMapper, Shouhou> implements ShouhouExtendService {
    private final ShouhouService shouhouService;
    private final AreaInfoClient areaInfoClient;
    private final WeixiuzuKindService weixiuzuKindService;
    private final AbstractCurrentRequestComponent currentRequestComponent;
    private final CaigouBasketRefShouhouService caigouBasketRefShouhouService;
    private final ShouhouDaojishiService shouhouDaojishiService;
    private final DiaoboSubService diaoboSubService;
    private final DiaobosubCommentService diaobosubCommentService;
    private final DiaoboBasketService diaoboBasketService;
    private final SmsService smsService;
    private final UserInfoClient userInfoClient;
    private final ShouhoutestInfoService shouhoutestInfoService;
    private final MemberClient memberClient;
    private final SubService subService;
    private final ShouhouExService shouhouExService;
    private final MsoftService msoftService;
    private final WeixinUserService weixinUserService;
    private final DaiyongjiService daiyongjiService;
    private final AddinfopsService addinfopsService;
    private final WeixiuzulogsService weixiuzulogsService;
    private final BasketService basketService;
    private final BbsxpUsersService usersService;
    private final ProductinfoService productinfoService;
    private final ProductKcService productKcService;
    private final ShouhouTuihuanService shouhouTuihuanService;
    private final ShouHouPjMapper shouHouPjMapper;
    private final NumberCardService numberCardService;
    private final CardLogsService cardLogsService;
    private final ShouHouPjService shouHouPjService;
    private final Executor pushMessageExecutor;
    private final SubReceptionService subReceptionService;
    private final OaApiService oaApiService;
    private final AuthConfigService authConfigService;
    private final SysConfigClient sysConfigClient;
    private final AreainfoService areainfoService;
    private final RabbitTemplate rabbitTemplate;
    private final BbsxpUsersMapper bbsxpUsersMapper;
    private final ShouhouQudaoService shouhouQudaoService;
    private final ProductSupportCloud productSupportCloud;
    private final WxkcoutputService wxkcoutputService;
    private final IRecoverMkcService recoverMkcService;
    private final MkcDellogsService mkcDellogsService;

    private final RecoverMkcNewLogsRepository recoverMkcNewLogsRepository;
    private final Integer THREAD_HOLD = 1000;
    @CreateCache(name = "ShouhouExtendService.listAfter:", expire = 5,localLimit = 2,timeUnit = TimeUnit.MINUTES,cacheType = CacheType.LOCAL)
    @CachePenetrationProtect
    private Cache<String, List<ShouhouIndexRes>> listAfterCache;

    @Override
    @DS(value = DataSourceConstants.CH999_OA_NEW)
    public IPage<ShouhouIndexRes> listAfterServiceIndex(ShouhouIndexReq shouhou) {
        shouhou.setKey(shouhou.getKey().replaceAll("\\s+", ""));
        shouhou.setKeyIsNum(NumberUtil.isNumber(shouhou.getKey()));
        shouhou.setWaisongNameIsNumber(NumberUtil.isNumber(shouhou.getWaisongname()));
        shouhou.setIsweixiu(StrUtil.emptyIfNull(shouhou.getIsweixiu()));
        shouhou.setBaoxiu(StrUtil.emptyIfNull(shouhou.getBaoxiu()));
        shouhou.setKey(StrUtil.emptyIfNull(shouhou.getKey()).trim().replace("'", ""));
        OaUserBO currentUser = currentRequestComponent.getCurrentStaffId();
        shouhou.setAuthid(currentUser.getAuthorizeId());
        shouhou.setArea_kind1(currentUser.getAreaKind1());
        IPage<ShouhouIndexRes> page = getList(shouhou);
        return page;

    }

    public IPage<ShouhouIndexRes> getList(ShouhouIndexReq shouhou) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        // 3天未跟进通知
//        ResultTimeRemind();
        //注意:非保自修和非保交易查询时不能已保修自动同时查询
        boolean isBaoxiuNotEmpty = false;
        if (StrUtil.isNotBlank(shouhou.getFeibaoKind()) && (Objects.equals(shouhou.getFeibaoKind(), "feibaozixiu") || Objects.equals(shouhou.getFeibaoKind(), "feibaojiaoyi"))) {
            shouhou.setBaoxiu("");
        }
        long current = 1;
        long pageSize = 10;
        if (shouhou.getPagination() != null) {
            current = shouhou.getPagination().getCurrent() == null ? 1 : shouhou.getPagination().getCurrent();
            pageSize = shouhou.getPagination().getPageSize() == null ? 10 : shouhou.getPagination().getPageSize();
        }
        Page<ShouhouIndexRes> page = new Page<>(current, pageSize);
        if (CollectionUtils.isNotEmpty(shouhou.getAreaIds()) && shouhou.getAreaIds().size() > 1000) {
            shouhou.setAreaIds(new ArrayList<>());
        }
        List<Integer> huiShouAndNewJ = shouhou.getHuiShouAndNewJ();
        if(XtenantEnum.isJiujiXtenant() && CollectionUtils.isNotEmpty(huiShouAndNewJ)){
            shouhou.setSourceType(NumberConstant.ONE);
            if(huiShouAndNewJ.contains(HuiShouAndNewJEnum.NEW_MACHINE.getCode())){
                shouhou.setIsNewMachine(NumberConstant.ONE);
            }
            if(huiShouAndNewJ.contains(HuiShouAndNewJEnum.EXCELLENT_PRODUCT.getCode())){
                shouhou.setIsExcellentProduct(NumberConstant.ONE);
            }
            if(huiShouAndNewJ.contains(HuiShouAndNewJEnum.GOOD_PRODUCT.getCode())){
                shouhou.setIsGoodProduct(NumberConstant.ONE);
            }
            if(huiShouAndNewJ.contains(HuiShouAndNewJEnum.VALUE_ADDED_RECYCLING.getCode())){
                shouhou.setIsValueAddedRecycling(NumberConstant.ONE);
            }
            if(huiShouAndNewJ.contains(HuiShouAndNewJEnum.EXTERNAL_REPAIR_MACHINE.getCode())){
                shouhou.setIsExternalRepairMachine(NumberConstant.ONE);
            }
            if(huiShouAndNewJ.contains(HuiShouAndNewJEnum.GOODS_IN_STOCK.getCode())){
                shouhou.setIsGoodsInStock(NumberConstant.ONE);
            }
        }

        if(IndexPageEnums.ShouKind1.ID.getCode().equals(shouhou.getShou_kind1()) && StringUtils.isNotEmpty(shouhou.getKey())){
            String key = shouhou.getKey().trim();
            //判断key是否为用,拼接数字的字符串 采用正则表达式来判断
            if(key.matches("^\\d+(,\\d+)*$")){
                //判断是否为用,拼接数字的字符串
               shouhou.setShouHouIdList(Arrays.stream(key.split(",")).map(Integer::parseInt).collect(Collectors.toList()));
            } else {
                throw new CustomizeException("请输入正确的"+IndexPageEnums.ShouKind1.ID.getMessage());
            }
        }
        //授权隔离
        shouhou.setXtenant(oaUserBO.getXTenant());
        //授权隔离
        Boolean authPart = authConfigService.isAuthPart(oaUserBO);
        if (authPart) {
            AreaBelongsDcHqD1AreaId backInfo = areainfoService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
            //非HQ地区，数据需要隔离,排除九机
            if (backInfo != null && oaUserBO.getXTenant() >= THREAD_HOLD && !CollUtil.contains(backInfo.getHqAreaIds(), oaUserBO.getAreaId())) {
                shouhou.setIsAuthPart(Boolean.TRUE);
                shouhou.setAuthorizeId(oaUserBO.getAuthorizeId());
            }
        }else if(Boolean.TRUE.equals(shouhou.getKeyIsNum()) && IndexPageEnums.ShouKind1.ID.getCode().equals(shouhou.getShou_kind1())
                && StrUtil.isNotBlank(shouhou.getKey())){
            //单号查询特殊处理 忽略地区限制 只保留授权隔离
            idQuerySetOtherNull(shouhou);
        }
        Integer selectHis = Optional.ofNullable(shouhou.getSelectHis()).orElse(NumberConstant.ZERO);
        Long count = null;
        if( NumberConstant.ONE.equals(selectHis)){
            count = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> baseMapper.getListCount(shouhou));
        } else {
            count = baseMapper.getListCount(shouhou);
        }
        if (count == 0) {
            return page;
        }
        List<ShouhouIndexRes> result = null;
        LocalDateTime today = LocalDateTime.now();
        //查询数量达到阈值且时间不包含当天进行缓存
        if(ObjectUtil.defaultIfNull(page.getSize(),0L) > NumberConstant.ONE_HUNDRED
              && shouhou.getDate1() != null && shouhou.getDate2() != null && (today.isBefore(shouhou.getDate1()) || today.isAfter(shouhou.getDate2()))){
            result = listAfterCache.computeIfAbsent(DigestUtil.md5Hex(JSON.toJSONString(shouhou)), key -> findList(shouhou,
                    (page.getCurrent() - 1) * page.getSize(), page.getSize()));
        }else{
            result = findList(shouhou,
                    (page.getCurrent() - 1) * page.getSize(), page.getSize());
        }
        List<Integer> shouhouIds = result.parallelStream().map(Shouhou::getId).collect(Collectors.toList());
        // 查询测试结果
        List<ShouhouTestResultBo> testResultBoList = shouhoutestInfoService.checkShouhouTestResult(shouhouIds);
        //查询配件发货
        List<Integer> needQueryResult = caigouBasketRefShouhouService.getPeijianfahuo(shouhouIds);
        List<ShouhouDaojishi> daojishiList = shouhouDaojishiService.getDaojishiProduct();
        List<AreaInfo> allArea = areaInfoClient.listAll().getData();
        //查询配件分类
        List<ShouhouCategoryRes> getCategoryByCidList = new ArrayList<>();
        //查询订单的售后渠道
        List<ShouhouQudao> shouhouQudaoList = new ArrayList<>();
        if( NumberConstant.ONE.equals(selectHis)){
            getCategoryByCidList = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, ()->CommenUtil.bigDataInQuery(shouhouIds,ids -> baseMapper.getCategoryByShouIdList(ids)));
            shouhouQudaoList = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS,()->CommonUtils.bigDataInQuery(NumberConstant.PROGRAMMER_NUMBER, shouhouIds, ids ->
                    shouhouQudaoService.list(new LambdaQueryWrapper<ShouhouQudao>().in(ShouhouQudao::getShouhouid, ids))));
        } else {
            getCategoryByCidList = CommenUtil.bigDataInQuery(shouhouIds,ids -> baseMapper.getCategoryByShouIdList(ids));
            shouhouQudaoList = CommonUtils.bigDataInQuery(NumberConstant.PROGRAMMER_NUMBER, shouhouIds, ids ->
                    shouhouQudaoService.list(new LambdaQueryWrapper<ShouhouQudao>().in(ShouhouQudao::getShouhouid, ids)));
        }
        Map<Integer, List<ShouhouCategoryRes>> collect = getCategoryByCidList.stream().collect(Collectors.groupingBy(ShouhouCategoryRes::getWxid));
        Map<Integer, ShouhouQudao> shouhouQudaoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(shouhouQudaoList)){
            shouhouQudaoMap = shouhouQudaoList.stream().filter(CommenUtil.distinctByKey(ShouhouQudao::getShouhouid)).collect(Collectors.toMap(ShouhouQudao::getShouhouid, e -> e));
        }
        for (ShouhouIndexRes shouhouIndexRes : result) {
            shouhouIndexRes.setArea(allArea.parallelStream().filter(p -> Objects.equals(shouhouIndexRes.getAreaid(), p.getId()))
                    .map(AreaInfo::getArea).findFirst().orElse(""));
            shouhouIndexRes.setToarea(Objects.equals(shouhouIndexRes.getToareaid(), 0) ? "" : allArea.parallelStream()
                    .filter(p -> Objects.equals(shouhouIndexRes.getToareaid(), p.getId()))
                    .map(AreaInfo::getArea).findFirst().orElse(""));
            shouhouIndexRes.setDaojishiLabel(getShouHouWeiXiuZqByShouhouInfo(shouhouIndexRes,
                    daojishiList.parallelStream().filter(p -> Objects.equals(p.getProductId().longValue(), shouhouIndexRes.getProductId()))
                            .findFirst().orElse(null), allArea).getTimeTextDjs());
            shouhouIndexRes.setNeedFahuo(needQueryResult.contains(shouhouIndexRes.getId()));
            shouhouIndexRes.setTestResultInfo(testResultBoList.parallelStream().filter(p -> Objects.equals(p.getShouhouId(), shouhouIndexRes.getId())).findFirst()
                    .map(ShouhouTestResultBo::getTestResultInfo).orElse(""));
            //添加维修配件分类
            collect.forEach((k, v) -> {
                if (Objects.equals(k, shouhouIndexRes.getId())) {
                    // 去重
                    List<ShouhouCategoryRes> collect1 = v.stream().distinct().filter(item -> item.getWxpj() != null).collect(Collectors.toList());
                    String name = collect1.stream().map(ShouhouCategoryRes::getWxpj).map(String::valueOf).collect(Collectors.joining(","));
                    if (name.isEmpty()) {
                        shouhouIndexRes.setWxpjCategory("其他");
                    } else {
                        shouhouIndexRes.setWxpjCategory(name);
                    }
                }
            });


            if (shouhouIndexRes.getModidate() != null) {
                Duration duration = Duration.between(shouhouIndexRes.getModidate(), today);
                if (duration.toDays() >= 1) {
                    shouhouIndexRes.setModidateLabel("(" + duration.toDays() + "天)");
                } else if (duration.toHours() >= 1) {
                    shouhouIndexRes.setModidateLabel("(" + duration.toHours() + "小时)");
                } else {
                    shouhouIndexRes.setModidateLabel("(" + duration.toMinutes() + "分钟)");
                }
            } else {
                shouhouIndexRes.setModidateLabel("");
            }
            if (shouhouIndexRes.getTradedate() != null) {
                Duration duration = Duration.between(shouhouIndexRes.getTradedate(), today);
                shouhouIndexRes.setTradedateLabel((duration.toDays() + 1) + "天");
            } else {
                shouhouIndexRes.setTradedateLabel("");
            }
            if (shouhouIndexRes.getResultDtime() != null) {
                Duration duration = Duration.between(shouhouIndexRes.getResultDtime(), today);
                shouhouIndexRes.setResultDtimeLabel("(" + duration.toDays() + "天)");
            } else {
                shouhouIndexRes.setResultDtimeLabel("");
            }
            BigDecimal feiyong = shouhouIndexRes.getFeiyong() == null ? BigDecimal.ZERO : shouhouIndexRes.getFeiyong();
            BigDecimal youhuifeiyong = shouhouIndexRes.getYouhuifeiyong() == null ? BigDecimal.ZERO : shouhouIndexRes.getYouhuifeiyong();
            shouhouIndexRes.setYingfum(feiyong.subtract(youhuifeiyong));
            if (shouhouIndexRes.getIsquji_() == null || shouhouIndexRes.getIsquji() == null){
                shouhouIndexRes.setIsquji_(Boolean.TRUE.equals(Optional.ofNullable(shouhouIndexRes.getIsQuJi()).orElse(NumberConstant.ZERO) == 1));
                shouhouIndexRes.setIsquji(Boolean.TRUE.equals(Optional.ofNullable(shouhouIndexRes.getIsQuJi()).orElse(NumberConstant.ZERO) == 1));
            }
            //添加渠道名称
            if (CollUtil.isNotEmpty(shouhouQudaoMap)) {
                Optional<ShouhouQudao> shouhouQudao = Optional.ofNullable(shouhouQudaoMap.get(shouhouIndexRes.getId()));
                shouhouQudao.ifPresent(qudao -> shouhouIndexRes.setQuDaoName(qudao.getShqd2name()));
            }
        }
        page.setRecords(result);
        page.setTotal(count);
        return page;
    }

    private List<ShouhouIndexRes> findList( ShouhouIndexReq shouhou, long startIndex,  long pageSize){
        Integer selectHis = Optional.ofNullable(shouhou.getSelectHis()).orElse(NumberConstant.ZERO);
        List<ShouhouIndexRes> shouhouIndexRes = new ArrayList<>();
        if(NumberConstant.ONE.equals(selectHis)){
            shouhouIndexRes = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS,()->baseMapper.getList(shouhou, startIndex, pageSize));
        } else {
            shouhouIndexRes = baseMapper.getList(shouhou, startIndex, pageSize);
        }
        return shouhouIndexRes;
    }

    private void idQuerySetOtherNull(ShouhouIndexReq shouhou) {
        shouhou.setAreaIds(null);
        shouhou.setIssoft(null);
        shouhou.setShou_kind2(null);
        shouhou.setBaoxiu(null);
        shouhou.setIsyouhui(null);
        shouhou.setIsquji(null);
        shouhou.setDate1(null);
        shouhou.setDate2(null);
        shouhou.setWaisong(null);
    }

//    /**
//     * getList方法多线程版(fix ClientAbortException)
//     *
//     * @param req
//     * @return
//     */
//    public IPage<ShouhouIndexRes> getListParallel(ShouhouIndexReq req) {
//        // 3天未跟进通知
////        ResultTimeRemind();
//        //注意:非保自修和非保交易查询时不能已保修自动同时查询
//        boolean isBaoxiuNotEmpty = false;
//        LocalDateTime now = LocalDateTime.now();
//        if (StrUtil.isNotBlank(req.getFeibaoKind()) && (Objects.equals(req.getFeibaoKind(), "feibaozixiu") || Objects.equals(req.getFeibaoKind(), "feibaojiaoyi"))) {
//            req.setBaoxiu("");
//        }
//        long current = 1;
//        long pageSize = 10;
//        if (req.getPagination() != null) {
//            current = req.getPagination().getCurrent() == null ? 1 : req.getPagination().getCurrent();
//            pageSize = req.getPagination().getPageSize() == null ? 10 : req.getPagination().getPageSize();
//        }
//        Page<ShouhouIndexRes> page = new Page<>(current, pageSize);
//        Long count = baseMapper.getListCount(req);
//        if (count == 0) {
//            return page;
//        }
//        CompletableFuture<List<AreaInfo>> areaInfoFuture = CompletableFuture.supplyAsync(
//                () -> areaInfoClient.listAll().getData(), pushMessageExecutor);
//        CompletableFuture<List<ShouhouIndexRes>> resultFuture = CompletableFuture.supplyAsync(
//                () -> baseMapper.getList(req,
//                        (page.getCurrent() - 1) * page.getSize(), page.getSize()), pushMessageExecutor);
//        CompletableFuture<List<Integer>> shouhouIdsFuture = resultFuture.thenApply(shouhouIndexRes ->
//                shouhouIndexRes.stream().map(Shouhou::getId).collect(Collectors.toList()));
//        CompletableFuture<List<ShouhouDaojishi>> daojishiListFuture = CompletableFuture.supplyAsync(
//                shouhouDaojishiService::getDaojishiProduct, pushMessageExecutor);
//        CompletableFuture<List<ShouhouTestResultBo>> testResultBoListFuture = shouhouIdsFuture.thenApplyAsync(
//                shouhoutestInfoService::checkShouhouTestResult, pushMessageExecutor);
//        CompletableFuture<List<Integer>> needQueryResultFuture = shouhouIdsFuture.thenApplyAsync(
//                caigouBasketRefShouhouService::getPeijianfahuo, pushMessageExecutor);
//
//        //等待所有任务执行完毕
//        CompletableFuture.allOf(
//                areaInfoFuture,
//                resultFuture,
//                shouhouIdsFuture,
//                testResultBoListFuture,
//                needQueryResultFuture,
//                daojishiListFuture).join();
//        // 查询测试结果
//        List<AreaInfo> allArea = areaInfoFuture.join();
//        List<ShouhouIndexRes> result = resultFuture.join();
//        List<ShouhouTestResultBo> testResultBoList = testResultBoListFuture.join();
//        //查询配件发货
//        List<Integer> needQueryResult = needQueryResultFuture.join();
//        List<ShouhouDaojishi> daojishiList = daojishiListFuture.join();
//
//
//        for (ShouhouIndexRes shouhouIndexRes : result) {
//            shouhouIndexRes.setArea(allArea.stream().filter(p -> Objects.equals(shouhouIndexRes.getAreaid(), p.getId()))
//                    .map(AreaInfo::getArea).findFirst().orElse(""));
//            shouhouIndexRes.setToarea(Objects.equals(shouhouIndexRes.getToareaid(), 0) ? "" : allArea.stream()
//                    .filter(p -> Objects.equals(shouhouIndexRes.getToareaid(), p.getId()))
//                    .map(AreaInfo::getArea).findFirst().orElse(""));
//            shouhouIndexRes.setDaojishiLabel(getShouHouWeiXiuZqByShouhouInfo(shouhouIndexRes,
//                    daojishiList.stream().filter(p -> Objects.equals(p.getProductId().longValue(), shouhouIndexRes.getProductId()))
//                            .findFirst().orElse(null), allArea).getTimeTextDjs());
//            shouhouIndexRes.setNeedFahuo(needQueryResult.contains(shouhouIndexRes.getId()));
//            shouhouIndexRes.setTestResultInfo(testResultBoList.stream().filter(p -> Objects.equals(p.getShouhouId(), shouhouIndexRes.getId())).findFirst()
//                    .map(ShouhouTestResultBo::getTestResultInfo).orElse(""));
//            if (Objects.equals(null, shouhouIndexRes.getModidate())) {
//                shouhouIndexRes.setModidateLabel("");
//            } else {
//                Duration duration = Duration.between(shouhouIndexRes.getModidate(), now);
//                if (duration.toDays() >= 1) {
//                    shouhouIndexRes.setModidateLabel("(" + duration.toDays() + "天)");
//                } else if (duration.toHours() >= 1) {
//                    shouhouIndexRes.setModidateLabel("(" + duration.toHours() + "小时)");
//                } else {
//                    shouhouIndexRes.setModidateLabel("(" + duration.toMinutes() + "分钟)");
//                }
//            }
//
//            if (shouhouIndexRes.getTradedate() != null) {
//                shouhouIndexRes.setTradedateLabel((
//                        Duration.between(shouhouIndexRes.getTradedate(), now).toDays() + 1) + "天");
//            } else {
//                shouhouIndexRes.setTradedateLabel("");
//            }
//            if (shouhouIndexRes.getResultDtime() != null) {
//                shouhouIndexRes.setResultDtimeLabel("("
//                        + Duration.between(shouhouIndexRes.getResultDtime(), now).toDays() + "天)");
//            } else {
//                shouhouIndexRes.setResultDtimeLabel("");
//            }
//        }
//        page.setRecords(result);
//        page.setTotal(count);
//        return page;
//    }

    @Override
    public Map<String, List<EnumVO>> getIndexEnums() {
        Map<String, List<EnumVO>> enums = Maps.newHashMapWithExpectedSize(16);
        enums.put("shou_kind1", EnumUtil.toEnumVOList(IndexPageEnums.ShouKind1.class, "mobile"));
        enums.put("issoft", EnumUtil.toEnumVOList(IndexPageEnums.Issoft.class, "0"));
        enums.put("shou_kind2", EnumUtil.toEnumVOList(IndexPageEnums.ShouKind2.class, "clz"));
        enums.put("baoxiu", EnumUtil.toEnumVOList(IndexPageEnums.Baoxiu.class));
        enums.put("isquji", EnumUtil.toEnumVOList(IndexPageEnums.Isquji.class, 0));
        enums.put("tuihuan_kind", EnumUtil.toEnumVOList(IndexPageEnums.Tuihuankind.class));
        enums.put("huiShouAndNewJ", EnumUtil.toEnumVOList(HuiShouAndNewJEnum.class));
        List<EnumVO> weixiuzuid = new ArrayList<>();
        EnumVO vo = new EnumVO();
        vo.setLabel("不需维修");
        vo.setValue("0");
        weixiuzuid.add(vo);
        EnumVO vo1 = new EnumVO();
        vo1.setLabel("空");
        vo1.setValue("");
        weixiuzuid.add(vo1);
        weixiuzuid.addAll(weixiuzuKindService.getWxGroupList().parallelStream().map(p -> {
            EnumVO tmp = new EnumVO();
            tmp.setLabel(p.getName());
            tmp.setValue(p.getId().toString());
            return tmp;
        }).collect(Collectors.toList()));
        enums.put("weixiuzuid", weixiuzuid);
        enums.put("istoarea", EnumUtil.toEnumVOList(IndexPageEnums.IsToArea.class));
        List<EnumVO> serviceTypeEnums = EnumUtil.toEnumVOList(BaoXiuTypeEnum.class);
        AreaInfo currentArea = shouhouService.getAreaSubject(currentRequestComponent.getCurrentStaffId().getAreaId());
        EnumVO serviceType = new EnumVO();
        serviceType.setLabel(ConfigConsts.WEB_NAME.equals(currentArea.getPrintName()) ? "非九机服务" : "非" + currentArea.getPrintName() + "服务");
        serviceType.setValue("3");
        serviceTypeEnums.add(serviceType);
        enums.put("serviceType", serviceTypeEnums);
        enums.put("repairLevel", EnumUtil.toEnumVOList(IndexPageEnums.RepairLevel.class));
        if (XtenantEnum.isJiujiXtenant()){
            List<Integer> codeList = Arrays.stream(ServiceEnum.values()).filter(s -> Boolean.TRUE.equals(s.isShouHouService()))
                    .map(ServiceEnum::getCode).collect(Collectors.toList());
            enums.put("shouhouServicesSale", EnumUtil.toEnumVOList(ServiceEnum.class)
                    .stream().filter(x -> (codeList.contains(Convert.toInt(x.getValue())))).collect(Collectors.toList()));
        }else {
            enums.put("shouhouServicesSale", EnumUtil.toEnumVOList(ServiceEnum.class));
        }
        enums.put("webType2", EnumUtil.toEnumVOList(IndexPageEnums.WebType.class));
        enums.put("youPinMachine", EnumUtil.toEnumVOList(IndexPageEnums.YouPinMachine.class));
        enums.put("waisong", EnumUtil.toEnumVOList(IndexPageEnums.WaiSong.class));
        enums.put("date_kind", EnumUtil.toEnumVOList(IndexPageEnums.DateKindV2.class, ""));
        enums.put("isCodeSub", Arrays.asList(new EnumVO().setValue("true").setLabel("是"), new EnumVO().setValue("false").setLabel("否")));
        enums.put("couponKinds", EnumUtil.toEnumVOList(TuangouCouponKindsEnum.class));
        return enums;
    }

    @Override
    public ShouhouWxzqRes getShouHouWeiXiuZqByShouhouInfo(Shouhou info, ShouhouDaojishi shouhouDaoJiShi, List<AreaInfo> allArea) {
        Integer zaituDay = 0;
        Integer wxzq = 0;
        Integer ppriceid;
        if (info != null) {
            ppriceid = info.getPpriceid();
            if (ppriceid != null && ppriceid > 0 && shouhouDaoJiShi != null) {
                zaituDay = shouhouDaoJiShi.getZtday();
                Integer wxkind = info.getWxkind() == null ? 0 : info.getWxkind();
                if (wxkind.equals(ServicesWayEnum.YYDD.getCode())) {
                    wxzq = shouhouDaoJiShi.getXday();
                } else if (wxkind.equals(ServicesWayEnum.SMQJ.getCode())) {
                    wxzq = shouhouDaoJiShi.getHday();
                } else if (wxkind.equals(ServicesWayEnum.YJSX.getCode())) {
                    wxzq = shouhouDaoJiShi.getDday();
                } else if (wxkind.equals(ServicesWayEnum.SMWX.getCode())) {
                    wxzq = shouhouDaoJiShi.getSday();
                } else if (wxkind.equals(ServicesWayEnum.DMYY.getCode())) {
                    wxzq = shouhouDaoJiShi.getZday();
                } else if (wxkind.equals(ServicesWayEnum.SMAZ.getCode())) {
                    wxzq = shouhouDaoJiShi.getKday();
                } else {
                    wxzq = shouhouDaoJiShi.getXday();
                }
            }

            if (info.getDaojishi() != null) {
                wxzq = info.getDaojishi();
            } else {
                //接件地非昆明市区倒计时要加上"在途时间"
                Integer cityId = allArea.parallelStream().filter(p -> Objects.equals(p.getId(), info.getAreaid()))
                        .map(AreaInfo::getCityId).findFirst().orElse(-1);
                if (!Arrays.asList(530102, 530103, 530111, 530112).contains(cityId)) {
                    wxzq += zaituDay;
                }
            }
        }
        ShouhouWxzqRes wxzqRes = new ShouhouWxzqRes();
        if (wxzq != 0 && info.getModidate() != null) {
            //维修倒计时`
            String timeTextDjs = "";
            LocalDateTime yDate = LocalDateTime.now();
            if (!Boolean.TRUE.equals(info.getIsquji()) && info.getQujitongzhitime() != null) {
                yDate = info.getQujitongzhitime();
            } else if (!Boolean.TRUE.equals(info.getIsquji()) && info.getQujitongzhitime() == null) {
                yDate = LocalDateTime.now();
            } else if (Boolean.TRUE.equals(info.getIsquji())) {
                if (info.getQujitongzhitime() != null) {
                    yDate = info.getQujitongzhitime();
                } else {
                    yDate = info.getOfftime();
                }
            }
            Duration timesp = Duration.between(yDate == null ? LocalDateTime.now() : yDate, info.getModidate().plusDays(wxzq));
            if (timesp.toHours() >= 24 || timesp.toHours() < -24) {
                timeTextDjs = String.format("%.1f", timesp.toHours() / 24.0) + "天";
            } else if (timesp.toHours() > 1 || timesp.toHours() < 0) {
                timeTextDjs = String.format("%.1f", timesp.toMinutes() / 60.0) + "小时";
            } else {
                timeTextDjs = timesp.toMinutes() + "分钟";
            }
            Long chaoshiDay = timesp.toDays();
            wxzqRes.setChaoshiDay(chaoshiDay);

            //维修总时长
            String timeTextSum = "";
            if (Boolean.TRUE.equals(info.getIsquji()) || info.getQujitongzhitime() != null) {
                if (info.getQujitongzhitime() != null) {
                    Duration timesp1 = Duration.between(info.getModidate(), info.getQujitongzhitime());

                    if (timesp1.toHours() >= 24 || timesp1.toHours() < -24) {
                        timeTextSum = String.format("%.1f", Math.abs(timesp1.toHours() / 24.0)) + "天";
                    } else if (timesp1.toHours() > 1 || timesp1.toHours() < 0) {
                        timeTextSum = String.format("%.1f", Math.abs(timesp1.toMinutes() / 60.0)) + "小时";
                    } else {
                        timeTextSum = Math.abs(timesp1.toMinutes()) + "分钟";
                    }
                } else if (info.getModidate() != null && info.getOfftime() != null) {
                    Duration timesp2 = Duration.between(info.getModidate(), info.getOfftime());
                    if (timesp2.toHours() >= 24 || timesp2.toHours() < -24) {
                        timeTextSum = String.format("%.1f", Math.abs(timesp2.toHours() / 24.0)) + "天";
                    } else if (timesp2.toHours() > 1 || timesp2.toHours() < 0) {
                        timeTextSum = String.format("%.1f", Math.abs(timesp2.toMinutes() / 60.0)) + "小时";
                    } else {
                        timeTextSum = Math.abs(timesp2.toMinutes()) + "分钟";
                    }

                }
            }
            wxzqRes.setTimeTextDjs(timeTextDjs);
            wxzqRes.setTimeTextSum(timeTextSum);
        }
        return wxzqRes;
    }

    @Override
    public Boolean deliverGoods(@RequestParam Integer shouhouId, @RequestParam String inUser) {
        //Shouhou shouhou = shouhouService.getById(shouhouId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU,shouhouId);
        if (shouhou == null) {
            throw new RRException("所指定的维修单不存在");
        }
        List<BasketsQueryBO> baskets = baseMapper.listBaskets(shouhouId);
        Map<Integer, DiaoboSub> diaoboSubs = Maps.newHashMapWithExpectedSize(10);
        List<DiaoboBasket> diaoboBaskets = new ArrayList<>();
        Integer areaId = shouhou.getToareaid() == null ? shouhou.getAreaid() : shouhou.getToareaid();
        if (areaId == null) {
            areaId = 0;
        }
        List<AreaInfo> allArea = areaInfoClient.listAll().getData();
        if (allArea == null) {
            allArea = Collections.emptyList();
        }
        for (BasketsQueryBO basket : baskets) {
            if (areaId.equals(basket.getAreaid()) || AreaConsts.AREA_DC.equals(areaId)) {
                continue;
            }
            DiaoboSub diaoboSub;
            if (diaoboSubs.containsKey(areaId)) {
                diaoboSub = diaoboSubs.get(areaId);
            } else {
                Integer finalAreaId = areaId;
                AreaInfo areaInfo = allArea.parallelStream().filter(p -> p.getId().equals(finalAreaId)).findFirst().orElse(null);
                String title = String.format("门店[%s]采购登记发货自动生成调拨单", areaInfo != null ? areaInfo.getArea() : "");
                diaoboSub = diaoboSubService.getOne(new LambdaQueryWrapper<DiaoboSub>().eq(DiaoboSub::getTitle, title)
                        .eq(DiaoboSub::getAreaid, basket.getAreaid()).eq(DiaoboSub::getToareaid, areaId)
                        .eq(DiaoboSub::getStats, 1).eq(DiaoboSub::getKinds, "wx"), true);
                if (diaoboSub == null) {
                    diaoboSub = new DiaoboSub().setInuser(inUser).setAreaid(basket.getAreaid()).setDtime(LocalDateTime.now())
                            .setKinds("wx").setToareaid(areaId).setStats(1).setTitle(title);
                    diaoboSubService.save(diaoboSub);
                }
                diaoboSubs.put(areaId, diaoboSub);
            }
            diaoboBaskets.add(new DiaoboBasket().setSubId(diaoboSub.getId().longValue()).setBasketId(basket.getBasketId())
                    .setPpriceid(basket.getPpriceid()).setInprice(basket.getInprice()).setLcount(1).setStatus(0));
            String log = String.format("维修单[%s]采购登记[%s-%s]发货,ppriceid:%s inprice:%s count:1", shouhouId, basket.getSubId(),
                    basket.getBasketId(), basket.getPpriceid(), basket.getInprice());
            DiaobosubComment diaobosubComment = new DiaobosubComment().setShowType(false).setInuser(inUser).setComment(log)
                    .setDbId(diaoboSub.getId());
            diaobosubCommentService.save(diaobosubComment);
        }
        diaoboBasketService.saveBatch(diaoboBaskets);
        if (AreaConsts.AREA_DC.equals(areaId)) {
            shouhouService.saveShouhouLog(shouhouId, "配件已到货麻烦领取", inUser);
            R<Ch999UserVo> r = userInfoClient.getCh999UserByUserName(shouhou.getWeixiuren());
            if (r.getCode() == ResultCode.SUCCESS) {
                weixinUserService.senWeixinAndOaMsg("配件已到货麻烦领取", "配件已到货麻烦领取", null, String.valueOf(r.getData().getCh999Id()), OaMesTypeEnum.SHTZ.getCode().toString());
            }
        }
        caigouBasketRefShouhouService.update(new LambdaUpdateWrapper<CaigouBasketRefShouhou>().set(CaigouBasketRefShouhou::getIsFahuo, true)
                .eq(CaigouBasketRefShouhou::getShouhouId, shouhouId).eq(CaigouBasketRefShouhou::getIsFahuo, false));
        if (!diaoboSubs.isEmpty()) {
            List<String> areas = new ArrayList<>();
            for (DiaoboSub sub : diaoboSubs.values()) {
                AreaInfo areaInfo = allArea.parallelStream().filter(p -> p.getId().equals(sub.getAreaid())).findFirst().orElse(new AreaInfo());
                areas.add(areaInfo.getArea());
            }
            List<Integer> ids = diaoboSubs.values().parallelStream().map(DiaoboSub::getId).collect(Collectors.toList());
            String content = String.format("订购配件已从[%s]发出；调拨单号：调拔操作 wx [%s]",
                    StrUtil.join(",", areas), StrUtil.join(",", ids));
            shouhouService.saveShouhouLog(shouhouId, content, inUser);
        }
        return true;
    }

    @Override
    public MemberSubsRes searchUserSub(@RequestParam String key,@RequestParam Integer pageSize,@RequestParam Integer currentPage) {

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        MemberSubsRes result = new MemberSubsRes();
        BbsxpUserSimpleRes user = memberClient.getMemberBasicInfoByTel(key, oaUserBO.getXTenant()).getData();
        if (user == null) {
            return result;
        }
        String shieldName = areainfoService.getShieldName(oaUserBO.getAreaId());
        List<MemberSubVO> userSubs = subService.searchSubInfoByUserId(user.getId());
        if(XtenantEnum.isJiujiXtenant()){
            MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> subService.searchSubInfoByUserId(user.getId()))
                    .stream().forEach(userSubs::add);
        }
        result.setTotal(userSubs.size());
        userSubs = userSubs.stream()
                .skip((long) pageSize * (currentPage - 1)).limit(pageSize).collect(Collectors.toList());
        userSubs.addAll(CommenUtil.autoQueryMergeHist(() -> subService.searchLpInfoByUserId(user.getId())));
        List<MemberSubVO> shieldService = subService.getShieldService(key);
        for (MemberSubVO memberSubVO : shieldService) {
            if (StrUtil.isNotEmpty(memberSubVO.getProductName())) {
                memberSubVO.setProductName(String.format("【%s】%s",shieldName,memberSubVO.getProductName()));
            }
        }
        result.setSize(pageSize);
        result.setCurrent(currentPage);
        //企业粉丝加粉状态查询
        result.setIsCorporateFans(shouhouService.getCorporateFans(user.getId()));
        userSubs.addAll(shieldService);
        result.setUser(user);
        result.setSubs(userSubs);
        List<ShouhouTuiHuanInfo> tuiHuanList = shouhouTuihuanService.getAfterServicesDiscount(user.getId());
        result.setAfterServicesDiscount(tuiHuanList.size());
        result.setTuiHuanList(tuiHuanList);
        //获取是否是Care+会员的标签
        R<String> configR = sysConfigClient.getValueByCode(SysConfigConstant.IMG_URL);
        BbsxpUsers bbsxpUsers = bbsxpUsersMapper.selectOne(new LambdaQueryWrapper<BbsxpUsers>().eq(BbsxpUsers::getId, user.getId()).last("and userTypes = userTypes | 8"));
        if (bbsxpUsers != null){
            result.setIsCarePlusMember(Boolean.TRUE);
            String format = String.format("%s/newstatic/1402,03d2402853821b51", configR.getData());
            result.setIsCarePlusUrl(format);
        }
        return result;
    }

    @Override
    public R<List<RepairHistoryRes>> repairHistories(String tradingHours, String imei, Long userId, String mobile, Boolean isLp) {
        List<RepairHistoryRes> repairHistoryRes = this.baseMapper.repairHistories(imei, userId, mobile, isLp,tradingHours).stream().distinct()
                .map(repairHistory->{
                    repairHistory.setServiceTypeText(ObjectUtil.defaultIfNull(EnumUtil.getMessageByCode(BaoXiuTypeEnum.class,repairHistory.getServiceType()),"无服务"));
                    return repairHistory;
                })
                .collect(Collectors.toList());
        R<List<RepairHistoryRes>> result = R.success(repairHistoryRes);
        Integer total = baseMapper.repairHistoriesCount(imei, userId, mobile, isLp, tradingHours);
        result.put("total", total);
        return result;
    }




    @Override
    public LastRepairRes selectLastRepair(LastRepairReq lastRepairReq) {

        return this.baseMapper.selectLastRepair(lastRepairReq);
    }

    @Override
    public IPage<PageRepairImeiHisVo> selectRepairImeiHis(PageRepairImeiReq pageRepairImeiReq) {
        Page<PageRepairImeiHisVo> page = new Page<>();
        page.setSize(Optional.ofNullable(pageRepairImeiReq.getSize()).orElse(NumberConstant.TEN))
                .setDesc("f.id")
                .setCurrent(Optional.ofNullable(pageRepairImeiReq.getCurrent()).orElse(NumberConstant.ONE));
        Page<PageRepairImeiHisVo> result = this.baseMapper.selectRepairImeiHis(page, pageRepairImeiReq);
        List<PageRepairImeiHisVo> records = result.getRecords();
        if(CollectionUtils.isNotEmpty(records)){
            records.forEach(item->{
                //保修枚举转换
                Optional.ofNullable(item.getBaoxiu()).ifPresent((Integer bao)-> item.setBaoxiuValue(BaoxiuStatusEnum.getMessageByCode(bao)));
                //服务枚举转换
                Optional.ofNullable(item.getServiceType()).ifPresent((Integer ser)-> item.setServiceTypeValue(BaoXiuTypeEnum.getMessageByCode(ser)));
            });
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateRepairOrder(RepairOrderLogVO repairOrderLogVO) {
        OaUserBO oaUserBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息超时，请重新登录"));
        String userName = oaUserBO.getUserName();
        repairOrderLogVO.setInUser(userName);
        Integer operationType = repairOrderLogVO.getOperationType();
        Integer shouHouId = repairOrderLogVO.getShouHouId();
        LambdaUpdateChainWrapper<Shouhou> updateChainWrapper = shouhouService.lambdaUpdate().eq(Shouhou::getId, shouHouId)
                .eq(Shouhou::getImei,repairOrderLogVO.getImei())
                .ne(Shouhou::getUserid, "76783");
        if(RepairOperationTypeEnum.ADD.getCode().equals(operationType)){
            //新增逻辑
            updateChainWrapper.set(Shouhou::getRepairOrderId, repairOrderLogVO.getRepairOrderId())
                    .set(Shouhou::getIsfan,Boolean.TRUE );
        } else if(RepairOperationTypeEnum.DELETE.getCode().equals(operationType)){
            //删除逻辑
            updateChainWrapper.set(Shouhou::getRepairOrderId, null).isNotNull(Shouhou::getRepairOrderId)
            .set(Shouhou::getIsfan,Boolean.FALSE);
        }
        if(!CommenUtil.autoWriteHist(()->updateChainWrapper.update())){
            throw new CustomizeException("修改失败");
        }
        //日志记录
        shouhouService.addRepairOrderLog(repairOrderLogVO);
        return "修改成功";
    }


    @Override
    public Integer exchangeCount(Long userId, LocalDateTime startDate) {
        return baseMapper.exchangeCount(userId, startDate);
    }

    @Override
    public Integer hasShouhou(Integer mkcId, String imei) {
        return baseMapper.hasShouhou(mkcId, imei);
    }

    @Override
    public Integer hasRecoveryShouhou(Integer mkcId, String imei) {
        return baseMapper.hasRecoveryShouhou(mkcId, imei);
    }

    @Override
    public Integer hasProductMKC(Integer mkcId, String imei) {
        return productKcService.hasProductMKC(mkcId, imei);
    }

    @Override
    public Integer has4GjiaTaocan(String imei) {
        return baseMapper.has4GjiaTaocan(imei);
    }

    /**
     * 回收生成售后单
     * @param recoveryCreateShouHouReq
     * @return
     */
    @Override
    public R<String> recoveryCreateShouHou(RecoveryCreateShouHouReq recoveryCreateShouHouReq) {
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前登录信息为空"));
        recoveryCreateShouHouReq.setUserBO(userBO);
        Integer mkcId = recoveryCreateShouHouReq.getMkcId();
        String imei = recoveryCreateShouHouReq.getImei();
        Integer hasShouhou = Optional.ofNullable(hasRecoveryShouhou(mkcId, imei)).orElse(NumberConstant.ZERO);
        if(hasShouhou==NumberConstant.ONE){
            return R.error("系统中已经有相同的imei记录正在处理中");
        }
        ShouhouReceiveReq shouhou = new ShouhouReceiveReq();
        Integer ppriceid = recoveryCreateShouHouReq.getPpriceid();
        Productinfo productinfo = Optional.ofNullable(productinfoService.getProductinfoByPpid(ppriceid)).orElseThrow(()->new CustomizeException("ppid查询商品为空"));
        //封装创建回收单信息
        shouhou.setPpriceid(ppriceid);
        shouhou.setProductId(Convert.toLong(productinfo.getProductId()));
        shouhou.setName(productinfo.getProductName());
        shouhou.setProductColor(productinfo.getProductColor());
        shouhou.setMkcId(mkcId);
        shouhou.setUserid(Convert.toLong(recoveryCreateShouHouReq.getUserid()));
        shouhou.setImei(imei);
        shouhou.setNotBakDataDesc("无法备份资料（原因：回收增值机维修）");
        shouhou.setIsBakData(NumberConstant.ZERO);
        shouhou.setConnectionMethod("回收增值机维修");
        shouhou.setProblem(Optional.ofNullable(recoveryCreateShouHouReq.getProblem()).orElse("回收增值机维修"));
        shouhou.setBaoxiu(BaoxiuStatusEnum.WX.getCode());
        shouhou.setIssoft(Boolean.FALSE);
        shouhou.setMobile(recoveryCreateShouHouReq.getMobile());
        shouhou.setAreaid(recoveryCreateShouHouReq.getMkcAreaId());
        shouhou.setIshuishou(IshuishouEnum.VALUE_ADDED_RECYCLING.getCode());
        shouhou.setWeixiuzuid(NumberConstant.ONE);
        shouhou.setAreaid(recoveryCreateShouHouReq.getMkcAreaId());
        R<String> result = formSave(shouhou, Shouhou.WaiGuanStatusEnum.WAN_HAO.getCode(), 0, "0");
        //维修单新增成功之后才进行日志记录
        if(result.isSuccess()){
            ShouhouLogNoticeBo shouhouLogNoticeBo = new ShouhouLogNoticeBo();
            shouhouLogNoticeBo.setNeedNotice(Boolean.FALSE);
            shouhouLogNoticeBo.setToWeixin(NumberConstant.ONE);
            shouhouLogNoticeBo.setToSms(NumberConstant.ZERO);
            shouhouLogNoticeBo.setToEmail(NumberConstant.ZERO);
            shouhouLogNoticeBo.setReciver("");
            SpringUtil.getBean(ShouhouLogsService.class).addShouhouLog(userBO.getUserName(), Convert.toInt(result.getData()), NumberConstant.THREE, "回收增值机维修", shouhouLogNoticeBo, Boolean.FALSE,NumberConstant.ZERO);
            //处理回收业务
            try {
                handleHuiShou(recoveryCreateShouHouReq);
            } catch (Exception e) {
                RRExceptionHandler.logError("回收业务数据处理异常", recoveryCreateShouHouReq, e, smsService::sendOaMsgTo9JiMan);
            }
        }
        return result;
    }

    /**
     * 处理回收业务
     * @param recoveryCreateShouHouReq
     */
    @Override
    public void handleHuiShou(RecoveryCreateShouHouReq recoveryCreateShouHouReq){
        String userName = Optional.ofNullable(recoveryCreateShouHouReq.getUserBO()).orElse(new OaUserBO()).getUserName();
        Integer mkcId = recoveryCreateShouHouReq.getMkcId();
        BigDecimal inbeihuoprice = Optional.ofNullable(recoverMkcService.getRecoverMkcVOByMkcId(mkcId)).orElseThrow(() -> new CustomizeException("回收库存查询为空")).getInbeihuoprice();
        // 2. 更新回收库存状态为"增值机维修中"
        boolean update = recoverMkcService.lambdaUpdate().eq(RecoverMkc::getId, mkcId)
                .eq(RecoverMkc::getMkcCheck, RecoverMkcStateEnum.STOCK.getCode())
                .set(RecoverMkc::getMkcCheck, RecoverMkcStateEnum.AFTER_SALES.getCode())
                .update();
        if (!update) {
            throw new CustomizeException("回收库存状态更新失败");
        }
        // 3. 添加回收库存操作日志
        MkcDellogs mkcDellogs = new MkcDellogs();
        mkcDellogs.setMkcId(mkcId);
        mkcDellogs.setInuser(userName);
        mkcDellogs.setArea(Convert.toStr(recoveryCreateShouHouReq.getMkcAreaId()));
        mkcDellogs.setComment("回收库存确认增值");
        // h5 表示转售后机器
        mkcDellogs.setKinds("h5");
        mkcDellogs.setPpriceid(recoveryCreateShouHouReq.getPpriceid());
        mkcDellogs.setPrice1(inbeihuoprice);
        mkcDellogs.setPrice2(inbeihuoprice);
        mkcDellogsService.insertMkcDellogs(mkcDellogs, userName);
        //mongodb 日志记录
        RecoverMkcNewLogs recoverMkcNew_logs = recoverMkcNewLogsRepository.findById(mkcId).orElse(null);
        if (recoverMkcNew_logs == null) {
            recoverMkcNew_logs = new RecoverMkcNewLogs(mkcId);
        }
        String comment = String.format("增值确认自动创建维修单，库存状态由【库存】变更为【售后】，故障描述：%s", recoveryCreateShouHouReq.getProblem());
        RecoverMkcNewLogs.Conts conts = new RecoverMkcNewLogs.Conts();
        conts.setComment(comment);
        conts.setDTime(LocalDateTime.now());
        conts.setInUser(userName);
        conts.setShowType(Boolean.FALSE);
        recoverMkcNew_logs.getCons().add(conts);
        recoverMkcNewLogsRepository.save(recoverMkcNew_logs);
    }

    @Override
    public R<String> formSave(ShouhouReceiveReq shouhou, Integer iswaiguan, Integer isweb, String doReCb) {
        OaUserBO currentUser = currentRequestComponent.getCurrentStaffId();
        shouhou.setStats(0);
        Integer insertID = 0;
        if (Objects.equals(isweb, 1)) {
            AreaInfo areaInfo = areaInfoClient.getAreaInfoByArea(shouhou.getArea()).getData();
            shouhou.setStats(null);
            shouhou.setAreaid(Optional.ofNullable(areaInfo).map(AreaInfo::getId).orElseGet(()->Optional.ofNullable(currentUser)
                    .map(OaUserBO::getAreaId).orElseThrow(()->new CustomizeException("获取不到门店,无法接件"))));
        } else {
            shouhou.setAreaid(currentUser.getAreaId());
        }
//        R<String> checkR = checksaveOrUpdateShouhouInfo(shouhou);
//        if(!checkR.isSuccess()){
//            return checkR;
//        }
        if(StrUtil.endWith(shouhou.getWuliyou(),ShouhouExService.WU_LI_YOU_TEXT)
                && XtenantEnum.isJiujiXtenant() && shouhou.getTradedate() != null
                && Duration.between(shouhou.getTradedate(),LocalDateTime.now()).toDays()>ShouhouExService.getWuLiYouDays()
        ){
            //校验无理由退货的时间
            return R.error(StrUtil.format("*已超过{}{}时间，无法办理",ShouhouExService.getWuLiYouDays(),ShouhouExService.WU_LI_YOU_TEXT));
        }
        if (StrUtil.endWith(shouhou.getWuliyou(),ShouhouExService.WU_LI_YOU_TEXT) && CommenUtil.isNotNullZero(shouhou.getMkcId())) {
            R<Boolean> returnByMkcId = getReturnByMkcId(shouhou.getMkcId());
            if (returnByMkcId.isSuccess()){
                Boolean data = returnByMkcId.getData();
                //当结果为false时  不满足15天无理由退货
                if (Boolean.FALSE.equals(Optional.ofNullable(data).orElse(Boolean.TRUE))){
                    return R.error(StrUtil.format("*该商品不支持{}{}，详情请见商品详情页",ShouhouExService.getWuLiYouDays(),ShouhouExService.WU_LI_YOU_TEXT));
                }
            }
        }
        Integer subSubId = ObjectUtil.defaultIfNull(shouhou.getSubId(), 0);
        Integer subBasketId = ObjectUtil.defaultIfNull(shouhou.getBasketId(), 0);
        if((subSubId <=0 || subBasketId<=0) && (subSubId>0 || subBasketId>0)){
            return R.error(StrUtil.format("接件订单商品信息异常单号[{}],basketId[{}]",shouhou.getSubId(), shouhou.getBasketId()));
        }


        //关系维护
        if (Boolean.TRUE.equals(shouhou.getIsguanxi())) {
            //赠送原因，写入备注
            shouhou.setComment(shouhou.getZsremark());
            shouhou.setIsquji(true);
            shouhou.setStats(1);
            shouhou.setIszp(true);
            if (Objects.equals(shouhou.getZpppid(), 0)) {
                return R.error("加单失败,错误的赠品PPID");
            }
        }
        if (StrUtil.isNotBlank(shouhou.getImei())) {
            if (!Objects.equals(shouhou.getQuestionType(), ShouhouResourceTypeEnum.POWER_OFF.getCode())) {
                Integer id = this.hasShouhou(0, shouhou.getImei());
                if (id != null) {
                    return R.error(StrUtil.format("请不要重复开单，请检查以前的维修记录[{}]，状态是否为：已取走",id));
                }
            }
            if (Boolean.TRUE.equals(shouhou.getIssoft())) {
                shouhou.setIsticheng(true);
                List<Msoft> msofts = shouhouExService.getMSoft(shouhou.getImei());
                if (!msofts.isEmpty()) {
                    shouhou.setWcount(msofts.size());
                    Msoft msoft = msofts.get(0);
                    Duration duration = Duration.between(msoft.getModidate(), LocalDateTime.now());
                    if (shouhou.getWcount() > 1 || duration.toHours() < 72) {
                        msoft.setIsticheng(false);
                        msoft.setIsfan(true);
                        msoftService.updateById(msoft);
                    }
                } else {
                    shouhou.setWcount(0);
                    Shouhou last = baseMapper.getShohouIstichengSoft(shouhou.getImei());
                    if (last != null) {
                        Duration duration = Duration.between(last.getModidate(), LocalDateTime.now());
                        if (shouhou.getWcount() > 1 || duration.toHours() < 72) {
                            shouhouService.updateIsticheng(last.getId(), false);
                        }
                    }
                }
            } else {
                shouhou.setIsticheng(false);
                shouhou.setIsfan(false);
                shouhou.setWcount(baseMapper.getWxCount(shouhou.getImei()));
            }
        }
        shouhou.setInuser(currentUser.getUserName());
        // 外修机，购机日期为空，提成需收银后才有
        Boolean isMSoft = false;
        if (Boolean.TRUE.equals(shouhou.getIssoft()) && Boolean.TRUE.equals(shouhou.getIsquick() && CommenUtil.isNotNullZero(shouhou.getSubId()))
                && (shouhou.getBaoxiu() == null || (shouhou.getBaoxiu() != 0 && shouhou.getBaoxiu() != 2))) {
            //测试串号
            boolean isTestIMEI = "A0000045BDF307".equals(shouhou.getImei());
            if (shouhou.getCurrentMsoftdtime() != null) {
                Duration seconds = Duration.between(shouhou.getCurrentMsoftdtime(), LocalDateTime.now());
                if (!isTestIMEI && seconds.getSeconds() <= 50) {
                    return R.error("安装频率过快，存在作弊嫌疑！");
                }
            }
            int count = msoftService.count(new LambdaQueryWrapper<Msoft>().eq(Msoft::getImei, shouhou.getImei()).eq(Msoft::getImeifid, shouhou.getImeifid()));
            if (!isTestIMEI && StrUtil.isNotBlank(shouhou.getImeifid()) && count > 0) {
                return R.error("不拍照接件，存在作弊嫌疑！！！");
            }
            shouhou.setModidate(LocalDateTime.now());
            shouhou.setAreaid(shouhou.getAreaid() == 0 ? currentUser.getAreaId() : shouhou.getAreaid());
            if (!isTestIMEI) {
                // 软件接件
                insertID = msoftService.AddMSoft(shouhou);
            }
            isMSoft = true;
//            return R.error("软件接件还没有重构");
        } else {
            shouhou.setCostprice(BigDecimal.ZERO);
            shouhou.setFeiyong(BigDecimal.ZERO);
            shouhou.setXianshi(true);
            if (shouhou.getWeixiuzuid() != null) {
                shouhou.setWeixiuStartdtime(LocalDateTime.now());
            }
            if (Objects.equals(iswaiguan, 1)) {
                shouhou.setWaiguan(null);
            }
            if(ObjectUtil.defaultIfNull(iswaiguan,0) <0){
                shouhou.setWaiGuanStatus(Shouhou.WaiGuanStatusEnum.NO_SELECT.getCode());
            }else{
                shouhou.setWaiGuanStatus(iswaiguan);
            }

            if (Objects.equals(shouhou.getDyjid(), 0)) {
                shouhou.setDyjid(null);
            }
            if (Boolean.TRUE.equals(shouhou.getIssoft()) && Objects.nonNull(shouhou.getBaoxiu()) && !shouhou.getBaoxiu().equals(2)) {
                shouhou.setStats(1);
                shouhou.setIsquji(true);
            }
            if (Boolean.FALSE.equals(shouhou.getIssoft())) {
                shouhou.setOrderid(shouhouService.getOrderIdsh());
            }

            if (!Objects.equals(isweb, 1) && Objects.isNull(shouhou.getRepairOrderId())) {
                // 非web 且 非返修
                shouhou.setWebtype1(null);
                shouhou.setWebtype2(null);
                shouhou.setWebstats(null);
            } else {
                shouhou.setWebstats(2);
            }
            shouhou.setModidate(LocalDateTime.now());
            if (Boolean.TRUE.equals(shouhou.getIssoft()) && Boolean.TRUE.equals(shouhou.getIsquick())) {
                shouhou.setStats(1);
            }

            R<Boolean> saveResult = shouhouService.saveShouhou(shouhou);
            if (saveResult.getCode() != ResultCode.SUCCESS) {
                return R.error("接件保存失败！: " + saveResult.getMsg() + "--" + saveResult.getUserMsg());
            }
            //接件成功 标志是否扫码加单 工单: 识别码加单兼容售后流程功能开发 xxk
            //异步增加扫描标记
            asyncScanCodeAddShouhou(shouhou,currentUser, BusinessTypeEnum.AFTER_ORDER);
            //推送风险确认书
            ShouhouRiskNotificationService shouhouRiskNotificationService = SpringUtil.getApplicationContext().getBean(ShouhouRiskNotificationService.class);
            shouhouRiskNotificationService.pushRiskNotification(shouhou);
            insertID = shouhou.getId();
            if (Boolean.TRUE.equals(shouhou.getIsguanxi())) {
                //赠送赠品，出库
                R<String> result = shouhouExService.zengSongPpid(shouhou.getZpppid(), insertID, BigDecimal.ZERO, 1, shouhou.getZsremark());
                if (ResultCode.SUCCESS == result.getCode()) {
                    smsService.sendSms(shouhou.getMobile(), result.getData(), LocalDateTime.now().minusMinutes(2)
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), "系统", smsService.getSmsChannelByTenant(currentUser.getAreaId(), ESmsChannelTypeEnum.YZMTD));
                } else {
                    return R.error("赠送失败");
                }
            }

            if (shouhou.getDyjid() != null && !shouhou.getDyjid().equals(0)) {
                daiyongjiService.updateDYJ(shouhou.getDyjid(), insertID, currentUser.getUserName());
            }
            if (Objects.equals(isweb, 1)) {
                int cityid1 = shouhou.getDid1() == 0 ? shouhou.getZid1() == 0 ? shouhou.getPid1() : shouhou.getZid1() : shouhou.getDid1();
                int cityid2 = shouhou.getDid2() == 0 ? shouhou.getZid2() == 0 ? shouhou.getPid2() : shouhou.getZid2() : shouhou.getDid2();
                int webtype2 = shouhou.getWebtype2() != null ? shouhou.getWebtype2() : -1;
                if (webtype2 == 2) {
                    Addinfops addinfops = new Addinfops().setReciver(shouhou.getReciver1()).setAddress(shouhou.getAddress1())
                            .setCityid(cityid1).setType(1).setBindId(insertID);
                    addinfopsService.save(addinfops);
                    if (StrUtil.isNotBlank(shouhou.getReciver2()) || StrUtil.isNotBlank(shouhou.getAddress2())) {
                        addinfops.setReciver(shouhou.getReciver2()).setAddress(shouhou.getAddress2())
                                .setCityid(cityid2).setType(2).setBindId(insertID);
                        addinfopsService.save(addinfops);
                    }

                } else {
                    if (StrUtil.isNotBlank(shouhou.getReciver2()) || StrUtil.isNotBlank(shouhou.getAddress2())) {
                        Addinfops addinfops = new Addinfops().setReciver(shouhou.getReciver2()).setAddress(shouhou.getAddress2())
                                .setCityid(cityid2).setType(2).setBindId(insertID);
                        addinfops.setReciver(shouhou.getReciver2()).setAddress(shouhou.getAddress2())
                                .setCityid(cityid2).setType(2).setBindId(insertID);
                        addinfopsService.save(addinfops);
                    }
                }
            }
            if (Boolean.FALSE.equals(shouhou.getIssoft()) && shouhou.getWeixiuzuid() != null) {
                Weixiuzulogs weixiuzulogs = new Weixiuzulogs();
                weixiuzulogs.setWeixiuzuid1(null);
                weixiuzulogs.setWeixiuzuid2(shouhou.getWeixiuzuid());
                weixiuzulogs.setInuser(currentUser.getUserName());
                weixiuzulogs.setDtime(LocalDateTime.now());
                weixiuzulogs.setShouhouId(insertID);
                weixiuzulogsService.save(weixiuzulogs);
            }
            if (shouhou.getUserid() != null && !shouhou.getIssoft()) {
                // 查询会员详情
                R<MemberBasicRes> memberBasicInfoR = memberClient.getMemberBasicInfo(Math.toIntExact(shouhou.getUserid()));
                MemberBasicRes memberBasicRes = CommonUtils.isRequestSuccess(memberBasicInfoR) ? memberBasicInfoR.getData() : null;
                if (memberBasicRes != null) {
                    boolean isDiamondMember = UserClassEnum.USER_CLASS_DIAMOND.getCode().equals(memberBasicRes.getUserClass())
                            || UserClassEnum.USER_CLASS_DOUBLE_DIAMOND.getCode().equals(memberBasicRes.getUserClass());
                    // 钻石会员或双钻会员且不在DC、H1、HQ、D1区域内
                    if (isDiamondMember && !authConfigService.getAllAreaHqAndDcAndH1AndD1().contains(currentUser.getAreaId())) {
                        List<Ch999UserBasicBO> ch999UserList = oaApiService.listAllUserBasicInfo();
                        List<Ch999UserBasicBO> matchUserList = ch999UserList.stream()
                                .filter(item -> item.getMobile() != null && item.getMobile().equals(memberBasicRes.getMobile()))
                                .collect(Collectors.toList());
                        // 通过手机号码排除内部会员 并且不是 isSubNeedReception 则进行存储
                        boolean isSubNeedReception = subReceptionService.isSubNeedReception(2, currentUser.getUserId(), currentUser.getAreaId());
                        if (matchUserList.isEmpty() && isSubNeedReception) {
                            subReceptionService.save(new SubReceptionBO()
                                    .setSubId(insertID.longValue())
                                    .setType(2)
                                    .setCh999Id(currentUser.getUserId())
                                    .setUserId(memberBasicRes.getId())
                                    .setAreaId(currentUser.getAreaId()));
                        }
                    }
                }
            }

            if (CommenUtil.isNotNullZero(shouhou.getOrderSource()) && CommenUtil.isNotNullZero(shouhou.getMkcId())) {
                R<String> valueR = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
                if (valueR.getCode() == ResultCode.SUCCESS && valueR.getData() != null) {
                    //增值机业务需要调用C#告知一下提交业务状态
                    String url = MessageFormat.format(UrlConstant.VALUE_ADDED_MACHINE_URL, valueR.getData());
                    MachineValueAddedReq req = new MachineValueAddedReq();
                    req.setMkcId(shouhou.getMkcId())
                            .setMkc_id(shouhou.getMkcId())
                            .setUserName(currentUser.getUserName());
                    String jsonStr = JSON.toJSONString(req);
                    CompletableFuture.runAsync(() -> HttpUtil.post(url, jsonStr));
                }
            }
            //接件成功后消息推送 用于记录会员与员工的关系
            Integer subId = insertID;
            CompletableFuture.runAsync(() -> smsService.pushMemberScanBind(Math.toIntExact(shouhou.getUserid()), currentUser.getUserId(), BusinessTypeEnum.AFTER_ORDER.getCode(), subId, BusinessNodeEnum.BUSINESS_BEGIN.getCode()));
            CompletableFuture.runAsync(()->{
                //发送维修加单通知 xxk 2021-09-24
                String pushMsg = JSON.toJSONString(new SubPushMsgBO()
                        .setAct("subAddMsgPushToFollow").setData(new SubPushMsgBO.DataBO().setSubType(BusinessTypeEnum.AFTER_ORDER)
                                .setOpType(SubPushMsgBO.OpTypeEnum.ADD_SUB).setSubId(shouhou.getId()).setUserId(shouhou.getUserid())));
                StaticLog.info("推送维修加单消息:{}",pushMsg);
                rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, pushMsg);
            });
            R<String> success = R.success("接件成功！", insertID.toString());
            // 重大办返回标记
            success.setExData(Optional.ofNullable(saveResult.getExData()).orElse(new HashMap<>()));
            return success;
        }
        //todo 3465行后业务逻辑

        //3525
        if (shouhou.getIssoft() != null && shouhou.getIssoft()) {
            Boolean isShowDetailBut = Boolean.FALSE;
            if (Objects.equals(shouhou.getPlateForm(), PlateFormConstant.MOA) && !isMSoft) {
                isShowDetailBut = Boolean.TRUE;
            }
        }

        if (shouhou.getIsxianchang()) {
            List<MsoftXianChangRecord> softDt = msoftService.getXianchang(shouhou.getInuser(), null);


        }
        return R.success("1");

    }

    /**
     * 扫码加单备注处理 识别码
     * @param scanCode
     * @param currentUser
     * @param businessType
     */
    @Override
    public void asyncScanCodeAddShouhou(ScanCodeAddSub scanCode, OaUserBO currentUser, BusinessTypeEnum businessType) {
        //获取inwcf地址
        R<String> valueR = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        Optional.ofNullable(scanCode.getScanCodeUserMobile()).filter(StrUtil::isNotBlank)
                .map(scum -> memberClient.getMemberBasicInfoByTel(scum, currentUser.getXTenant()))
                .filter(R::isSuccess).map(R::getData).map(MemberBasicRes::getId)
                //调用c#是否是扫码加单,不是扫码加单,原因写入售后日志,并增加是否扫描加单记录
                .ifPresent(uId -> Optional.ofNullable(valueR).filter(R::isSuccess).map(R::getData).filter(StrUtil::isNotBlank)
                        .ifPresent(inWcf -> CompletableFuture.runAsync(() -> scanCodeAddShouhou(scanCode, currentUser, uId, inWcf, businessType))
                                .exceptionally(e -> {
                                    log.error(String.format("是否扫码加单备注异常%s", scanCode.getId()), e);
                                    return null;
                                })));
    }


    /**
     * 获取用户加单信息
     * @param currentUser
     * @param uId
     * @return
     */
    @Override
    public MemberUserCodeRes createMemberUserCodeRes(OaUserBO currentUser, Integer uId){
        R<String> valueR = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        log.warn("获取用户加单信息获取inwcf地址传入参数：{},返回结果：{}",SysConfigConstant.IN_WCF_HOST, JSON.toJSONString(valueR));
        if(ResultCode.SUCCESS!=valueR.getCode()){
            log.warn("获取用户加单信息获取inwcf异常，地址传入参数：{},返回结果：{}",SysConfigConstant.IN_WCF_HOST, JSON.toJSONString(valueR));
            return null;
        }
        //非扫码加单
        String getUserCodeRemarkUrl = StrUtil.format("{}{}", valueR.getData(), "/oaapi.svc/rest/GetUserCodeAddSubData");
        String remarkReqBody = JSON.toJSONString(Dict.create()
                .set("areaId", currentUser.getAreaId())
                .set("staffId", currentUser.getUserId())
                .set("userId", uId));
        HttpResponse getUserCodeRes = HttpUtil.createPost(getUserCodeRemarkUrl).body(remarkReqBody).execute();
        if(!getUserCodeRes.isOk() || StrUtil.isBlank(getUserCodeRes.body())){
            log.warn("获取用户加单信息异常，传入参数：{}，返回结果：{}",remarkReqBody,getUserCodeRes.body());
            return null;
        }
        R<MemberUserCodeRes> r = JSON.parseObject(getUserCodeRes.body(), new TypeReference<R<MemberUserCodeRes>>() {});
        if(!r.isSuccess() || r.getData() == null){
            log.warn("获取用户加单信息数据异常，传入参数：{}，返回结果：{}",remarkReqBody,JSON.toJSONString(r));
            return null;
        }
        log.warn("获取用户加单信息成功，传入参数：{}，返回结果：{}",remarkReqBody,JSON.toJSONString(r));
        return r.getData();
    }



    /**
     * 扫码加单备注处理
     * @param scanCode
     * @param currentUser
     * @param uId
     * @param inWcf
     * @param businessType
     */
    private void scanCodeAddShouhou(ScanCodeAddSub scanCode, OaUserBO currentUser, Integer uId, String inWcf, BusinessTypeEnum businessType) {
        boolean isCodeAddSub = StrUtil.isBlank(scanCode.getAddType()) || "0".equals(scanCode.getAddType());
        //非扫码加单
        String getUserCodeRemarkUrl = StrUtil.format("{}{}", inWcf, "/oaapi.svc/rest/GetUserCodeAddSubData");
        String remarkReqBody = JSON.toJSONString(Dict.create()
                .set("areaId", currentUser.getAreaId())
                .set("staffId", currentUser.getUserId())
                .set("userId", uId));
        HttpResponse getUserCodeRes = HttpUtil.createPost(getUserCodeRemarkUrl).body(remarkReqBody).execute();
        if(!getUserCodeRes.isOk() || StrUtil.isBlank(getUserCodeRes.body())){
            log.error("获取用户二维码加单备注异常,{}id:{},参数:{},状态码:{}",businessType.getMessage(), scanCode.getId(),remarkReqBody,getUserCodeRes.getStatus());
            return;
        }

        R<MemberUserCodeRes> r = JSON.parseObject(getUserCodeRes.body(), new TypeReference<R<MemberUserCodeRes>>() {});
        if(!r.isSuccess() || r.getData() == null){
            log.error("获取用户二维码加单备注异常,{}id:{},参数:{},原因:{}",businessType.getMessage(), scanCode.getId(),remarkReqBody,r.getUserMsg());
            return;
        }
        MemberUserCodeRes mu = r.getData();
        if (StrUtil.isNotBlank(mu.getAddSubRemrk())) {
            switch (businessType){
                case AFTER_ORDER:
                    //添加售后日志
                    shouhouService.saveShouhouLog(scanCode.getId(), mu.getAddSubRemrk(), currentUser.getUserName());
                    break;
                case APPOINTMENT_ORDER:
                    //预约单日志
                    SpringUtil.getBean(YuyueLogsService.class).yuyueLogsAdd(scanCode.getId(), mu.getAddSubRemrk(),
                            currentUser.getUserName(), YuyueLogViewTypeEnum.WZ_NO_SHOUW.getCode());
                    break;
                default:
                    break;
            }

        }
        Boolean needAddRecord = mu.getNeedAddRecord();

        if (Boolean.TRUE.equals(needAddRecord) || !isCodeAddSub) {
            //添加扫码加单记录
            String userCodeSubRecordUrl = StrUtil.format("{}{}", inWcf, "/oaapi.svc/rest/userCodeSubRecord");
            String saveCodeRecordBody = JSON.toJSONString(Dict.create().set("input", Dict.create()
                    .set("subId", scanCode.getId())
                    .set("userId", uId)
                    .set("areaId", currentUser.getAreaId())
                    .set("inuser", currentUser.getUserName())
                    .set("addType", DecideUtil.iif(NumberUtil.isInteger(scanCode.getAddType()),()-> Convert.toInt(scanCode.getAddType()),()->0))
                    .set("subType", businessType.getCode())));
            HttpResponse saveCodeRecordRes = HttpUtil.createPost(userCodeSubRecordUrl).body(saveCodeRecordBody).execute();
            if(!saveCodeRecordRes.isOk() || StrUtil.isBlank(saveCodeRecordRes.body())){
                log.error("添加扫码加单记录异常,{}id:{},参数:{},状态码:{}",businessType.getMessage(), scanCode.getId(),saveCodeRecordBody,saveCodeRecordRes.getStatus());
                return;
            }
            R<?> sr = JSON.parseObject(saveCodeRecordRes.body(),R.class);
            if(!sr.isSuccess()){
                log.error("添加扫码加单记录异常,{}id:{},参数:{},原因:{}",businessType.getMessage(), scanCode.getId(),saveCodeRecordBody,sr.getUserMsg());
                return;
            }
        }
    }

    @Override
    public R<Shouhou> addForm(ShouhouFM fm) {
        OaUserBO currentUser = currentRequestComponent.getCurrentStaffId();
        if ((currentUser.getRank().contains("50") && Boolean.TRUE.equals(fm.getIsSoft()))) {
            return R.error("你没有权限,权限：50");
        }
        if (currentUser.getRank().contains("56") && Boolean.FALSE.equals(fm.getIsSoft())) {
            return R.error("你没有权限,权限：56");
        }
        ShouhouReceiveReq model = new ShouhouReceiveReq();
        model.setIsticheng(!Objects.equals(fm.getAct(), "user")).setBuyareaid(fm.getAreaId())
                .setIsquick(Boolean.TRUE.equals(fm.getIsSoft())).setIssoft(fm.getIsSoft());
        model.setIsguanxi(fm.getIsguanxi());
        model.setIsXcMkc(fm.getIsXcMkc());
        model.setIsXcMkcInfo(fm.getIsXcMkcInfo());
        Boolean blacklist = null;
        int brandid = 0, userclass = 0;
        String mobile1 = "";
        String tel1 = "";
        if ("trade".equalsIgnoreCase(fm.getAct()) && fm.getBasketId() != null) {
            BasketInfoBO basketInfo = basketService.getBasketInfo(fm.getBasketId());
            if (basketInfo != null) {
                BeanUtils.copyProperties(basketInfo, model);
                mobile1 = model.getMobile();
                tel1 = model.getTel();
                blacklist = basketInfo.getBlacklist();
                userclass = basketInfo.getUserclass();
                brandid = basketInfo.getBrandid();
            }
        } else if ("mkc".equalsIgnoreCase(fm.getAct()) && fm.getMkcId() != null) {
            ProductMkcInfoBO productMkcInfoBO = basketService.getProductMKC(fm.getMkcId(), fm.getIsHuishou());
            if (productMkcInfoBO != null) {
                BeanUtils.copyProperties(productMkcInfoBO, model);
                model.setUserid(76783L);
                model.setUsername("现货");
                brandid = productMkcInfoBO.getBrandid();
            }
        } else if ("user".equalsIgnoreCase(fm.getAct())) {
            BeanUtils.copyProperties(fm, model);
            model.setIsquick(fm.getIsSoft());
            model.setSubId(null).setBasketId(null).setTradedate(null).setBaoxiu(null);
            if (fm.getUserId() != null && fm.getUserId() > 0) {
                BbsxpUsers userInfo = usersService.getById(fm.getUserId());
                if (userInfo != null) {
                    model.setUserid(userInfo.getId().longValue());
                    model.setUsername(userInfo.getUsername());
                    model.setMobile(userInfo.getMobile());
                    model.setTel(userInfo.getTel());
                    mobile1 = model.getMobile();
                    userclass = userInfo.getUserclass();
                    blacklist = userInfo.getBlacklist();
                }
            }
        } else if ("history".equalsIgnoreCase(fm.getAct())) {
            BeanUtils.copyProperties(fm, model);
            model.setBuyareaid(fm.getAreaId());
            if (fm.getUserId() != null && fm.getUserId() > 0) {
                BbsxpUsers userInfo = usersService.getById(fm.getUserId());
                if (userInfo != null) {
                    model.setUsername(userInfo.getUsername());
                    model.setMobile(userInfo.getMobile());
                    model.setTel(userInfo.getTel());
                    mobile1 = model.getMobile();
                    tel1 = model.getTel();
                    userclass = userInfo.getUserclass();
                    blacklist = userInfo.getBlacklist();
                }
            }
            if (fm.getPpriceId() != null && fm.getPpriceId() > 0) {
                Productinfo productinfo = productinfoService.getOne(new LambdaQueryWrapper<Productinfo>().select(Productinfo::getBrandID)
                        .eq(Productinfo::getPpriceid, fm.getPpriceId()));
                if (productinfo != null) {
                    brandid = productinfo.getBrandID() == null ? 0 : productinfo.getBrandID();
                }
            }
        }
        return null;
    }

    @Override
    public Boolean checkFanxiu(String imei) {
        return baseMapper.checkFanxiu(imei) != null;
    }

    @Override
    public List<EnumVO> listRepairGroups(Integer productId) {
        OaUserBO getCurrentUser = currentRequestComponent.getCurrentStaffId();
        List<EnumVO> result = new ArrayList<>();
        if (Objects.equals(getCurrentUser.getAreaId(), 16)) {
            for (WeixiuzuKindVo weixiuzuKindVo : weixiuzuKindService.getWxGroupList()) {
                if (!weixiuzuKindVo.getId().equals(5)) {
                    EnumVO enumVO = new EnumVO();
                    enumVO.setLabel(weixiuzuKindVo.getName());
                    enumVO.setValue(weixiuzuKindVo.getId());
                    result.add(enumVO);
                }
            }
        } else {
            EnumVO zhz = new EnumVO();
            zhz.setLabel("综合组");
            zhz.setValue(4);
            result.add(zhz);
            EnumVO quick = new EnumVO();
            quick.setLabel("门店快修组");
            quick.setValue(14);
            result.add(quick);
        }
        EnumVO noRepair = new EnumVO();
        noRepair.setLabel("不需要维修");
        noRepair.setValue(0);
        result.add(noRepair);
        return result;
    }



    @Override
    @Transactional
    public R<Boolean> cancelYouHuiMa(Integer shouhouId, String code) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Shouhou shouhou = this.getById(shouhouId);
        if (shouhou == null) {
            return R.error("维修单不存在或已删除");
        }
        if (shouhou.getShouyinglock() != null && shouhou.getShouyinglock().equals(1)) {
            return R.error("已收银不可撤销优惠码");
        }
        if (shouhou.getYouhuifeiyong() == null || shouhou.getYouhuifeiyong().compareTo(BigDecimal.ZERO) == 0) {
            return R.error("当前维修单优惠码使用信息不存在");
        }
        //
        Optional<NumberCard> cardInfoByCodeOpt = Optional.empty();
        if (StrUtil.isNotBlank(code)){
            cardInfoByCodeOpt = Optional.ofNullable(numberCardService.lambdaQuery().eq(NumberCard::getCardID,code).one());
        }
        // 初始化累积变量
        BigDecimal totalDeduction = ObjectUtil.defaultIfNull(shouhou.getYouhuifeiyong(), BigDecimal.ZERO);
        List<String> youhuimaList = StrUtil.splitTrim(shouhou.getYouhuima(), StringPool.PIPE);
        //使用过优惠码，则返回优惠码
        List<Integer> cardLogIds = shouHouPjMapper.getCardIdfromCardLogsByWxId(shouhouId, null, cardInfoByCodeOpt.map(NumberCard::getId).orElse(null));
        if (CollUtil.isNotEmpty(cardLogIds)){
            LocalDate todayDate = LocalDate.now();
            for (Integer cardLogId : cardLogIds) {
                if (CommenUtil.isNotNullZero(cardLogId)) {
                    NumberCard cardInfo = cardInfoByCodeOpt.filter(ci -> Objects.equals(ci.getId(),cardLogId))
                            .orElseGet(()->numberCardService.getById(cardLogId));
                    //更新优惠码使用次数
                    numberCardService.update(new LambdaUpdateWrapper<NumberCard>()
                            .setSql("use_count=use_count-1,State=case when isnull(limit,0)=2 then State else 0 end")
                            //抖音撤销卷,修改结束时间 优惠码的生效时间修改为2天（当天算第一天）
                            .set(ObjectUtil.defaultIfNull(cardInfo.getEndTime(),todayDate).isAfter(todayDate.plusDays(NumberConstant.ONE))
                                            && Optional.ofNullable(shouhouService.getLastDouYinCouponLog(shouhouId, DouYinCouponLogRes.SubKindEnum.AFTERSALES.getCode()))
                                            .filter(dycl -> Objects.equals(dycl.getCardId(), cardInfo.getId())
                                                    && DouYinCouponLogRes.CouponStatus.SUCCESS.getCode().equals(dycl.getStatus())).isPresent(),
                                    NumberCard::getEndTime, todayDate.plusDays(NumberConstant.ONE))
                            .eq(NumberCard::getId, cardLogId));
                    cardLogsService.remove(new QueryWrapper<CardLogs>().lambda()
                            .eq(CardLogs::getSubId, shouhouId).eq(CardLogs::getCardid,cardLogId)
                            .eq(CardLogs::getUseType, 1));

                    //更新售后表优惠费用、优惠码值  （该方法如果存在多个情况  会出现覆盖更新）
//                    shouhouService.update(new UpdateWrapper<Shouhou>().lambda()
//                            .set(Shouhou::getYouhuifeiyong, NumberUtil.max(ObjectUtil.defaultIfNull(shouhou.getYouhuifeiyong(),BigDecimal.ZERO)
//                                    .subtract(ObjectUtil.defaultIfNull(cardInfo.getTotal(),BigDecimal.ZERO)),BigDecimal.ZERO))
//                            .set(Shouhou::getYouhuima, Optional.ofNullable(shouhou.getYouhuima())
//                                    .map(yhmStr -> StrUtil.splitTrim(yhmStr, StringPool.PIPE).stream()
//                                            .filter(yhm -> !Objects.equals(cardInfo.getCardID(),yhm)).collect(Collectors.joining(StringPool.PIPE)))
//                                    .orElse(null)).eq(Shouhou::getId, shouhouId));

                    // 累积扣减金额
                    BigDecimal cardTotal = ObjectUtil.defaultIfNull(cardInfo.getTotal(), BigDecimal.ZERO);
                    totalDeduction = totalDeduction.subtract(cardTotal);
                    if (totalDeduction.compareTo(BigDecimal.ZERO) < 0) {
                        log.warn("优惠费用计算结果为负: shouhouId={}, deduction={}", shouhouId, totalDeduction);
                        totalDeduction = BigDecimal.ZERO;
                    }

                    // 移除优惠码
                    youhuimaList.removeIf(yhm -> Objects.equals(cardInfo.getCardID(), yhm));
                    //添加售后进程
                    String youhuimaCode = cardInfo.getCardID();
                    String comment ="撤销优惠码，已恢复抵扣费用" + cardInfo.getTotal() + "元！优惠码： <a class='yhmTip' data="+youhuimaCode+" title=标题：" +cardInfo.getGName()+"，限制金额："+cardInfo.getTotal()+ShouhouExServiceImpl.getDrLimitValue(Optional.ofNullable(cardInfo.getLimit1()).orElse(NumberConstant.ZERO))+">" +youhuimaCode+ "</a>";
                    shouhouService.saveShouhouLog(shouhouId, comment, oaUserBO.getUserName(), null, true);

                } else {
                    return R.error("优惠码已被撤销");
                }
            }
        }



        // 一次性更新 Shouhou 表
        String newYouhuima = StrUtil.join(StringPool.PIPE, youhuimaList);
        totalDeduction = totalDeduction.setScale(2, RoundingMode.HALF_UP); // 设置精度
        shouhouService.update(new UpdateWrapper<Shouhou>().lambda()
                .set(Shouhou::getYouhuifeiyong, totalDeduction)
                .set(Shouhou::getYouhuima, newYouhuima)
                .eq(Shouhou::getId, shouhouId));

        shouHouPjService.updateShouhouFeiyong(shouhouId);

        return R.success("操作成功");
    }

    @Override
    public Map<String, Object> listRepairEnum(Integer productId) {
        Dict result = Dict.create();

        // 设置维修单类型
        BigShouhouOrderTypeEnum orderType;
        if(XtenantEnum.isJiujiXtenant() && isDJIRepairOrder(productId)){
            orderType = BigShouhouOrderTypeEnum.DJI;
        }else{
            orderType = BigShouhouOrderTypeEnum.NORMAL;
        }
        result.set("orderType", orderType.getCode());

        List<ListBean.OptionsBean> faultDesEnum;
        if(XtenantEnum.isJiujiXtenant()){
            faultDesEnum = Arrays.stream(FaultDesNewEnum.values())
                    .filter(fd -> Objects.equals(fd.getOrderType(), orderType))
                    .map(fd->new ListBean.OptionsBean(fd.getCode(),fd.getMessage(),fd.getRiskNotificationType()))
                    .collect(Collectors.toList());
        }else{
            faultDesEnum = Arrays.stream(FaultDesEnum.values()).map(fd->new ListBean.OptionsBean(fd.getCode(),fd.getMessage()))
                    .collect(Collectors.toList());
        }

        //故障描述快捷选项
        result.set("faultDesEnum", faultDesEnum);
        //软件故障描述快捷选项
        result.set("softFaultDesEnum", Arrays.stream(SoftFaultDesEnum.values()).map(fd->new ListBean.OptionsBean(fd.getCode(),fd.getMessage()))
                .collect(Collectors.toList()));
        //附带配件(根据订单类型过滤)
        result.set("withFittingDescEnum", Arrays.stream(WithFittingDescEnum.values())
                .filter(fd -> Objects.equals(fd.getOrderType(), orderType))
                .map(fd->new ListBean.OptionsBean(fd.getCode(),fd.getMessage()))
                .collect(Collectors.toList()));
        //不需要备份
        result.set("notNeedBackupDes", Optional.of(ProcessDesEnum.NOT_NEED_DATA_BACKUP).map(nnb->new ListBean.OptionsBean(nnb.getCode(),nnb.getMessage())).orElse(null));
        //附带配件
        result.set("notNeedBackupEnum", Arrays.stream(ProcessDesEnum.NotNeedBackupEnum.values()).map(fd->new ListBean.OptionsBean(fd.getCode(),fd.getMessage()))
                .collect(Collectors.toList()));
        //维修方式
        String platform = MessageFormat.format("{0}自修", Optional
                .of(sysConfigClient.getValueByCode(SysConfigConstant.PRINT_NAME))
                .map(R::getData).filter(StringUtils::isNotEmpty)
                .orElse(""));
        result.set("repairWayEnum", Arrays.asList(new LabelValue().setLabel(platform).setValue(WxKindEnum.XIU.getCode()),
                new LabelValue().setLabel("外送厂家").setValue(WxKindEnum.SONG.getCode())));
        return result;
    }

    @Override
    public R<Boolean> getReturnByMkcId(Integer mkcId) {
        Result<SalfGoodsUsageVO> salfGoodsUsageInfo = productSupportCloud.getSalfGoodsUsageInfo(mkcId);
        if (salfGoodsUsageInfo.getCode() == SUCCESS && ObjectUtil.isNotEmpty(salfGoodsUsageInfo.getData())) {
            //设置配件id
            SalfGoodsUsageVO data = salfGoodsUsageInfo.getData();
            //已配置为全新未激活或退款限制机型的良品（只要满足其中一个条件），则不支持十五天无理由退货。 返回false
            return R.success(!(Optional.ofNullable(data.getBrandNew()).orElse(Boolean.FALSE) || Optional.ofNullable(data.getLimitModel()).orElse(Boolean.FALSE)));
        } else {
            //可以十五天无理由退货 返回true
            return R.success(Boolean.TRUE);
        }
    }

    @Override
    public R<String> formSaveFast(ShouhouReceiveFastReq shouhou, Integer iswaiguan, Integer isweb, String doReCb) {
        ShouhouReceiveReq shouhouReceive = shouhou.getShouhouReceive();
        R<String> shouhouR = this.formSave(shouhouReceive, iswaiguan, isweb, doReCb);
        if (shouhouR.getCode() != 0) {
            return R.error(shouhouR.getUserMsg());
        }
        String shouhouId = shouhouR.getData();
        List<ShouhouLogReq> logs = shouhou.getLogs();
        for (ShouhouLogReq x : logs) {
            x.setWxId(Integer.valueOf(shouhouId));
            shouhouService.addShouHouLog(x);
        }
        R<String> success = R.success("维修单快捷接件成功", shouhouId);
        success.setExData(Optional.ofNullable(shouhouR.getExData()).orElse(new HashMap<>()));
        return success;
    }

    @Override
    public Integer getPpriceIdByShouhouInfo(Shouhou shouhou){
        return baseMapper.getPpriceIdByShouhouInfo(shouhou);
    }

    @Data
    @Accessors(chain = true)
    public static class LabelValue{
        private String label;

        private Integer value;
    }

    private R<String> checksaveOrUpdateShouhouInfo(ShouhouReceiveReq shouhou) {
        try {
            // 15天无理由退货
            Assert.isFalse(StrUtil.endWith(shouhou.getWuliyou(),ShouhouExService.WU_LI_YOU_TEXT) && ObjectUtil.isNotNull(shouhou.getProductId())
                    && !shouhouService.isNoReason(Collections.singletonList(Convert.toInt(shouhou.getProductId()))),"该商品不支持{}{}，详情请见商品详情页"
                    ,ShouhouExService.getWuLiYouDays(),ShouhouExService.WU_LI_YOU_TEXT);
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }
        return R.success(null);
    }

    public boolean isDJIRepairOrder(Integer productId) {
        return isDJIRepairOrder(productId, null);
    }

    @Override
    public boolean isDJIRepairOrder(Integer productId, Integer shouhouId) {
        if(XtenantEnum.isSaasXtenant()){
            return false;
        }
        if (productId == null && shouhouId == null) {
            return false;
        }
        Optional<Shouhou> shouhouInfoOpt;
        if(ObjectUtil.defaultIfNull(shouhouId, 0) >0){
            shouhouInfoOpt = shouhouService.lambdaQuery().eq(Shouhou::getId, shouhouId).select(Shouhou::getId, Shouhou::getProductId)
                    .list().stream().findFirst();
        }else{
            shouhouInfoOpt = Optional.empty();
        }

        if(ObjectUtil.defaultIfNull(productId, 0) <= 0 && shouhouInfoOpt.isPresent()){
            productId = shouhouInfoOpt.map(Shouhou::getProductId).map(Convert::toInt).orElse(null);
        }

        if (productId == null) {
            return false;
        }

        // 检查是否是大疆无人机
        // 1. 通过productId获取品牌分类信息
        // 2. 检查品牌分类信息是否是大疆无人机 (品牌ID: 1,

        // 通过SpringUtil获取ProductService实例
        ProductService productService = SpringUtil.getBean(ProductService.class);
        CategoryService categoryService = SpringUtil.getBean(CategoryService.class);

        // 获取品牌分类信息
        List<BrandCategoryNameBo> brandCategories = productService.getBrandCategoryByProductIds(
            Collections.singletonList(productId));

        if (CollectionUtils.isEmpty(brandCategories)) {
            return false;
        }

        BrandCategoryNameBo brandCategory = brandCategories.get(0);

        List<Integer> droneCidList = categoryService.getProductChildCidList(Collections.singletonList(CategoryConstants.DRONE));

        // 检查是否是大疆无人机
        boolean isDji = brandCategory != null
                && droneCidList.contains(brandCategory.getCategoryId())  // 无人机分类ID
                && Objects.equals(brandCategory.getBrandId(), BrandConstants.DJI);   // 大疆品牌ID

        isDji = isDji && !shouhouInfoOpt
                .filter(shouhou -> wxkcoutputService.lambdaQuery().eq(Wxkcoutput::getWxid, shouhou.getId())
                        .in(Wxkcoutput::getPartType, Wxkcoutput.PartTypeEnum.HUI_SHOU_ZHE_JIA_PAY.getCode(),
                                Wxkcoutput.PartTypeEnum.ZHE_JIA_PAY.getCode()).count() > 0)
                .isPresent();

        return isDji;
    }

}
