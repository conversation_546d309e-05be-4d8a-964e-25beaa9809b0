package com.jiuji.oa.afterservice.patchsearch.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Auther: qiweiqing
 * @Date: 2020/03/05
 * @Description:
 */
@Data
public class PatchSearchRes {

    @ApiModelProperty(value = "地区")
    private String areaName;

    @ApiModelProperty(value = "地区")
    private String area;
    @ApiModelProperty(value = "订单号")
    private Long subId;
    @ApiModelProperty(value = "订单名称")
    private String product;
    private Integer subCheck;
    @ApiModelProperty(value = "订单状态")
    private String subCheckName;
    @ApiModelProperty(value = "订单日期")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private LocalDateTime subDate;
    @ApiModelProperty(value = "订单类型")
    private String type;
    @ApiModelProperty(value = "订单链接")
    private String link;
}
