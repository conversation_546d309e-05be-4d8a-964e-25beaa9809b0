package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouMsgPushMessageBo;
import com.jiuji.oa.afterservice.bigpro.dao.DaiyongjiMapper;
import com.jiuji.oa.afterservice.bigpro.enums.*;
import com.jiuji.oa.afterservice.bigpro.machinehero.config.MqttAfterConfig;
import com.jiuji.oa.afterservice.bigpro.machinehero.vo.req.ForwardMqttReq;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.statistics.bo.DaiYongJiBo;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.req.DaiYongJiListQueryReq;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.req.DaiYongToAreaReq;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.res.DaiYongJiListResVo;
import com.jiuji.oa.afterservice.bigpro.vo.req.DaiYongJiListReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.dyj.CancelDaiYongjiReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.DaiYongJiInfoRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.DaiYongJiListRes;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.RRException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.HttpClientUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.bo.AreaBelongsDcHqD1AreaId;
import com.jiuji.oa.afterservice.other.bo.PingzhengBO;
import com.jiuji.oa.afterservice.other.bo.PingzhengResultBO;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.other.service.VoucherService;
import com.jiuji.oa.afterservice.shouhou.vo.req.ShouhouDaiYongjiAttachmentsAddReq;
import com.jiuji.oa.afterservice.smallpro.enums.JiujiTenantEnum;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.LambdaCaseWhen;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.app.AttachmentTypeEnum;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Service("daiyongjiService")
public class DaiyongjiServiceImpl extends ServiceImpl<DaiyongjiMapper, Daiyongji> implements DaiyongjiService {

    @Resource
    private MqttAfterConfig mqttAfterConfig;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Autowired
    private DaiyongjiYajinService daiyongjiYajinService;
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private DaiyongjilogsService daiyongjilogsService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ShouhouMsgService shouhouMsgService;
    @Autowired
    private VoucherService voucherService;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private AttachmentsService attachmentsService;
    @Resource
    private WebCloud webCloud;
    @Resource
    private AreainfoService areainfoService;

    @Override
    public R<Page<DaiYongJiListRes>> getDaiYongJiPage(DaiYongJiListReq req) {
        if (req.getCurrent() == null) {
            req.setCurrent(1);
        }
        if (req.getSize() == null || req.getSize() == 0) {
            req.setSize(10);
        }

        Page<Daiyongji> page = new Page<>(req.getCurrent(), req.getSize());
        Page<DaiYongJiListRes> res = baseMapper.getDaiYongJiPage(page, req);
        for (DaiYongJiListRes x : res.getRecords()) {
            String gradeName = EnumUtil.getMessageByCode(DaiyongjiGradeEnum.class, x.getGrade());
            x.setGradeName(gradeName);
        }
        return R.success(res);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> applyDaiyongji(Integer wxId, Integer dyjId, BigDecimal yajin) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        Integer flag = baseMapper.checkDyjUse(wxId);

        if (flag > 0) {
            return R.error("此维修单已经使用了备用机");
        }
        List<Daiyongji> dyjList = super.list(new QueryWrapper<Daiyongji>().lambda().eq(Daiyongji::getId, dyjId)
                .and(bo -> bo.eq(Daiyongji::getIsdel, NumberConstant.ZERO).or().isNull(Daiyongji::getIsdel)));
        if (CollectionUtils.isEmpty(dyjList)) {
            return R.error("备用机不存在，请核对！");
        }
        Daiyongji dyjInfo = dyjList.get(0);
        Integer dyjStats = dyjInfo.getStats();
        if (!dyjStats.equals(DaiYongJiStatsEnum.FREE.getCode()) && !dyjStats.equals(DaiYongJiStatsEnum.SMJC.getCode())) {
            return R.error("此备用机不是空闲状态，不可使用");
        }

        Integer yajinId = this.saveDaiYongji(wxId, dyjId, yajin, oaUserBO.getUserName());
        if (yajinId == null || yajinId == 0) {
            throw new RRException("备用机记录保存失败！");
        }
        //备用机日志
        Daiyongjilogs dyjLogs = new Daiyongjilogs();
        String comment = "【代用中】维修单号：<a href=\"/shouhou/edit/" + wxId + "\">" + wxId + "</a>";
        String comment2 = "备用机借出成功";
        DaiyongjiYajin dyjYajinInfo = daiyongjiYajinService.getDyjYajinById(wxId, dyjId);
        if (dyjYajinInfo != null) {
            R<String> viewUrl = getViewUrl(wxId, dyjId);
            String data = "";
            if (viewUrl.isSuccess()) {
                data = viewUrl.getUserMsg();
            }
            if (StringUtils.isNotEmpty(data)) {
                comment += "，押金：" + dyjYajinInfo.getYajin() + " ，押金支付方式："
                        + (Objects.isNull(dyjYajinInfo.getPaytype()) ? "无" : dyjYajinInfo.getPaytype()) + "， "
                        + StrUtil.indexedFormat("<a href=\"{0}\" target=\"_blank\">{1}</a> "
                        , data, "《备用机协议》");
                comment2 += "，机型：" + dyjInfo.getName()
                        + (Objects.isNull(dyjInfo.getProductColor()) ? "" : dyjInfo.getProductColor())
                        + "，押金：" + dyjYajinInfo.getYajin() + " ，押金支付方式："
                        + (Objects.isNull(dyjYajinInfo.getPaytype()) ? "无" : dyjYajinInfo.getPaytype())+ "， "
                        + StrUtil.indexedFormat("<a href=\"{0}\" target=\"_blank\">{1}</a> "
                        , data, "《备用机协议》");
            }
        }
        dyjLogs.setDyjid(dyjId);
        dyjLogs.setInuser(oaUserBO.getUserName());
        dyjLogs.setDtime(LocalDateTime.now());
        dyjLogs.setComment(comment);
        daiyongjilogsService.save(dyjLogs);
        if (StringUtils.isNotEmpty(comment2)) {
            shouhouService.saveShouhouLog(wxId, comment2, "系统", null, false);
        }
        //日志记录
        String dyjUrl = String.format(" 编号：<a href=\"/staticpc/#/market/bak-machines?id=%s\">%s</a>", dyjId, dyjId);
//        comment = (dyjStats.equals(DaiYongJiStatsEnum.SMJC.getCode()) ? "从预约转入备用机" : "添加备用机") + "【" + dyjInfo.getName() + dyjUrl + "】，" + (yajin.intValue() == 0 ? "无押金" : "应收押金" + String.format("%.2f", yajin) + "元");
//        shouhouService.saveShouhouLog(wxId, comment, oaUserBO.getUserName(), null, true);

        //Shouhou shData = shouhouService.getById(wxId);
        Shouhou shData = CommenUtil.autoQueryHist(() ->shouhouService.getById(wxId), MTableInfoEnum.SHOUHOU, wxId);
        if (shData != null) {
            Integer areaId = shData.getToareaid() == null ? shData.getAreaid() : shData.getToareaid();
            ShouhouMsgPushMessageBo pushMsgBo = new ShouhouMsgPushMessageBo();
            pushMsgBo.setMsgId(20);
            pushMsgBo.setShouhouId(wxId);
            pushMsgBo.setAreaId(areaId);
            pushMsgBo.setUserId(shData.getUserid() == null ? 0 : shData.getUserid().intValue());
            pushMsgBo.setOptUser(oaUserBO.getUserName());
            Map<String, String> tmpData = new HashMap<>();
            tmpData.put("beiyongji", dyjInfo.getName() + " 编号：" + dyjId);
            tmpData.put("beiyongjiYajin", String.format("%.2f", yajin));

            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                tmpData.put("shoptel", areaInfoR.getData().getCompanyTel1());
            }
            pushMsgBo.setTmpData(tmpData);
            shouhouService.pushMessage(pushMsgBo, true);
        }
        //如果是上门借出的备用机
        if (dyjStats.equals(DaiYongJiStatsEnum.SMJC.getCode())) {
            comment = "备用机由上门借出转入售后维修单：<a href=\"/shouhou/edit/" + wxId + "\">" + wxId + "</a>，状态改为代用中";
            dyjLogs.setComment(comment);
            daiyongjilogsService.save(dyjLogs);
        }

        return R.success(true);
    }


    @Override
    public Integer updateDaiYongJiByIdAndStats(Integer wxId, LocalDateTime applydate, Integer dyjId) {
        return baseMapper.updateDaiYongJiByIdAndStats(wxId, LocalDateTime.now(), dyjId);
    }

    @Override
    public R<DaiYongJiInfoRes> getDaiYongJiBy(Integer dyjId, Integer wxId) {
        Daiyongji daiyongji = baseMapper.getDaiYongJiInfoBy(dyjId, wxId);
        if (daiyongji == null) {
            return R.error("备用机信息不存在");
        }
        DaiYongJiInfoRes result = new DaiYongJiInfoRes();
        BeanUtils.copyProperties(daiyongji, result);
        result.setAreaId(daiyongji.getAreaid());
        result.setApplyCount(daiyongji.getApplycount());
        result.setApplyDate(daiyongji.getApplydate());
        result.setToAreaStats(daiyongji.getToareaStats());
        result.setIsPanDian(daiyongji.getPandian());
        result.setPanDianDate(daiyongji.getPandiandate());
        result.setKcCheck(StrUtil.str(daiyongji.getKcCheck(), Charset.defaultCharset()));
        result.setJieChuRen(daiyongji.getJiechuren());
        result.setJieChuTime(daiyongji.getJiechutime());
        result.setYuYueId(daiyongji.getYuyueid());
        result.setIsDel(daiyongji.getIsdel());
        result.setJiKuang(daiyongji.getJikuang());
        result.setProductColor(daiyongji.getProductColor());
        result.setWaiGuan(daiyongji.getWaiGuan());
        result.setGrade(daiyongji.getGrade());
        String gradeName = EnumUtil.getMessageByCode(DaiyongjiGradeEnum.class, daiyongji.getGrade());
        result.setGradeName(gradeName);

        if (daiyongji.getAreaid() != null) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(daiyongji.getAreaid());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                result.setArea(areaInfoR.getData().getArea());
            }
        }

        DaiyongjiYajin dyjYajinInfo = daiyongjiYajinService.getDyjYajinById(wxId, dyjId);
        if (dyjYajinInfo != null) {
            result.setYajin(dyjYajinInfo.getYajin());
            result.setPaystate(dyjYajinInfo.getPaystate());
            result.setDyjYajin(dyjYajinInfo);
        }

        //备用机签署协议图片地址
        R<String> checkSignR = checkDyjSign(wxId, dyjId, null);
        if (checkSignR.getCode() == ResultCode.SUCCESS && StringUtils.isNotEmpty(checkSignR.getData())) {
            result.setSignImgsrc(checkSignR.getData());
        }

        return R.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> cancelDaiyongji(Integer wxId, Integer dyjId, Boolean isGuihuan, Boolean isCancelRefund) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }

        DaiyongjiYajin dyjYajin = daiyongjiYajinService.getDyjYajinById(wxId, dyjId);
        if (dyjYajin == null) {
            return R.error("未能查询到备用机记录");
        }
        Daiyongji dyjInfo = super.getById(dyjId);
        if (dyjInfo == null) {
            return R.error("备用机不存在，请核对！");
        }
        String dyjName = dyjInfo.getName();
        String jieChuRen = dyjInfo.getJiechuren();
        Boolean hasJieChuRen = StringUtils.isNotEmpty(jieChuRen);

        //记录操作人，生成凭证时需要使用
        redisTemplate.opsForValue().set("shouhoudyjyajin_tui_" + dyjYajin.getId(), oaUserBO.getUserName(), 30, TimeUnit.MINUTES);
        Boolean refundFlag = false;
        if (!isCancelRefund) {
            //退押金
            R<Dict> errMsg = SpringUtil.getBean(DaiyongjiService.class).refundDyjYajin(oaUserBO, dyjYajin);
            if (!errMsg.isSuccess()){
                return R.error(errMsg.getUserMsg());
            }
        }
        //取消备用机
        Integer paystate = (isCancelRefund != null && isCancelRefund) ? DaiyongjiPayStatesEnum.YKYJ.getCode() : DaiyongjiPayStatesEnum.REFUND.getCode();
        try {

            Integer b = baseMapper.cancelDaiYongJiYajin(dyjYajin.getId(), paystate, oaUserBO.getUserName());
            if (b < 1) {
                return R.error("备用机取消失败");
            }
            //当归还备用机，如果借出人不为空，表示备用机是上门服务人借出的，归还备用机应当更新状态为【上门借出】，不能为【空闲】状态。
            //上门借出的备用机，应当完成借出归还流程,备用机才会转为空闲状态
            Integer dyjStats = DaiYongJiStatsEnum.FREE.getCode();
            if (hasJieChuRen && !isGuihuan) {
                dyjStats = DaiYongJiStatsEnum.SMJC.getCode();
            }
            List<String> dyjLogs = new ArrayList<>();
            // 备用机归还,转到当前操作地区（包括上门借出的备用机）
            Integer daiYongJIAreaId = baseMapper.getDaiYongJiAreaId(dyjId);
            Integer updateDyjAreaId = null;
            if (CommenUtil.isNotNullZero(daiYongJIAreaId) && !daiYongJIAreaId.equals(oaUserBO.getAreaId())) {
                updateDyjAreaId = oaUserBO.getAreaId();
                if (!isCancelRefund) {
                    dyjLogs.add("备用机收回并转到" + oaUserBO.getArea());
                }
            }
            if (isCancelRefund) {
                dyjLogs.add("备用机无法收回，已扣除备用机押金：" + dyjYajin.getTotal() + "元，售后单:<a href=\"/shouhou/edit/" + dyjYajin.getWxid() + "\">" + dyjYajin.getWxid() + "</a>");
            }

            super.update(new LambdaUpdateWrapper<Daiyongji>()
                    .set(updateDyjAreaId != null, Daiyongji::getAreaid, updateDyjAreaId)
                    .set(Daiyongji::getStats, dyjStats)
                    .set(Daiyongji::getPos, null)
                    .set(Daiyongji::getGuihuandate, LocalDateTime.now())
                    .eq(Daiyongji::getId, dyjId));
            shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getDyjid, null).eq(Shouhou::getId, wxId));
            if (hasJieChuRen) {
                if (isGuihuan) {
                    //备用机已同时归还至库管处
                    UpdateWrapper<Daiyongji> up = new UpdateWrapper<>();
                    up.lambda().set(Daiyongji::getJiechuren, null).set(Daiyongji::getJiechutime, null).set(Daiyongji::getYuyueid, null).eq(Daiyongji::getId, dyjId);
                    this.update(up);
                    dyjLogs.add(String.format("%s上门维修借出的备用机由%s收回，已归还", jieChuRen, oaUserBO.getUserName()));
                    dyjLogs.add("【空闲】收回备用机从代用中恢复为空闲，售后单<a href=\"/shouhou/edit/" + wxId + "\">" + wxId + "</a>");
                } else {
                    //备用机在收回人手中，后续需要借出归还操作
                    dyjLogs.add(String.format("%s上门维修借出的备用机由%s收回，暂未归还", jieChuRen, oaUserBO.getUserName()));
                    dyjLogs.add("【上门借出】收回备用机并转为上门借出，售后单<a href=\"/shouhou/edit/" + wxId + "\">" + wxId + "</a>");
                }
            } else {
                dyjLogs.add("【空闲】备用机从代用中恢复为空闲，售后单<a href=\"/shouhou/edit/" + wxId + "\">" + wxId + "</a>");
            }
            this.saveDaiYongJiLogsBatch(dyjId, dyjLogs, oaUserBO.getUserName());
            String dyjUrl = "，编号：<a href=\"/staticpc/#/market/bak-machines?id=" + dyjId + "\">" + dyjId + "</a>";
            if (isCancelRefund) {
                shouhouService.saveShouhouLog(wxId, "备用机无法收回，已扣除备用机押金：" + dyjYajin.getTotal() + "元【" + dyjName + dyjUrl + "】", oaUserBO.getUserName(), null, true);
            } else {
                shouhouService.saveShouhouLog(wxId, "收回备用机【" + dyjName + dyjUrl + "】", oaUserBO.getUserName(), null, true);
                Integer nowAreaId = baseMapper.getShouhouAreaId(wxId);
                R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(nowAreaId);
                Boolean iszhongyou = false;
                if (ResultCode.SUCCESS == areaInfoR.getCode()) {
                    iszhongyou = (StringUtils.isNotEmpty(areaInfoR.getData().getPrintName()) && areaInfoR.getData().getPrintName().indexOf("中邮") > 0);
                }
                shouhouMsgService.sendWeixinMsg(wxId, "收回备用机" + dyjName, iszhongyou, false);
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("备用机取消异常:{}", e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("备用机取消异常:" + e.getMessage());
        }

        //归还备用机，删除附件信息
        attachmentsService.remove(new LambdaQueryWrapper<Attachments>()
                .eq(Attachments::getLinkedID, wxId).eq(Attachments::getType, 6)
                .eq(Attachments::getKind1, 7));
        //移除网站签署协议信息
        this.removeSignatureByDataId(wxId);
        return R.success("取消备用机成功");
    }

    @Override
    public R<String> checkDyjSign(Integer wxId, Integer dyjId, BigDecimal yajin) {
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUser == null) {
            return R.unLogin();
        }
        Integer agreementType = 2;

        long xtenant = Namespaces.get();
        if (xtenant == 1) {
            agreementType = 4;
        }

        String host = sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.M_URL, oaUser.getXTenant());
        String signApiUrl = host + "/web/api/document/getSignature/v1?type=" + agreementType + "&dataId=" + wxId;
//        if (CommenUtil.isNotNullZero(dyjId)) {
//            Daiyongji dyjInfo = super.getById(dyjId);
//            String imei = dyjInfo.getImei();
//            if (StringUtils.isNotEmpty(imei)) {
//                signApiUrl += "&imei=" + imei;
//            }
//        }

        HttpRequest getSign = HttpUtil.createGet(signApiUrl);
        SpringContextUtil.getRequest()
                .ifPresent(req -> {
                    String reqCookie = req.getHeader("Cookie");
                    if (StrUtil.isNotBlank(reqCookie)) {
                        if(!StrUtil.contains(reqCookie, "oaToken")){
                            reqCookie = StrUtil.format("{};oaToken={}", reqCookie, req.getHeader(HttpHeaders.AUTHORIZATION));
                        }
                        getSign.cookie(reqCookie);
                    }
                });
        String jsonStr = getSign.execute().body();

        JSONObject jsonObject = JSON.parseObject(jsonStr);
        String signatureImage = Optional.ofNullable(jsonObject).map(jo->jo.getJSONObject("data")).map(d->d.getString("signatureImage")).orElse(null);
        if (StringUtils.isNotEmpty(signatureImage)) {
            return R.success("该用户已签署协议", signatureImage);
        } else {
            //未签署协议,检验地区是否一致
            if(!Objects.equals(shouhouService.getAreaIdByShouhouId(wxId),oaUser.getAreaId())){
                //地区不一致
                return R.error("跨地区,请先切换再操作!");
            }
            //缓存
            Optional.ofNullable(yajin).ifPresent(yj-> redisTemplate.opsForValue().set(String.format("dyj_yajin:%s_%s", wxId,dyjId),yj.toString()
                    ,NumberConstant.FIVE,TimeUnit.MINUTES));

            return R.success("该用户还未签署协议");
        }

    }

    @Override
    public Boolean setDyjRefundsComplete(Integer yaJinId) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        daiyongjiYajinService.update(new LambdaUpdateWrapper<DaiyongjiYajin>()
                .set(DaiyongjiYajin::getPaystate, DaiyongjiPayStatesEnum.REFUND.getCode())
                .eq(DaiyongjiYajin::getId, yaJinId).eq(DaiyongjiYajin::getPaystate, DaiyongjiPayStatesEnum.PAY_OFF.getCode()));

        Integer areaId = 0;
        areaId = baseMapper.getDyjNowAreaId(yaJinId);

        DaiyongjiYajin yajinInfo = daiyongjiYajinService.getById(yaJinId);

        Integer wxId = yajinInfo.getWxid();
        Integer dyjId = yajinInfo.getDyjid();
        String payType = yajinInfo.getPaytype();
        BigDecimal total = yajinInfo.getTotal() == null ? BigDecimal.ZERO : yajinInfo.getTotal();

        //操作人：
        String userName = redisTemplate.opsForValue().get("shouhoudyjyajin_tui_" + yaJinId).toString();
        if (StringUtils.isEmpty(userName)) {
            userName = "系统";
        }
        if (CommenUtil.isNullOrZero(wxId)) {
            return false;
        }

        //凭证
        String kemu = "";
        String remark = "";
        String fzhs = "无|无";
        String jief = total + "|0";
        String daif = "0|" + total;
        String payName = "";
        if ("wx".equals(payType) || "wxapp".equals(payType)) {
            payName = "微信";
            kemu = "224135|122112";
        } else if (Arrays.asList("wxDz", "wxDzApp").contains(payType)) {
            payName = "微信";
            kemu = "224135|1002233";
        } else if (Arrays.asList("alipay", "pay1").contains(payType)) {
            payName = "支付宝";
            kemu = "224135|100231";
            if (!oaUserBO.getXTenant().equals(0)) {
                kemu = "224135|1002225";
            }
        } else if ("dzpay".equals(payType)) {
            payName = "支付宝";
            kemu = "224135|100231";
        }

        remark = MessageFormat.format(payName + "退备用机押金，维修单号：{0}，操作人：{1}", wxId.toString(), userName);
        remark = remark + "|" + remark;
        String zt = "1";
        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
        if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
            AreaInfo areaInfo = areaInfoR.getData();
            if (areaInfo.getXtenant().equals(JiujiTenantEnum.JIUJI_TENANT_YAYA.getCode().intValue())) {
                zt = "32";
                kemu = "224121|112207";
                fzhs = areaInfo.getArea() + "|无";
                if ("wx".equals(payType) || "wxapp".equals(payType)) {
                    kemu = "224121|112208";
                }
            }
        }

        String comment = MessageFormat.format("退备用机押金：{0}元", total.toString());

        PingzhengBO pingzheng = new PingzhengBO();
        pingzheng.setZhaiyao(remark);
        pingzheng.setKemu(kemu);
        pingzheng.setFzhs(fzhs);
        pingzheng.setJief(jief);
        pingzheng.setDaif(daif);
        pingzheng.setPzdate(DateUtil.localDateTimeToString(LocalDateTime.now()));
        pingzheng.setZtid(zt);

        PingzhengResultBO voucherResult = voucherService.addPingZheng(pingzheng);
        if (!voucherResult.getFlag()) {
            shouhouService.saveShouhouLog(wxId, "退备用机押金退回成功，但是凭证生成失败(" + voucherResult.getPzId() + ")", userName, null, false);
        } else {
            Integer pzId = voucherResult.getPzId();
            daiyongjiYajinService.update(new LambdaUpdateWrapper<DaiyongjiYajin>().set(DaiyongjiYajin::getPzid2, pzId).eq(DaiyongjiYajin::getId, yaJinId));

            comment += "，凭证编号：" + pzId;
            shouhouService.saveShouhouLog(wxId, comment, userName, null, false);

            Daiyongjilogs logs = new Daiyongjilogs();
            logs.setDyjid(dyjId);
            logs.setComment(comment);
            logs.setInuser(userName);
            logs.setDtime(LocalDateTime.now());
            daiyongjilogsService.save(logs);
        }
        return true;
    }

    @Override
    public Integer getDyjNowAreaId(Integer yaJinId) {
        return baseMapper.getDyjNowAreaId(yaJinId);
    }

    @Override
    public List<Daiyongji> getDaiYongJiPage() {
        return this.list(new LambdaUpdateWrapper<Daiyongji>().isNull(Daiyongji::getPos).eq(Daiyongji::getStats, 1).eq(Daiyongji::getIsdel, 0));
    }

    @Override
    public void updateDYJ(Integer dyjId, Integer wxId, String user) {
        DaiyongjiYajin dyjYajinInfo = daiyongjiYajinService.getDyjYajinById(wxId, dyjId);
        String comment = "【代用中】维修单号：<a href=\"/shouhou/edit/" + wxId + "\">" + wxId + "</a>";
        if (dyjYajinInfo != null) {
            //备用机签署协议图片地址
            //添加日志记录
            R<String> viewUrl = getViewUrl(wxId, dyjId);
            String data = "";
            if (viewUrl.isSuccess()) {
                data = viewUrl.getUserMsg();
            }
            if (StringUtils.isNotEmpty(data)) {
                comment +="，押金：" + dyjYajinInfo.getYajin() + " ，押金支付方式："
                        + (Objects.isNull(dyjYajinInfo.getPaytype()) ? "无" : dyjYajinInfo.getPaytype()) + "， "
                        + StrUtil.indexedFormat("<a href=\"{0}\" target=\"_blank\">{1}</a> "
                        , data, "《备用机协议》");
            }
        }
        this.update(new LambdaUpdateWrapper<Daiyongji>().set(Daiyongji::getPos, wxId).set(Daiyongji::getStats, 2).eq(Daiyongji::getId, dyjId));
        Daiyongjilogs dyjLogs = new Daiyongjilogs();
        dyjLogs.setDyjid(dyjId);
        dyjLogs.setInuser(user);
        dyjLogs.setDtime(LocalDateTime.now());
        dyjLogs.setComment(comment);
        daiyongjilogsService.save(dyjLogs);
    }

    @Override
    public R<String> daiYongJiPanDian(Integer dyjId) {
        if (CommenUtil.isNullOrZero(dyjId)) {
            return R.error("未指定备用机信息");
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        Daiyongji daiyongji = super.getById(dyjId);
        String resMsg;
        if (daiyongji == null) {
            resMsg = "编号错误，不存在此机器！";
            return R.error(resMsg);
        }
        if (Objects.equals(daiyongji.getPandian(), Boolean.TRUE)) {
            resMsg = "此机已盘点，盘点时间：" + DateUtil.localDateTimeToString(daiyongji.getPandiandate());
            return R.error(resMsg);
        }

        Integer areaId = oaUserBO.getAreaId();

        if (!areaId.equals(daiyongji.getAreaid())) {
            resMsg = "盘点地区错误";
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(daiyongji.getAreaid());

            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                resMsg += "，该机器属于[" + areaInfoR.getData().getArea() + "],当前地区[" + oaUserBO.getArea() + "]!";

            }
            return R.error(resMsg);
        }

        Boolean flag = super.update(new LambdaUpdateWrapper<Daiyongji>()
                .set(Daiyongji::getPandian, Boolean.TRUE)
                .set(Daiyongji::getPandiandate, LocalDateTime.now())
                .set(Daiyongji::getPanUser, oaUserBO.getUserName())
                .eq(Daiyongji::getId, dyjId));

        if (!flag) {
            return R.error("操作失败");
        }
        Daiyongjilogs logs = new Daiyongjilogs();
        logs.setDyjid(dyjId);
        logs.setComment("【盘点】操作，已盘点");
        logs.setInuser(oaUserBO.getUserName());
        logs.setDtime(LocalDateTime.now());
        daiyongjilogsService.save(logs);

        resMsg = "<li bianhao='" + dyjId.toString() + "'><span class='nrong'> 编号[" + dyjId + "]&nbsp;" + daiyongji.getName() + "已盘点</span></li>";

        return R.success("操作成功", resMsg);
    }

    @Override
    public R<String> pandianStart() {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        AreaBelongsDcHqD1AreaId backEndInfo = areainfoService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
        LambdaUpdateWrapper<Daiyongji> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Daiyongji::getPandian, Boolean.FALSE);
        if (CollUtil.contains(backEndInfo.getHqAreaIds(),oaUserBO.getAreaId())) {
            updateWrapper.eq(Daiyongji::getAreaid, oaUserBO.getAreaId());
        }

        super.update(updateWrapper);
        return R.success("操作成功");
    }

    @Override
    public List<DaiYongJiListResVo> getList(DaiYongJiListQueryReq req) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        AreaBelongsDcHqD1AreaId backEndInfo = areainfoService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
        if (CollUtil.contains(backEndInfo.getHqAreaIds(),req.getCurAreaId())){
            //当前地区是在hqareaid 的时候，set22的值
            req.setCurAreaId(22);
        }
        return baseMapper.getList(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> submitToArea(DaiYongToAreaReq req) {
        if (CollectionUtils.isEmpty(req.getIds())) {
            return R.error("备用机ID不能为空");
        }
        List<DaiYongJiBo> dyjList = baseMapper.getDaiYongjiListByIds(req.getIds());
        if (CollectionUtils.isEmpty(dyjList)) {
            return R.error("该机器正在调拨中!");
        }
        List<DaiyongjiToarea> toareaList = dyjList.stream().map(e -> {
            DaiyongjiToarea dyjToArea = new DaiyongjiToarea();
            dyjToArea.setDaiyongjiId(e.getId());
            dyjToArea.setAreaid(e.getAreaId());
            dyjToArea.setToareaid(req.getToAreaId());
            LocalDate localDate = LocalDateTime.now().toLocalDate();
            dyjToArea.setShibian(e.getAreaId() + localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            return dyjToArea;
        }).collect(Collectors.toList());

        //todo
        return null;
    }

    @Override
    public R<Boolean> saveDaiYongJiAttachments(ShouhouDaiYongjiAttachmentsAddReq dyjAttachments) {
        if (CollectionUtils.isEmpty(dyjAttachments.getAttachmentsList())) {
            return R.error("附件信息为空");
        }
        Integer userId = dyjAttachments.getUserId();
        Integer wxId = dyjAttachments.getShouhouId();
        if(userId != null){
//            Optional<Integer> shouhouIdOpt = CommenUtil.autoQueryHist(()-> shouhouService.lambdaQuery()
//                    .select(Shouhou::getId).list().stream().map(Shouhou::getId).findFirst(),MTableInfoEnum.SHOUHOU,wxId);
            Optional<Integer> shouhouIdOpt =CommenUtil.autoQueryHist(()->shouhouService.lambdaQuery().eq(Shouhou::getId, wxId).eq(Shouhou::getUserid, userId)
                    .select(Shouhou::getId).list().stream().map(Shouhou::getId).findFirst(),MTableInfoEnum.SHOUHOU,wxId);
            if(!shouhouIdOpt.isPresent()) {
                return R.error("您没有权限操作该售后单");
            }
        }
        attachmentsService.remove(new LambdaQueryWrapper<Attachments>().eq(Attachments::getLinkedID, wxId)
                .eq(Attachments::getType, 6)
                .eq(Attachments::getKind1, 7));
        Integer dyjId = dyjAttachments.getDyjId();
        List<Attachments> attachmentsList = dyjAttachments.getAttachmentsList().stream()
                .map(item -> {
                    Attachments attachments = new Attachments();
                    String dtime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
                    attachments.setFid(item.getFid()).setFilepath(item.getFilePath())
                            .setFilename("备用机编号【" + dyjId + "】-" + dtime)
                            .setLinkedID(wxId)
                            .setDtime(LocalDateTime.now())
                            .setKind1(7)
                            .setType(6)
                            .setDtime(LocalDateTime.now());
                    return attachments;
                }).collect(Collectors.toList());
        attachmentsList.stream().forEach(item -> {
            attachmentsService.save(item);
        });
        //添加日志记录
        return R.success("操作成功");
    }

    @Override
    public R<List<FileReq>> getDaiYongJiAttachments(Integer shouhouId, Integer userId) {
        if(userId != null){
//            Optional<Integer> shouhouIdOpt = shouhouService.lambdaQuery().eq(Shouhou::getId, shouhouId).eq(Shouhou::getUserid, userId)
//                    .select(Shouhou::getId).list().stream().map(Shouhou::getId).findFirst();
            Optional<Integer> shouhouIdOpt =CommenUtil.autoQueryHist(()->shouhouService.lambdaQuery().eq(Shouhou::getId, shouhouId).eq(Shouhou::getUserid, userId)
                    .select(Shouhou::getId).list().stream().map(Shouhou::getId).findFirst(),MTableInfoEnum.SHOUHOU,shouhouId);
            if(!shouhouIdOpt.isPresent()) {
                return R.success(Collections.emptyList());
            }
        }

        List<Attachments> list = attachmentsService.newList(new LambdaQueryWrapper<Attachments>().orderByAsc(Attachments::getId)
                .eq(Attachments::getLinkedID, shouhouId)
                .eq(Attachments::getType, AttachmentsEnum.SHOUHOU.getCode())
                .eq(Attachments::getKind1, 7), shouhouId, AttachmentsEnum.SHOUHOU.getCode(), null);
        if (CollectionUtils.isEmpty(list)) {
            return R.success(new ArrayList<>());
        }
        return R.success(attachmentsService.convertList(list));
    }

    @Override
    public R<String> removeSignatureByDataId(Integer shouhouId) {
        if (CommenUtil.isNullOrZero(shouhouId)){
            return R.error("维修单号不能为空");
        }
//        Result<String> result = webCloud.removeSignatureByDataId(shouhouId, null);
        String host = sysConfigService.getValueByCode(SysConfigConstant.WEB_URL);
        String url = host + "/web/api/document/removeSignatureByDataId/v1?dataId=" + shouhouId;
        String result = HttpClientUtil.get(url);

        return R.success(result);
    }

    @Override
    public String forwardMqtt(ForwardMqttReq req) {
        mqttAfterConfig.publish(req.getTopic(),req.getCodeStr());
        return "转发成功";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Dict> cancelDaiyongjiV2(CancelDaiYongjiReq cancelDaiYongjiReq, OaUserBO oaUserBO) {
        if(oaUserBO == null){
            return R.error("请先登录");
        }
        Integer dyjId = cancelDaiYongjiReq.getDyjId();
        Integer wxId = cancelDaiYongjiReq.getWxId();
        DaiyongjiYajin dyjYajin = daiyongjiYajinService.getDyjYajinById(wxId, dyjId);
        Daiyongji dyjInfo = super.getById(dyjId);
        BigDecimal oldPrice = dyjInfo.getPrice();
        R<Dict> checkResult = checkCancelDaiyongji(dyjYajin, dyjInfo,cancelDaiYongjiReq, oldPrice);
        if (!checkResult.isSuccess()){
            return checkResult;
        }
        String dyjName = dyjInfo.getName();
        boolean isCancelRefund = Boolean.TRUE.equals(cancelDaiYongjiReq.getIsCancelRefund());
        //记录操作人，生成凭证时需要使用
        redisTemplate.opsForValue().set("shouhoudyjyajin_tui_" + dyjYajin.getId(), oaUserBO.getUserName(), 30, TimeUnit.MINUTES);
        if (!isCancelRefund) {
            R<Dict> errMsg = SpringUtil.getBean(DaiyongjiService.class).refundDyjYajin(oaUserBO, dyjYajin);
            if (!errMsg.isSuccess()){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return errMsg;
            }
        }
        List<String> dyjLogs = new ArrayList<>();
        R<Dict> updateDbResult = SpringUtil.getBean(DaiyongjiService.class).updateDbTocancelDaiyongji(oaUserBO,cancelDaiYongjiReq,dyjYajin,dyjInfo, dyjLogs);
        if (!updateDbResult.isSuccess()){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return updateDbResult;
        }
        R<Dict> kcResult = SpringUtil.getBean(DaiyongjiService.class).handleDaiyongjiKc(oaUserBO, cancelDaiYongjiReq, dyjInfo, dyjLogs, oldPrice);
        if(!kcResult.isSuccess()){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return kcResult;
        }
        R<Dict> saveLogResult = SpringUtil.getBean(DaiyongjiService.class).saveCancelDyjLogs(oaUserBO, cancelDaiYongjiReq, dyjYajin, dyjInfo, dyjLogs);
        if(!saveLogResult.isSuccess()){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return saveLogResult;
        }

        if (!isCancelRefund){
            //发送微信通知
            Integer nowAreaId = baseMapper.getShouhouAreaId(wxId);
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(nowAreaId);
            Boolean iszhongyou = false;
            if (ResultCode.SUCCESS == areaInfoR.getCode()) {
                iszhongyou = (StringUtils.isNotEmpty(areaInfoR.getData().getPrintName()) && areaInfoR.getData().getPrintName().indexOf("中邮") > 0);
            }
            shouhouMsgService.sendWeixinMsg(wxId, "收回备用机" + dyjName, iszhongyou, false);
        }


        return R.success("取消备用机成功");
    }

    private R<Dict> checkCancelDaiyongji(DaiyongjiYajin dyjYajin, Daiyongji dyjInfo, CancelDaiYongjiReq cancelDaiYongjiReq,BigDecimal oldPrice) {
        if (dyjYajin == null) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("未能查询到备用机记录");
        }
        if (dyjInfo == null) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("备用机不存在，请核对");
        }
        if(!Boolean.TRUE.equals(cancelDaiYongjiReq.getIsCancelRefund())){
            if(ObjectUtil.defaultIfNull(cancelDaiYongjiReq.getNowPrice(),BigDecimal.ZERO).compareTo(BigDecimal.ZERO)<=0){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error(StrUtil.format("备用机现价不能为空"));
            }
            //通过判断现价和原价判断是否需要附件
            if (CollUtil.isEmpty(cancelDaiYongjiReq.getFileReqs())) {
                //如果是九机且价格有变则需要附件，输出暂时不改动
                if (Objects.nonNull(cancelDaiYongjiReq.getNowPrice()) && cancelDaiYongjiReq.getNowPrice().compareTo(oldPrice) != 0)  {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return R.error(StrUtil.format("备用机附件不能为空"));
                }
            }else {
                if (XtenantEnum.isJiujiXtenant() && cancelDaiYongjiReq.getFileReqs().size() != 6) {
                    return R.error(StrUtil.format("外观附件必须有6张图片，上传图片上限限制6张"));
                }
            }
            if (ObjectUtil.defaultIfNull(dyjInfo.getPrice(),BigDecimal.ZERO)
                    .compareTo(ObjectUtil.defaultIfNull(cancelDaiYongjiReq.getNowPrice(),BigDecimal.ZERO))<0){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error(StrUtil.format("备用机现价不能高于{}",ObjectUtil.defaultIfNull(dyjInfo.getPrice(),BigDecimal.ZERO)
                        .setScale(2,RoundingMode.HALF_UP)));
            }
        }else{
            //取消并扣除押金,现价为零元
//            cancelDaiYongjiReq.setNowPrice(BigDecimal.ZERO);
        }
        return R.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Dict> handleDaiyongjiKc(OaUserBO oaUserBO, CancelDaiYongjiReq cancelDaiYongjiReq, Daiyongji dyjInfo, List<String> dyjLogs, BigDecimal oldPrice) {
        String jieChuRen = dyjInfo.getJiechuren();
        boolean hasJieChuRen = StringUtils.isNotEmpty(jieChuRen);
        boolean isGuihuan = Boolean.TRUE.equals(cancelDaiYongjiReq.getIsGoHomeGuihuan());
        Integer dyjId = cancelDaiYongjiReq.getDyjId();
        Integer wxId = cancelDaiYongjiReq.getWxId();
        boolean isUpdateKc = false;
        if (hasJieChuRen) {
            if (isGuihuan) {
                isUpdateKc = true;
                dyjLogs.add(String.format("%s上门维修借出的备用机由%s收回，已归还", jieChuRen, oaUserBO.getUserName()));
                //只做九机
                if ((XtenantEnum.isJiujiXtenant() && Objects.nonNull(cancelDaiYongjiReq.getNowPrice()) && cancelDaiYongjiReq.getNowPrice().compareTo(oldPrice) != 0) || !XtenantEnum.isJiujiXtenant()){
                    dyjLogs.add(StrUtil.indexedFormat("【空闲】备用机从代用中恢复为空闲，售后单<a href=\"/shouhou/edit/{0,number,#}\">{0,number,#}</a>更新现价{1},点击查看<a href=\"{2}\" target=\"_blank\">评估附件</a>"
                            , wxId, cancelDaiYongjiReq.getNowPrice().setScale(2, RoundingMode.HALF_UP), cancelDaiYongjiReq.getFileReqs().stream().findFirst().map(FileReq::getFilePath).orElse("")));
                }else {
                    dyjLogs.add("【空闲】备用机从代用中恢复为空闲");
                }
            } else {
                //备用机在收回人手中，后续需要借出归还操作
                dyjLogs.add(String.format("%s上门维修借出的备用机由%s收回，暂未归还", jieChuRen, oaUserBO.getUserName()));
                dyjLogs.add("【上门借出】收回备用机并转为上门借出，售后单<a href=\"/shouhou/edit/" + wxId + "\">" + wxId + "</a>");
            }
        } else {
            isUpdateKc = true;
            //只做九机
            if (XtenantEnum.isJiujiXtenant() && Objects.nonNull(cancelDaiYongjiReq.getNowPrice()) && oldPrice.compareTo(cancelDaiYongjiReq.getNowPrice()) != 0) {
                dyjLogs.add(StrUtil.indexedFormat("【空闲】备用机从代用中恢复为空闲，售后单<a href=\"/shouhou/edit/{0,number,#}\">{0,number,#}</a>更新现价{1},点击查看<a href=\"{2}\" target=\"_blank\">评估附件</a>"
                        , wxId, cancelDaiYongjiReq.getNowPrice().setScale(2, RoundingMode.HALF_UP), cancelDaiYongjiReq.getFileReqs().stream().findFirst().map(FileReq::getFilePath).orElse("")));
            }else {
                dyjLogs.add("【空闲】备用机从代用中恢复为空闲");
            }
        }
        if (isUpdateKc){
            //保存附件
            attachmentsService.saveAttachemnts(cancelDaiYongjiReq.getFileReqs(), dyjId, AttachmentTypeEnum.DAI_YONG_JI.getCode(), oaUserBO.getUserId(),null);
            List<Attachments> attachments = cancelDaiYongjiReq.getFileReqs().stream().map(fileReq -> fileReq.getAttachments()).filter(Objects::nonNull).collect(Collectors.toList());
            cancelDaiYongjiReq.setFileReqs(attachmentsService.convertList(attachments));
            //备用机已同时归还至库管处
            UpdateWrapper<Daiyongji> up = new UpdateWrapper<>();
            up.lambda().set(Daiyongji::getJiechuren, null).set(Daiyongji::getJiechutime, null).set(Daiyongji::getYuyueid, null)
//                    .set(Daiyongji::getPrice,cancelDaiYongjiReq.getNowPrice())
                    .set(Daiyongji::getFids,Optional.ofNullable(cancelDaiYongjiReq.getFileReqs()).map(f->f.stream().map(FileReq::getId).map(StrUtil::toString)
                            .collect(Collectors.joining(","))).orElse(null))
                    .eq(Daiyongji::getId, dyjId);
            this.update(up);
        }
        return R.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Dict> saveCancelDyjLogs(OaUserBO oaUserBO, CancelDaiYongjiReq cancelDaiYongjiReq, DaiyongjiYajin dyjYajin, Daiyongji dyjInfo, List<String> dyjLogs) {
        Integer dyjId = cancelDaiYongjiReq.getDyjId();
        Integer wxId = cancelDaiYongjiReq.getWxId();
        String dyjName = dyjInfo.getName();
        boolean isCancelRefund = Boolean.TRUE.equals(cancelDaiYongjiReq.getIsCancelRefund());
        String testResult = Objects.isNull(cancelDaiYongjiReq.getTestResult())?"空":cancelDaiYongjiReq.getTestResult();
        String waiGuan =  Objects.isNull(cancelDaiYongjiReq.getWaiGuan())?"空":cancelDaiYongjiReq.getWaiGuan();
        StringBuilder attachment = new StringBuilder();
        List<FileReq> fileReqs = cancelDaiYongjiReq.getFileReqs();
        Map<Integer,String> map = new HashMap<>();
        List<String> list = Arrays.asList("正面关于本机界面图", "机身背面照片", "右侧机身边框照片", "左侧机身边框照片", "机身顶部照片", "机身底部照片");
        for (int i = 0; i < list.size(); i++) {
            map.put(i,list.get(i));
        }
        for (int i = 0; i < fileReqs.size(); i++) {
            FileReq fileReq = fileReqs.get(i);
            String fileName = Objects.nonNull(map.get(i)) ? map.get(i) : fileReq.getFileName();
            String s = StrUtil.indexedFormat("<a href=\"{0}\" target=\"_blank\">{1}</a> "
                    , fileReq.getFilePath(), fileName);
            attachment.append(s);
        }
        String comment = "归还备用机，机型："+dyjInfo.getName()+"，编号：" + dyjInfo.getId()
                + "，押金："+dyjYajin.getYajin()+"，是否可以继续使用："+ FlagEnum.getMessageByCode(Convert.toInt(cancelDaiYongjiReq.getIsContinueUse()))
                +"，是否有保护壳："+ FlagEnum.getMessageByCode(Convert.toInt(cancelDaiYongjiReq.getIsProtectiveShell())) +"，是否有保护膜："
                + FlagEnum.getMessageByCode(Convert.toInt(cancelDaiYongjiReq.getIsProtectiveFilm())) +"，测试结果："+ testResult
                + "，外观描述：" + waiGuan + " ，现价：" + cancelDaiYongjiReq.getNowPrice() + "，备用机附件："
                + attachment;
        dyjLogs.add(comment);
        //保存日志
        this.saveDaiYongJiLogsBatch(dyjId, dyjLogs, oaUserBO.getUserName());
        shouhouService.saveShouhouLog(wxId, comment, oaUserBO.getUserName(), null, false);
        String dyjUrl = "，编号：<a href=\"/staticpc/#/market/bak-machines?id=" + dyjId + "\">" + dyjId + "</a>";
        if (isCancelRefund) {
            shouhouService.saveShouhouLog(wxId, "备用机无法收回，已扣除备用机押金：" + dyjYajin.getTotal() + "元【" + dyjName + dyjUrl + "】", oaUserBO.getUserName(), null, true);
        } else {
            shouhouService.saveShouhouLog(wxId, "收回备用机【" + dyjName + dyjUrl + "】", oaUserBO.getUserName(), null, true);
        }
        return R.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Dict> updateDbTocancelDaiyongji(OaUserBO oaUserBO, CancelDaiYongjiReq cancelDaiYongjiReq, DaiyongjiYajin dyjYajin
            , Daiyongji dyjInfo, List<String> dyjLogs) {
        Integer dyjId = cancelDaiYongjiReq.getDyjId();
        Integer wxId = cancelDaiYongjiReq.getWxId();
        boolean hasJieChuRen = StringUtils.isNotEmpty(dyjInfo.getJiechuren());
        boolean isCancelRefund = Boolean.TRUE.equals(cancelDaiYongjiReq.getIsCancelRefund());
        //取消备用机
        Integer paystate = LambdaCaseWhen
                // 扣除押金且已付款
                .lambdaCaseWhen(() -> isCancelRefund && Objects.equals(dyjYajin.getPaystate(), DaiyongjiPayStatesEnum.PAY_OFF.getCode())
                        , () -> DaiyongjiPayStatesEnum.YKYJ.getCode())
                // 退押金且已付款
                .when(() -> !isCancelRefund && Objects.equals(dyjYajin.getPaystate(), DaiyongjiPayStatesEnum.PAY_OFF.getCode()),
                        () -> DaiyongjiPayStatesEnum.REFUND.getCode())
                // 其他保持原来的退款状态
                .endElse(() -> dyjYajin.getPaystate());
        boolean isGuihuan = Boolean.TRUE.equals(cancelDaiYongjiReq.getIsGoHomeGuihuan());
        Integer b = baseMapper.cancelDaiYongJiYajin(dyjYajin.getId(), paystate, oaUserBO.getUserName());
        if (b < 1) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("备用机取消押金失败");
        }
        //当归还备用机，如果借出人不为空，表示备用机是上门服务人借出的，归还备用机应当更新状态为【上门借出】，不能为【空闲】状态。
        //上门借出的备用机，应当完成借出归还流程,备用机才会转为空闲状态
        Integer dyjStats = DaiYongJiStatsEnum.FREE.getCode();
        if (hasJieChuRen && !isGuihuan) {
            dyjStats = DaiYongJiStatsEnum.SMJC.getCode();
        }

        // 备用机归还,转到当前操作地区（包括上门借出的备用机）
        Integer daiYongJIAreaId = baseMapper.getDaiYongJiAreaId(dyjId);
        Integer updateDyjAreaId = null;
        if (CommenUtil.isNotNullZero(daiYongJIAreaId) && !daiYongJIAreaId.equals(oaUserBO.getAreaId())) {
            updateDyjAreaId = oaUserBO.getAreaId();
            if (!isCancelRefund) {
                dyjLogs.add("备用机收回并转到" + oaUserBO.getArea());
            }
        }
        if (isCancelRefund) {
            dyjLogs.add("备用机无法收回，已扣除备用机押金：" + dyjYajin.getTotal() + "元，售后单:<a href=\"/shouhou/edit/" + dyjYajin.getWxid() + "\">" + dyjYajin.getWxid() + "</a>");
        }
        LambdaUpdateWrapper<Daiyongji> daiyongjiLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        if (Objects.nonNull(cancelDaiYongjiReq.getIsProtectiveShell())){
            daiyongjiLambdaUpdateWrapper.set(Daiyongji::getIsProtectiveShell, cancelDaiYongjiReq.getIsProtectiveShell());
        }
        if (Objects.nonNull(cancelDaiYongjiReq.getIsProtectiveFilm())){
            daiyongjiLambdaUpdateWrapper.set(Daiyongji::getIsProtectiveFilm, cancelDaiYongjiReq.getIsProtectiveFilm());
        }
        if (Objects.nonNull(cancelDaiYongjiReq.getIsContinueUse())){
            daiyongjiLambdaUpdateWrapper.set(Daiyongji::getIsContinueUse, cancelDaiYongjiReq.getIsContinueUse());
        }
        if (StringUtils.isNotEmpty(cancelDaiYongjiReq.getTestResult())){
            daiyongjiLambdaUpdateWrapper.set(Daiyongji::getTestResult, cancelDaiYongjiReq.getTestResult());
        }
        if (StringUtils.isNotEmpty(cancelDaiYongjiReq.getWaiGuan())){
            daiyongjiLambdaUpdateWrapper.set(Daiyongji::getWaiGuan, cancelDaiYongjiReq.getWaiGuan());
        }
        daiyongjiLambdaUpdateWrapper.set(Daiyongji::getGuihuandate, LocalDateTime.now());
        daiyongjiLambdaUpdateWrapper.eq(Daiyongji::getId, dyjId).eq(Daiyongji::getPos,dyjInfo.getPos());
        daiyongjiLambdaUpdateWrapper.set(updateDyjAreaId != null, Daiyongji::getAreaid, updateDyjAreaId);
        daiyongjiLambdaUpdateWrapper.set(Daiyongji::getStats, dyjStats);
        daiyongjiLambdaUpdateWrapper.set(Daiyongji::getPos, null);
        boolean isDyjUp = super.update(daiyongjiLambdaUpdateWrapper);
        if(!isDyjUp){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("备用机解锁售后单更新失败");
        }
        boolean isShUp = shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getDyjid, null).eq(Shouhou::getId, wxId));
        if(!isShUp){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("售后单解锁备用机更新失败");
        }
        //归还备用机，删除附件信息
        attachmentsService.remove(new LambdaQueryWrapper<Attachments>()
                .eq(Attachments::getLinkedID, wxId).eq(Attachments::getType, AttachmentTypeEnum.SHOUHOU.getCode())
                .eq(Attachments::getKind1, 7));
        //移除网站签署协议信息
        this.removeSignatureByDataId(wxId);
        return R.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Dict> refundDyjYajin(OaUserBO oaUserBO, DaiyongjiYajin dyjYajin) {
        Boolean refundFlag;//退押金
        String errMsg = "";
        if (dyjYajin.getTotal() != null && dyjYajin.getTotal().compareTo(BigDecimal.ZERO) > 0 && DaiyongjiPayStatesEnum.PAY_OFF.getCode().equals(dyjYajin.getPaystate())) {

            if (Arrays.asList("wx", "wxapp", "yayawx", "yayawxapp", "wxDz", "wxDzApp", "微信").contains(dyjYajin.getPaytype()) || dyjYajin.getPaytype().contains("微信")) {
                //payapi.WeiXinRefund
                R<String> refundR = daiyongjiYajinService.dyjYajinRefund("weixin", dyjYajin.getPayorderid(), dyjYajin.getTotal(), dyjYajin.getId().toString(), "退回备用机押金", dyjYajin.getPaytype(), oaUserBO.getXTenant());
                refundFlag = refundR.getCode() == ResultCode.SUCCESS;
                errMsg = refundR.getUserMsg();
            } else {
                //payapi.AliRefund
                R<String> refundR = daiyongjiYajinService.dyjYajinRefund("alipay", dyjYajin.getPayorderid(), dyjYajin.getTotal(), dyjYajin.getId().toString(), "退回备用机押金", dyjYajin.getPaytype(), oaUserBO.getXTenant());
                refundFlag = refundR.getCode() == ResultCode.SUCCESS;
                errMsg = refundR.getUserMsg();
            }
            if (!refundFlag) {
                return R.error(errMsg);
            }

            if (refundFlag) {
                //生成退款凭证
                setDyjRefundsComplete(dyjYajin.getId());
            }
        }
        return R.success(null);
    }

    @Override
    public R<String> getViewUrl(Integer wxid, Integer dyjId) {
        if (ObjectUtil.defaultIfNull(wxid, 0) <=0){
            return R.error("维修id不能为空");
        }
        Shouhou shouhou = shouhouService.getById(wxid);
        if(shouhou == null){
            return R.error("维修单不存在");
        }
        String moaUrl = SpringUtil.getBean(SysConfigService.class).getValueByCode(SysConfigConstant.M_URL);
        Integer type;
        if(Objects.equals(XtenantEnum.getXtenant(), 1)){
            type = 4;
        }else{
            type = 2;
        }
        StringJoiner viewUrlJoiner = new StringJoiner("").add(moaUrl);
        viewUrlJoiner.add("/after-service/agreement?type=").add(Convert.toStr(type))
                .add("&dataId=").add(Convert.toStr(wxid));
        if(dyjId != null){
            viewUrlJoiner.add("&dyjid=").add(Convert.toStr(dyjId));
        }
        return R.success(viewUrlJoiner.toString());
    }

    private Integer saveDaiYongji(Integer wxId, Integer dyjId, BigDecimal yajin, String inuser) {
        DaiyongjiYajin yajinInfo = new DaiyongjiYajin();
        yajinInfo.setWxid(wxId);
        yajinInfo.setDyjid(dyjId);
        yajinInfo.setYajin(yajin);
        yajinInfo.setApplydate(LocalDateTime.now());
        yajinInfo.setPaystate(yajin.compareTo(BigDecimal.ZERO) > 0 ? DaiyongjiPayStatesEnum.UN_PAY.getCode() : DaiyongjiPayStatesEnum.PAY_OFF.getCode());
        yajinInfo.setCancel(false);
        yajinInfo.setInuser(inuser);
        boolean result = daiyongjiYajinService.save(yajinInfo);
        if (result) {
            this.updateDaiYongJiByIdAndStats(wxId, LocalDateTime.now(), dyjId);

            //更新售后信息
            shouhouService.update(new UpdateWrapper<Shouhou>().lambda().set(Shouhou::getDyjid, dyjId).eq(Shouhou::getId, wxId));
        }
        return yajinInfo.getId();
    }

    private void saveDaiYongJiLogsBatch(Integer dyjId, List<String> commentList, String inuser) {
        if (CollectionUtils.isEmpty(commentList)) {
            return;
        }
        List<Daiyongjilogs> logs = commentList.stream().map(t -> {
            Daiyongjilogs log = new Daiyongjilogs();
            log.setDtime(LocalDateTime.now());
            log.setInuser(inuser);
            log.setComment(t);
            log.setDyjid(dyjId);
            daiyongjilogsService.save(log);
            return log;
        }).collect(Collectors.toList());
        //这里批量插入会报错
//        daiyongjilogsService.saveBatch(logs);
    }
}
