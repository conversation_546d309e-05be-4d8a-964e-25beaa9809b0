package com.jiuji.oa.afterservice.bigpro.service.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jiuji.oa.afterservice.bigpro.bo.BindPpidInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.WxFeeBo;
import com.jiuji.oa.afterservice.bigpro.enums.AccessoryTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.CorrelationTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.PlanTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.RepairPlanMasterStatusEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.service.strategy.RepairPlanConfirmStrategy;
import com.jiuji.oa.afterservice.bigpro.vo.OrderPartsVo;
import com.jiuji.oa.afterservice.bigpro.vo.req.CostPriceNewReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.RepairPlanConfirmReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.BindPpidKcInfo;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import com.jiuji.oa.afterservice.bigpro.vo.req.CorrelationInfo;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 抽象简单维修策略实现 - 只更新主表和记录日志，不处理维修配件
 * 适用于以旧换新和以换代修
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Slf4j
public abstract class AbstractSimpleRepairStrategy implements RepairPlanConfirmStrategy {

    @Resource
    private ShouhouService shouhouService;


    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private ProductKcService kcService;


    @Resource
    private RepairAccessoriesService repairService;
    
    @Resource
    private RepairPlanMasterService repairPlanMasterService;
    @Resource
    private RepairFaultService faultService;
    @Resource
    private RepairPlanMasterTypeService repairPlanMasterTypeService;
    @Resource
    private ShouhouYuyueService yuyueService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer confirm(RepairPlanMaster master, RepairPlanConfirmReq req) {
        Integer shouHouId = req.getShouHouId();
        String confirmUser = req.getConfirmUser();
        Integer repairPlanMasterTypeId = req.getRepairPlanMasterTypeId();
        RepairPlanMasterType planMasterType = Optional.ofNullable(repairPlanMasterTypeService.getById(repairPlanMasterTypeId))
                .orElseThrow(() -> new CustomizeException("不支持的维修方案类型"));
        // 更新维修方案主表
        updateRepairPlanMaster(master, confirmUser);
        // 更新维修方案主表类型表
        updateRepairPlanMasterType(repairPlanMasterTypeId);
        // 记录确认日志
        String planTypeName = PlanTypeEnum.getDescByCode(planMasterType.getPlanType());
        shouhouService.saveShouhouLog(shouHouId, "确认" + planTypeName + "，确认人：" + confirmUser, confirmUser);
        
        return master.getId();
    }

    /**
     * 更新维修方案主表类型表
     * @param id
     */
    public void updateRepairPlanMasterType(Integer id){
        boolean update = repairPlanMasterTypeService.lambdaUpdate().eq(RepairPlanMasterType::getId, id)
                .set(RepairPlanMasterType::getIsGenerateAccessory, NumberConstant.ONE)
                .update();
        if(!update){
            throw new CustomizeException("更新维修方案类型表信息失败");
        }
    }
    /**
     * 更新维修方案主表
     * 
     * @param master 维修方案主表
     * @param confirmUser 确认用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRepairPlanMaster(RepairPlanMaster master, String confirmUser) {
        boolean updatePlanMaster = repairPlanMasterService.lambdaUpdate()
                .eq(RepairPlanMaster::getId, master.getId())
                .set(RepairPlanMaster::getConfirmUser, confirmUser)
                .set(RepairPlanMaster::getConfirmTime, LocalDateTime.now())
                .set(RepairPlanMaster::getStatus, RepairPlanMasterStatusEnum.USER_CONFIRMATION.getCode())
                .update();
        if (!updatePlanMaster) {
            throw new CustomizeException("更新维修方案主表信息失败");
        }
    }

    /**
     * 添加维修配件
     *
     * @param repairPlans 维修方案列表
     * @param shouHouId 售后单ID
     * @return 配件价格请求
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CorrelationInfo> addWxFeeBos(List<RepairPlan> repairPlans, Integer shouHouId) {
        List<CorrelationInfo> correlationInfoList = new ArrayList<>();
        Shouhou shouhou = Optional.ofNullable(CommenUtil.autoQueryHist(() ->shouhouService.getById(shouHouId), MTableInfoEnum.SHOUHOU, shouHouId))
                .orElseThrow(() -> new CustomizeException("未找到指定的售后单"));
        //商品成本获取
        List<Integer> ppidList = repairPlans.stream().map(RepairPlan::getAccessoryPpid).collect(Collectors.toList());
        Integer areaId = Optional.ofNullable(shouhou.getToareaid()).orElse(shouhou.getAreaid());
        Map<Integer, ProductKc> kcMap = kcService.lambdaQuery().in(ProductKc::getPpriceid, ppidList)
                .eq(ProductKc::getAreaid, areaId).list()
                .stream().collect(Collectors.toMap(ProductKc::getPpriceid, Function.identity(), (n1, n2) -> n2));
        //商品信息获取
        Map<Integer, Productinfo> productinfoMap = productinfoService.getProductMapByPpids(ppidList);
        // 遍历每个方案，调用shouhouService.addCostPrice
        List<WxFeeBo> wxFeeBoList = new ArrayList<>();
        List<OrderPartsVo> orderPartsVoList = new ArrayList<>();
        Integer faultId = repairPlans.stream().filter(plan -> ObjectUtil.isNotNull(plan.getFaultId()))
                .findFirst().orElse(new RepairPlan())
                .getFaultId();
        RepairFault repairFault = Optional.ofNullable(faultService.getById(faultId)).orElseThrow(() -> new CustomizeException("未找到指定的故障"));
        repairPlans.forEach(plan -> {
            WxFeeBo wxFeeBo = new WxFeeBo();
            wxFeeBo.setShouhouId(shouHouId);
            wxFeeBo.setAreaId(areaId);
            //成本价格获取
            Integer ppid = Optional.ofNullable(plan.getAccessoryPpid()).orElse(NumberConstant.ZERO);
            ProductKc productKc = Optional.ofNullable(kcMap.get(ppid)).orElse(new ProductKc());
            wxFeeBo.setInprice(productKc.getInprice());
            wxFeeBo.setIshexiao(Boolean.FALSE);
            wxFeeBo.setPpid(ppid);
            wxFeeBo.setRepairPlanId(plan.getId());
            wxFeeBo.setPrice1(plan.getPrice());
            wxFeeBo.setPrice(plan.getPrice());
            Productinfo productinfo = Optional.ofNullable(productinfoMap.get(ppid)).orElse(new Productinfo());
            boolean isRepairCost = AccessoryTypeEnum.REPAIR_COST.getCode().equals(plan.getAccessoryType());
            if(isRepairCost){
                wxFeeBo.setProductName(plan.getAccessoryName());
                wxFeeBo.setKinds(NumberConstant.ONE);
            } else {
                wxFeeBo.setKinds(NumberConstant.TWO);
                wxFeeBo.setProductName(productinfo.getProductName());
            }
            wxFeeBo.setPriceGs(BigDecimal.ZERO);
            Integer leftCount = Optional.ofNullable(productKc.getLeftCount()).orElse(NumberConstant.ZERO);
            //只有库存大于0或者允许负库存出库或者维修成本  的时候才进行绑定配件的查询 并且直接走添加配件
            Boolean isNegativeInventory = kcService.negativeInventory(ppid);
            if(leftCount>NumberConstant.ZERO || isNegativeInventory || isRepairCost){
                //查询绑定配件信息
                List<BindPpidKcInfo> bindPpidKcInfo = repairService.createBindPpidKcInfo(ppid, areaId);
                if(CollUtil.isNotEmpty(bindPpidKcInfo)){
                    List<BindPpidInfoBo> bindPpidInfos = bindPpidKcInfo.stream().map(item -> {
                        BindPpidInfoBo bindPpidInfoBo = new BindPpidInfoBo();
                        BeanUtil.copyProperties(item, bindPpidInfoBo);
                        bindPpidInfoBo.setOutPutNumber(NumberConstant.ONE);
                        return bindPpidInfoBo;
                    }).collect(Collectors.toList());
                    wxFeeBo.setBindPpidInfos(bindPpidInfos);
                }
                wxFeeBoList.add(wxFeeBo);
            } else {
                OrderPartsVo orderPartsVo = new OrderPartsVo();
                orderPartsVo.setPpid(ppid)
                        .setProductName(productinfo.getProductName())
                        .setShouHouId(shouHouId)
                        .setRepairPlanId(plan.getId())
                        .setContent(repairFault.getFaultName());
                orderPartsVoList.add(orderPartsVo);

            }
        });
        //存在库存的直接走添加
        if(CollUtil.isNotEmpty(wxFeeBoList)){
            CostPriceNewReq costPriceNewReq = new CostPriceNewReq();
            costPriceNewReq.setWxFeeBoList(wxFeeBoList);
            repairService.addCostPriceNew(costPriceNewReq);
            wxFeeBoList.forEach(wxFeeBo -> {
                CorrelationInfo correlationInfo = new CorrelationInfo();
                correlationInfo.setCorrelationType(CorrelationTypeEnum.WXKCOUTPUT.getCode())
                        .setCorrelationId(wxFeeBo.getId())
                        .setRepairPlanId(wxFeeBo.getRepairPlanId())
                        .setAccessoryPpid(wxFeeBo.getPpid());
                correlationInfoList.add(correlationInfo);
            });
        }
        //不存在库存的走订购
        if(CollUtil.isNotEmpty(orderPartsVoList)){
            List<CorrelationInfo> orderPartsNonTransactional = yuyueService.createOrderPartsNonTransactional(orderPartsVoList);
            if(CollUtil.isNotEmpty(orderPartsNonTransactional)){
                correlationInfoList.addAll(orderPartsNonTransactional);
            }
        }
        return correlationInfoList;
    }
}
