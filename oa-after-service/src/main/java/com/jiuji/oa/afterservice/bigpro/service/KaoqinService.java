package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.po.Kaoqin;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-18
 */
public interface KaoqinService extends IService<Kaoqin> {
    /**
     * 查询正在上班的用户
     * @param ch999Ids
     * @return
     */
    List<Integer> getCurrentWork(List<Integer> ch999Ids);
    /**
     * 查询正在上班的用户
     * @param ch999Ids
     * @return
     */

    /**
     * 根据门店id获取当前工作的员工信息
     * @param areaId
     * @param ch999Ids
     * @return
     */
    List<Integer> getCurrentWorkByAreaIdAndCh999Ids(Integer areaId,List<Integer> ch999Ids);
    /**
     * 根据门店id获取当前工作的员工信息
     * @param areaId
     * @param roleIds
     * @return
     */
    List<Integer> getCurrentWorkByAreaIdAndRoleIds(Integer areaId,List<Integer> roleIds);

    /**
     * 根据门店id获取当前工作的员工信息
     * @param areaId
     * @param zhiWuIds
     * @return
     */
    List<Integer> getCurrentWorkByAreaIdAndZhiWuIds(Integer areaId, List<Integer> zhiWuIds);



    List<Integer> getCh999IdsByRole(List<Integer> roleIds);


    List<Integer> getCh999IdsByzhiWu(List<Integer> zhiWuIds);

}
