package com.jiuji.oa.afterservice.bigpro.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 维修方案主表类型
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("repair_plan_master_type")
@ApiModel(value = "RepairPlanMasterType对象", description = "维修方案主表类型")
public class RepairPlanMasterType extends Model<RepairPlanMasterType> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "维修方案主表ID")
    @TableField("master_id")
    private Integer masterId;

    @ApiModelProperty(value = "是否生成配件(0:否 1:是)")
    @TableField("is_generate_accessory")
    private Integer isGenerateAccessory;

    /**
     * @see com.jiuji.oa.afterservice.bigpro.enums.PlanTypeEnum
     */
    @ApiModelProperty(value = "方案类型")
    @TableField("plan_type")
    private Integer planType;

    @ApiModelProperty(value = "创建人")
    @TableField("create_user")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "逻辑删除(0:未删除 1:已删除)")
    @TableLogic
    @TableField("is_del")
    private Integer isDel;

}
