package com.jiuji.oa.afterservice.refund.service;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.bigpro.po.BbsxpUsers;
import com.jiuji.oa.afterservice.bigpro.service.BbsxpUsersService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeV1Enum;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.refund.dao.RefundValidMemberMapper;
import com.jiuji.oa.afterservice.refund.service.valid.CommonChildValidService;
import com.jiuji.oa.afterservice.refund.vo.req.CommonValidVo;
import com.jiuji.oa.afterservice.sys.service.BaseValidMemberService;
import com.jiuji.oa.afterservice.sys.service.ValidMemberService;
import com.jiuji.oa.afterservice.sys.vo.req.BaseValid;
import com.jiuji.oa.afterservice.sys.vo.req.ValidMemberReq;
import com.jiuji.oa.afterservice.sys.vo.req.ValidReq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2022/8/2 14:52
 */
@Service
@Slf4j
public class CommonValidMemberServiceImpl implements BaseValidMemberService<CommonValidVo> {
    @Resource
    private RefundValidMemberMapper refundValidMemberMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private SmsService smsService;
    @Override
    public R<Boolean> sendCode(ValidMemberReq<CommonValidVo> req) {
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        R<Boolean> e = commonCheckAndSet(req);
        if (!e.isSuccess()){
            return e;
        }
        CommonValidVo validVo = req.getT();
        try {
            switch (req.getValidTypeEnum()){
                case SMS_CODE:
                    return sendSmsCode(req, oaUser, validVo);
                case AUTH_CODE:
                    return sendAuthCode(req,oaUser,validVo);

            }
        } catch (IllegalArgumentException ex) {
            return R.error(ex.getMessage());
        }
        return R.error("该验证类型不支持发送验证信息");
    }

    /**
     * 生成授权验证信息
     * @param req
     * @param oaUser
     * @param refundVo
     * @return
     */
    private R<Boolean> sendAuthCode(ValidMemberReq<CommonValidVo> req, OaUserBO oaUser, CommonValidVo refundVo) {
        return R.error("通用验证暂不支持授权验证");
    }

    /**
     * 发送短信验证信息
     * @param req
     * @param oaUser
     * @param validVo
     * @return
     */
    private R<Boolean> sendSmsCode(ValidMemberReq<CommonValidVo> req, OaUserBO oaUser, CommonValidVo validVo) {
        AreaInfo areaInfo = Optional.ofNullable(areaInfoClient.getAreaInfoById(oaUser.getAreaId())).map(R::getData).orElseThrow(() -> new CustomizeException("门店信息有误"));
        if(!Boolean.TRUE.equals(areaInfo.getIsSend())){
            return R.error("门店不支持发送短信");
        }
        if(StrUtil.isBlank(validVo.getMobile())){
            SpringUtil.getBean(BbsxpUsersService.class).lambdaQuery().eq(BbsxpUsers::getId, validVo.getUserid())
                    .select(BbsxpUsers::getId, BbsxpUsers::getMobile).list().stream().findFirst()
                    .ifPresent(user -> {
                        validVo.setMobile(user.getMobile());
                    });
        }
        Assert.isFalse(StrUtil.isBlank(validVo.getMobile()),"电话号码不能为空！");
        String codeKey = StrUtil.format(RedisKeys.AFTER_SMS_CODE_KEY, req.getBusinessType(), StrUtil.format("{}_{}",req.getOrderId(), validVo.getMobile()));
//        if (!stringRedisTemplate.hasKey(codeKey) || stringRedisTemplate.getExpire(codeKey) < TimeUnit.MINUTES.toSeconds(1)){
//            stringRedisTemplate.opsForValue().setIfAbsent(codeKey, StrUtil.format("{}",CommonUtils.getRandom4Code()), NumberConstant.FIVE, TimeUnit.MINUTES);
//        }
        // XSWL-20315:每次推送都要重新生成验证码，以前发送过的作废
        stringRedisTemplate.opsForValue().set(codeKey, StrUtil.format("{}",CommonUtils.getRandom4Code()), NumberConstant.FIVE, TimeUnit.MINUTES);

        validVo.setValidCodeKey(codeKey);
        String numCode = stringRedisTemplate.opsForValue().get(codeKey);
        log.warn("{}单号:{},手机号:{},验证码:{}", req.getBusinessTypeEnum().getMessage(), req.getOrderId(),validVo.getMobile(), numCode);
        R<Boolean> sendResult = sendCodeContent(numCode, req, validVo, oaUser);
        if (sendResult.isSuccess()) {
            return R.success("验证码发送成功", Boolean.TRUE).addAllBusinessLog(sendResult.businessLogs());
        } else {
            return new R(ResultCode.SERVER_ERROR, sendResult.getMsg(), sendResult.getUserMsg()).addAllBusinessLog(sendResult.businessLogs());
        }
    }

    private R<Boolean> sendCodeContent(String numCode, ValidMemberReq<CommonValidVo> req, CommonValidVo validVo, OaUserBO oaUser) {
        //  String tips = DecideUtil.iif(Objects.equals(XtenantEnum.getXtenant(), XtenantEnum.Xtenant_JIUJI), "如有疑问可致电咨询：************。", "");
        // format 可用变量: 验证码 validCode 单号 orderId 业务名称 businessName 九机或者输出特殊提示 tips
        String msg = StrUtil.format(validVo.getBusinessTypeEnum().getSmsFormat(), Dict.create().set("validCode", numCode)
                .set("orderId", req.getOrderId()).set("businessName", validVo.getBusinessTypeEnum().getMessage())
                .set("paramBusinessName", validVo.getBusinessName()));
        return smsService.sendSms(validVo.getMobile(), msg, DateUtil.localDateTime2LocalDate(LocalDateTime.now().minusMinutes(NumberConstant.TWO)),
                oaUser.getUserName(), smsService.getSmsChannelByTenant(oaUser.getAreaId(), ESmsChannelTypeEnum.VERIFICATION_CODE));
    }

    @Override
    public R<Boolean> valid(ValidReq<CommonValidVo> req) {
        //公共验证
        R<Boolean> e = commonCheckAndSet(req);
        if (!e.isSuccess()){
            return e;
        }
        if(req.getValidCode() == null){
            //
            return R.error("验证码不能为空");
        }
        try {
            CommonValidVo validVo = req.getT();
            switch (req.getValidTypeEnum()){
                case SMS_CODE:
                    //电话号码为空, 获取会员的号码
                    if(StrUtil.isBlank(validVo.getMobile())){
                        SpringUtil.getBean(BbsxpUsersService.class).lambdaQuery().eq(BbsxpUsers::getId, validVo.getUserid())
                                .select(BbsxpUsers::getId, BbsxpUsers::getMobile).list().stream().findFirst()
                                .ifPresent(user -> {
                                    validVo.setMobile(user.getMobile());
                                });
                    }
                    Assert.isFalse(StrUtil.isBlank(validVo.getMobile()),"电话号码不能为空！");
                    String codeKey = StrUtil.format(RedisKeys.AFTER_SMS_CODE_KEY, req.getBusinessType(),
                            StrUtil.format("{}_{}",req.getOrderId(), validVo.getMobile()));
                    String code = stringRedisTemplate.opsForValue().get(codeKey);
                    Assert.isFalse(StrUtil.isBlank(code), "请先发送验证码");
                    Assert.isTrue(Objects.equals(req.getValidCode(), code), "验证码错误！");
                    validVo.setValidCodeKey(codeKey);
                    break;
                case PAY_PASSWORD:
                    Assert.isFalse(validVo.getUserid() == null,"会员信息不能为空！");
                    SpringUtil.getBean(ValidMemberService.class).assertValidPayPwd(req.getValidCode(),validVo.getUserid());
                    break;
                case MEMBER_CODE:
                    Assert.isFalse(validVo.getUserid() == null,"会员信息不能为空！");
                    SpringUtil.getBean(ValidMemberService.class).assertValidMemberToken(req.getValidCode(),validVo.getUserid());
                    break;
                case AUTH_CODE:
                    return R.error("暂不支持授权验证");
                default:
                    return R.error("未实现该验证处理");
            }
            // 根据子业务调用对应类执行
            SpringUtil.getBeansOfType(CommonChildValidService.class).entrySet().stream()
                    .filter(ccv -> ccv.getValue().support(validVo.getBusinessTypeEnum()))
                    .findFirst()
                    .ifPresent(ccv -> ccv.getValue().validSuccess(req));
        } catch (IllegalArgumentException iae) {
            return R.error(iae.getMessage());
        }
        return R.success(Boolean.TRUE);
    }

    @Override
    public boolean support(BusinessTypeV1Enum businessTypeEnum) {
        if (businessTypeEnum == null){
            return false;
        }
        switch (businessTypeEnum){
            case COMMON:
                return true;
            default:
                return false;
        }
    }


    private R<Boolean> commonCheckAndSet(BaseValid<CommonValidVo> req) {
        try {
            //设置对象值
            Optional.ofNullable(req.getJson()).map(json -> json.toJavaObject(CommonValidVo.class)).ifPresent(req::setT);
            CommonValidVo validVo = req.getT();
            Assert.isFalse(req.getJson() == null && validVo == null,"通用验证参数错误");
            //设置枚举值
            if(CommenUtil.isNullOrZero(validVo.getBusinessType()) && validVo.getBusinessTypeEnum() != null){
                validVo.setBusinessType(validVo.getBusinessTypeEnum().getCode());
            }else if(validVo.getBusinessTypeEnum() == null && CommenUtil.isNotNullZero(validVo.getBusinessType())){
                validVo.setBusinessTypeEnum(EnumUtil.getEnumByCode(CommonValidVo.BusinessEnum.class, validVo.getBusinessType()));
            }
            Assert.isFalse(validVo.getBusinessType() == null, "通用验证下的业务类型不能为空");
            Assert.isFalse(validVo.getBusinessTypeEnum() == null, "通用验证的业务类型{}不支持", validVo.getBusinessType());
        } catch (IllegalArgumentException e) {
            return R.error(e.getMessage());
        }
        return R.success(null);
    }
}
