package com.jiuji.oa.afterservice.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.ch999.common.util.utils.HttpClientUtils;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.afterservice.wuliu.sf.vo.TencentMapUploadReq;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 腾讯地图工具
 *
 * <AUTHOR>
 * @date 2022/10/9 20:19
 */
@Slf4j
public class TencentMapUtil {

    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    public static final String TENCENT_MAP_UPLOAD_KEY = "after:browser:record:upload";


    private static final String UPLOAD_URL = "/bigdata/api/general/upload/v1";
    public static final Integer BICYCLING_RATE_LIMIT = 120;
    private static final Integer SUCCESS_CODE=0;
    private static final String KEY = "3XTBZ-Y2TKR-OAVWG-WRAB2-XWWQH-O6BYJ";
    private static final String TENCENT_DRIVING_URL = "https://apis.map.qq.com/ws/direction/v1/driving/?from=%s&to=%s&waypoints=%s&output=json&callback=cb&key=%s";
    private static final String TENCENT_BICYCLING_URL = "https://apis.map.qq.com/ws/direction/v1/bicycling/?from=%s&to=%s&output=json&callback=cb&key=%s";
    @Resource
    private static RedissonClient redissonClient;


    public static R<String> upload(String purl, String deviceName) {
        TencentMapUploadReq req = new TencentMapUploadReq();
        req.setP(purl);
        req.setD(deviceName);
        OaUserBO curStaffUser = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        Integer userId = curStaffUser.getUserId();
        String zone = SpringContextUtil.getContext().getEnvironment().getProperty("instance-zone","");
        req.setXt(Convert.toStr(curStaffUser.getXTenant()));
        req.setSt(zone);
        req.setRd(String.valueOf(NumberCipherUtil.encrypt(userId)));
        //添加分布式锁(锁租户)
        int xTenant = Integer.parseInt(req.getXt());
        String key = TENCENT_MAP_UPLOAD_KEY
                + ":" + xTenant + ":" + purl + ":" + userId;
        SysConfigService sysConfigService = SpringUtil.getBean(SysConfigService.class);
        String url = sysConfigService.getValueByCode(15);
        RLock lock = SpringUtil.getBean(RedissonClient.class).getLock(key);
        try {
            boolean isLock = lock.tryLock(0, 5L, TimeUnit.MINUTES);
            if (isLock) {
                log.info(String.format("调用开始！%s",  JSON.toJSONString(req)));
                String uploadUrl = url + UPLOAD_URL;
                String respJsonString = HttpClientUtils.postJson(uploadUrl, JSON.toJSONString(req));
                log.info(String.format("调用成功！%s", respJsonString));
            } else {
                log.info("已收到,正在处理中:{}", JSON.toJSONString(req));
            }
        } catch (Exception e) {
            log.error("调用失败！", e);
            return R.error(Boolean.FALSE.toString());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return R.success(zone);
    }
}
