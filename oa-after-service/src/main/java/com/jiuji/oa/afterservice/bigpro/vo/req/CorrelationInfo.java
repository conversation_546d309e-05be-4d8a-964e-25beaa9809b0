package com.jiuji.oa.afterservice.bigpro.vo.req;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CorrelationInfo {

    @ApiModelProperty(value = "关联ppid")
    private Integer accessoryPpid;

    /**
     * 关联id
     */
    @ApiModelProperty(value = "关联id")
    private Integer correlationId;

    /**
     * @see com.jiuji.oa.afterservice.bigpro.enums.CorrelationTypeEnum
     * 1-wxkcoutput 表的id
     * 2-shouhou_apply 表的id
     */
    @ApiModelProperty(value = "关联id类型")
    private Integer correlationType;

    /**
     * 内部数据流转使用
     */
    private Integer repairPlanId;
}
