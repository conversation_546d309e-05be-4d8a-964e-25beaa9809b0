package com.jiuji.oa.afterservice.bigpro.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 维修方案VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@ApiModel(value = "RepairPlanVO对象", description = "维修方案VO")
public class RepairPlanVO implements Serializable {


    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "方案名称")
    private String planName;

    @ApiModelProperty(value = "关联配件类型：1-维修配件（默认），2-维修成本")
    private Integer accessoryType;

    private String accessoryTypeName;

    @ApiModelProperty(value = "关联配件ID")
    private Integer accessoryPpid;

    @ApiModelProperty(value = "关联名称")
    private String accessoryName;

    /**
     * 关联维修配件id
     */
    @ApiModelProperty(value = "关联维修配件id")
    private Integer wxKcOutPutId;

    /**
     * 选中生成配件
     */
    private Boolean isGenerateAccessory;

    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品规格
     */
    private String productColor;

    /**
     * 库存
     */
    private Integer leftCount;

    @ApiModelProperty(value = "方案价格")
    private BigDecimal price;
} 