package com.jiuji.oa.afterservice.delayQueuePush.appointmentFormStrategy.impl;

import cn.hutool.json.JSONUtil;
import com.jiuji.oa.afterservice.common.enums.SmsReceiverClassfyEnum;
import com.jiuji.oa.afterservice.delayQueuePush.appointmentFormStrategy.AppointmentFormPushStrategy;
import com.jiuji.oa.afterservice.delayQueuePush.vo.AppointmentFormPush;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import com.meitu.platform.lmstfy.exception.LmstfyException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 处理方式为邮寄送修的预约单
 * <AUTHOR>
 */
@Slf4j
@Service(value = "MailAppointmentFormQueueStrategy")
public class MailAppointmentFormQueueStrategy extends CommonStrategy implements AppointmentFormPushStrategy {

    @Value("${lmstfy.mult.first-lmstfy-client.mailAppointmentFormQueue}")
    private String mailAppointmentFormQueue;
    @Value("${lmstfy.mult.first-lmstfy-client.mailAppointmentFormAccumulationQueue}")
    private String mailAppointmentFormAccumulationQueue;
    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;

    /**
     * 预约单在“业务确认”状态超72小时未变更状态，系统给订单业务确认人推送超时信息，推送一次即可
     * @param appointmentFormPush
     */
    @Override
    public void pushDataDelayDetection(AppointmentFormPush appointmentFormPush) {
        try {
            //设置超时时间为72小时
            appointmentFormPush.setDelaySecond(SEVENTY_TWO_HOUR);
            //计算延迟队列时间和统计推送次数
            Integer calculationDelaySecond = this.calculationDelaySecond(appointmentFormPush, null);
            String publish = firstLmstfyClient.publish(mailAppointmentFormQueue, JSONUtil.toJsonStr(appointmentFormPush).getBytes(), 0, (short) 1, calculationDelaySecond);
            log.warn("邮寄送修的预约单延迟队列推送成功，队列名称{}，推送参数{}，返回结果{}",mailAppointmentFormQueue,JSONUtil.toJsonStr(appointmentFormPush),publish);
        } catch (LmstfyException e){
            appointmentFormPush.setPushNumber(appointmentFormPush.getPushNumber()- NumberConstant.ONE);
            log.error("邮寄送修的预约单延迟队列推送异常，队列名称{}，推送参数{}",mailAppointmentFormQueue,JSONUtil.toJsonStr(appointmentFormPush),e);
        }

    }

    @Override
    public void pushData(AppointmentFormPush appointmentFormPush) {
        //判断当前是否为0点到9点30
        Boolean accumulationTime = super.isAccumulationTime();
        if(accumulationTime){
            //在晚上0点到9点30的订单把订单推送到积压队列
            try {
                //计算延迟队列时间和统计推送次数
                Integer calculationDelaySecond = super.calculationDelaySecond(appointmentFormPush, null);
                String publish = firstLmstfyClient.publish(mailAppointmentFormAccumulationQueue, JSONUtil.toJsonStr(appointmentFormPush).getBytes(), 0, (short) 1, calculationDelaySecond);
                log.warn("邮寄送修的预约单延迟队列（堆积）推送成功，队列名称{}，推送参数{}，返回结果{}",mailAppointmentFormAccumulationQueue,JSONUtil.toJsonStr(appointmentFormPush),publish);
            } catch (LmstfyException e){
                appointmentFormPush.setPushNumber(appointmentFormPush.getPushNumber()- NumberConstant.ONE);
                log.error("邮寄送修的预约单延迟队列（堆积）推送异常，队列名称{}，推送参数{}",mailAppointmentFormAccumulationQueue,JSONUtil.toJsonStr(appointmentFormPush),e);
            }
        } else {
            //直接推送OA信息
            pushDirectMsg(appointmentFormPush, SmsReceiverClassfyEnum.YJSX_QR_SEND);
            //消息推送之后进行消息延迟监控
            pushDataDelayDetection(appointmentFormPush);
        }

    }
}
