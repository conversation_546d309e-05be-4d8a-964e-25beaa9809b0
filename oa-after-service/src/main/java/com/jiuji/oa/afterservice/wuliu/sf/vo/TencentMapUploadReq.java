package com.jiuji.oa.afterservice.wuliu.sf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 数据上报字段对象
 * </p>
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-04-14
 */
@Data
@ApiModel("数据上报字段对象")
public class TencentMapUploadReq {

    @ApiModelProperty(value = "操作类型，1启动页面，2点击事件")
    private String a = "2";

    @ApiModelProperty(value = "来源页面的URL")
    private String f;

    @ApiModelProperty(value = "事件类型")
    private String p = "";

    @ApiModelProperty(value = "事件类型")
    private String et;

    @ApiModelProperty(value = "事件唯一标识")
    private String ei = "qqMapApiApply";

    @ApiModelProperty(value = "事件名称")
    private String en = "腾讯地址api调用";

    @ApiModelProperty(value = "uuid")
    private String u = "81239106-47ff-8080-1600-38378798fdaa";

    @ApiModelProperty(value = "系统版本")
    private String os = "stock";

    @ApiModelProperty(value = "应用版本")
    private String av;

    @ApiModelProperty(value = "平台 1 ios 2 安卓 3 M端 4小程序 5PC")
    private String pt = "5";

    @ApiModelProperty(value = "载体app/browser")
    private String b;

    @ApiModelProperty(value = "city_id")
    private String c;

    @ApiModelProperty(value = "门店ID")
    private String s;

    @ApiModelProperty(value = "设备名称  web传分辨率")
    private String d = "";


    @ApiModelProperty(value = " 用户 ID")
    private String rd;


    @ApiModelProperty(value = "毫秒级时间戳")
    private String v = String.valueOf(System.currentTimeMillis());


    @ApiModelProperty(value = "gps")
    private String g;


    @ApiModelProperty(value = "saas_tenant")
    private String st = "";


    @ApiModelProperty(value = "xtenant")
    private String xt = "";


    @ApiModelProperty(value = "网络状态  4G 5G Wired有线")
    private String n;


    @ApiModelProperty(value = "1-九机+九讯云(大中型)商城， 2-九机+九讯云(大中型)OA，3-九讯云NEO（小型）商城，4-九讯云NEO（小型）OA， 5-拍靓机，6-九讯云官网")
    private String pf= "2";

    @ApiModelProperty(value = "神策SDK搜集的数据")
    private String sp;

    @ApiModelProperty(value = "APP生命周期的唯一标志")
    private String si;


}
