package com.jiuji.oa.afterservice.bigpro.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.bigpro.bo.servicerecord.StopShouhouInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.servicerecord.UseServiceRecordBo;
import com.jiuji.oa.afterservice.bigpro.po.BbsxpUsers;
import com.jiuji.oa.afterservice.bigpro.po.ServiceRecord;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Mapper
public interface ServiceRecordMapper extends BaseMapper<ServiceRecord> {

    /**
     * 获取订单服务信息
     * @param imeis
     * @return
     */
    ServiceInfoVO getSub(@Param("imeis") List<String> imeis);

    /**
     *  是否购买过 Care+
     * @param userId
     * @return
     */
    Integer getCareId(@Param("imeis") List<String> imeis, @Param("userId") Integer userId);

    /**
     * 获取历史记录
     * @param imei 串号
     * @param userId 用户主键
     * @return
     */
    ServiceInfoVO getHistoryRecord(@Param("imei") String imei, @Param("userId") Integer userId);

    /**
     * 获取九机服务记录
     * @param basketId
     * @param imei
     * @param serversOutDtime
     * @return
     */
    List<ServiceRecord> list9jiServiceRecord(@Param("serviceTypes") Collection<Integer> serviceTypes, @Param("imei") String imei, @Param("serversOutDtime") LocalDateTime serversOutDtime);

    /**
     * 根据串号查询售后服务信息
     *
     * @param imei
     * @param shouhouServiceTypes
     * @param transactionDate
     * @return
     */
    List<ServiceRecord> listShouhouService(@Param("imei") String imei, @Param("shouhouServiceTypes") List<Integer> shouhouServiceTypes,
                                           @Param("transactionDate") LocalDateTime transactionDate);

    /**
     * 根据串号查询良品订单信息
     * @param imei
     * @param userId
     * @return
     */
    ServiceInfoVO getLpSub(@Param("imei") String imei, @Param("userId") Integer userId);

    /**
     * 获取售后id
     * @param imei
     * @param serviceType  售后服务类型
     * @param startTime
     * @param endTime
     * @return
     */
    StopShouhouInfoBo getStopShouhouInfo(@Param("imei") String imei, @Param("serviceType") Integer serviceType
            , @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据imei获取回收单的付款时间
     * @param imei
     * @return
     */
    LocalDateTime getRecoverSubPayTime(@Param("imei") String imei);

    /**
     * 获取会员信息
     * @param userId
     * @param mobile
     * @param xTenant
     * @return
     */
    BbsxpUsers getBbsxpUser(@Param("userId") Integer userId, @Param("mobile") String mobile, @Param("xTenant") Integer xTenant);

    Integer getFilmByImei(@Param("imei") String imei);

    Integer getYaDingRegisterState(@Param("serviceRecordId")Integer serviceRecordId);

    Integer getYaDingRegisterStateNew(@Param("serviceRecordId")Integer serviceRecordId);



    Integer getHuiJiBaoRegisterState(@Param("basketId")Integer basketId);

    Integer getHuiJiBaoRegisterStateNew(@Param("serviceRecordId")Integer serviceRecordId);

    /**
     * 获取出险的订单号
     * @param discountBasketId
     * @param businessType
     * @return
     */
    Integer getStopOrderId(@Param("discountBasketId") Integer discountBasketId, @Param("businessType") Integer businessType);

    /**
     * 获取出险的服务记录
     * @param shouhou
     * @param serviceTypes
     * @param isGaoJiOutService
     * @param outServiceImeis
     * @return
     */
    UseServiceRecordBo getTop1UseServiceRecord(@Param("shouhou") Shouhou shouhou, @Param("serviceTypes") List<Integer> serviceTypes,
                                               @Param("isGaoJiOutService") Boolean isGaoJiOutService,
                                               @Param("outServiceImeis") Set<String> outServiceImeis);

    /**
     * 售后服务出险
     * @param shouhou
     * @param serviceRecordId
     */
    int useServiceRecord(@Param("shouhou") Shouhou shouhou, @Param("serviceRecordId") Integer serviceRecordId);
}
