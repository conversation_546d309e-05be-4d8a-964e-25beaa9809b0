package com.jiuji.oa.afterservice.bigpro.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouRefundMapper;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouRefundService;
import com.jiuji.oa.afterservice.bigpro.vo.refund.TuihuanFormVo;
import com.jiuji.oa.afterservice.bigpro.vo.refund.TuihuanHistoryVo;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.constants.NumberConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 售后退款处理器
 * <AUTHOR>
 * @since 2021/10/25 16:37
 * 工单地址: https://pm.9ji.com/index.php?m=story&f=view&storyID=10430
 */
@Api(tags = "大件：售后退款")
@RestController
@RequestMapping("/api/bigpro/shouhou/refund")
public class ShouhouRefundController {
    @Autowired
    private ShouhouRefundService shouhouRefundService;

    /**
     * 退款详情页面
     * @param shouhouId
     * @return
     */
    @GetMapping("detail")
    @ApiOperation("退款详情页面")
    public R<ShouhouRefundDetailVo> detail(@RequestParam("shouhouId") Integer shouhouId){
        return shouhouRefundService.detail(shouhouId);
    }

    @PostMapping("sendCode")
    @ApiOperation("发送短信验证码")
    public R<Boolean> sendCode(@RequestParam("shouhouId") Integer shouhouId,
                               @RequestParam(value = "tuihuanId",required = false) Integer tuihuanId,
                               @RequestParam(required = false,value = "tuihuanKind",defaultValue = "5") Integer tuihuanKind){

        StringRedisTemplate strRedisTemplate = SpringUtil.getBean(StringRedisTemplate.class);
        String smsSendKey = String.format("shouhou_sendCode_%s_%s_%s", shouhouId, tuihuanId, tuihuanKind);
        if(StrUtil.isNotBlank(smsSendKey) && !Boolean.TRUE.equals(strRedisTemplate.opsForValue().setIfAbsent(smsSendKey,Boolean.TRUE.toString(), NumberConstant.ONE, TimeUnit.MINUTES))){
            return R.error("1分钟内只能发送一次");
        }

        R<Boolean> result = shouhouRefundService.sendCode(shouhouId,tuihuanId,tuihuanKind);
        if (!result.isSuccess()) {
            //解除一分钟发送一次的限制
            strRedisTemplate.delete(smsSendKey);
        }
        return result;
    }

    /**
     * 提交退款
     * @param tuihuanForm
     * @return
     */
    @PostMapping("save")
    @ApiOperation("提交退款")
    public R<Integer> save(@Valid @RequestBody TuihuanFormVo tuihuanForm){
        return shouhouRefundService.save(tuihuanForm);
    }

    /**
     * 退款详情页面
     * @param id
     * @return
     */
    @PostMapping("cancelRefund")
    @ApiOperation("撤销退款")
    public R<Integer> cancelRefund(@RequestParam("id") Integer id){
        return shouhouRefundService.cancelRefund(id);
    }

    /**
     * 提交审批
     * @param id
     * @return
     */
    @PostMapping("submitCheck")
    @ApiOperation("提交审批")
    public R<Integer> submitCheck(@RequestParam("id") Integer id,@RequestParam("processStatus") Integer processStatus,
                                  @RequestParam(value = "password2",required = false) String password2){
        ShouhouTuiHuanPo tuiHuan = SpringUtil.getBean(ShouhouRefundMapper.class).getHistory(id);
        return shouhouRefundService.submitCheck(tuiHuan, processStatus, password2);
    }

    /**
     * 退款详情页面
     * @param shouhouId
     * @param tuihuanKind
     * @return
     */
    @GetMapping("listHistoryBatch")
    @ApiOperation("获取退款批次")
    public R<List<TuihuanHistoryVo.TuihuanSimpleHistoryVo>> listHistoryBatch(@RequestParam("shouhouId") Integer shouhouId,
                                                                             @RequestParam(required = false,value = "tuihuanKind",defaultValue = "5") Integer tuihuanKind,
                                                                             @RequestParam(required = false,value = "tuiKinds") Integer tuiKinds
    ){
        return shouhouRefundService.listHistoryBatch(shouhouId,tuihuanKind, tuiKinds);
    }

    /**
     * 退款详情页面
     * @param id
     * @return
     */
    @GetMapping("getHistory")
    @ApiOperation("获取历史退款详情")
    public R<TuihuanHistoryVo> getHistory(@RequestParam("id") Integer id){
        return shouhouRefundService.getHistory(id);
    }

    /**
     * 获取原路径退款支付记录
     * @param shouhouId
     * @param refundWay
     * @return
     */
    @GetMapping("listPayRecord")
    @ApiOperation("获取原路径退款支付记录")
    public R<List<ShouhouRefundDetailVo.PayRecordVo>> listPayRecord(@RequestParam("shouhouId") Integer shouhouId,
                                                                    @RequestParam("refundWay") String refundWay,
                                                                    @RequestParam(value = "shouyinType",defaultValue = "2",required = false) Integer shouyinType){
        return shouhouRefundService.listPayRecord(shouhouId,refundWay, shouyinType);
    }

    /**
     * 根据订单获取
     * 获取退款方式接口列表
     */
    @GetMapping("listRefundWay")
    @ApiOperation("获取退款方式列表")
    public R<List<String>> listRefundWay(@ApiParam("订单id") @RequestParam("subId") Integer subId,
                                         @ApiParam("订单类型 ") @RequestParam("subType") Integer subType){
        return shouhouRefundService.listRefundWay(subId,subType);
    }
}
