package com.jiuji.oa.afterservice.bigpro.service;

import cn.hutool.core.lang.Dict;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.cloud.after.enums.ProductTypeEnum;
import com.jiuji.cloud.after.vo.req.CutScreenShouhouReq;
import com.jiuji.cloud.after.vo.req.PjtShouhouReq;
import com.jiuji.cloud.after.vo.res.PjtShouhouRes;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.enums.BigShouhouOrderTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.ShouHouPpidDict;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.vo.RepairOrderLogVO;
import com.jiuji.oa.afterservice.bigpro.vo.UpdatePriceVO;
import com.jiuji.oa.afterservice.bigpro.vo.req.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.vo.PageVo;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRepairInfoRes;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
public interface ShouhouService extends IService<Shouhou> {


    /**
     * 是否可以负库存出库
     * @param ppid
     * @return
     */
     Boolean isNegativeInventory(Integer ppid);

     R<Shouhou> createShouHou(ShouhouReq sh, OaUserBO oaUserBO);
    /**
     * 更新imei
     * 这个方法不能开事务，服务查询会切换不成功
     * @param req
     * @return
     */
    R<UpdateShouHouImeiRes> updateShouHouImei(UpdateShouHouImeiReq req);

    R<Boolean> updatePrice(UpdatePriceVO updatePrice);

    void sendSubCollectionMessage(Integer ch999Id,String link,String message);

        /**
         * 手动出现
         * @param shouhouId
         */
    /**
     * 手动出现
     * @param shouhouId
     */
    void qujiUseServiceRecord(Integer shouhouId);
    /**
     * 返修单日志
     * @param repairOrderLogVO
     */
    void addRepairOrderLog(RepairOrderLogVO repairOrderLogVO);
    /**
     * 获取客户维修记录
     *
     * @param page
     * @param userId
     * @return
     */
    IPage<WxRecordBo> getWxRecord(Page<WxRecordBo> page, Integer userId);

    /**
     * 获取硬件历史记录
     * @param imei
     * @return
     */
    List<HardwareHistoryRecordBo> getHardwareHistoryRecords(String imei);

    /**
     * 获取预约配件
     * @param yuyuePeijianAddReq
     * @return
     */
    R<List<ShouhouYuyuePeijianInfoRes>> getPeijian(YuyuePeijianAddReq yuyuePeijianAddReq);


    /**
     * 优品商品连接
     * @param req
     * @return
     */
    SuperiorProductsLinkRes getSuperiorProductsLink(SuperiorProductsLinkReq req);

    /**
     * 串号查询
     * @param imei
     * @param limit
     * @param saveSearchLog
     */
    List<ImeiQueryRes> imeiQuery(String imei, Integer limit, Boolean saveSearchLog);

    /**
     * 获取维修门店库存数量
     * @param ppid
     * @param areaId
     * @return
     */
    Integer getWxkcNumber(int ppid, int areaId);

    /**
     * 锁定维修配件
     * @param  type 1锁定配件，2解除锁定
     * @return
     */
    R<Boolean> lockWxPeijian(Integer yyId,Integer ppid,Integer areaId,Integer type);

    /**
     * 删除预约单后更新售后单
     * @param yyid
     * @return
     */
    Boolean delYuyueUpdateShouhou(Integer yyid);

    /**
     * 获取自增id
     * @return
     */
    @Cached(name = "com.jiuji.oa.afterservice.bigpro.service.getOrderIdsh",expire = 20,timeUnit = TimeUnit.MINUTES)
    String getOrderIdsh();

    /**
     * 通过串号获取售后信息
     * @param imei
     * @return
     */
    Shouhou getShouhouInfoByImei(String imei);

    /**
     * 更改维修单提成状态
     * @param id
     * @param isSoft
     * @return
     */
    Boolean updateIsticheng(Integer id,Boolean isSoft);

    /**
     * 保存售后单
     * @param sh
     * @return
     */
    R<Boolean> saveShouhou(ShouhouReq sh);


    /**
     * 售后维修列表查询
     * @param param
     * @return
     */
    PageVo getShouhouPage(ShouhouListReq param);

    /**
     * 获取modidate
     * @param shouhouId
     * @return
     */
    LocalDateTime getModidate(Integer shouhouId);

    /**
     * 保修信息
     * @param imei
     * @param saveSearch
     * @return
     */
    BaoxiuAndBuyBo getServiceInfo(String imei, Boolean saveSearch);

    /**
     * 保修信息 V2
     * @param imei
     * @param saveSearch
     * @return
     */
    R<String> getServiceInfoV2(String imei, Boolean saveSearch);

    /**
     * 第三方串号查询 只有登录后用户可用
     * @param imei
     * @return
     */
    ImeiQueryInfoBo imeiQueryInfo(String imei);

    /**
     * 不限制用户 没有用户信息也可以使用
     * @param imei
     * @return
     */
    ImeiQueryInfoBo getImeiQueryInfoNotLimitUser(String imei);

    /**
     * 获取售后信息
     * @param shouhouId
     * @return
     */
    R<ShouhouInfoRes> getShouhouInfo(Integer shouhouId);

    @NotNull
    BigShouhouOrderTypeEnum getOrderType(Integer shouHouId);

    /**
     * 获取大件的商品类型
     * @param shouhou
     * @return
     */
    ProductTypeEnum getProductTypeEnum(Shouhou shouhou);

    /**
     * 获取企粉状态
     *
     * @param userId 用户id
     * @return 是否加粉
     */
    boolean getCorporateFans(Integer userId);

    /**
     * 获取九机服务
     * @param shouhouId 售后id
     * @return
     */
    R<JiuJiFuWuChuXianRes> getJiuJiFuWu(Integer shouhouId);

    /**
     * 办理九机服务出险
     * @param req
     * @return
     */
    R<Boolean> saveOrUpdateJiuJiFuWu(JiuJiFuWuChuXianReq req);

    /**
     * 批量保存售后单
     * @param shList
     * @return
     */
    Boolean saveBatchShouhou(List<ShouhouReq> shList);

    /**
     * 倒计时修改
     * @param shouhouId
     * @param day
     * @return
     */
    R<Boolean> updateDaojishi(Integer shouhouId,Integer day);

    /**
     * 取机队列
     * @param shouhouId
     */
    R<Boolean> nahou(Integer shouhouId);

    /**
     * 售后优惠码发送
     * @param shouhouId
     * @return
     */
    R<Boolean> sendNumberCard(Integer shouhouId);

    /**
     * 售后消息推送
     * @param pushmsg
     * @param isRecordLog
     * @return
     */
    R<Boolean> pushMessage(ShouhouMsgPushMessageBo pushmsg,Boolean isRecordLog);

    /**
     * 获取预计送达时间
     * @param areaId
     * @param toAreaId
     * @param timeFrom
     * @return
     */
    String getReachTime(int areaId, int toAreaId, LocalDateTime timeFrom);

    /**
     * 获取维修单地区
     * @param shouhouId
     * @return
     */
    ShouhouAreaIdBo getShouhouAreaIdNew(Integer shouhouId);

    /**
     * 获取接近时间
     * @param shouhouId
     * @return
     */
    LocalDateTime getJiejianTime(Integer shouhouId);
    /**
     * 邮寄预约地址发送
     * @param yuyueId
     * @param smsMobile 发短信电话
     * @param mobile 收件人电话
     * @param addr 预约地址
     * @param rUserName
     * @param addressCityId
     * @return
     */
    R<Boolean> sendAddressInfo(Integer yuyueId, String smsMobile, String mobile, String addr, String rUserName, Integer addressCityId);

    /**
     * 添加维修费,并自动增加自动出库的配件
     * @param m
     * @param autoHuishou
     * @param isLockKc
     * @return
     */
    R<ShouhouCostPriceRes> addCostPriceWithAutoBind(WxFeeBo m, Boolean autoHuishou, Boolean isLockKc);

    /**
     *添加维修费
     * @param m
     * @param autoHuishou
     * @param isLockKc
     * @return
     */
    R<ShouhouCostPriceRes> addCostPrice(WxFeeBo m,Boolean autoHuishou,Boolean isLockKc);


    /**
     * 配件出库操作
     * @return
     */
    R<Boolean> pjck(WxFeeBo m, Boolean autoHuishou, Boolean isLockKc,Shouhou shouhou,Boolean priceChangeFlag,Boolean shouhouServicesFlag,List<ShouHouPpidDict> ppidDictList, BigDecimal pjPrice);

    /**
     * 保存售后日志
     * @param shouhouId
     * @param comment
     * @param inUser
     * @param type
     * @param isweb
     */
    void saveShouhouLog(Integer shouhouId,String comment,String inUser,Integer type,Boolean isweb);


    /**
     * 保存售后日志
     * @param shouhouId
     * @param comment
     * @param inUser
     * @param type
     * @param isweb
     */
    void saveShouhouLog(Integer shouhouId,String comment,String inUser,Integer type,Boolean isweb,Integer templateId);

    /**
     * 保存售后日志
     * @param shouhouId
     * @param comment
     * @param inUser
     */
    void saveShouhouLog(Integer shouhouId,String comment,String inUser);

    /**
     * //附带出库其他配件逻辑
     * @param wxFeeBo
     * @return
     */
    R<Boolean> addOtherCostPriceByPPriceid(WxFeeBo wxFeeBo,List<ShouHouPpidDict> ppidDictList);

    /**
     * 推送处理确认可视进程
     * @param shouhouId
     * @param type
     * @return
     */
    R<Boolean> setProcessConfirmLog(Integer shouhouId, Integer type);

    /**
     * 更新电话跟进时间
     * @param shouhouId
     * @return
     */
    R<Boolean> updateTelTime(Integer shouhouId);

    /**
     * 售后添加批签
     * @param shouhouId
     * @param orderId
     * @return
     */
    R<Boolean> addPqDan(Integer shouhouId,String orderId);


    /**
     * 获取售后费用
     * @param wxId
     * @param shouhouServicesFlag
     * @return
     */
    ShouhouFeiyongBo getShouhouFeiyong(Integer wxId,Boolean shouhouServicesFlag);


    /**
     * 退换显示
     * @param id
     */
    ShouhouTuihuanRes huan(Integer id);

    /**
     * 更新售后费用
     * @param price
     * @param shouhouId
     * @return
     */
    Integer updateFeiYongByPriceAndId(BigDecimal price,Integer shouhouId);

    /**
     * 获取微信红包金额
     * @param baksetId
     * @param imei
     * @return
     */
    BigDecimal getWeiXinHongbao(Integer baksetId, String imei);

    /**
     * 根据PID和维修类型获取维修周期
     * @param ppriceid
     * @return
     */
    List<ShouhouDaoJiShiBo> getShouhouTimeByPid(Integer ppriceid);

    /**
     * 获取售后日志
     * @param shouhouId
     * @return
     */
    List<ShouhouLogBo> getShouhouLogs(Integer shouhouId);

    /**
     * 售后维修进度添加
     * @param req
     * @return
     */
    R<Boolean> addShouHouLog(ShouhouLogReq req);

    /**
     * 判断当前地区是否昆明市
     * @param areaId
     * @return
     */
    Boolean isKunMingShi(Integer areaId);

    ShouhouBo getOne(Integer shouhouId);

    /**
     * 根据售后id获取区域id
     * @param shouhouId
     * @return
     */
    Integer getAreaIdByShouhouId(Integer shouhouId);

    /**
     * 重大办
     * @param shouhouId
     * @return
     */
    ShouHouImportantBo isSubCollectZdb(Integer shouhouId);

    /**
     * 订单是否已关注
     * @param subId
     * @param ch999Id
     * @param collectType  订单=1,维修=2,客户=3
     * @return
     */
    boolean isSubCollect(Integer subId,Integer ch999Id,Integer collectType);

    List<String> searchSubCollect(Integer subId, Integer collectType);
        /**
         * 订单 关注 、 取消关注操作
         * @param subId
         * @param type 0 关注  1 取消
         * @return
         */
    R<String> subCollectDeal(Integer subId,Integer type,Integer subCollectKind);

    /**
     *检测退款方式默认
     * @param id
     * @param type
     * @return
     */
    R<String> checkReturnType(long id, int type);

    /**
     * 根据支付方式获取退款方式
     * @return
     */
    String getReturnTypeByPayWay(String inuser,Integer areakind1);

    /**
     * 取消盘点
     * @param wxId
     * @return
     */
    R<Boolean>  pandianCancel(Integer wxId);

    /**
     * 预约接件确认
     * @param shouhouId
     * @return
     */
    R<String> yuyueCheck(Integer shouhouId);

    /**
     * 发送电子凭证
     * @param shouhouId
     * @param userId
     * @param mobile
     * @return
     */
    R<String>sendWxPz(Integer shouhouId, Integer userId, String mobile);

    /**
     * 取机通知发送，添加可视进程
     * @param wxId
     * @param msg
     * @return
     */
    R<String>qujiSendmsg(Integer wxId,  String msg);

    /**
     * 获取AreaSubject
     * @param nowArea
     * @return
     */
    AreaInfo getAreaSubject(Integer nowArea);

    /**
     * 保存中邮真实送修信息
     * @param shouhouId
     * @param mobile
     * @param username
     * @return
     */
    R<String>saveZySxBusinessInfo(Integer shouhouId,String mobile,String username);

    /**
     * 保存修改售后维修单
     * @param req
     * @return
     */
    R<Boolean>saveOrUpdateShouhouInfo(ShouhouInfoRes req);

    /**
     * 是否是进水保
     * @param shouhouId
     * @param serviceType
     * @return
     */
    Boolean hasJinshuibao(Integer shouhouId, Integer serviceType);

    /**
     * 更新售后预约单信息
     * @param req
     * @return
     */
    R<Boolean> updateShouhouInfo( ShouhouInfoReq req);


    /**
     * 维修配件改价
     * @param req
     * @return
     */
    R<Boolean> editPrice(WxPjEditPriceReq req);


    /**
     * 备注改价
     * @param req
     * @return
     */
    R<Boolean> remarkAndUpdatePrice(RemarkAndUpdatePriceReq req);
    /**
     * 維修配件改价，内部方法调用
     * @param sh
     * @param kcOutId
     * @param kcPrice
     * @param kcInprice
     * @param kcPriceGs
     * @param myRank
     * @param user
     * @param areaKind1
     * @param showKind
     * @param updateWrapper
     * @param shLogs
     * @param isJiujiServiceFlag
     * @throws Exception
     */
    R<Boolean> editPrice(ShouhouDtBo sh, List<Integer> kcOutId,
                         List<BigDecimal> kcPrice, List<BigDecimal> kcInprice, List<BigDecimal> kcPriceGs, List<String> myRank,
                         String user, Integer areaKind1, String showKind, LambdaUpdateWrapper<Shouhou> updateWrapper,
                         List<ShouhouLogNewBo> shLogs, boolean isJiujiServiceFlag);

    /**
     * 更新优惠码使用信息
     * @param youhuiMa
     * @param youhuimaTotal
     * @return
     */
    Integer updateYouhuiMaUseInfo(Integer shouhouId,String youhuiMa,BigDecimal youhuimaTotal);

    /**
     * 售后信息（退换显示）
     * @param shouhouId
     * @return
     */
    ShouhouHuanBo getShouhouHuan(Integer shouhouId);

    /**
     * 根据售后Id获取购买地
     * @param shouhouId
     * @return
     */
    Integer getBuyAreaIdByShouhouId(Integer shouhouId);

    /**
     * 查询九机会员是否有修手机记录
     * @param userIds
     * @return
     */
    List<Long> getShouhouRepaireUserInfo(List<Long> userIds);

    /**
     * 主站使用，根据串号获取维修信息
     * @param imei
     * @return
     */
    ShouhouRepairInfoRes getRepairRecordByImei(String imei);


    /**
     * 解锁预留配件
     * @param yyid
     */
    void unlockWxpjByYyid(Integer yyid);

    /**
     * 根据串号获取商品的不再保修信息
     * @param imei
     * @param islp
     * @return
     */
    Shouhou getNotBaoxiu(String imei, boolean islp,LocalDateTime startTime,LocalDateTime endTime);

    /**
     * 获取存在的售后信息
     * @param wxId
     * @param xTenant
     * @param areaId
     * @param hasAuthPart
     * @param authorizeId
     * @return
     */
    Shouhou getShouhouNotDel(Integer wxId, Integer xTenant, Integer areaId, boolean hasAuthPart, Integer authorizeId);

    /**
     * 获取绑定的配件信息
     * @param ppid
     * @param xtenant
     * @return
     */
    List<BindPpidKcInfo> getBindPpidKcInfo(Integer ppid, Integer xtenant);

    /**
     * 添加绑定配件信息
     * @param wxFeeBo
     * @param autoOutPut
     * @param isKcLock
     * @return
     */
    R<List<ShouhouCostPriceRes>> addBindCostPrice(WxFeeBo wxFeeBo, boolean autoOutPut, Boolean isKcLock,boolean isAnyErrorRollBack);

    /**
     * 批量获取售后日志
     * @param shouhouIds
     * @param type
     * @return
     */
    Map<Integer,List<ShouhouLogBo>> listShouhouLogs(List<Integer> shouhouIds, Integer type);

    /**
     * 无理由退货检测
     * @param pIds
     * @return
     */
    R<Dict> checkNoReasonReturn(List<Integer> pIds);

    /**
     * 是否为无理由退货
     * @param pIds
     * @return
     */
    boolean isNoReason(List<Integer> pIds);

    /**
     * 统计订单之后设备历史售后次数
     * @param imei 串号
     * @param userId 用户id
     * @param tradeDate 订单交易时间
     * @return
     */
    Integer countShouhouTimes(String imei, Integer userId, LocalDateTime tradeDate);


    Boolean handlePjck(WxFeeBo m, Boolean isLockKc, Shouhou shouhou);

    /**
     * 换货时设置维修费用为0
     * @param req req
     * @return
     */
    String editPriceByIshuanhuo(ShouhouHuiShouReq req);



    /**
     * 售后取机方法
     * @param qujiReq
     * @return
     */
    R quji(QujiReq qujiReq);

    void qujiYouhuiMaAllocation(Integer wxId, Shouhou shouhou);

    /**
     * 保存售后服务记录
     * @param qujiReq
     * @param shouhou
     */
    void saveShouhouServiceRecord(QujiReq qujiReq, Shouhou shouhou);

    Integer getFromsourceById(Integer id);


    /**
     * 拍机堂一键售后
     */
    PjtShouhouRes addPjtShouhou(PjtShouhouReq req);

    /**
     * 取机售后服务补偿出险,有部分在c#出险
     * 服务迭代太快会忘记同步,这里做个补偿
     * @param id
     * @param shouhou
     */
    void qujiUseServiceRecord(Integer id, Shouhou shouhou);

    /**
     * 配件发优惠券
     * @param id
     * @param shouhou
     */
    void sendCouponsParts(Shouhou shouhou);

    /**
     * 获取抖音团购最后一次使用记录
     * @param shouhouId
     * @param subKinds
     * @return
     */
    DouYinCouponLogRes getLastDouYinCouponLog(Integer shouhouId, Integer subKinds);

    Boolean addCutScreenReqLogShouhou(CutScreenShouhouReq req);

    DouYinCouponLogRes getLastDouYinCouponLogByShouYingId(Integer shouyingId);
}
