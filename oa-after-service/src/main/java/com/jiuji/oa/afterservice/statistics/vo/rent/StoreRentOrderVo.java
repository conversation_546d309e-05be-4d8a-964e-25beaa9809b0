package com.jiuji.oa.afterservice.statistics.vo.rent;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2021/6/17 20:30
 */
@Getter
@Setter
@ToString
@ApiModel("门店租金订单查询Vo")
public class StoreRentOrderVo {
    /**支付宝付款*/
    @ApiModelProperty("支付宝付款")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal alipayPayment;
    /**已付金额*/
    @ApiModelProperty("已付金额")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal amountPaid;
    /**现金付款*/
    @ApiModelProperty("现金付款")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal cashPayment;
    /**
     * 订单总价
     */
    @ApiModelProperty("订单总价")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal totalPrice;
    /**
     * 代金券付款
     */
    @ApiModelProperty("代金券付款")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal voucherPayment;
    /**
     * 微信付款
     */
    @ApiModelProperty("微信付款")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal wechatPayment;
    /**
     * 其他付款方式
     */
    @ApiModelProperty("其他付款方式")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal otherPayment;

    /**商品数量*/
    @ApiModelProperty("商品数量")
    private Integer commodityCount;
    /**商品Id*/
    @ApiModelProperty("商品Id")
    private Integer commodityId;
    /**商品名*/
    @ApiModelProperty("商品名")
    private String commodityName;
    /**商品名*/
    @ApiModelProperty("价格（定价）")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal commodityPrice;
    /**
     * 商品类型 0：大件，1：小件，2：维修
     */
    @ApiModelProperty("商品类型 0：大件，1：小件，2：维修")
    private Integer type;
    /**单号*/
    @ApiModelProperty("单号")
    private Integer orderId;
    /**
     * 订单类型 1：订单，2：维修
     */
    @ApiModelProperty("订单类型 1：订单，2：维修")
    private Integer orderType;
    /**付款时间*/
    @ApiModelProperty("付款时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paymentTime;
    /**
     * POS机刷卡付款
     */
    @ApiModelProperty("POS机刷卡付款")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal posCardPayment;
    /**销售人名*/
    @ApiModelProperty("销售人名")
    private String salesmenName;
    /**状态*/
    @ApiModelProperty("状态")
    private String status;
    /**门店Id*/
    @ApiModelProperty("门店Id")
    private Integer storeId;
    /**门店编号*/
    @ApiModelProperty("门店编号")
    private String storeNo;

    /**
     * ‘pos机刷卡’& POS机的类型为‘商场专用’ 标识
     */
    @ApiModelProperty("商场pos机标识")
    private Boolean marketPosFlag;

    /**
     * 代金券支付&代金券活动类型为‘商场活动’ 标识
     */
    @ApiModelProperty("商场代金券标识")
    private Boolean marketVoucherFlag;

    /**
     * 商场小票机打印标识
     */
    @ApiModelProperty("商场小票机打印标识")
    private Boolean isMarketPrint;

    /**
     * <AUTHOR>
     * @since 2021/6/17 20:30
     */
    @Getter
    @Setter
    @ToString
    @ApiModel("门店租金订单查询query")
    public static class OrderQuery{
        /**
         * 单号
         */
        @ApiModelProperty("单号")
        @NotNull(message = "单号不能为空")
        private Integer orderId;
        /**
         * 订单类型 1：订单，2：维修
         */
        @ApiModelProperty("订单类型 1：订单，2：维修")
        @NotNull(message = "订单类型不能为空")
        private Integer orderType;
    }
}
