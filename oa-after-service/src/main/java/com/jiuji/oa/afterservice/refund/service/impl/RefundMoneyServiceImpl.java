package com.jiuji.oa.afterservice.refund.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.jiuji.oa.afterservice.apollo.ApolloEntity;
import com.jiuji.oa.afterservice.bigpro.bo.OpenIdInfoBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouRefundMapper;
import com.jiuji.oa.afterservice.bigpro.enums.SubSubTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.SubTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouRefundService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.tuihuan.TuiHuanService;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.*;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeV1Enum;
import com.jiuji.oa.afterservice.common.enums.RankEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.csharp.returns.service.CsharpReturnService;
import com.jiuji.oa.afterservice.csharp.returns.vo.ReturnWayReqVo;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import com.jiuji.oa.afterservice.refund.bo.DetailParamBo;
import com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo;
import com.jiuji.oa.afterservice.refund.dao.ShouhouRefundMoneyMapper;
import com.jiuji.oa.afterservice.refund.enums.BankTransferStatus;
import com.jiuji.oa.afterservice.refund.enums.ThirdRefundTypeEnum;
import com.jiuji.oa.afterservice.refund.enums.TuiGroupEnum;
import com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanDetailPo;
import com.jiuji.oa.afterservice.refund.service.*;
import com.jiuji.oa.afterservice.refund.service.kind.BaseTuiHuanKindService;
import com.jiuji.oa.afterservice.refund.service.way.CardPayOriginWayService;
import com.jiuji.oa.afterservice.refund.service.way.NetPayOriginWayService;
import com.jiuji.oa.afterservice.refund.service.way.OtherRefundService;
import com.jiuji.oa.afterservice.refund.service.way.ThirdOriginWayService;
import com.jiuji.oa.afterservice.refund.utils.RefundMoneyUtil;
import com.jiuji.oa.afterservice.refund.vo.req.AlipayInfoReqVo;
import com.jiuji.oa.afterservice.refund.vo.req.CardPayReqVo;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.req.RefundValidVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundWayDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiHuanCheckVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.Refund;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.sub.dao.SubMapper;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sys.enums.ValidMemberType;
import com.jiuji.oa.afterservice.sys.service.BaseValidMemberService;
import com.jiuji.oa.afterservice.sys.service.Password2ValidService;
import com.jiuji.oa.afterservice.sys.vo.req.ValidReq;
import com.jiuji.oa.finance.BeDirectPayConfigCloud;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.oacore.csharp.cloud.CsharpOaWcfCloud;
import com.jiuji.oa.oacore.csharp.vo.res.BankPaymentRes;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import com.jiuji.tc.utils.enums.xtenant.ShortXtenantEnum;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 退款入口服务类
 * <AUTHOR>
 * @since 2022/7/19 16:00
 */
@Service
@Slf4j
public class RefundMoneyServiceImpl implements RefundMoneyService {
    @Resource
    @Lazy
    private RefundMoneyService refundMoneyService;
    @Resource
    private RefundCommonService refundCommonService;
    @Resource
    private CsharpReturnService csharpReturnService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private ShouhouRefundMoneyMapper shouhouRefundMoneyMapper;
    @Resource
    private ApolloEntity apolloEntity;
    @Resource
    private ShouhouTuihuanService shouhouTuihuanService;
    @Resource
    private SmsService smsService;
    @Resource
    private SubMapper subMapper;
    @Resource
    private SmallproMapper smallproMapper;
    @Resource
    private BeDirectPayConfigCloud beDirectPayConfigCloud;

    @Resource
    private SysConfigClient sysConfigClient;

    /**
     * 获取退款详情信息
     * @param detailParamBo
     * @param subInfoMaxRefundPriceFun
     */
    @Override
    public R<RefundMoneyDetailVo> detail(DetailParamBo detailParamBo, Function<DetailParamBo, Optional<RefundSubInfoBo>> subInfoMaxRefundPriceFun){
        R<RefundMoneyDetailVo> checkR = checkDetail(detailParamBo);
        if (!checkR.isSuccess()) {
            return checkR;
        }
        //集合都设置默认值为空集合
        detailParamBo.setTuihuanDetailPos(Collections.emptyList());
        // 最大的可退金额由调用方提供 当前退款金额,当前退款金额通过后面的责任链进行计算
        RefundMoneyDetailVo detailVo = new RefundMoneyDetailVo().setMaxRefundPrice(BigDecimal.ZERO).setRefundPrice(BigDecimal.ZERO)
                .setYingfuM(BigDecimal.ZERO).setYifuM(BigDecimal.ZERO).setRefundWayDetails(new LinkedList<>())
                .setRefundWays(Collections.emptyList()).setNotes(new LinkedList<>());
        R<RefundMoneyDetailVo> result = R.success(detailVo);
        //获取最近一次的退款记录
        ShouhouTuiHuanPo tuiHuanPo = refundMoneyService.getRefund(detailParamBo.getTuihuanId(), detailParamBo.getOrderId(), detailParamBo.getTuihuanKindEnum());
        detailParamBo.setTuiHuanPo(tuiHuanPo);
        //校验信息
        if(ObjectUtil.defaultIfNull(detailParamBo.getTuihuanId(),0)>0 && Objects.isNull(tuiHuanPo)){
            return R.error("退款编号错误");
        }
        //相同退款类型的订单,已经存在非组合退款,进行提示,只能跳转到进行非组合退款
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        if(tuiHuanPo == null && !refundMoneyService.enable(detailParamBo.getOrderId(),Collections.singletonList(detailParamBo.getTuihuanKindEnum().getCode()),
                oaUser.getAreaId())){
            return R.error(ResultCode.NO_DATA, StrUtil.format("{}存在普通退款,跳转原页面进行处理",detailParamBo.getTuihuanKindEnum().getMessage()));
        }
        //设置最大可退金额
        subInfoMaxRefundPriceFun.apply(detailParamBo)
                .ifPresent(subInfoBo -> {
                    detailVo.setYifuM(subInfoBo.getYifuM());
                    detailVo.setYingfuM(subInfoBo.getYingfuM());
                    detailVo.setMaxRefundPrice(subInfoBo.getMaxRefundPrice());
                    //设置退款的订单信息
                    detailParamBo.setRefundSubInfo(subInfoBo);
                    exchangeDetailTuiHuanKind(detailParamBo, tuiHuanPo, subInfoBo,null);
                });
        if(tuiHuanPo == null){
            setNewDetail(detailParamBo, detailVo);
        }else if (Objects.equals(ObjectUtil.defaultIfNull(tuiHuanPo.getTuiKinds(),0), ShouhouTuiHuanPo.TuiKindsEnum.NORMAL_TUI.getCode())){
            return R.error(ResultCode.NO_DATA,"只支持组合退款的详情展示,跳转原页面进行处理");
        }else{
            tuiHuanNotNullSetDetailParam(detailParamBo, detailVo, tuiHuanPo);
        }
        //设置审批信息
        setProcessAndCheckInfo(detailVo,tuiHuanPo,detailParamBo);
        //数据流处理责任链,处理完后由方法,交由下一个服务类进行处理
        RefundMoneyService.forEachRefundInvoke(refundService -> refundService.doDetailChain(detailParamBo,result));
        //数据流处理责任链,处理完后由方法,交由下一个服务类进行处理
        BaseTuiHuanKindService.getBean(detailParamBo.getTuihuanKindEnum()).doDetailChain(detailParamBo,result);
        //数据排序
        if(result.getData() != null && CollUtil.isNotEmpty(result.getData().getRefundWayDetails())){
            result.getData().getRefundWayDetails().sort(Comparator.comparing(rwd -> rwd.getGroupEnum().getSort()));
        }
        if(tuiHuanPo == null){
            //退款为0元默认数据处理
            setGroupRefundZeroDefaultRefundWay(detailVo,detailParamBo.getTuihuanKindEnum());
        }
        //设置nodes
        addNodes(detailVo, detailParamBo);
        //恢复良品的tuihuanKind
        exchangeDetailTuiHuanKind(detailParamBo,tuiHuanPo,detailParamBo.getRefundSubInfo(),kindEnum -> detailVo.setTuihuanKind(kindEnum.getCode()));
        return result.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    /**
     * 天机注意事项
     * @param detailVo
     * @param detailParamBo
     */
    private void addNodes(RefundMoneyDetailVo detailVo, DetailParamBo detailParamBo) {
        if(XtenantEnum.isJiujiXtenant()){
            detailVo.getNotes().add(getAllShouYingDescription(detailParamBo.getRefundSubInfo().getOrderId(), detailParamBo.getTuihuanKindEnum()));
            detailVo.getNotes().addAll(SpringUtil.getBean(RefundMachineService.class).getHeedInfo(detailVo));
            String subNote = getSubNotes(detailParamBo.getOrderId());
            if(StringUtils.isNotBlank(subNote)){
                detailVo.getNotes().add(subNote);
            }
        }
    }

    private String getSubNotes(Integer orderId){
        if(null == orderId){
            return null;
        }
        Smallpro smallpro = smallproMapper.selectById(orderId);
        if(null == smallpro){
            return null;
        }
        if(null == smallpro.getSubId()){
            return null;
        }
        Sub sub = subMapper.selectById(smallpro.getSubId());
        if(null == sub){
            return null;
        }
        String note = null;
        // 美团订单提示文案
        if(SubSubTypeEnum.MEI_TUAN_SHAN_GOU.getCode().equals(sub.getSubtype())){
            note = "* 注意：引导客户美团平台退款申请，平台申请售后时效25天内，(超25天订单未处理，只能走财务批签退款)";
        }
        if(SubSubTypeEnum.JING_DONG_DAO_JIA.getCode().equals(sub.getSubtype())){
            note = "* 注意：引导客户京东平台退款申请，平台申请售后时效30天内，(超30天订单未处理，只能走财务批签退款)";
        }
        if(SubSubTypeEnum.DOU_YIN_DING_DAN.getCode().equals(sub.getSubtype())){
            note = "* 注意：引导客户抖音平台退款申请，平台申请售后时效7天内【超7天订单平台无法申请退款，门店在售后退款时可选择微信支付宝等方式退款】";
        }
        return note;
    }
    @Override
    public String getAllShouYingDescription(Integer orderId, TuihuanKindEnum tuihuanKindEnum) {
        // 添加原订单收银记录
        StringJoiner shouYingMsgJoiner = new StringJoiner("+", "* 原订单支付信息：", "");
        List<Refund> allShouYingList = listAllShouYing(orderId, tuihuanKindEnum);
        if(allShouYingList.isEmpty()){
            shouYingMsgJoiner.add("无");
        }else {
            allShouYingList.stream()
                    .map(refund -> StrUtil.format("{}({})", refund.getReturnWayName(),
                            NumberUtil.decimalFormat(",##0.00",refund.getActualPayPrice())))
                    .forEach(shouYingMsgJoiner::add);
        }
        return shouYingMsgJoiner.toString();
    }

    private void exchangeDetailTuiHuanKind(DetailParamBo detailParamBo, ShouhouTuiHuanPo tuiHuanPo, RefundSubInfoBo subInfoBo, Consumer<TuihuanKindEnum> consumer) {
        TuihuanKindEnum tuihuanKindEnum = detailParamBo.getTuihuanKindEnum();
        if(detailParamBo.getTuihuanKindEnum() == null && tuiHuanPo != null){
            tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuiHuanPo.getTuihuanKind());
        }
        tuihuanKindEnum = exchangeLpTuiHuanKind(subInfoBo,tuihuanKindEnum,consumer);
        if (tuihuanKindEnum != null) {
            detailParamBo.setTuihuanKindEnum(tuihuanKindEnum);
        }
        if(tuihuanKindEnum != null && tuiHuanPo != null){
            tuiHuanPo.setTuihuanKind(tuihuanKindEnum.getCode());
        }
    }

    /**
     * 切换良品的退款类型
     * @param subInfoBo
     * @param tuihuanKindEnumParam
     */
    private TuihuanKindEnum exchangeLpTuiHuanKind(RefundSubInfoBo subInfoBo, TuihuanKindEnum tuihuanKindEnumParam, Consumer<TuihuanKindEnum> consumer) {
        TuihuanKindEnum tuihuanKindEnum = tuihuanKindEnumParam;
        if (!BusinessTypeEnum.LP_ORDER.getCode().equals(subInfoBo.getBusinessType()) || tuihuanKindEnum == null){
            return tuihuanKindEnum;
        }
        switch (tuihuanKindEnum){
            case TK:
                tuihuanKindEnum = TuihuanKindEnum.TK_LP;
                break;
            case TK_LP:
                tuihuanKindEnum = TuihuanKindEnum.TK;
                break;
            default:
                break;
        }
        if(tuihuanKindEnum != null && consumer != null){
            consumer.accept(tuihuanKindEnum);
        }
        return tuihuanKindEnum;
    }

    private void setGroupRefundZeroDefaultRefundWay(RefundMoneyDetailVo detailVo, TuihuanKindEnum tuihuanKindEnum) {
        BigDecimal maxRefundPrice = ObjectUtil.defaultIfNull(detailVo.getMaxRefundPrice(),BigDecimal.ZERO);
        List<RefundWayDetailVo> refundWayDetails = detailVo.getRefundWayDetails();
        if(maxRefundPrice.compareTo(BigDecimal.ZERO)>0 || CollUtil.isEmpty(refundWayDetails) || tuihuanKindEnum == null){
            return;
        }

        refundWayDetails.stream().filter(rwd -> CollUtil.isNotEmpty(rwd.getGroupData()))
                .flatMap(rwd -> rwd.getGroupData().stream())
                .forEach(gd -> ((Refund) gd).setRefundPrice(BigDecimal.ZERO));
        // 换其他型号, 移除0元金额, 没必要保留
        switch (tuihuanKindEnum){
            case HQTXH:
            case SMALL_PRO_HQTXH:
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "换其他型号, 退款0元, 移除以下[{}]退款方式",
                        refundWayDetails.stream().map(RefundWayDetailVo::getGroupData).flatMap(List::stream)
                                .map(gd -> ((Refund) gd).getReturnWayName())
                                .collect(Collectors.joining(",")));
                refundWayDetails.stream().map(RefundWayDetailVo::getGroupData).forEach(List::clear);
                break;
            default:
                break;
        }
    }

    @Override
    @RepeatSubmitCheck(expression = "#{classFullName}:#{tuihuanForm.subId}",message = "存在未完成的退款")
    public R<Integer> save(GroupTuihuanFormVo tuihuanForm, Function<GroupTuihuanFormVo, Optional<RefundSubInfoBo>> subInfoMaxRefundPriceFun) {
        R<Integer> r = refundMoneyService.invokeSave(tuihuanForm, subInfoMaxRefundPriceFun);
        if(r.isSuccess() && tuihuanForm.getTuihuanId() != null){
            // 申请成功之后的操作
            BaseTuiHuanKindService.getBean(tuihuanForm.getTuihuanKindEnum()).saveSuccess(tuihuanForm);
        }
        return r;
    }

    /**
     * 提交退款申请 返回退款id
     * @param tuihuanForm
     * @param subInfoMaxRefundPriceFun
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Integer> invokeSave(GroupTuihuanFormVo tuihuanForm, Function<GroupTuihuanFormVo, Optional<RefundSubInfoBo>> subInfoMaxRefundPriceFun) {
        Optional<RefundSubInfoBo> subInfoOpt = subInfoMaxRefundPriceFun.apply(tuihuanForm);
        try {
            R<Integer> checkR = assertCheckSaveAndSet(tuihuanForm, subInfoOpt.orElse(null));
            if (!checkR.isSuccess()) {
                return checkR;
            }
            checkR = BaseTuiHuanKindService.getBean(tuihuanForm.getTuihuanKindEnum()).assertCheckSaveAndSet(tuihuanForm);
            if (!checkR.isSuccess()) {
                return checkR;
            }
            //按分组校验
            RefundMoneyService.forEachRefundInvoke(refundService -> refundService.assertCheckSave(tuihuanForm, tuihuanForm.getRefundWayDetails()));
            //如果是传1那就是单纯校验
            if(RefundShouhouService.CHECK_DATA_VALUE.equals(Optional.of(tuihuanForm).orElse(new GroupTuihuanFormVo()).getCheckData())){
                return R.success(RefundShouhouService.CHECK_DATA);
            }
            //校验验证
            R<Integer> validR = validCode(tuihuanForm);
            if (!validR.isSuccess()){
                return validR;
            }

            boolean isOnlyOriginFirst = subInfoOpt.flatMap(suInfo -> RefundMoneyUtil.filterChangeThirdOrigin(
                            SpringUtil.getBean(ThirdOriginWayService.class).listAll(tuihuanForm.getSubId(), tuihuanForm.getTuihuanKindEnum())
                                   , tuihuanForm.getTuihuanKindEnum(), suInfo.getBasketIds()).findFirst()).isPresent();

            //按分组数据预处理
            RefundMoneyService.forEachRefundInvoke(refundService ->
                    refundService.preHandleSaveData(tuihuanForm, tuihuanForm.getRefundWayDetails()), (r1, r2) -> {
                // 换其他型号, 调整排序, 优先扣减三方原路径的金额
                if(isOnlyOriginFirst){
                    if(TuiGroupEnum.THIRD_PAY_ORIGIN_WAY.equals(r1.getMyGroup())) {
                        return -1;
                    }
                    if(TuiGroupEnum.THIRD_PAY_ORIGIN_WAY.equals(r2.getMyGroup())){
                        return 1;
                    }
                }
                return 0;
            });

            //入库之前恢复tuihuanKind
            exchangeLpTuiHuanKind(tuihuanForm.getSubInfoBo(),tuihuanForm.getTuihuanKindEnum(),kindEnum -> {
                tuihuanForm.setTuihuanKind(kindEnum.getCode());
                tuihuanForm.setTuihuanKindEnum(kindEnum);
            });
            //插入主表信息
            shouhouRefundMoneyMapper.insertTuihuan(tuihuanForm,ObjectUtil.defaultIfNull(tuihuanForm.getShouhouId(),tuihuanForm.getSubId()),tuihuanForm.getTuihuanKind());
            //按分组入库
            RefundMoneyService.forEachRefundInvoke(refundService -> refundService.save(tuihuanForm, tuihuanForm.getRefundWayDetails()));

            //按退款分类入库
            BaseTuiHuanKindService.getBean(tuihuanForm.getTuihuanKindEnum()).save(tuihuanForm);
            //加入自动审核逻辑
            try {
                shouhouTuihuanService.autoTuihuanCheck(tuihuanForm.getTuihuanId());
            } catch (Exception e){
                RRExceptionHandler.logError("组合退自动审核异常", tuihuanForm.getTuihuanId(), e, smsService::sendOaMsgTo9JiMan);
            }
            return R.success(tuihuanForm.getTuihuanId());
        } catch (IllegalArgumentException ie) {
            throw new CustomizeException(ie.getMessage());
        }
    }

    /**
     * 验证验证码
     * @param tuihuanForm
     * @return
     */
    private R<Integer> validCode(GroupTuihuanFormVo tuihuanForm) {
        tuihuanForm.setValidtWayEnum(EnumUtil.getEnumByCode(ValidMemberType.class, tuihuanForm.getValidtWay()));
        //如果订单信息跳过验证, 设置跳过验证
        if(Boolean.TRUE.equals(tuihuanForm.getSubInfoBo().getIsNotNeedValid())){
            // 跳过用户验证
            SpringContextUtil.getRequest()
                    .ifPresent(req -> req.setAttribute(RequestAttrKeys.PASS_GROUP_REFUND_IS_NEED_VALID,Boolean.TRUE));
        }
        boolean isNeedValid = refundCommonService.isNeedValid(tuihuanForm.getSubId(), tuihuanForm.getShouhouId(), tuihuanForm.getValidtWay(), tuihuanForm.getTuihuanKindEnum());
        boolean isNotNeedValid = !isNeedValid;
        Assert.isTrue(isNotNeedValid || Objects.nonNull(tuihuanForm.getValidtWay()),"验证类型不能为空");
        Assert.isTrue(isNotNeedValid || Objects.nonNull(tuihuanForm.getValidtWayEnum()),"不支持验证类型{}", tuihuanForm.getValidtWay());
        if(isNeedValid){
            R<Boolean>  validtR = SpringUtil.getBean(BaseValidMemberService.class).valid(LambdaBuild.create(new ValidReq<RefundValidVo>())
                    .set(ValidReq::setValidCode, tuihuanForm.getValidtCode()).set(ValidReq::setValidType, tuihuanForm.getValidtWay())
                    .set(ValidReq::setBusinessType, BusinessTypeV1Enum.REFUND_MONEY.getCode()).set(ValidReq::setOrderId, Convert.toLong(tuihuanForm.getSubId()))
                    .set(ValidReq::setT,new RefundValidVo().setShouhouId(tuihuanForm.getShouhouId()).setTuihuanKind(tuihuanForm.getTuihuanKind())).build());
            if(!validtR.isSuccess()){
                return R.error(ResultCode.NEED_VERIFY,validtR.getUserMsg());
            }
        }
        return R.success(null);
    }

    @Override
    @RepeatSubmitCheck(expression = "#{classFullName}:#{tuihuanId}")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> cancelRefund(Integer tuihuanId) {
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        //获取退换表信息
        ShouhouTuiHuanPo tuiHuanPo = shouhouRefundMoneyMapper.getRefund(tuihuanId);
        if(tuiHuanPo == null){
            throw new CustomizeException("退货id错误");
        }
        //先处理退款id
        ShouhouRefundMapper shouhouRefundMapper = SpringUtil.getBean(ShouhouRefundMapper.class);
        int result = shouhouRefundMapper.cancelRefund(tuihuanId,oaUser);
        if (result < 1) {
            throw new CustomizeException("只有退款办理前的才可以撤销");
        }
        //删除returnDetails
        shouhouRefundMapper.deleteReturnDetails(tuihuanId);
        //查询详情信息
        List<ShouhouTuihuanDetailPo> tuihuanDetailPos = refundCommonService.listTuihuanDetail(tuihuanId, null,true);
        if(!ShouhouTuiHuanPo.TuiKindsEnum.GROUP_TUI.getCode().equals(tuiHuanPo.getTuiKinds())){
            throw new CustomizeException("只能撤销组合退");
        }
        //按分组撤销各分组的数据
        RefundMoneyService.forEachRefundInvoke(refundService -> refundService.cancelRefund(tuihuanId,tuihuanDetailPos));
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuiHuanPo.getTuihuanKind());
        //
        BaseTuiHuanKindService.getBean(tuihuanKindEnum).cancelRefund(tuiHuanPo);
        //撤销详情信息
        int ctdUpdateNum = shouhouRefundMoneyMapper.cancelTuihuanDetail(tuihuanId);
        if (ctdUpdateNum < tuihuanDetailPos.stream().filter(thd -> ObjectUtil.notEqual(Boolean.TRUE,thd.getIsDel())).count()) {
            throw new CustomizeException("详情撤销失败");
        }
        if(tuihuanKindEnum != null){
            switch (tuihuanKindEnum){
                case TWXF:
                case TDJ_WXF:
                    SpringUtil.getBean(ShouhouService.class).lambdaUpdate().set(Shouhou::getIstui,null)
                            .eq(Shouhou::getId,tuiHuanPo.getShouhouId()).isNotNull(Shouhou::getIstui).update();
                    break;
                case TDJ:
                case TDJ_LP:
                    refundCommonService.cancelTdjAndTdjLpCheck2(tuiHuanPo,tuihuanKindEnum);
                    break;
                default:
                    break;
            }
        }

        return R.success(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    @RepeatSubmitCheck(expression = "#{classFullName}:#{tuiHuanCheckVo.tuihuanId}")
    public R<Integer> submitCheck(TuiHuanCheckVo tuiHuanCheckVo) {
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        R<Integer> checkR = checkSubmitCheckAndSet(tuiHuanCheckVo,oaUser);
        if (!checkR.isSuccess()){
            return checkR;
        }
        //根据退款类型不同来处理
        checkR = BaseTuiHuanKindService.getBean(tuiHuanCheckVo.getTuihuanKindEnum()).checkSubmitCheckAndSet(tuiHuanCheckVo);
        if (!checkR.isSuccess()){
            return checkR.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
        }
        ShouhouRefundDetailVo.ProcessStatus statusEnum = tuiHuanCheckVo.getProcessStatusEnum();
        //按不同的退款方式进行更新数据
        if(statusEnum != ShouhouRefundDetailVo.ProcessStatus.CHECK3) {
            RefundMoneyService.forEachRefundInvoke(refundService -> refundService.updateCheck(tuiHuanCheckVo));
        }
        R<Integer> result;
        switch (statusEnum) {
            case CHECK1:
            case CHECK2:
                result = refundMoneyService.submitCheck2(tuiHuanCheckVo, oaUser, statusEnum);
                break;
            case CHECK3:
                //退款办理操作 单独事务进行提交
                MultipleTransaction.build().execute(DataSourceConstants.DEFAULT,()->{
                    RefundMoneyService.forEachRefundInvoke(refundService -> refundService.updateCheck(tuiHuanCheckVo));
                }).commit();
                // c# 接口
                SpringUtil.getBean(ShouhouRefundService.class).mobileHttpTuikuan(tuiHuanCheckVo.getTuiHuanPo(), oaUser);
                result = R.success("退款办理成功");
                // 退款办理成功之后的操作
                BaseTuiHuanKindService.getBean(tuiHuanCheckVo.getTuihuanKindEnum()).submitCheck3Success(tuiHuanCheckVo);
                break;
            default:
                result = R.error("流程状态错误");
        }

        if(result.isSuccess() && !tuiHuanCheckVo.getSubLogs().isEmpty()){
            asyncAddLogs(tuiHuanCheckVo);
        }
        return result.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    /**
     * 二审数据处理
     * @param tuiHuanCheckVo
     * @param oaUser
     * @param statusEnum
     * @return
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    @RepeatSubmitCheck(expression = "#{classFullName}:#{tuiHuanCheckVo.tuihuanId}")
    public R<Integer> submitCheck2(TuiHuanCheckVo tuiHuanCheckVo, OaUserBO oaUser, ShouhouRefundDetailVo.ProcessStatus statusEnum) {
        int n = SpringUtil.getBean(ShouhouRefundMapper.class).updateCheck(tuiHuanCheckVo.getTuihuanId(), tuiHuanCheckVo.getProcessStatus(), oaUser);
        if(n <= 0){
            throw new CustomizeException("审批失败,已经审核");
        }
        //退定金 审核2数据保存
        refundCommonService.saveTdjAndTdjLpCheck2(tuiHuanCheckVo);
        return R.success(StrUtil.format("{}审批通过!", statusEnum.getMessage()));
    }

    /**
     * 自动退款办理(实际办理处加锁,此处不要加锁)
     * @param tuiHuanCheckVo
     * @return
     */
    @Override
    public R<Integer> tryAutoCheck3(TuiHuanCheckVo tuiHuanCheckVo, Function<TuiHuanCheckVo, R<Integer>> submitcheck3Fun) {
        if(ObjectUtil.notEqual(tuiHuanCheckVo.getProcessStatusEnum(),ShouhouRefundDetailVo.ProcessStatus.CHECK2)){
            return null;
        }

        //判断是否自动执行退款办理
        List<ShouhouTuihuanDetailPo> tuihuanDetailPos = tuiHuanCheckVo.getTuihuanDetailPos();
        List<ShouhouTuihuanDetailPo> bankDetails = tuihuanDetailPos.stream().filter(td -> TuiGroupEnum.BANK_PAY_TRANSFER_ACCOUNTS.getCode().equals(td.getTuiGroup()))
                .collect(Collectors.toList());
        boolean isContainBank = !bankDetails.isEmpty();
        TuihuanKindEnum tuihuanKindEnum = tuiHuanCheckVo.getTuihuanKindEnum();
        //判断银企直联银行转账
        boolean beDirectPay = BankTransferStatus.DIRECT_BANK_CONNECTION.equals(determineBankTransferStatus(tuiHuanCheckVo));

        boolean isAutoCheck3 = !(
                //建行分期 除了智乐方外都不需要自动办理退款
                (tuihuanDetailPos.stream().map(ShouhouTuihuanDetailPo::getTuiWay)
                        .anyMatch(tuiWay-> ObjectUtil.equal(tuiWay,ShouhouRefundService.REFUND_WAY_JIAN_BANK_FENQI))
                        && ObjectUtil.notEqual(CommonUtils.toShotWebXtenant(XtenantEnum.getXtenant()), ShortXtenantEnum.ZLF.getCode()))
                //saas 银行转账,微信支付宝秒退 都不可以自动办理退款
                || XtenantEnum.isSaasXtenant() && tuihuanDetailPos.stream()
                        .anyMatch(td -> Stream.of(TuiGroupEnum.BANK_PAY_TRANSFER_ACCOUNTS,TuiGroupEnum.WECHAT_ALIPAY_SECONDS_REFUND)
                        .map(TuiGroupEnum::getCode).anyMatch(c -> ObjectUtil.equal(c,td.getTuiGroup())))
                //jiuji 银行转账(非银企直连) 都不可以自动办理退款
                || XtenantEnum.isJiujiXtenant() && !beDirectPay && isContainBank

        );
        //自动退款办理
        BaseTuiHuanKindService baseTuiHuanKindService = BaseTuiHuanKindService.getBean(tuihuanKindEnum);
        isAutoCheck3 = isAutoCheck3 && baseTuiHuanKindService.isAutoCheck3(tuiHuanCheckVo);
        //手动提交当前事务
        if(isAutoCheck3){
            long xtenant = Namespaces.get();
            String traceId = TraceIdUtil.getTraceId();
            return CompletableFuture
                    .supplyAsync(() -> {
                        MDC.put(TraceIdUtil.TRACE_ID_KEY, traceId);
                        R<Integer> submitcheck3InvokeWithUser;
                        try {
                            submitcheck3InvokeWithUser = SpringUtil.getBean(AbstractCurrentRequestComponent.class)
                                    .invokeWithUser(xtenant, LambdaBuild.create(tuiHuanCheckVo.getCurrUser().copy())
                                                    .set(OaUserBO::setRank, Stream.concat(tuiHuanCheckVo.getCurrUser().getRank().stream(),
                                                            Arrays.stream(RankEnum.values()).map(RankEnum::getCode)).collect(Collectors.toList()))
                                                    .set(OaUserBO::setUserName, "系统").build(),
                                            user -> {
                                                //自动办理特殊提交, 需要进行匹配处理
                                                TuiHuanCheckVo tuiHuanCheck3Vo = tuiHuanCheckVo.setProcessStatus(ShouhouRefundDetailVo.ProcessStatus.CHECK3.getCode())
                                                        .setProcessStatusEnum(ShouhouRefundDetailVo.ProcessStatus.CHECK3);
                                            SpringContextUtil.removeReqAttr(RequestAttrKeys.SHOUHOU_TUIHUAN_INFO, RequestAttrKeys.REQUEST_CACHE_PREV);
                                            return submitcheck3Fun.apply(tuiHuanCheck3Vo);
                                            });
                            log.warn("自动退款办理结果: {}, 请求参数:{}", JSON.toJSONString(submitcheck3InvokeWithUser), JSON.toJSONString(tuiHuanCheckVo));
                        }catch (Exception e){
                            R<Integer> eR = R.error();
                            RRExceptionHandler.logError("自动退款办理", tuiHuanCheckVo,e, eR::setUserMsg);
                            return eR;
                        }finally {
                            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
                        }
                        return submitcheck3InvokeWithUser;
                    }).join();
        }
        return null;
    }

    private static BankTransferStatus determineBankTransferStatus(TuiHuanCheckVo tuiHuanCheckVo) {
        //判断是否自动执行退款办理
        List<ShouhouTuihuanDetailPo> tuihuanDetailPos = tuiHuanCheckVo.getTuihuanDetailPos();
        List<ShouhouTuihuanDetailPo> bankDetails = tuihuanDetailPos.stream().filter(td -> TuiGroupEnum.BANK_PAY_TRANSFER_ACCOUNTS.getCode().equals(td.getTuiGroup()))
                .collect(Collectors.toList());
        boolean isContainBank = !bankDetails.isEmpty();
        TuihuanKindEnum tuihuanKindEnum = tuiHuanCheckVo.getTuihuanKindEnum();
        ShouhouTuiHuanPo tuiHuanPo = tuiHuanCheckVo.getTuiHuanPo();
        TuihuanKindEnum realTuihuanKind = RefundMoneyUtil.getRealTuihuanKind(tuiHuanPo.getShouhouId(), tuihuanKindEnum);
        if (XtenantEnum.isJiujiXtenant() && isContainBank) {
            Integer areaid = tuiHuanPo.getAreaid();
            R<BankPaymentRes> bankR = SpringUtil.getBean(CsharpOaWcfCloud.class).bankPayment(areaid, realTuihuanKind.getOrderBusinessTypeEnum().getCode());
            if (bankR.isSuccess() && bankR.getData() != null) {
                //如果银行转账的状态
                List<Integer> bankDetailIds = bankDetails.stream()
                        .filter(bd -> ObjectUtil.isNull(bd.getPayOpenType()))
                        .map(ShouhouTuihuanDetailPo::getId).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(bankDetailIds)){
                    //更新银行转账为银企直连
                    SpringUtil.getBean(ShouhouTuihuanDetailService.class).lambdaUpdate()
                            .in(ShouhouTuihuanDetailPo::getId, bankDetailIds)
                            .isNull(ShouhouTuihuanDetailPo::getPayOpenType)
                            .set(ShouhouTuihuanDetailPo::getPayOpenType, OpenIdInfoBo.OpenType.BANK_PAY.getCode())
                            .update();
                }
                return BankTransferStatus.DIRECT_BANK_CONNECTION;
            }else{
                return BankTransferStatus.BANK_TRANSFER_NON_DIRECT;
            }
        }
        return BankTransferStatus.NO_BANK_TRANSFER;
    }

    private void asyncAddLogs(TuiHuanCheckVo tuiHuanCheckVo) {
        //更新订单日志
        CompletableFuture
                .runAsync(() -> {
                    R<Boolean> logR = addLogs(tuiHuanCheckVo.getTuihuanKindEnum(),tuiHuanCheckVo.getSubLogs());
                    Assert.isTrue(logR.isSuccess(),"批量添加日志: {}异常",logR.getUserMsg());
                })
                .exceptionally(e -> {
                    RRExceptionHandler.logError("组合退款增加日志", tuiHuanCheckVo.getSubLogs(), e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
                    return null;
                });
    }

    /**
     * 根据退款类型批量增加日志
     * @param tuihuanKindEnum
     * @param subLogs
     * @return
     */
    @Override
    public R<Boolean> addLogs(TuihuanKindEnum tuihuanKindEnum, List<SubLogsNewReq> subLogs) {
        R<Boolean> logR = R.success(null);
        switch (RefundMoneyUtil.getBusinessType(tuihuanKindEnum)) {
            case LP_ORDER:
                logR = SpringUtil.getBean(SubLogsCloud.class).addLpSubLogBatch(subLogs);
                break;
            case SALE_ORDER:
                logR = SpringUtil.getBean(SubLogsCloud.class).addSubLogBatch(subLogs);
                break;
            default:
                break;
        }
        return logR;
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public boolean enable(Integer orderId, List<Integer> tuiHuanKinds, Integer areaId) {
        // 原逻辑: 只要有一个启用组合退, 就是组合退
        return mapEnable(orderId, tuiHuanKinds, areaId).entrySet().stream().anyMatch(entry -> Boolean.TRUE.equals(entry.getValue()));
    }

    @Override
    //@DS(DataSourceConstants.CH999_OA_NEW)
    public Map<Integer, Boolean> mapEnable(Integer orderId, List<Integer> tuiHuanKinds, Integer areaId) {
        // 总开关
        AtomicBoolean isGroupRefundEnabled = new AtomicBoolean(ModuleVersionKeys.isEnabled(ModuleVersionKeys.GROUP_REFUND_V1, true));
        RedisTemplate<String, String> redisTemplate = SpringUtil.getBean(RedisTemplate.class);
        AtomicReference<Boolean> isAllCachedRef = new AtomicReference<>(Boolean.TRUE);
        int enableResultIndex = 0;
        int isCachedIndex = 1;
        int cacheKeyIndex = 2;
        // Tuple: enableResult isCache cacheKey
        Map<Integer, Tuple> result = tuiHuanKinds.stream().collect(Collectors.toMap(Function.identity(),
                kind ->{
                    String cacheKey = StrUtil.format(RedisKeys.GROUP_TUIHUAN_ENABLE,orderId, kind, areaId);
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "缓存key: {}", cacheKey);
                    String cacheValue = redisTemplate.opsForValue().get(cacheKey);
                    boolean isCached = StrUtil.isNotBlank(cacheValue);
                    boolean isEnable = Convert.toBool(cacheValue, isGroupRefundEnabled.get());
                    if(!isCached){
                        isAllCachedRef.set(Boolean.FALSE);
                        isEnable = BaseTuiHuanKindService.getBean(kind).isEnable(orderId, kind, isEnable);
                    }
                    return new Tuple(isEnable, isCached, cacheKey);
                }, (v1, v2) -> v1));

        if(Boolean.TRUE.equals(isAllCachedRef.get())){
            return result.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(), entry -> entry.getValue().get(enableResultIndex)));
        }
        //先处理相同开关数据
        //九机的特殊判断处理
        if(isGroupRefundEnabled.get() && apolloEntity.getProhibitSwitch()){
            String areaIdStr = Convert.toStr(areaId);
            //进行门店判断 并且如果配置 -1 那就说明不进行门店的限制
            String refundAreaId = apolloEntity.getRefundAreaId();
            List<String> refundAreaIdList = StrUtil.splitTrim(refundAreaId, StringPool.COMMA);
            if(isGroupRefundEnabled.get() && CollUtil.isNotEmpty(refundAreaIdList)){
                isGroupRefundEnabled.set(refundAreaIdList.contains(areaIdStr));
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "支持门店id: {}", apolloEntity.getRefundAreaId());
            }
            String kind = apolloEntity.getKind();
            Map<String, String> kindXtenantMap = apolloEntity.getKindXtenantMap();
            if (StrUtil.isNotBlank(kindXtenantMap.get(XtenantEnum.getTenantName()))){
                kind = kindXtenantMap.get(XtenantEnum.getTenantName());
            }
            List<Integer> apolloKindList = StrUtil.splitTrim(kind, StringPool.COMMA).stream()
                    .map(Convert::toInt).filter(Objects::nonNull).collect(Collectors.toList());
            //退款类型记录日志
            if(CollUtil.isNotEmpty(apolloKindList)){
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "支持配置类型: {}", kind);
            }
            //更新各退款类型的开关信息
            for (Integer tuiHuanKind : tuiHuanKinds) {
                Tuple tuple = result.get(tuiHuanKind);
                boolean isEnabled = tuple.get(enableResultIndex);
                boolean isCached = tuple.get(isCachedIndex);
                if(isCached){
                    continue;
                }
                //非缓存的才进行更新
                tuple.getMembers()[enableResultIndex] = isEnabled && isGroupRefundEnabled.get() && (apolloKindList.isEmpty() || apolloKindList.contains(tuiHuanKind));
            }
        }

        TuiHuanService tuiHuanService = SpringUtil.getBean(TuiHuanService.class);
        BiConsumer<String,Boolean> putCacheFun = (cacheKey, value) -> redisTemplate.opsForValue().set(cacheKey, Convert.toStr(value), Duration.ofMinutes(4));

        if (ObjectUtil.defaultIfNull(orderId,0)>0 && CollUtil.isNotEmpty(tuiHuanKinds)){
            LambdaQueryChainWrapper<ShouhouTuiHuanPo> tuiHuanLambdaQuery = tuiHuanService.lambdaQuery();
            tuiHuanLambdaQuery.and(cnd ->{
                for (Integer tuiHuanKind : tuiHuanKinds) {
                    Tuple tuple = result.get(tuiHuanKind);
                    boolean isCached = tuple.get(isCachedIndex);
                    if(isCached){
                        continue;
                    }
                    TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuiHuanKind);
                    if(tuihuanKindEnum == null){
                        //跳过不存在的枚举
                        continue;
                    }
                    refundCommonService.buildTuiHuanQuery(cnd.or(),orderId,tuihuanKindEnum);
                }
                return cnd;
            });
            //数据库存在数据的时候, 优先级最高, 同组的数据都按数据的来
            List<Integer> tuiKindShouhouGroups = Stream.of(TuihuanKindEnum.HJT, TuihuanKindEnum.HZB, TuihuanKindEnum.TK, TuihuanKindEnum.HQTXH, TuihuanKindEnum.TK_LP)
                    .map(TuihuanKindEnum::getCode).filter(tk -> tuiHuanKinds.contains(tk)).collect(Collectors.toList());
            tuiHuanLambdaQuery
                     .select(ShouhouTuiHuanPo::getTuihuanKind,ShouhouTuiHuanPo::getTuiKinds)
                    .orderByDesc(ShouhouTuiHuanPo::getId).list()
                     .forEach(st -> {
                         boolean isGroupTui = Objects.equals(st.getTuiKinds(), ShouhouTuiHuanPo.TuiKindsEnum.GROUP_TUI.getCode());
                         if(tuiKindShouhouGroups.contains(st.getTuihuanKind())){
                             for (Integer tksg : tuiKindShouhouGroups) {
                                 Tuple tuple = result.get(tksg);
                                 tuple.getMembers()[enableResultIndex] = isGroupTui;
                             }
                         }else{
                             Tuple tuple = result.get(st.getTuihuanKind());
                             tuple.getMembers()[enableResultIndex] = isGroupTui;
                         }


                     });
        }
        for (Integer tuiHuanKind : tuiHuanKinds) {
            Tuple tuple = result.get(tuiHuanKind);
            boolean isCached = tuple.get(isCachedIndex);
            if(isCached){
                continue;
            }
            putCacheFun.accept(tuple.get(cacheKeyIndex), tuple.get(enableResultIndex));
        }
        return result.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(), entry -> entry.getValue().get(enableResultIndex)));
    }

    @Override
    public List<Refund> listAllShouYing(Integer orderId, TuihuanKindEnum tuihuanKindEnum) {
        List<Refund> result = new LinkedList<>();
        //现金,余额
        SpringUtil.getBean(OtherRefundService.class).listAll(orderId,tuihuanKindEnum).stream().forEach(result::add);
        //刷卡支付
        SpringUtil.getBean(CardPayOriginWayService.class).getCardPayInfo(new CardPayReqVo().setSubId(orderId).setTuihuanKind(tuihuanKindEnum)).forEach(result::add);
        //网上支付
        SpringUtil.getBean(NetPayOriginWayService.class).getAlipayPayInfo(new AlipayInfoReqVo().setSubId(orderId).setTuihuanKind(tuihuanKindEnum)).forEach(result::add);
        //三方支付
        SpringUtil.getBean(ThirdOriginWayService.class).listAll(orderId,tuihuanKindEnum).forEach(result::add);
        return result.stream()
                .sorted((r1,r2) -> CompareUtil.compare(r1.getDtime(), r2.getDtime()))
                .collect(Collectors.toList());
    }

    @Override
    public ShouhouTuiHuanPo getRefund(Integer tuihuanId,Integer subId, TuihuanKindEnum tuihuanKindEnum) {
        return SpringContextUtil.reqCache(() -> DecideUtil.iif(ObjectUtil.defaultIfNull(tuihuanId,0)<=0,
                ()->shouhouRefundMoneyMapper.getLastNotCompleteRefund(subId, tuihuanKindEnum.getCode()),
                ()->shouhouRefundMoneyMapper.getRefund(tuihuanId)
                ),
                RequestCacheKeys.GET_LAST_NOT_COMPLETE_REFUND, tuihuanId, subId, tuihuanKindEnum);
    }


    /**
     * 设置支持的退款方式
     * @param detailParamBo
     * @param detailVo
     * @param oaUserBO
     */
    private void setSupportRefundWays(DetailParamBo detailParamBo, RefundMoneyDetailVo detailVo, OaUserBO oaUserBO) {
        Integer orderId = detailParamBo.getRefundSubInfo().getOrderId();
        TuihuanKindEnum tuihuanKindEnum = detailParamBo.getTuihuanKindEnum();
        Integer areaId = oaUserBO.getAreaId();
        Integer supportSeconds = detailParamBo.getSupportSeconds();
        AtomicInteger applyIdRef = detailParamBo.getApplyIdRef();
        List<RefundMoneyDetailVo.RefundWayVo> refundWays = getRefundWayVos(orderId, areaId, tuihuanKindEnum, supportSeconds, applyIdRef);
        detailVo.setRefundWays(refundWays);
    }

    private List<RefundMoneyDetailVo.RefundWayVo> getRefundWayVos(Integer orderId, Integer areaId, TuihuanKindEnum tuihuanKindEnum,
                                                                  Integer supportSeconds, AtomicInteger applyIdRef) {
        List<RefundMoneyDetailVo.RefundWayVo> refundWays = new LinkedList<>();
        List<String> returnWays = getReturnWays(orderId, areaId, tuihuanKindEnum, supportSeconds);
        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"接口返回支持退款方式列表: {}", returnWays);
        //设置退款方式列表
        returnWays.stream()
                .map(r->setTuiGroupByRefundWay(r,new RefundMoneyDetailVo.RefundWayVo().setName(r)
                        .setId(applyIdRef.getAndIncrement()).setPossibleTuiGroups(Collections.emptySet())))
                .forEach(refundWays::add);
        //追加三方支付退款方式
        SpringUtil.getBean(ThirdOriginWayService.class).listAll(orderId, tuihuanKindEnum)
        .stream()
                // 过滤出原路径退的方式
                .filter(tor -> ThirdRefundTypeEnum.originRefund(tor.getRefundType()))
                // 只取还有钱的退款方式
                .filter(tor -> tor.getRefundPrice().compareTo(BigDecimal.ZERO)>0)
                .map(tor -> new RefundMoneyDetailVo.RefundWayVo().setName(tor.getReturnWayName())
                        .setId(applyIdRef.getAndIncrement())
                        .setTuiGroup(TuiGroupEnum.THIRD_PAY_ORIGIN_WAY.getCode()).setPossibleTuiGroups(Collections.emptySet()))
                .forEach(refundWays::add);
        return refundWays;
    }

    private List<String> getReturnWays(Integer orderId, Integer areaId, TuihuanKindEnum tuihuanKindEnum, Integer supportSeconds) {
        R<List<String>> returnWayR = csharpReturnService.getReturnWayJava(new ReturnWayReqVo().setId(orderId)
                .setAreaId(areaId).setTuiHuanKind(tuihuanKindEnum.getCode())
                .setSupportSeconds(supportSeconds));
        if(!returnWayR.isSuccess()){
            throw new CustomizeException(returnWayR.getUserMsg());
        }
        List<String> returnWays = ObjectUtil.defaultIfNull(returnWayR.getData(), Collections.emptyList());
        return returnWays;
    }

    /**
     * 初筛退款方式的分组
     * @param tuiWay
     * @param refundWayVo
     * @return
     */
    private RefundMoneyDetailVo.RefundWayVo setTuiGroupByRefundWay(String tuiWay, RefundMoneyDetailVo.RefundWayVo refundWayVo) {
        if(StrUtil.endWith(tuiWay,ShouhouRefundService.REFUND_WAY_ORIGIN)){
            //原路径退款 数据流动的时候,会动态处理分组
            refundWayVo.setPossibleTuiGroups(CollUtil.newHashSet(TuiGroupEnum.NET_PAY_ORIGIN_WAY.getCode(),
                    TuiGroupEnum.CARD_PAY_ORIGIN_WAY.getCode()));
        }else if(StrUtil.endWith(tuiWay,ShouhouRefundService.REFUND_WAY_SECONDS)){
            //微信秒退
            refundWayVo.setTuiGroup(TuiGroupEnum.WECHAT_ALIPAY_SECONDS_REFUND.getCode());
        }else if(StrUtil.equals(tuiWay,ShouhouRefundService.REFUND_WAY_BANK)){
            //银行转账
            refundWayVo.setTuiGroup(TuiGroupEnum.BANK_PAY_TRANSFER_ACCOUNTS.getCode());
        }else{
            //其他付款方式
            refundWayVo.setTuiGroup(TuiGroupEnum.OTHER_REFUND.getCode());
        }
        return refundWayVo;
    }

    /**
     * 校验详情参数
     * @param detailParamBo
     * @return
     */
    private R<RefundMoneyDetailVo> checkDetail(DetailParamBo detailParamBo) {
        //增加上线限制,只有上线的租户才会生效
        if(ModuleVersionKeys.isNotEnabled(ModuleVersionKeys.GROUP_REFUND_V1,true)){
            return R.error(ResultCode.NO_DATA,"未启用组合退,跳转原页面进行处理");
        }
        if(CommenUtil.isNullOrZero(detailParamBo.getOrderId()) && CommenUtil.isNullOrZero(detailParamBo.getTuihuanId())){
            return R.error("单号和退款id不能同时为空");
        }
        if(CommenUtil.isNotNullZero(detailParamBo.getOrderId()) && Objects.isNull(detailParamBo.getTuihuanKindEnum())){
            return R.error("单号查询,退款分类不能为空");
        }
        return R.success(null);
    }

    /**
     * 获取退款详情
     * @param tuiHuanPo
     * @param isContainOtherRefund
     * @return
     */
    private List<ShouhouTuihuanDetailPo> listShouhouTuihuanDetail(ShouhouTuiHuanPo tuiHuanPo, boolean isContainOtherRefund) {
        if (Objects.isNull(tuiHuanPo)){
            return Collections.emptyList();
        }
        //组合退款
        if(Objects.equals(tuiHuanPo.getTuiKinds(), ShouhouTuiHuanPo.TuiKindsEnum.GROUP_TUI.getCode())){
            //获取组合退的详情结果集
            return refundCommonService.listTuihuanDetail(tuiHuanPo.getId(), null, isContainOtherRefund);
        }
        //原来的退款
        ShouhouTuihuanDetailPo std = LambdaBuild.create(new ShouhouTuihuanDetailPo()).set(ShouhouTuihuanDetailPo::setFkTuihuanId,tuiHuanPo.getId())
                .set(ShouhouTuihuanDetailPo::setId,0).set(ShouhouTuihuanDetailPo::setRefundPrice,tuiHuanPo.getTuikuanM())
                .set(ShouhouTuihuanDetailPo::setBankFuming,tuiHuanPo.getBankfuming()).set(ShouhouTuihuanDetailPo::setBankName, tuiHuanPo.getBankname())
                .set(ShouhouTuihuanDetailPo::setBankNumber,tuiHuanPo.getBanknumber()).set(ShouhouTuihuanDetailPo::setIsValidt,tuiHuanPo.getIsValidt())
                .set(ShouhouTuihuanDetailPo::setKemuTui,tuiHuanPo.getKemuTui()).set(ShouhouTuihuanDetailPo::setTuiWay, tuiHuanPo.getTuiWay())
                .set(ShouhouTuihuanDetailPo::setTuiGroup, RefundMoneyService.getTuiGroupByTuiWay(tuiHuanPo.getTuiWay()).getCode())
                .set(ShouhouTuihuanDetailPo::setPayKinds,null).set(ShouhouTuihuanDetailPo::setPayOpenId,tuiHuanPo.getPayOpenId())
                .set(ShouhouTuihuanDetailPo::setPayOpenType,null)
                .build();
        return Collections.singletonList(std);
    }

    /**
     * 设置流程状态
     * @param refundMoneyDetail
     * @param tuiHuan
     * @param detailParamBo
     */
    @Override
    public void setProcessAndCheckInfo(RefundMoneyDetailVo refundMoneyDetail, ShouhouTuiHuanPo tuiHuan, DetailParamBo detailParamBo) {
        if(tuiHuan == null){
            refundMoneyDetail.setIsCanCancel(Boolean.FALSE);
            //设置流程状态
            ShouhouRefundDetailVo.ProcessStatus processStatus;
            if (refundMoneyDetail.getMaxRefundPrice().compareTo(BigDecimal.ZERO) < 0
                    // 是否可以退款
                    || !refundCommonService.isCanSubmit(detailParamBo.getTuihuanKindEnum(),detailParamBo.getRefundSubInfo().getSubCheck())) {
                processStatus = ShouhouRefundDetailVo.ProcessStatus.NOT_SUBMIT;
            } else {
                processStatus = ShouhouRefundDetailVo.ProcessStatus.SUBMIT;
            }
            refundMoneyDetail.setProcessStatus(processStatus.getCode());
            refundMoneyDetail.setCurrProcessStatus(ShouhouRefundDetailVo.ProcessStatus.SUBMIT.getCode());
            refundMoneyDetail.setProcessName(processStatus.getMessage());
            return;
        }
        //设置流程信息
        List<ShouhouRefundDetailVo.CheckHistoryVo> checkHistorys = new LinkedList<>();
        TuihuanKindEnum tuihuanKindEnum = detailParamBo.getTuihuanKindEnum();
        ShouhouRefundDetailVo.ProcessStatus firstStatus = getFirstProcessStatus(tuihuanKindEnum);
        ShouhouRefundDetailVo.ProcessStatus processStatus = ShouhouRefundService.addProcessInfo(tuiHuan, firstStatus, checkHistorys);
        ShouhouRefundDetailVo.ProcessStatus nextStatus = ShouhouRefundDetailVo.ProcessStatus.nextProcess(processStatus);
        if(nextStatus != ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK){
            refundMoneyDetail.setIsCanCancel(Boolean.TRUE);
        }else{
            refundMoneyDetail.setIsCanCancel(Boolean.FALSE);
        }
        refundMoneyDetail.setCurrProcessStatus(processStatus.getCode());
        refundMoneyDetail.setProcessStatus(nextStatus.getCode());
        refundMoneyDetail.setProcessName(nextStatus.getMessage());
        refundMoneyDetail.setCheckHistorys(checkHistorys);
    }

    private ShouhouRefundDetailVo.ProcessStatus getFirstProcessStatus(TuihuanKindEnum tuihuanKindEnum) {
        ShouhouRefundDetailVo.ProcessStatus firstStatus = ShouhouRefundDetailVo.ProcessStatus.CHECK1;
        if(Stream.of(TuihuanKindEnum.TK,TuihuanKindEnum.TK_LP,TuihuanKindEnum.HQTXH, TuihuanKindEnum.SMALL_PRO_HQTXH,TuihuanKindEnum.BATCH_TK)
                .anyMatch(tk -> Objects.equals(tk, tuihuanKindEnum))){
            firstStatus = ShouhouRefundDetailVo.ProcessStatus.SUBMIT;
        }
        return firstStatus;
    }

    private R<Integer> checkSubmitCheckAndSet(TuiHuanCheckVo tuiHuanCheckVo, OaUserBO oaUser) {
        boolean isApp = SpringContextUtil.getRequest().map(req -> req.getHeader("Platform"))
                .filter(platForm -> StrUtil.containsAnyIgnoreCase(platForm, "MOA")).isPresent();
        try {
            ShouhouRefundDetailVo.ProcessStatus status = EnumUtil.getEnumByCode(ShouhouRefundDetailVo.ProcessStatus.class, tuiHuanCheckVo.getProcessStatus());
            Assert.isFalse(status == null,"流程状态错误");
            //非m版操作,必须要验证二级密码
            Assert.isFalse(status == ShouhouRefundDetailVo.ProcessStatus.CHECK2 && !isApp
                    && !SpringUtil.getBean(Password2ValidService.class).valid(tuiHuanCheckVo.getPassword2(),oaUser.getUserId()),"密码2输入错误！");
            ShouhouTuiHuanPo tuiHuanPo = SpringContextUtil.reqCache(() -> shouhouRefundMoneyMapper.getRefund(tuiHuanCheckVo.getTuihuanId()),
                    RequestCacheKeys.SHOUHOU_REFUND_MONEY_MAPPER_GET_REFUND, tuiHuanCheckVo.getTuihuanId());
            Assert.isFalse(tuiHuanPo == null,"退款审核id错误");
            //校验退款地区
            if (ObjectUtil.notEqual(oaUser.getAreaId(), tuiHuanPo.getAreaid())){
                return R.error(StrUtil.format("地区不符，请切换至{}再操作！",Optional.ofNullable(SpringUtil.getBean(AreaInfoClient.class).getAreaInfoById(tuiHuanPo.getAreaid()))
                        .map(R::getData).map(AreaInfo::getArea).orElse(Convert.toStr(tuiHuanPo.getAreaid()))));
            }
            TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuiHuanPo.getTuihuanKind());
            Assert.isFalse(tuihuanKindEnum == null,"退款类型[{}]不支持",tuiHuanPo.getTuihuanKind());
            ShouhouRefundDetailVo.ProcessStatus firstStatus = getFirstProcessStatus(tuihuanKindEnum);
            //校验库的状态是否与当前状态一致
            ShouhouRefundDetailVo.ProcessStatus currStatus = ShouhouRefundService.addProcessInfo(tuiHuanPo, firstStatus, new LinkedList<>());
            Assert.isTrue(ShouhouRefundDetailVo.ProcessStatus.nextProcess(currStatus) == status,"审批失败,已经审核");
            List<ShouhouTuihuanDetailPo> allTuihuanDetailPos = listShouhouTuihuanDetail(tuiHuanPo, true);
            List<ShouhouTuihuanDetailPo> tuihuanDetailPos = allTuihuanDetailPos.stream().filter(td -> ObjectUtil.notEqual(Boolean.TRUE, td.getIsDel())).collect(Collectors.toList());
            List<ShouhouTuihuanDetailPo> otherTuihuanDetailPos = allTuihuanDetailPos.stream().filter(td -> ObjectUtil.equal(Boolean.TRUE, td.getIsDel())).collect(Collectors.toList());
            //权限校验
            Assert.isFalse(BaseTuiHuanKindService.getBean(tuihuanKindEnum).processStatusRank(status,tuiHuanPo,tuihuanDetailPos)
                            == ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK, "你没有{}的权限(提示: {})", status.getMessage(),
                    SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.NOT_WORK_RANK).stream().collect(Collectors.joining(StringPool.SPACE)));
            // 查询所有商品的 basketId
            List<Integer> basketIds = SpringUtil.getBean(BasketService.class).lambdaQuery().select(Basket::getBasketId)
                    .eq(Basket::getSubId, tuiHuanPo.getSubId()).and(cnd -> cnd.isNull(Basket::getIsdel).or().eq(Basket::getIsdel, Boolean.FALSE))
                    .list().stream().map(Basket::getBasketId).collect(Collectors.toList());
            Assert.isFalse(status == ShouhouRefundDetailVo.ProcessStatus.CHECK2
                            && TuihuanKindEnum.TDJ.getCode().equals(tuiHuanPo.getTuihuanKind())
                            && ObjectUtil.isNotEmpty(basketIds)
                            && shouhouRefundMoneyMapper.existExchangedDiy(basketIds)
                    ,"DIY保护壳已经兑换过，不允许退换！");

            //设置值
            tuiHuanCheckVo.setProcessStatusEnum(status);
            tuiHuanCheckVo.setTuiHuanPo(tuiHuanPo);
            tuiHuanCheckVo.setTuihuanDetailPos(tuihuanDetailPos);
            tuiHuanCheckVo.setOtherTuihuanDetailPos(otherTuihuanDetailPos);
            tuiHuanCheckVo.setCurrUser(oaUser);
            tuiHuanCheckVo.setSubLogs(new LinkedList<>());
            tuiHuanCheckVo.setBasketIds(basketIds);
            tuiHuanCheckVo.setTuihuanKindEnum(tuihuanKindEnum);
            // 方法内校验并抛出异常
            Assert.isFalse(status == ShouhouRefundDetailVo.ProcessStatus.CHECK2
                            && BankTransferStatus.BANK_TRANSFER_NON_DIRECT.equals(determineBankTransferStatus(tuiHuanCheckVo))
                    ,"暂不支持银行转账，请改用其他方式！");
            return R.success(null);
        } catch (IllegalArgumentException e) {
            return R.error(e.getMessage());
        }
    }

    /**
     *  @param tuihuanForm
     * @param subInfoBo
     * @return
     */
    private R<Integer> assertCheckSaveAndSet(GroupTuihuanFormVo tuihuanForm, RefundSubInfoBo subInfoBo) {
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuihuanForm.getTuihuanKind());
        //设置退款类型
        tuihuanForm.setTuihuanKindEnum(tuihuanKindEnum);
        //设置值
        tuihuanForm.setTuiKinds(ShouhouTuiHuanPo.TuiKindsEnum.GROUP_TUI.getCode());
        tuihuanForm.setSubInfoBo(subInfoBo);
        assertCheckSaveAndSet(tuihuanForm);
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        tuihuanForm.setCurrUser(oaUserBO);
        AreaInfoClient areaInfoClient = SpringUtil.getBean(AreaInfoClient.class);
        tuihuanForm.setCurrAreaInfo(Optional.ofNullable(areaInfoClient.getAreaInfoById(oaUserBO.getAreaId()))
                .map(R::getData).orElseThrow(()->new CustomizeException("获取售后门店信息异常")));
        tuihuanForm.setSubAreaInfo(Optional.ofNullable(areaInfoClient.getAreaInfoById(oaUserBO.getAreaId()))
                        .map(R::getData).orElseThrow(()->new CustomizeException("获取订单门店信息异常")));
        if(ObjectUtil.notEqual(tuihuanForm.getSubAreaInfo().getXtenant(), tuihuanForm.getCurrAreaInfo().getXtenant())){
            return R.error("该订单不属于当前门店租户, 不允许退款");
        }
        Boolean isAuth = Convert.toBool(CommonUtils.getResultData(sysConfigClient.getValueByCode(SysConfigConstant.AUTHORIZE),
                userMsg -> {
                    throw new CustomizeException(StrUtil.format("获取授权配置异常, 原因: {}", userMsg));
                }));
        if(Boolean.TRUE.equals(isAuth) && ObjectUtil.notEqual(tuihuanForm.getSubAreaInfo().getAuthorizeId(), tuihuanForm.getCurrAreaInfo().getAuthorizeId())){
            return R.error("该订单不属于当前门店授权体系, 不允许退款");
        }
        refundCommonService.assertCheckTdj(tuihuanForm,subInfoBo);
        //设置成本和门店id
        refundCommonService.setInpriceAndSubAreaId(tuihuanForm,subInfoBo);
        //校验退款地区
        Integer sAreaId = ObjectUtil.defaultIfNull(tuihuanForm.getShouhouAreaId(), tuihuanForm.getSubAreaId());
        if (ObjectUtil.notEqual(tuihuanForm.getCurrUser().getAreaId(), sAreaId)){
            return R.error(StrUtil.format("地区不符，请切换至{}再操作！",Optional.ofNullable(areaInfoClient.getAreaInfoById(sAreaId))
                    .map(R::getData).map(AreaInfo::getArea).orElse(Convert.toStr(sAreaId))));
        }
        //设置支持的退款方式
        tuihuanForm.setRefundWays(getRefundWayVos(subInfoBo.getOrderId(), oaUserBO.getAreaId(),tuihuanForm.getTuihuanKindEnum(),
                1, new AtomicInteger(1)));
        checkTdProduct(tuihuanForm,subInfoBo);
        return R.success(null);
    }

    /**
     * 主商品和关联商品删除校验
     * @return
     */
    private void checkTdProduct(GroupTuihuanFormVo tuihuanForm, RefundSubInfoBo subInfoBo){
        Integer tuihuanKind = tuihuanForm.getTuihuanKind();
        List<Integer> checkList = Arrays.asList(TuihuanKindEnum.TDJ.getCode(), TuihuanKindEnum.TDJ_LP.getCode());
        BigDecimal yifuM = ObjectUtil.defaultIfNull(subInfoBo.getYifuM(), BigDecimal.ZERO);
        BigDecimal refundPrice = ObjectUtil.defaultIfNull(tuihuanForm.getRefundPrice(), BigDecimal.ZERO);
        //如果 不是退订和良品退订 或者 退款金额不等于已付金额 那就不进行退款校验
        if(!checkList.contains(tuihuanKind) || refundPrice.compareTo(yifuM)!=0){
            return;
        }
        //获取inwcf的域名
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST)).map(R::getData)
                .filter(StrUtil::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        Map<String, Object> parm = new HashMap<>();
        parm.put("subId", subInfoBo.getOrderId());
        if(TuihuanKindEnum.TDJ.getCode().equals(tuihuanKind)){
            parm.put("subKinds", NumberConsts.ONE);
        } else if (TuihuanKindEnum.TDJ_LP.getCode().equals(tuihuanKind)){
            parm.put("subKinds", NumberConstant.TWO);
        }
        String evidenceUrl = host + "/oaApi.svc/rest/CheckSubUnsubscribe";
        HttpResponse evidenceResult = HttpUtil.createPost(evidenceUrl)
                .body(JSONUtil.toJsonStr(parm))
                .execute();
        if(evidenceResult.isOk()){
            log.warn("主商品和关联商品删除校验传入参数：{}，返回结果：{}",evidenceUrl,evidenceResult.body());
            R result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
            if(!result.isSuccess()){
                throw new CustomizeException(Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
            }
        } else {
            log.warn("主商品和关联商品删除校验异常传入参数：{}",evidenceUrl);
            throw new CustomizeException("主商品和关联商品删除校验异常");
        }


    }

    private void assertCheckSaveAndSet(GroupTuihuanFormVo tuihuanForm) {
        RefundSubInfoBo subInfoBo = tuihuanForm.getSubInfoBo();
        TuihuanKindEnum tuihuanKindEnum = tuihuanForm.getTuihuanKindEnum();
        Assert.isTrue(Objects.nonNull(tuihuanKindEnum),"不支持{}退款类型", tuihuanForm.getTuihuanKind());
        Assert.isFalse(subInfoBo == null,"订单信息不能为空");
        Assert.isFalse(tuihuanForm.getRefundPrice().compareTo(BigDecimal.ZERO)<0,"退款金额必须大于等于0");
        Assert.isTrue(tuihuanForm.getRefundPrice().compareTo(subInfoBo.getMaxRefundPrice())<=0,"退款金额不能大于{}", subInfoBo.getMaxRefundPrice().setScale(2, RoundingMode.HALF_DOWN));
        List<JSONObject> refundWayDetails = tuihuanForm.getRefundWayDetails();
        Assert.isFalse(CollUtil.isEmpty(refundWayDetails), "退款方式至少有一个");
        Assert.isFalse(refundWayDetails.stream().anyMatch(Objects::isNull),"退款方式存在空对象");
        //设置生成的退款详情id值为空
        refundWayDetails.forEach(rwd -> rwd.fluentPut(Refund.ID_KEY,null));
        Assert.isFalse(refundWayDetails.stream().anyMatch(rwd -> rwd.getBigDecimal(Refund.REFUND_PRICE_KEY) == null),"退款明细金额不能为空");
        Assert.isFalse(refundWayDetails.stream().anyMatch(rwd -> rwd.getInteger(Refund.GROUP_CODE_KEY) == null),"退款明细分组代码不能为空");
        Assert.isFalse(refundWayDetails.stream().anyMatch(rwd -> EnumUtil.getEnumByCode(TuiGroupEnum.class,rwd.getInteger(Refund.GROUP_CODE_KEY)) == null),"退款明细存在不支持的分组");
        BigDecimal detailSum = refundWayDetails.stream()
                .map(jsonObj -> {
                    BigDecimal refundPrice = jsonObj.getBigDecimal(Refund.REFUND_PRICE_KEY);
                    jsonObj.fluentPut(Refund.REFUND_PRICE_KEY,refundPrice.setScale(4,RoundingMode.HALF_UP));
                    return refundPrice;
                })
                .reduce(BigDecimal.ZERO,BigDecimal::add);
        Assert.isTrue(tuihuanForm.getRefundPrice().compareTo(detailSum) == 0,"累计金额[{}]与明细金额总和[{}]不一致", tuihuanForm.getRefundPrice(),detailSum);
        ShouhouTuiHuanPo lastNotCompleteRefund = shouhouRefundMoneyMapper.getLastNotCompleteRefund(ObjectUtil.defaultIfNull(tuihuanForm.getShouhouId(),tuihuanForm.getSubId()), tuihuanForm.getTuihuanKind());
        Assert.isTrue(lastNotCompleteRefund == null,"已经存在退款记录!");
        //良品切换退款记录 tk -> tk_lp
        exchangeLpTuiHuanKind(tuihuanForm.getSubInfoBo(),tuihuanKindEnum,kindEnum -> {
            tuihuanForm.setTuihuanKind(kindEnum.getCode());
            tuihuanForm.setTuihuanKindEnum(kindEnum);
        });
        //设置其他退款金额总和
        tuihuanForm.setOtherRefundPrice(totalOtherRefundOriginPrice(tuihuanForm.getSubInfoBo(),tuihuanForm.getTuihuanKindEnum(),refundWayDetails));

    }

    private BigDecimal totalOtherRefundOriginPrice(RefundSubInfoBo subInfoBo, TuihuanKindEnum tuihuanKindEnum, List<JSONObject> refundWayDetails){
        AtomicReference<BigDecimal> currTotalOtherPriceRef = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> totalOriginPriceRef = new AtomicReference<>(BigDecimal.ZERO);

        RefundMoneyService.forEachRefundInvoke(baseService -> {
            if(baseService.isOriginWay()){
                //当前组属于原路径退, 进行累加
                Stream<BigDecimal> refundPriceStream = baseService.listAll(subInfoBo.getOrderId(), tuihuanKindEnum).stream()
                        .map(r -> Convert.convert(Refund.class, r).getRefundPrice());
                totalOriginPriceRef.set(refundPriceStream.reduce(totalOriginPriceRef.get(),BigDecimal::add));
            }else{
                //当前组属于其他退,进行累加
                BigDecimal currTotalOtherPrice = refundWayDetails.stream()
                        .filter(jo -> baseService.isMyGroup(jo.getInteger(Refund.GROUP_CODE_KEY)))
                        .map(jo -> jo.getBigDecimal(Refund.REFUND_PRICE_KEY))
                        .reduce(currTotalOtherPriceRef.get(), BigDecimal::add);
                currTotalOtherPriceRef.set(currTotalOtherPrice);
            }
        });
        BigDecimal currTotalOtherPrice = currTotalOtherPriceRef.get();
        if(currTotalOtherPrice.compareTo(BigDecimal.ZERO)<=0){
            return BigDecimal.ZERO;
        }
        // 占用原路径其他退金额: max(当前其他退金额-max(已付金额-总原路径可退,0),0)
        BigDecimal subMaxRefundPrice = ObjectUtil.defaultIfNull(subInfoBo.getSubMaxRefundPrice(), BigDecimal.ZERO);
        BigDecimal zheJiaM = subInfoBo.getZheJiaM();
        return NumberUtil.max(zheJiaM.add(currTotalOtherPriceRef.get())
                .subtract(NumberUtil.max(subMaxRefundPrice.subtract(totalOriginPriceRef.get()), BigDecimal.ZERO)),BigDecimal.ZERO);
    }

    /**
     * 退换信息非空设置详细参数
     * @param detailParamBo
     * @param detailVo
     * @param tuiHuanPo
     */
    @Override
    public void tuiHuanNotNullSetDetailParam(DetailParamBo detailParamBo, RefundMoneyDetailVo detailVo, ShouhouTuiHuanPo tuiHuanPo) {
        //设置备注和退换id
        detailVo.setComment(tuiHuanPo.getComment()).setTuihuanId(tuiHuanPo.getId());
        //组合退获取退款明细
        List<ShouhouTuihuanDetailPo> tuihuanDetailPos = listShouhouTuihuanDetail(tuiHuanPo, false);
        //设置当前退款金额 改为由明细计算得到
        detailVo.setRefundPrice(tuihuanDetailPos.stream().map(ShouhouTuihuanDetailPo::getRefundPrice)
                .filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add));
        if(Stream.of(TuihuanKindEnum.TDJ,TuihuanKindEnum.TDJ_LP,TuihuanKindEnum.TDJ_WXF)
                .noneMatch(tdj -> Objects.equals(tdj,detailParamBo.getTuihuanKindEnum()))){
            //詳情的时候最大可退与当前退款金额相等
            detailVo.setMaxRefundPrice(detailVo.getRefundPrice());
        }

        detailVo.setZheJiaM(tuiHuanPo.getZhejiaM());
        if(tuiHuanPo.getBuypriceM() == null){
            detailVo.setTotalPrice(ObjectUtil.defaultIfNull(tuiHuanPo.getTuikuanM(),BigDecimal.ZERO)
                    .add(ObjectUtil.defaultIfNull(tuiHuanPo.getZhejiaM(),BigDecimal.ZERO)));
        }else{
            detailVo.setTotalPrice(tuiHuanPo.getBuypriceM());
        }

        Optional.ofNullable(detailParamBo.getRefundSubInfo()).map(RefundSubInfoBo::getShouhouAreaId)
                .filter(shouhouAreaId -> shouhouAreaId > 0).map(Optional::of)
                .orElseGet(() -> Optional.ofNullable(tuiHuanPo.getAreaid()))
                .flatMap(refundMoneyService::getCurrAreaInfo)
                .ifPresent(areaInfo -> {
                    detailVo.setAreaId(areaInfo.getId());
                    detailVo.setArea(areaInfo.getArea());
                    detailParamBo.setOrderAreaInfo(areaInfo);
                });

        detailVo.setShouhouId(tuiHuanPo.getShouhouId());
        detailVo.setOrderId(tuiHuanPo.getSubId());
        detailVo.setTuihuanKind(tuiHuanPo.getTuihuanKind());
        switch (detailParamBo.getTuihuanKindEnum()){
            case TWXF:
            case TDJ_WXF:
            case HJT:
            case HZB:
            case SMALL_PRO_REFUND_REPAIR_FEE:
                detailVo.setOrderId(tuiHuanPo.getShouhouId());
                break;
            case SMALL_PRO_REFUND:
            case TPJ:
            case SMALL_PRO_HQTXH:
                detailVo.setShouhouId(tuiHuanPo.getSmallproid());
                break;
            default:
                break;
        }
        detailParamBo.setTuihuanDetailPos(tuihuanDetailPos)
                .setTuihuanKindEnum(EnumUtil.getEnumByCode(TuihuanKindEnum.class,tuiHuanPo.getTuihuanKind()))
                .setTuihuanId(tuiHuanPo.getId());
    }

    @Override
    public void setNewDetail(DetailParamBo detailParamBo, RefundMoneyDetailVo detailVo) {
        //设置最大退款金额
        Optional<RefundSubInfoBo> subInfoOpt = Optional.ofNullable(detailParamBo.getRefundSubInfo());
        //设置退款的订单信息
        detailParamBo.setRefundSubInfo(subInfoOpt.orElse(null));
        detailVo.setZheJiaM(subInfoOpt.map(RefundSubInfoBo::getZheJiaM).orElse(BigDecimal.ZERO));
        detailVo.setMaxRefundPrice(subInfoOpt.map(RefundSubInfoBo::getMaxRefundPrice).orElse(BigDecimal.ZERO));
        detailVo.setYifuM(subInfoOpt.map(RefundSubInfoBo::getYifuM).orElse(BigDecimal.ZERO));
        detailVo.setYingfuM(subInfoOpt.map(RefundSubInfoBo::getYingfuM).orElse(BigDecimal.ZERO));
        detailVo.setTotalPrice(subInfoOpt.map(RefundSubInfoBo::getTotalPrice).orElseGet(()-> detailVo.getMaxRefundPrice()));
        //根据returnWay来获取支持的退款方式
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        setSupportRefundWays(detailParamBo, detailVo,oaUserBO);
        detailVo.setShouhouId(detailParamBo.getOrderId());
        detailVo.setTuihuanKind(detailParamBo.getTuihuanKindEnum().getCode());
        detailVo.setIsNeedValid(Boolean.TRUE);
        subInfoOpt
                .map(sub -> {
                    //设置订单信息
                    detailVo.setOrderId(sub.getOrderId());
                    if(Boolean.TRUE.equals(sub.getIsNotNeedValid())){
                        //不需要验证了
                        detailVo.setIsNeedValid(Boolean.FALSE);
                    }

                    return CommenUtil.currAreaId(sub.getShouhouAreaId(), sub.getAreaId());
                })
                .ifPresent(areaId -> {
                    detailVo.setAreaId(areaId);
                    AreaInfo areaInfo = refundMoneyService.getCurrAreaInfo(areaId).orElseThrow(() -> new CustomizeException("门店信息获取异常"));
                    detailParamBo.setOrderAreaInfo(areaInfo);
                    detailVo.setArea(areaInfo.getArea());
                });
    }

    @Override
    public Optional<AreaInfo> getCurrAreaInfo(Integer currAreaId) {
        if (currAreaId == null){
            return Optional.empty();
        }
        return SpringContextUtil.reqCache(() -> Optional.ofNullable(SpringUtil.getBean(AreaInfoClient.class).getAreaInfoById(currAreaId))
                .filter(R::isSuccess).map(R::getData), RequestCacheKeys.AREA_INFO_CLIENT_GET_AREA_INFO_BY_ID, currAreaId);
    }
}
