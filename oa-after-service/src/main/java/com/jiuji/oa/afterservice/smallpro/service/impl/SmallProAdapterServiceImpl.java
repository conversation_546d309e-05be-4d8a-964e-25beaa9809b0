package com.jiuji.oa.afterservice.smallpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.jiuji.oa.afterservice.apollo.ApolloKeys;
import com.jiuji.oa.afterservice.bigpro.po.ServiceRecord;
import com.jiuji.oa.afterservice.bigpro.service.ServiceRecordService;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardInfomationBO;
import com.jiuji.oa.afterservice.smallpro.po.refund.SmallRefundConfigPo;
import com.jiuji.oa.afterservice.smallpro.po.TiemoCard;
import com.jiuji.oa.afterservice.smallpro.service.SmallProAdapterService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproFilmCardService;
import com.jiuji.oa.afterservice.smallpro.service.smallpro.SmallProConfigService;
import com.jiuji.oa.afterservice.smallpro.service.TiemoCardService;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class SmallProAdapterServiceImpl implements SmallProAdapterService {

    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private SmallProConfigService smallProConfigService;

    private final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final LocalDateTime TIME_FRAME = LocalDateTime.parse("2020-02-10 00:00:00", YYYY_MM_DD_HH_MM_SS);

    private static final BigDecimal FIRST_MONTH_DISCOUNT_RATE = new BigDecimal("0.8");
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public LocalDateTime getFrameTime() {

        R<String> r = sysConfigClient.getValueByCode(SysConfigConstant.FRAME_TIME);
        LocalDateTime frameTime = TIME_FRAME;
        if (r.getCode() == ResultCode.SUCCESS && StringUtils.isNotEmpty(r.getData())) {
            frameTime = DateUtil.stringToLocalDateTime(r.getData());
        }

        return frameTime;
    }

    @Override
    public List<Integer> getSmallRelativeConfigList(Integer code) {
        R<String> r = sysConfigClient.getValueByCode(code);
        if (r.getCode() != ResultCode.SUCCESS || StringUtils.isEmpty(r.getData())) {
            return new ArrayList<>();
        }
        String value = r.getData();
        List<Integer> result = Stream.of(value.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        return result;
    }

    @Override
    public BigDecimal calculateDiscountAmount(Integer ppid, LocalDateTime buyTime, BigDecimal price, Integer basketId) {
        //半个月内：全退
        //半个月到一个月之间：折价0.8, 退款 = 实际支付金额*0.8
        //大于一个月：实际支付金额*0.8 - 实际支付金额*折价率*4
        if (CommenUtil.isNullOrZero(ppid) || (stringRedisTemplate.hasKey(StrUtil.format(RedisKeys.SMALLPRO_NOT_DISCOUNT_BASKET_KEY, basketId)))) {
            return price;
        }

        if (XtenantEnum.isJiujiXtenant()){
            //九机单独的计算逻辑
            return calculateDiscountAmountJiuji(ppid, price, basketId, buyTime);
        }
        //saas 输出还是按购买时间来计算
        return calculateDiscountAmountSaas(ppid, price, basketId, buyTime);

    }

    private BigDecimal calculateDiscountAmountSaas(Integer ppid, BigDecimal price, Integer basketId, LocalDateTime buyTime) {
        LocalDateTime discountStartTime = buyTime;
        LocalDateTime now = LocalDateTime.now();
        long day = Duration.between(discountStartTime, now).toDays();
        BigDecimal rate = this.getDisCountRate(ppid, basketId);
        BigDecimal result = BigDecimal.valueOf(0.0);

        double month = NumberUtil.div(ChronoUnit.DAYS.between(discountStartTime, now), 30, 0, RoundingMode.UP);
        Integer fullRefundDays = Convert.toInt(ApolloKeys.getApolloWithMainTenant(ApolloKeys.SERVICE_FULL_REFUND_DAYS_XTENANT, "15"), NumberConstant.FIFTEEN);
        Integer firstDiscountDays = Convert.toInt(ApolloKeys.getApolloWithMainTenant(ApolloKeys.SERVICE_FIRST_REFUND_DISCOUNT_DAYS_XTENANT, "30"), 30);
        double firstDiscountMonth = NumberUtil.div(firstDiscountDays.doubleValue(), 30, 0, RoundingMode.UP);
        StringJoiner bLog = new StringJoiner("").add("距离购买时间").add(Convert.toStr(day)).add("天折合").add(Convert.toStr(month)).add("月,获取折价率").add(Convert.toStr(rate));
        if (day <= fullRefundDays) {
            //半个月内全额退
            result = price;
            bLog.add(StrUtil.format(" 计算规则 {}天内全额退", fullRefundDays));
        } else if (day <= firstDiscountDays) {
            //半个月到一个月之间折价0.8
            result = price.multiply(FIRST_MONTH_DISCOUNT_RATE);
            bLog.add(StrUtil.format(" 计算规则 {}天< 当前 <={}天之间折价退", fullRefundDays, firstDiscountDays)).add(FIRST_MONTH_DISCOUNT_RATE.toString());
        }else if (rate.compareTo(BigDecimal.valueOf(0.0)) <= 0){
            // 折价率小于等于0,直接返回0元,在此之前不能使用折价率
            bLog.add(" 计算规则 折价率小于等于0,直接返回0元");
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,Convert.toStr(bLog));
            return BigDecimal.valueOf(0.0);
        }else if (month >= 1) {
            //调整，超过一个月的 第一个月折价0.8后不再进行折价计算
            result =
                    price.multiply(FIRST_MONTH_DISCOUNT_RATE).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(month - firstDiscountMonth)));
            bLog.add(StrUtil.format(" 计算规则 前{}个月折价", firstDiscountMonth)).add(FIRST_MONTH_DISCOUNT_RATE.toString())
                    .add(StrUtil.format("第{}个月开始按", firstDiscountMonth + 1)).add(Convert.toStr(rate)).add("折价");
        }else if (month > 19) {
            result = BigDecimal.valueOf(0.0);
        }
        if (result.compareTo(BigDecimal.ZERO) < 0){
            result = BigDecimal.ZERO;
        }
        bLog.add(" 可退金额: ").add(Convert.toStr(result.setScale(2, RoundingMode.HALF_UP)));
        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,Convert.toStr(bLog));
        return result;
    }

    /**
     * 九机计算年包折价单独逻辑
     * @param ppid
     * @param price
     * @param basketId
     * @param buyTime
     * @return
     */
    private BigDecimal calculateDiscountAmountJiuji(Integer ppid, BigDecimal price, Integer basketId, LocalDateTime buyTime) {
        FilmCardInfomationBO filmCardInfomation = SpringUtil.getBean(SmallproFilmCardService.class).getFilmCardInfomation(basketId, false);
        //非年包信息, 走原来的逻辑
        if(filmCardInfomation == null){
            return calculateDiscountAmountSaas(ppid, price, basketId, buyTime);
        }
        LocalDateTime discountStartTime = Optional.ofNullable(filmCardInfomation)
                .map(FilmCardInfomationBO::getStartTime)
                .orElse(buyTime).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime now = LocalDateTime.now().withMinute(0).withSecond(0).withNano(0);
        double day = NumberUtil.div(Duration.between(discountStartTime, now).toHours(), 24.0, 1);
        BigDecimal rate = this.getDisCountRate(ppid,basketId);
        BigDecimal result = BigDecimal.ZERO;

        double month = NumberUtil.div(day, 30, 0, RoundingMode.UP);
        StringJoiner bLog = new StringJoiner("").add("距离生效时间").add(Convert.toStr(day)).add("天折合").add(Convert.toStr(month)).add("月,获取折价率").add(Convert.toStr(rate));
        Integer fullRefundDays = Convert.toInt(ApolloKeys.getApolloWithMainTenant(ApolloKeys.SERVICE_FULL_REFUND_DAYS_XTENANT, "0"), 0);
        Integer firstDiscountDays = Convert.toInt(ApolloKeys.getApolloWithMainTenant(ApolloKeys.SERVICE_FIRST_REFUND_DISCOUNT_DAYS_XTENANT, "30"), 30);
        double firstDiscountMonth = NumberUtil.div(firstDiscountDays.doubleValue(), 30, 0, RoundingMode.UP);
        if (day <= fullRefundDays) {
            //生效前可全额退款
            result = price;
            bLog.add(StrUtil.format(" 计算规则 生效前{}天内可全额退款", fullRefundDays));
        } else if (day <= firstDiscountDays) {
            //生效首月折价退折价0.8
            result = price.multiply(FIRST_MONTH_DISCOUNT_RATE);
            bLog.add(StrUtil.format(" 计算规则 生效{}天前折价退", firstDiscountDays)).add(FIRST_MONTH_DISCOUNT_RATE.toString());
        }else if (rate.compareTo(BigDecimal.valueOf(0.0)) <= 0){
            // 折价率小于等于0,直接返回0元,在此之前不能使用折价率
            bLog.add(" 计算规则 折价率小于等于0,直接返回0元");
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,Convert.toStr(bLog));
            return BigDecimal.ZERO;
        }else{
            //调整，超过一个月的 第一个月折价0.8后不再进行折价计算
            result = price.multiply(FIRST_MONTH_DISCOUNT_RATE).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(month - firstDiscountMonth)));
            bLog.add(StrUtil.format(" 计算规则 前{}个月折价", firstDiscountMonth)).add(FIRST_MONTH_DISCOUNT_RATE.toString())
                    .add(StrUtil.format("第{}个月开始按", firstDiscountMonth - 1)).add(Convert.toStr(rate)).add("折价");
        }
        if (result.compareTo(BigDecimal.ZERO) < 0){
            result = BigDecimal.ZERO;
        }
        bLog.add(" 可退金额: ").add(Convert.toStr(result.setScale(2, RoundingMode.HALF_UP)));
        List<TiemoCard> tiemoCards = SpringUtil.getBean(TiemoCardService.class).lambdaQuery().eq(TiemoCard::getBasketid, basketId)
                .in(TiemoCard::getType, Arrays.asList(FilmCardInfomationBO.CardTypeEnum.TIE_MO.getCode(), FilmCardInfomationBO.CardTypeEnum.SAFE_SHELL.getCode()))
                .list();
        if(CollUtil.isNotEmpty(tiemoCards) && result.compareTo(BigDecimal.ZERO)>0){
            TiemoCard tiemoCard = Optional.ofNullable(tiemoCards.get(NumberConstant.ZERO)).orElse(new TiemoCard());
            Integer allCount = Optional.ofNullable(tiemoCard.getAllCount()).orElse(NumberConstant.ZERO);
            Integer useCount = Optional.ofNullable(tiemoCard.getUseCount()).orElse(NumberConstant.ZERO);
            if (allCount > 0 && useCount > 0) {
                // 如果allCount和useCount都不为0，则按剩余次数比例计算
                int remainCount = allCount - useCount;
                if (remainCount > 0) {
                    result = result.multiply(BigDecimal.valueOf(remainCount)).divide(BigDecimal.valueOf(allCount), 2, RoundingMode.HALF_UP);
                    bLog.add(" 按剩余次数比例计算: ").add(Convert.toStr(remainCount)).add("/").add(Convert.toStr(allCount));
                } else {
                    // 如果已经没有剩余次数，则返回0
                    result = BigDecimal.ZERO;
                    bLog.add(" 已无剩余次数，返回0");
                }
            }
        }
        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,Convert.toStr(bLog));
        return result;
    }

    @Override
    public BigDecimal calculateDiscountAmountV2(SmallRefundConfigPo refundConfig, BigDecimal price) {
        BigDecimal result;
        result = price.multiply(refundConfig.getDifferentPricePercent().setScale(2, RoundingMode.HALF_DOWN).divide(new BigDecimal(100),4, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
        if (result.compareTo(BigDecimal.ZERO) < 0){
            result = BigDecimal.ZERO;
        }
        return result;
    }

    @Override
    public String getValueByCode(Integer code) {
        R<String> r = sysConfigClient.getValueByCode(code);
        String value = "";
        if (r.getCode() == ResultCode.SUCCESS && StringUtils.isNotEmpty(r.getData())) {
            value = r.getData();
        }
        return value;
    }

    @Override
    public List<String> getAllOnlinePayWayList() {
        R<String> valueR = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        String host = valueR.getData();
        //获取所有网络支付方式 getAllOnlinePayWay
        String url = host + "/oaApi.svc/rest/getAllOnLinePayForJava";

        String json = HttpRequest.post(url)
                .execute().body();
        if (StringUtils.isEmpty(json)){
            return new ArrayList<>();
        }
        R<List<String>> jsonR = JSONObject.parseObject(json,R.class);
        return jsonR.getData();
    }

    private BigDecimal getDisCountRate(Integer ppid, Integer basketId) {
        BigDecimal rate = BigDecimal.valueOf(0.0);
        //查询下已删除看是否能查询到
        int years = Optional.ofNullable(basketId)
                .map(bId -> {
                    FilmCardInfomationBO cardInfo = SpringUtil.getBean(SmallproFilmCardService.class).getFilmCardInfomation(bId, false);
                    if(cardInfo != null){
                        if(cardInfo.getEndTime() == null || cardInfo.getStartTime() == null){
                            return null;
                        }
                        return Period.between(cardInfo.getStartTime().toLocalDate(),cardInfo.getEndTime().plusDays(NumberConstant.ONE).toLocalDate()).getYears();
                    }
                    ServiceRecord serviceRecord = SpringUtil.getBean(ServiceRecordService.class).lambdaQuery()
                            .and(cnd -> cnd.eq(ServiceRecord::getBasketId, basketId)).orderByDesc(ServiceRecord::getId)
                            .select(ServiceRecord::getStartTime, ServiceRecord::getEndTime).list().stream().findFirst()
                            .orElse(null);
                    if(serviceRecord != null){
                        if(serviceRecord.getEndTime() == null || serviceRecord.getStartTime() == null){
                            return null;
                        }
                        return Period.between(serviceRecord.getStartTime().toLocalDate(),serviceRecord.getEndTime().plusDays(NumberConstant.ONE).toLocalDate()).getYears();
                    }
                    return null;

                })
                .map(Math::abs)
                .orElse(-1);
        int oneYear = 1;
        int towYear = 2;
        if ((years>=0 && years < oneYear) || this.getSmallRelativeConfigList(SysConfigConstant.JIUJI_HALF_YEAR_SERVICE_PPID).contains(ppid)) {
            String value = this.getValueByCode(SysConfigConstant.HALF_YEAR_DISCOUNT_RATE);
            if (StringUtils.isEmpty(value)) {
                return BigDecimal.valueOf(0.0);
            }
            rate = new BigDecimal(value);
        } else if ((years>=oneYear && years < towYear) || this.getSmallRelativeConfigList(SysConfigConstant.JIUJI_ONE_YEAR_SERVICE_PPID).contains(ppid)) {
            String value = this.getValueByCode(SysConfigConstant.ONE_YEAR_DISCOUNT_RATE);
            if (StringUtils.isEmpty(value)) {
                return BigDecimal.valueOf(0.0);
            }
            rate = new BigDecimal(value);
        } else if (years>=towYear || this.getSmallRelativeConfigList(SysConfigConstant.JIUJI_TWO_YEAR_SERVICE_PPID).contains(ppid)) {
            String value = this.getValueByCode(SysConfigConstant.TWO_YEAR_DISCOUNT_RATE);
            if (StringUtils.isEmpty(value)) {
                return BigDecimal.valueOf(0.0);
            }
            rate = new BigDecimal(value);
        }
        return rate;
    }

}
