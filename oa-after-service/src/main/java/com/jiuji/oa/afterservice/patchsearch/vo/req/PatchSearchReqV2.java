package com.jiuji.oa.afterservice.patchsearch.vo.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * @Auther: qiweiqing
 * @Date: 2020/03/05
 * @Description:
 */
@Data
public class PatchSearchReqV2 {

    @ApiModelProperty(value = "basketId")
    @NotNull@NotNull(message = "basketId不能为空")
    private Integer basketId;
    /**
     *服务类型  1年包  2质保换新  3 贴膜损耗
     */
    @NotNull(message = "serviceType不能为空")
    private Integer serviceType;

    /**
     * type 1-膜 2-壳  3-膜+壳
     */
    @NotNull(message = "type不能为空")
    private Integer type;

    private String transferCode;

    /**
     * 服务生效时间
     */
    @NotNull(message = "expireDate不能为空")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireDate;

    /**
     * 服务截止时间
     */
    @NotNull(message = "startDate不能为空")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;
}
