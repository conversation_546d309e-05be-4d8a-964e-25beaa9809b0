package com.jiuji.oa.afterservice.bigpro.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 维修方案主表类型 VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Data
@ApiModel(value = "RepairPlanMasterTypeVO对象", description = "维修方案主表类型VO")
public class RepairPlanMasterTypeVO implements Serializable {


    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "维修方案主表ID")
    private Integer masterId;

    /**
     * 是否用户确认
     */
    @ApiModelProperty(value = "是否生成配件(0:否 1:是)")
    private Integer isGenerateAccessory;

    /**
     * OA选中  （该字段提供给前端使用  简化前端判断逻辑但是后端实际不进行数据的库的保存）
     */
    private Integer isSelectToOa;

    /**
     * @see com.jiuji.oa.afterservice.bigpro.enums.PlanTypeEnum
     */
    @ApiModelProperty(value = "方案类型")
    private Integer planType;



    private String planTypeName;



}
