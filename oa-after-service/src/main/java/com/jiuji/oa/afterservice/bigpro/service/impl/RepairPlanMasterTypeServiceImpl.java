package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.RepairPlanMasterTypeMapper;
import com.jiuji.oa.afterservice.bigpro.dto.PlanTypeParam;
import com.jiuji.oa.afterservice.bigpro.enums.PlanTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlanMasterType;
import com.jiuji.oa.afterservice.bigpro.service.RepairPlanMasterTypeService;
import com.jiuji.oa.afterservice.bigpro.vo.RepairPlanMasterTypeVO;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.Comparator;

/**
 * <p>
 * 维修方案主表类型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Slf4j
@Service
public class RepairPlanMasterTypeServiceImpl extends ServiceImpl<RepairPlanMasterTypeMapper, RepairPlanMasterType> implements RepairPlanMasterTypeService {

    @Override
    public List<RepairPlanMasterTypeVO> getByMasterId(Integer masterId) {
        List<RepairPlanMasterTypeVO> masterTypeVOS = new ArrayList<>();
        List<RepairPlanMasterType> list = this.lambdaQuery().eq(RepairPlanMasterType::getMasterId, masterId)
                .orderByDesc(RepairPlanMasterType::getCreateTime)
                .list();

        // 步骤一：处理isSelectToOa赋值逻辑
        if(CollUtil.isNotEmpty(list)){
            // 检查是否存在isGenerateAccessory为1的数据
            boolean hasGenerateAccessory = list.stream()
                    .anyMatch(item -> ObjectUtil.equal(item.getIsGenerateAccessory(), NumberConstant.ONE));

            list.forEach(item->{
                RepairPlanMasterTypeVO masterTypeVO = new RepairPlanMasterTypeVO();
                BeanUtil.copyProperties(item, masterTypeVO);
                masterTypeVO.setPlanTypeName(PlanTypeEnum.getDescByCode(item.getPlanType()));

                // 设置isSelectToOa的值
                if (hasGenerateAccessory) {
                    // 如果存在isGenerateAccessory为1的数据，只给对应的那条数据设置isSelectToOa为1
                    masterTypeVO.setIsSelectToOa(ObjectUtil.equal(item.getIsGenerateAccessory(), NumberConstant.ONE)
                            ? NumberConstant.ONE : NumberConstant.ZERO);
                } else {
                    // 如果不存在，所有数据都设置为1
                    masterTypeVO.setIsSelectToOa(NumberConstant.ONE);
                }

                masterTypeVOS.add(masterTypeVO);
            });
        }

        // 步骤二：补充缺失的枚举数据
        // 获取所有枚举类型
        PlanTypeEnum[] allPlanTypes = PlanTypeEnum.values();
        // 获取已存在的方案类型
        Set<Integer> existingPlanTypes = masterTypeVOS.stream()
                .map(RepairPlanMasterTypeVO::getPlanType)
                .collect(Collectors.toSet());

        // 补充缺失的枚举类型
        for (PlanTypeEnum planTypeEnum : allPlanTypes) {
            if (!existingPlanTypes.contains(planTypeEnum.getCode())) {
                RepairPlanMasterTypeVO missingTypeVO = new RepairPlanMasterTypeVO();
                missingTypeVO.setMasterId(masterId);
                missingTypeVO.setPlanType(planTypeEnum.getCode());
                missingTypeVO.setPlanTypeName(planTypeEnum.getMessage());
                missingTypeVO.setIsGenerateAccessory(NumberConstant.ZERO);
                missingTypeVO.setIsSelectToOa(NumberConstant.ZERO);
                masterTypeVOS.add(missingTypeVO);
            }
        }

        // 步骤三：按照PlanTypeEnum枚举里面的rank进行排序
        masterTypeVOS.sort(Comparator.comparing(vo -> PlanTypeEnum.getRankByCode(vo.getPlanType())));

        return masterTypeVOS;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveBatchByParam(PlanTypeParam param) {
        for (RepairPlanMasterTypeVO typeVO : param.getPlanTypeList()) {
            RepairPlanMasterType masterType = new RepairPlanMasterType();
            masterType.setMasterId(param.getMasterId());
            masterType.setPlanType(typeVO.getPlanType());
            masterType.setIsGenerateAccessory(typeVO.getIsGenerateAccessory());
            masterType.setCreateUser(param.getCurrentUser());
            masterType.setCreateTime(param.getNow());
            this.save(masterType);
        }
        StringBuilder masterChangeLog = new StringBuilder();
        masterChangeLog.append("新增方案类型[");
        if (CollUtil.isNotEmpty(param.getPlanTypeList())) {
            List<String> addedTypeNames = new ArrayList<>();
            for (RepairPlanMasterTypeVO typeVO : param.getPlanTypeList()) {
                String typeName = PlanTypeEnum.getDescByCode(typeVO.getPlanType());
                addedTypeNames.add(typeName);
            }
            String addedTypeName = addedTypeNames.stream().collect(Collectors.joining(","));
            masterChangeLog.append(addedTypeName).append("]；");
            return masterChangeLog.toString();
        }
        return "";
    }


}
