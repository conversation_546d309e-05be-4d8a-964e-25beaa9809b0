package com.jiuji.oa.afterservice.common.constant;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 功能上线版本控制和判断方法
 * <AUTHOR>
 * @since 2022/8/22 9:56
 */
@Slf4j
public class ModuleVersionKeys {
    /**
     * 完整key: afterservice:module:group_refund:v1
     * 组合退开关
     */
    public static final String GROUP_REFUND_V1 = "group_refund:v1";
    /**微信秒退开关*/
    public static final String WEIXINMIAOTUI_V1 = "weixin_miaotui:v1";
    /**组合退开关*/
    /**小件退款配置开关*/
    public static final String SMALLPRO_CONFIG_REFUND_V1 = "smallpro:config:refund:v1";
    /**模块是否启用请求头*/
    public static final String REQUEST_MODULE_IS_ENABLED = "module_is_enabled";
    public static final String SHOUHOU_SHENGEHE_V1 = "shouhou_shenghe:v1";
    /**
     * 是否不启用功能版本
     * @param versionKey  功能版本号
     * @param isDefaultEnabled  试点上线 程序控制默认值 false(即默认未启用) 全面上线默认值为 true(全面上线需要修改下代码)
     * @return
     */
    public static boolean isNotEnabled(String versionKey,boolean isDefaultEnabled){
        return !isEnabled(versionKey,isDefaultEnabled);
    }

    /**
     * 是否启用功能版本
     * @param versionKey
     * @param isDefaultEnabled 试点上线 程序控制默认值 false 全面上线默认值为 true(全面上线需要修改下代码)
     * @return
     */
    public static boolean isEnabled(String versionKey,boolean isDefaultEnabled){
        RedisTemplate<String,String> redisTemplate = SpringUtil.getBean(RedisTemplate.class);
        String key = StrUtil.format(RedisKeys.AFTERSERVICE_MODULE_KEY, versionKey);
        boolean isEnabled = ObjectUtil.equal(Boolean.TRUE, Convert.toBool(redisTemplate.opsForValue().get(key), isDefaultEnabled))
                //请求头进行启用
                || SpringContextUtil.getRequest().map(req -> req.getHeader(REQUEST_MODULE_IS_ENABLED)).map(Convert::toBool).orElse(Boolean.FALSE)
                ;
        log.debug("模块是否启用key:{} value: {}",key,isEnabled);
        return isEnabled;
    }
}
