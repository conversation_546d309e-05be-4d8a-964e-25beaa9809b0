package com.jiuji.oa.afterservice.common.event.bo;

import com.jiuji.tc.common.vo.R;
import org.springframework.context.ApplicationEvent;

import java.util.function.Supplier;

/**
 * 回滚事件对象
 * <AUTHOR>
 * @since 2023/1/5 8:42
 */
public class AfterRollBackEvent extends ApplicationEvent {
    /**
     * 执行回滚参数
     */
    private Object args;
    /**
     * 回滚执行函数
     */
    private Supplier<R> rollbackRun;

    /**
     * Create a new ApplicationEvent.
     * @param source
     * @param args
     * @param rollbackRun  如果是sql 语句必须是独立事务进行提交
     */
    public AfterRollBackEvent(Object source,Object args,Supplier<R> rollbackRun) {
        super(source);
        this.args = args;
        this.rollbackRun = rollbackRun;
    }

    public R rollback() {
        if(rollbackRun == null){
            return R.success(null);
        }
        return rollbackRun.get();
    }

    public Object getArgs() {
        return args;
    }
}
