package com.jiuji.oa.afterservice.bigpro.service;

import com.jiuji.oa.afterservice.bigpro.bo.BindPpidInfoBo;
import com.jiuji.oa.afterservice.bigpro.po.RepairServiceInfo;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouServiceConfig;
import com.jiuji.oa.afterservice.bigpro.vo.ProductColorRes;
import com.jiuji.oa.afterservice.bigpro.vo.req.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.tc.common.vo.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

public interface RepairAccessoriesService {


    /**
     * 维修配件绑定商品过滤
     * @param bindPpidInfos
     */
    void filterBindPpidInfos(List<BindPpidInfoBo> bindPpidInfos);
    /**
     * 主站获取套餐信息
     * @param ppidList
     * @return
     */
    List<ShouhouServiceConfig> getServiceInfoList(Long productId, List<Integer> ppidList);
    /**
     * 新版 维修配件使用的门店 如果list为空就是全部门店使用
     * @return
     */
    List<Integer> usingStores();

    /**
     * 查询绑定配件
     * @param ppid
     * @param currentAreaId
     * @return
     */
    List<BindPpidKcInfo> createBindPpidKcInfo(Integer ppid, Integer currentAreaId);


    /**
     * 新版 维修单使用的门店 如果list为空就是全部门店使用
     * @return
     */
    List<Integer> selectNewShouHouInfo();


    /**
     * 维修配件查询
     * @param req
     * @return
     */
    RepairAccessoriesRes searchRepairAccessories(RepairAccessoriesReq req);


    /**
     * 维修配件联想查询
     * @param key
     * @return
     */
    List<PartsAssociationRes> partsAssociation( String key);

    List<PartsAssociationRes> selectProductByKey(PartsAssociationReq req);

    /**
     * 维修配件详情查询
     * @param req
     * @return
     */
    AccessoriesDetailsRes selectAccessoriesDetails(AccessoriesDetailsReq req);

    /**
     * 商品规格查询
     * @param req
     * @return
     */
    ProductColorRes selectProductColor( ProductColorReq req);

    /**
     * 添加维修配件新
     * @param costPriceNewReq
     * @return
     */
    CostPriceNewRes addCostPriceNew(CostPriceNewReq costPriceNewReq);

    /**
     * 订购单删除
     * @param req
     * @return
     */
    R<String> applyDel(ApplyDelReq req);
}
