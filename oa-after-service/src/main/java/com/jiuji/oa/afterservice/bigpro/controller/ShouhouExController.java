package com.jiuji.oa.afterservice.bigpro.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.jiuji.cloud.after.enums.BaoXiuTypeEnum;
import com.jiuji.oa.afterservice.batchreturn.vo.res.BigAndSmallAreaInfo;
import com.jiuji.oa.afterservice.bigpro.bo.BrandCategoryNameBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouLogAddReq;
import com.jiuji.oa.afterservice.bigpro.bo.yuyue.ShouhouYuYueBasicInfo;
import com.jiuji.oa.afterservice.bigpro.enums.DealStatsEnum;
import com.jiuji.oa.afterservice.bigpro.enums.IndexPageEnums;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.WxStatusEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.service.impl.ShouhouYuyueServiceImpl;
import com.jiuji.oa.afterservice.bigpro.vo.OrderPartsVo;
import com.jiuji.oa.afterservice.bigpro.vo.RepairOrderLogVO;
import com.jiuji.oa.afterservice.bigpro.vo.WeixiuzuKindVo;
import com.jiuji.oa.afterservice.bigpro.vo.req.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.UrlConstant;
import com.jiuji.oa.afterservice.common.enums.BusinessNodeEnum;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeEnum;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.*;
import com.jiuji.oa.afterservice.common.vo.Pagination;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.DataTypeEnum;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@Api(tags = "大件：售后扩展")
@RestController
@RequestMapping("/api/bigpro/shouhou")
@RequiredArgsConstructor
@Slf4j
public class ShouhouExController extends BaseController {
    private final ShouhouService shouhouService;
    private final ShouhouExService shouhouExService;
    private final ShouHouPjService shouHouPjService;
    private final ShouhouImeichangeService shouhouImeichangeService;
    private final ShouhouLogsService shouhouLogsService;
    private final ShouhoutestInfoService shouhoutestInfoService;
    private final NumberCardService numberCardService;
    private final Ok3wQudaoService ok3wQudaoService;
    private final AddinfopsService addinfopsService;
    private final ShouhouExtendService shouhouExtendService;
    private final WeixinUserService weixinUserService;
    private final AreaInfoClient areaInfoClient;
    private final WxconfigService wxconfigService;
    private final ProductinfoService productinfoService;
    private final SmsService smsService;
    private final AbstractCurrentRequestComponent currentRequestComponent;
    private final UserInfoClient userInfoClient;
    private final SysConfigClient sysConfigClient;
    private final ShouhouHuishouService shouhouHuishouService;
    /**
     * 维修查询导出同时只能有20个导出
     */
    private final Semaphore exportIndexDataSemaphore = new Semaphore(20);

    @ApiOperation(value = "服务照片拍照上传", notes = "param传相应的json数据", httpMethod = "POST")
    @PostMapping("/setFuwuPic")
    public R<String> setFuwuPic(@RequestBody ShouhouFuwupic shouhouFuwupic) {
        return shouhouExService.setFuwuPic(shouhouFuwupic.getActName(), shouhouFuwupic);
    }

    @ApiOperation(value = "顾客未到店处理", notes = "撤销所有配件，并取机", httpMethod = "GET")
    @GetMapping("/cancelPj")
    @RepeatSubmitCheck
    public R<String> gukeWeiDaoDian(@RequestParam(value = "shouhouId") Integer shouhouId) {
        return shouHouPjService.cancelPj(shouhouId, Collections.emptyList());
    }

    @ApiOperation(value = "查询锁屏密码", notes = "根据id获取锁屏密码、设备账号ID、密码", httpMethod = "GET")
    @GetMapping("/getLockPwd")
    public R<ShouhouDeviceInfoRes> getLockPwd(@RequestParam(value = "shouhouId") Integer shouhouId) {
        return shouhouExService.getLockPwd(shouhouId);
    }

    @ApiOperation(value = "获取锁屏密码查看记录", notes = "取最新10条", httpMethod = "GET")
    @GetMapping("/getPwdlookLogsByShId")
    public R<List<ShouhouPasswordlooklog>> getPwdlookLogsByShId(@RequestParam(value = "shouhouId") Integer shouhouId) {
        return shouhouExService.getPwdlookLogsByShId(shouhouId);
    }

    @ApiOperation(value = "发送取机验证码", httpMethod = "GET")
    @GetMapping("/sendSrcode")
    public R<Boolean> sendSrcode(@RequestParam(value = "shouhouId") Integer shouhouId,
                                 @RequestParam(value = "subId", required = false, defaultValue = "0") Integer subId) {

        return shouhouExService.sendSrcode(shouhouId, subId);
    }

    @ApiOperation(value = "保存换货验证码", httpMethod = "GET")
    @GetMapping("/saveSrcode")
    public R<Boolean> saveSrcode(@RequestParam(value = "shouhouId") Integer shouhouId,
                                 @RequestParam(value = "code") String code) {
        return shouhouExService.saveSrcode(shouhouId, code);
    }

    @ApiOperation(value = "授权验证码", httpMethod = "GET")
    @GetMapping("/saveSrcodebyAdmin")
    public R<Boolean> saveSrcodebyAdmin(@RequestParam(value = "shouhouId") Integer shouhouId) {
        return shouhouExService.saveSrcodebyAdmin(shouhouId);
    }

    @ApiOperation(value = "维修配件改价", notes = "param传相应的json数据", httpMethod = "POST")
    @PostMapping("/editPrice")
    public R<Boolean> editPrice(@RequestBody WxPjEditPriceReq req) {
        return shouhouService.editPrice(req);
    }


    /**
     * 备注改价
     * @param req
     * @return
     */
    @PostMapping("/remarkAndUpdatePrice")
    public R<Boolean> remarkAndUpdatePrice(@RequestBody @Validated RemarkAndUpdatePriceReq req) {
        return shouhouService.remarkAndUpdatePrice(req);
    }

    @ApiOperation(value = "维修配件编辑", notes = "param传相应的json数据", httpMethod = "POST")
    @PostMapping("/editPJ")
    @RepeatSubmitCheck(argIndexs = {0})
    public R<Boolean> editPJ(@RequestBody WxPjEditReq req) {
        R<Boolean> booleanR = shouHouPjService.editWxPj(req.getShouhouId(), req.getAct(), req.getImei(), req.getKcOutId(), req.getProductId(), req.getFmStats(), req.getOldStats(), req.getTuiType());
        Map<String, Object> exData = booleanR.getExData();
        if(MapUtil.isNotEmpty(exData)){
            Object object = exData.get(ShouhouYuyueServiceImpl.ORDER_PARTS);
            if(ObjectUtil.isNotNull(object)){
                ShouhouYuyueService yuyueService = SpringUtil.getBean(ShouhouYuyueService.class);
                List<OrderPartsVo> list = JSONUtil.toList(JSONUtil.toJsonStr(object), OrderPartsVo.class);
                for (OrderPartsVo orderPartsVo : list) {
                    yuyueService.createOrderParts(orderPartsVo);
                }
            }
        }
        return booleanR;
    }

    /**
     * 硬件、软件、用户维修历史记录
     *
     * @param userId 用户id
     * @param imei   串号
     * @return R
     */
    @ApiOperation(value = "硬件、软件、用户维修历史记录", httpMethod = "GET")
    @GetMapping("/getHistory")
    public R<ShouhouHistoryRes> getHistory(@RequestParam(value = "userId") Integer userId, @RequestParam(value = "imei") String imei) {
        return shouhouExService.getHistory(imei, userId);
    }

    @ApiOperation(value = "配件点评提交", httpMethod = "POST", notes = "1 原装  2 组装  3 劣质")
    @PostMapping("/pjdianping")
    public R<Boolean> pjEvaluate(Integer dpid, Integer dianping) {
        return shouhouExService.pjEvaluate(dpid, dianping);
    }

    @ApiOperation(value = "转地区操作（获取数据）", httpMethod = "GET")
    @GetMapping("/toArea")
    public R<ShouhouToAreaRes> toArea(@RequestParam(value = "shouhouId") Integer shouhouId) {
        return shouhouExService.toArea(shouhouId);
    }

    @ApiOperation(value = "转地区操作(提交)", httpMethod = "POST")
    @PostMapping("/toAreaSet")
    public R<Boolean> toAreaSet(@RequestBody ToAreaSetReq req) {
        return shouhouExService.toAreaSubmit(req);
    }


    @ApiOperation(value = "转地区删除", httpMethod = "GET")
    @GetMapping("/delToArea")
    public R<Boolean> delToArea(@RequestParam(value = "id") Integer id,
                                @RequestParam(value = "shouhouId") Integer shouhouId) {
        return shouhouExService.delToArea(id, shouhouId);
    }

    @ApiOperation(value = "串号变更提交", httpMethod = "GET")
    @GetMapping("/imeiApply")
    public R<String> imeiApply(@RequestParam(value = "id", required = true) Integer id,
                               @RequestParam(value = "imei", required = true) String imei, String comment) {
        return shouhouExService.imeiApply(id, imei, comment);
    }

    @ApiOperation(value = "官方延保", httpMethod = "GET")
    @GetMapping("/officialInsurance/{subId}")
    public R<String> getOfficialInsurance(@PathVariable(value = "subId") Integer subId) {
        return R.success("", shouhouExService.getOfficialInsurance(subId));
    }

    @ApiOperation(value = "串号变更 审核", httpMethod = "GET")
    @GetMapping("/imeiApplyCheck")
    public R<String> imeiApplyCheck(@RequestParam(value = "id", required = true) Integer id) {
        return shouhouExService.imeiApplyCheck(id);
    }

    @ApiOperation(value = "串号变更撤销", httpMethod = "GET")
    @GetMapping("/imeiApplyDel")
    public R<String> imeiApplyDel(@RequestParam(value = "id", required = true) Integer id) {
        return shouhouExService.imeiApplyDel(id);
    }

    @ApiOperation(value = "串号变更申请查询", httpMethod = "GET")
    @GetMapping("/getImeiApply")
    public R<ShouhouImeichange> getImeiApply(@RequestParam(value = "shouhouId", required = true) Integer shouhouId) {
        return shouhouImeichangeService.getImeiApply(shouhouId);
    }

    @ApiOperation(value = "确认收银", httpMethod = "GET")
    @GetMapping("/enterShouyin")
    public R<Boolean> enterShouyin(@RequestParam(value = "shouhouId", required = true) Integer shouhouId) {
        return shouhouExService.enterShouyin(shouhouId);
    }

    @ApiOperation(value = "查询可赠送赠品", httpMethod = "POST")
    @PostMapping("/getZengpin")
    public R<Page<ChaoshiZengPinRes>> getZengpin(@RequestBody ChaoshiZengpinListReq req) {
        return shouhouExService.getZengpingPageList(req);
    }

    /**
     * 超时赔付送赠品(自定义PPID赠送)
     *
     * @param req req
     * @return R
     */
    @ApiOperation(value = "超时赔付送赠品(自定义PPID赠送)", httpMethod = "POST")
    @PostMapping("/zengSongPpid")
    public R<String> zengSongPpid(@RequestBody ZengpinZengSongPpidReq req) {
        return shouhouExService.zengSongPpid(req.getPpid(), req.getShouhouId(), req.getCsDays(), req.getType(), req.getRemark());
    }

    @ApiOperation(value = "超时赔付送赠品", httpMethod = "POST")
    @PostMapping("/zengSong")
    public R<Boolean> zengSong(@RequestBody ZengpinZengSongReq req) {
        return shouhouExService.zengSong(req.getPpid(), req.getShouhouId(), req.getPName(), req.getMemberprice(), req.getType(), req.getRemark());
    }

    @ApiOperation(value = "使用优惠码", httpMethod = "GET")
    @GetMapping("/useYouhuiMa")
    public R<Boolean> useYouhuiMa(@RequestParam(value = "shouhouId") Integer shouhouId,
                                  @RequestParam(value = "code") String code) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        return shouhouExService.useYouhuiMa(shouhouId, code, oaUserBO.getUserName());
    }

    @ApiOperation(value = "取消渠道", httpMethod = "GET")
    @GetMapping("/quxiaoQudao")
    public R<Boolean> quxiaoQudao(@RequestParam(value = "shouhouId", required = true) Integer shouhouId) {
        return shouhouExService.quxiaoQudao(shouhouId);
    }

    @ApiOperation(value = "查询渠道信息", httpMethod = "GET")
    @GetMapping("/insource")
    public R<List<Insource>> insource() {
        return shouhouExService.insourceList();
    }

    @ApiOperation(value = "添加售后已修好测试记录", httpMethod = "POST")
    @PostMapping("/addShouhouTestLog")
    public R<Boolean> addShouhouTestLog(@RequestBody ShouhouTestLogAddReq req) {
        return shouhouLogsService.addShouhouTestLog(req);
    }

    @ApiOperation(value = "提交测试参数", httpMethod = "POST")
    @PostMapping("/saveTestOptions")
    public R<Boolean> saveTestOptions(WeixiuTestOptionReq req) {
        return shouhoutestInfoService.saveTestOptions(req);
    }

    @ApiOperation(value = "获取用户绑定优惠码", httpMethod = "GET")
    @GetMapping("/getKxYouhuimas")
    public R<List<NumberCardRes>> getKxYouhuimas(Long userId, @RequestParam(value = "isSoft", required = false) Boolean isSoft, Integer shouhouId) {
        return numberCardService.getKxYouhuimas(userId, isSoft, shouhouId)
                .addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    @ApiOperation(value = "获取售后测试详情", httpMethod = "GET")
    @GetMapping("/getShouhouTestInfo")
    public R<ShouhouTestInfoRes> getShouhouTestInfo(@RequestParam(value = "shouhouId", required = true) Integer shouhouId,
                                                    @RequestParam(value = "testId", required = false) Integer testId,
                                                    @RequestParam(value = "showInfo", required = true) Boolean showInfo) {
        return shouhouExService.getShouhouTestInfo(shouhouId, testId, showInfo);
    }

    @ApiOperation(value = "获取渠道用户", httpMethod = "GET")
    @GetMapping("/autoQudaoUser")
    public R<List<QudaoUserRes>> autoQudaoUser(@RequestParam(value = "q", required = false) String q,
                                               @RequestParam(value = "kinds", required = false) String kinds,
                                               @RequestParam(value = "insourceid", required = false) String insourceid,
                                               @RequestParam(value = "authorizeid", required = false) Integer authorizeid) {
        authorizeid = CommenUtil.isNullOrZero(authorizeid) ? currentRequestComponent.getCurrentStaffId().getAuthorizeId() : authorizeid;
        return ok3wQudaoService.autoQudaoUser(q, kinds, insourceid, authorizeid);
    }

    @ApiOperation(value = "获取预约地址信息", httpMethod = "GET")
    @GetMapping("/getAddinfops")
    public R<List<Addinfops>> getAddinfops(Integer shouhouId) {
        //List<Addinfops> addinfopsList = addinfopsService.list(new LambdaQueryWrapper<Addinfops>().eq(Addinfops::getBindId, shouhouId).in(Addinfops::getType, Arrays.asList(1, 2)).orderByAsc(Addinfops::getType));
        List<Addinfops> addinfopsList = addinfopsService.getAddinfopsByBindIdAndTypes(shouhouId,Arrays.asList(1, 2));
        return R.success(addinfopsList);
    }

    @ApiOperation(value = "售后评价", httpMethod = "GET")
    @PostMapping("/pingJia")
    public R<Boolean> pingJia(@RequestParam(value = "id") Integer id,
                              @RequestParam(value = "pingjia", required = false) String pingjia,
                              @RequestParam(value = "pjkind") Integer pjkind) {
        return shouhouExService.pingJia(id, pingjia, pjkind);
    }

    @ApiOperation(value = "取机操作", httpMethod = "GET")
    @GetMapping("/quJi")
    public R<Boolean> quJi(Integer shouhouId, String remark) {
        return null;
    }

    @ApiOperation(value = "获取现货维修单", httpMethod = "GET")
    @GetMapping("/getShouhouIdByFromid")
    public R<List<Integer>> getShouhouIdByFromid(@RequestParam Integer fromId) {
        return R.success(shouhouExService.getShouhouIdByFromid(fromId));
    }

    /**
     * 售后列表查询
     *
     * @param shouhou 查询参数
     * @return R
     * @see .net:oa999UI.Controllers.ShouhouController.Index
     */
    @ApiOperation(value = "维修列表首页", httpMethod = "POST")
    @PostMapping("/index")
    public R<IPage<ShouhouIndexRes>> index(@RequestBody ShouhouIndexReq shouhou) {
        if (IndexPageEnums.ShouKind1.PRODUCTID.getCode().equals(shouhou.getShou_kind1()) && !NumberUtil.isNumber(shouhou.getKey())) {
            return R.error("productId 必须为数字");
        }
        //除了电话号码和串号 其他一律做合法数字校验
        if (!IndexPageEnums.ShouKind1.MOBILE.getCode().equals(shouhou.getShou_kind1())
                && !IndexPageEnums.ShouKind1.IMEI.getCode().equals(shouhou.getShou_kind1())
                && NumberUtil.isNumber(shouhou.getKey()) && !NumberUtil.isInteger(shouhou.getKey())) {
            return R.error("传入的参数值超过最大允许范围!");
        }
        shouhou = shouhou.uniqueClearOtherVariable(shouhou);
        return R.success(shouhouExtendService.listAfterServiceIndex(shouhou));
    }

    @ApiOperation(value = "维修列表首页", httpMethod = "POST")
    @PostMapping("/exportIndexData")
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{token}",message = "努力导出中...")
    public void exportIndexData(@RequestBody ShouhouIndexReq shouhou, HttpServletResponse response) throws IOException {
        if(!exportIndexDataSemaphore.tryAcquire()){
            ServletUtil.write(response, JSON.toJSONString(R.error("正在执行维修列表导出的人数太多了,请稍后再试")), ContentType.JSON.getValue());
            return;
        }
        try {
            shouhou = shouhou.uniqueClearOtherVariable(shouhou);
            handlerExportIndexData(shouhou,response);
        } finally {
            exportIndexDataSemaphore.release();
        }
    }


    private void handlerExportIndexData(ShouhouIndexReq shouhou, HttpServletResponse response) throws IOException {
        if (IndexPageEnums.ShouKind1.PRODUCTID.getCode().equals(shouhou.getShou_kind1()) && !NumberUtil.isNumber(shouhou.getKey())) {
            throw new RRException("productId 必须为数字");
        }
        String exportFileName = ExcelUtils.getExportFileName("维修查询");
        log.warn("维修查询导出条件: {}", JSON.toJSONString(shouhou));
        if (ExcelWriterUtil.outCacheFile("shouhouExport",shouhou, Duration.ofMinutes(NumberConstant.TEN),exportFileName,response)){
            return;
        }
        Pagination export = new Pagination();
        export.setCurrent(1);
        export.setPageSize(30000);
        shouhou.setPagination(export);
        shouhou.setExport(1);
        AreaInfo areaInfo = areaInfoClient.getAreaInfoById(getCurrentUser().getAreaId()).getData();
        String printName = areaInfo == null ? "" : areaInfo.getPrintName();
        List<String> header = CollUtil.newArrayList(
                "售后单",
                "接件地",
                "大区",
                "小区",
                "分类",
                "品牌",
                "机型名称",
                "串号",
                "故障描述",
                "接件时间",
                "购买时间",
                "跟进时间",
                "倒计时",
                "状态",
                "修不好原因",
                "保修",
                "配件分类",
                "维修配件",
                "换货配件",
                "修好/修不好时间",
                printName + "服务",
                "费用",
                "成本",
                "应付",
                "已付",
                "维修毛利",
                "维修人",
                "接件人",
                "预计时长",
                "取机",
                "旧件",
                "配件发货",
                "测试结果",
                "会员名称",
                "处理方式",
                "配置",
                "取机时间",
                "外送渠道",
                "维修组别"
        );
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("获取售后列表数据");
        IPage<ShouhouIndexRes> pageResult = shouhouExtendService.listAfterServiceIndex(shouhou);
        List<ShouhouIndexRes> records = pageResult.getRecords();
        List<Integer> collect = records.stream().map(ShouhouIndexRes::getId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, List<ShouhouHuishou>> huishouMapByshouhouIds = new HashMap<>();
        if (CollUtil.isNotEmpty(collect)){
            huishouMapByshouhouIds = shouhouHuishouService.getHuishouMapByshouhouIds(collect);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        WeixiuzuKindService weixiuzuKindService = SpringUtil.getBean(WeixiuzuKindService.class);
        Map<Integer, String> weixiuzuNameMap = weixiuzuKindService.getWxGroupList().stream().distinct().collect(Collectors.toMap(WeixiuzuKindVo::getId, WeixiuzuKindVo::getName,(n1, n2) -> n1));
        List<List<Object>> data = new ArrayList<>();
        // 获取大件的品牌和分类信息
        List<Integer> productIds = records.stream().map(ShouhouIndexRes::getProductId).filter(Objects::nonNull).distinct()
                .map(Convert::toInt).filter(productId -> productId>0).sorted().collect(Collectors.toList());
        Map<Long,BrandCategoryNameBo> brandCategoryNameMap = CommonUtils.bigDataInQuery(productIds, ids ->
                SpringUtil.getBean(ProductService.class).getBrandCategoryByProductIds(ids)).stream()
                .collect(Collectors.toMap(bcn -> Convert.toLong(bcn.getProductId()), Function.identity(),(v1,v2) -> v1));

        List<BigAndSmallAreaInfo> bigAndSmallAreaInfos = new ArrayList<>();
        Map<Integer, List<ShouHouCantFixReason>> shouHouCantFixReasonMap = new HashMap<>();
        if (CollUtil.isNotEmpty(pageResult.getRecords())) {
            Set<Integer> areaIdSet = pageResult.getRecords().stream().map(ShouhouIndexRes::getAreaid).collect(Collectors.toSet());
            bigAndSmallAreaInfos = CommonUtils.bigDataInQuery(areaIdSet, ids -> SpringUtil.getBean(AreainfoService.class).getBigAndSmallAreaInfoByIds(new ArrayList<>(ids)));
            List<Integer> shouhouIdList = pageResult.getRecords().stream().map(ShouhouIndexRes::getId).collect(toList());
            List<ShouHouCantFixReason> shouHouCantFixReasons = CommonUtils.bigDataInQuery(shouhouIdList, ids -> SpringUtil.getBean(ShouHouCantFixReasonService.class).selectByShouHouIdList(ids));
            shouHouCantFixReasonMap = shouHouCantFixReasons.stream().collect(Collectors.groupingBy(ShouHouCantFixReason::getShouHouId));
        }
        for (ShouhouIndexRes record : pageResult.getRecords()) {
            if (CollUtil.isNotEmpty(huishouMapByshouhouIds)){
                for (Map.Entry<Integer, List<ShouhouHuishou>> integerListEntry : huishouMapByshouhouIds.entrySet()) {
                    if(CommenUtil.isNullOrZero(integerListEntry.getKey())){
                        continue;
                    }
                    if (Objects.equals(integerListEntry.getKey(),record.getId())){
                        record.setShouhouHuishous(integerListEntry.getValue());
                    }
                }
            }
            BigDecimal feiyong = record.getFeiyong() == null ? BigDecimal.ZERO : record.getFeiyong();
            BigDecimal youhuifeiyong = record.getYouhuifeiyong() == null ? BigDecimal.ZERO : record.getYouhuifeiyong();
            List<Object> row = new ArrayList<>();
            row.add(record.getId());
            row.add(record.getArea());
            //大小区
            //大区
            if (XtenantEnum.isJiujiXtenant()) {
                row.add(bigAndSmallAreaInfos.stream().filter(bi -> bi.getAreaIds().contains(record.getAreaid()) && bi.getDataType().equals(DataTypeEnum.L_COMMUNITY.getCode()) && !bi.getId().equals(-1))
                        .map(BigAndSmallAreaInfo::getName).findFirst().orElse(""));
                //小区
                row.add(bigAndSmallAreaInfos.stream().filter(bi -> bi.getAreaIds().contains(record.getAreaid()) && bi.getDataType().equals(DataTypeEnum.S_COMMUNITY.getCode()) && !bi.getId().equals(-1))
                        .map(BigAndSmallAreaInfo::getName).findFirst().orElse(""));
            } else {
                BigAndSmallAreaInfo bigAndSmallAreaInfo = bigAndSmallAreaInfos.stream().filter(d -> Objects.equals(record.getAreaid(), d.getAreaId())).findAny().orElse(new BigAndSmallAreaInfo());
                row.add(bigAndSmallAreaInfo.getBigArea());
                row.add(bigAndSmallAreaInfo.getSmallArea());
            }
            // 分类+ 品牌信息
            row.add(Optional.ofNullable(brandCategoryNameMap.get(record.getProductId())).map(BrandCategoryNameBo::getCategoryName).orElse(""));
            row.add(Optional.ofNullable(brandCategoryNameMap.get(record.getProductId())).map(BrandCategoryNameBo::getBrandName).orElse(""));
            row.add(record.getName() + " " + StrUtil.nullToEmpty(record.getProductColor()));
            row.add(record.getImei());
            row.add(record.getProblem());
            row.add(record.getModidate() != null ? formatter.format(record.getModidate()) : "");
            row.add(record.getTradedate() != null ? formatter.format(record.getTradedate()) : "");
            row.add(record.getResultDtime() != null ? formatter.format(record.getResultDtime()) : "");
            row.add(record.getDaojishiLabel());
            DealStatsEnum shouKind2 = EnumUtil.getEnumByCode(DealStatsEnum.class, record.getStats());
            row.add(shouKind2 != null ? shouKind2.getMessage() : "");
            //修不好原因
            String reason = shouHouCantFixReasonMap.getOrDefault(record.getId(), new ArrayList<>()).stream()
                    .map(ShouHouCantFixReason::getReason).collect(Collectors.joining(","));
            row.add(reason);

            IndexPageEnums.Baoxiu baoxiu = EnumUtil.getEnumByCode(IndexPageEnums.Baoxiu.class, record.getBaoxiu());
            row.add(baoxiu != null ? baoxiu.getMessage() : "");
            //新增维修配件分类
            row.add(record.getWxpjCategory());
            row.add(record.getWxpjNames());
            //换货配件
            row.add(Optional.ofNullable(record.getShouhouHuishous()).orElse(new ArrayList<>()).stream().map(ShouhouHuishou::getName).collect(Collectors.joining(",")));
            //修好/修不好时间
            if(WxStatusEnum.YXH.getCode().equals(record.getStats())){
                row.add(record.getModidtime() != null ? formatter.format(record.getModidtime()) : "");
            } else {
                row.add(record.getResultTime() != null ? formatter.format(record.getResultTime()) : "");
            }
            BaoXiuTypeEnum serviceType = EnumUtil.getEnumByCode(BaoXiuTypeEnum.class, record.getServiceType());
            row.add(serviceType != null ? serviceType.getMessage() : "");
            row.add(feiyong);
            row.add(record.getCostprice());
            row.add(feiyong.subtract(Optional.ofNullable(record.getYifum()).orElse(BigDecimal.ZERO)));
            row.add(record.getYifum() == null ? BigDecimal.ZERO : record.getYifum());
            //维修毛利
            row.add(feiyong.subtract(record.getCostprice()));

            row.add(record.getWeixiuren());
            row.add(record.getInuser());
            row.add("预计时长");
            row.add(Boolean.TRUE.equals(record.getIsQuJi() == 1) ? "是" : "否");
            row.add(Boolean.TRUE.equals(record.getJiujian()) ? "是" : "否");
            row.add(Boolean.TRUE.equals(record.getNeedFahuo()) ? "是" : "否");
            row.add(record.getTestResultInfo());
            row.add(Boolean.TRUE.equals(record.getIszy()) ? record.getRealname() : record.getUsername());
            IndexPageEnums.WxKind wxKind = EnumUtil.getEnumByCode(IndexPageEnums.WxKind.class, record.getWxkind());
            row.add(wxKind != null ? wxKind.getMessage() : "");
            row.add(record.getPeizhi());
            row.add(Boolean.TRUE.equals(Optional.ofNullable(record.getIsQuJi()).orElse(NumberConstant.ZERO) == 1) && record.getOfftime() != null ? record.getOfftime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
            row.add(record.getQuDaoName());
            Optional.ofNullable(record.getWeixiuzuid()).ifPresent(weixiuzuId -> row.add(weixiuzuNameMap.get(weixiuzuId)));
            data.add(row);
        }
        ExcelUtils.bigExport(response, data, header, null, exportFileName, 1);
        stopWatch.stop();
        log.debug(stopWatch.prettyPrint());
    }

    @ApiOperation(value = "维修列表首页下拉列表集合", httpMethod = "GET")
    @GetMapping("/getIndexEnums")
    public R<Map<String, List<EnumVO>>> getIndexEnums() {
        return R.success(shouhouExtendService.getIndexEnums());
    }

    @ApiOperation(value = "获取用户绑定url", httpMethod = "GET")
    @GetMapping("/getWxBindUrl")
    public R<String> getWxBindUrl(@RequestParam Integer userId, @RequestParam Integer areaId, @RequestParam String subId, @RequestParam(required = false, defaultValue = "2") Integer type) {
        return R.success(weixinUserService.getWxBindUrl(userId, areaId, subId, type));
    }

    @ApiOperation(value = "获取用户绑定url", httpMethod = "GET")
    @GetMapping("/checkWxBind")
    public R<String> checkWxBind(@RequestParam Integer userId) {
        return R.success("success", weixinUserService.getWxBindUrl(userId, getCurrentUser().getAreaId(), "", 2));
    }

    /**
     * 维修查询列表发货
     *
     * @param shouhouId 售后id
     * @return R
     * @see .net oa999UI.Controllers.ShouhouController.Fahuo
     */
    @ApiOperation(value = "维修查询列表发货", httpMethod = "GET")
    @PostMapping("/deliverGoods")
    public R<Boolean> deliverGoods(@RequestParam Integer shouhouId) {
        return R.success(shouhouExtendService.deliverGoods(shouhouId, getCurrentUser().getUserName()));
    }


    @ApiOperation(value = "我的接件", httpMethod = "POST")
    @PostMapping("/myList")
    public R<IPage<ShouhouIndexRes>> myList(@RequestBody ShouhouIndexReq shouhou) {
        if (IndexPageEnums.ShouKind1.PRODUCTID.getCode().equals(shouhou.getShou_kind1()) && !NumberUtil.isNumber(shouhou.getKey())) {
            return R.error("productId 必须为数字");
        }
        //除了电话号码和串号 其他一律做合法数字校验
        if (!IndexPageEnums.ShouKind1.MOBILE.getCode().equals(shouhou.getShou_kind1())
                && !IndexPageEnums.ShouKind1.IMEI.getCode().equals(shouhou.getShou_kind1())
                && NumberUtil.isNumber(shouhou.getKey()) && !NumberUtil.isInteger(shouhou.getKey())) {
            return R.error("传入的参数值超过最大允许范围!");
        }
        return R.success(shouhouExtendService.listAfterServiceIndex(shouhou));
    }

    /**
     * 按手机号码查询购买记录
     *
     * @param key 关键字
     * @return R
     * @see .net:oa999UI.Controllers.ShouhouController.searchUser
     */
    @ApiOperation(value = "购买记录查询", httpMethod = "GET")
    @GetMapping("/searchUser")
    public R<MemberSubsRes> searchUser(@RequestParam String key,@RequestParam (value = "pageSize",required = false) Integer pageSize,@RequestParam (value = "currentPage",required = false) Integer currentPage) {
        if (Objects.isNull(currentPage)){
            currentPage = 1;
        }
        if (Objects.isNull(pageSize)){
            pageSize = 50;
        }
        return R.success(shouhouExtendService.searchUserSub(key,pageSize,currentPage));
    }


    /**
     * 按手机号码查询购买记录
     *
     * @return R
     * @see .net:oa999UI.Controllers.ShouhouController.WeixiuLishi
     */
    @ApiOperation(value = "维修历史", httpMethod = "GET")
    @GetMapping("/repairHistories")
    public R<List<RepairHistoryRes>> repairHistories(@RequestParam(required = false) String imei,
                                                     @RequestParam(required = false, defaultValue = "0") Long userId,
                                                     @RequestParam(required = false) String mobile,
                                                     @RequestParam(required = false) String tradingHours,
                                                     @RequestParam(required = false,defaultValue = "false") Boolean isLp) {
        return shouhouExtendService.repairHistories(tradingHours,imei, userId, mobile,isLp);
    }


    /**
     *  查询串号上一次使用记录
     * @param lastRepairReq
     * @return
     */
    @PostMapping("/selectLastRepair")
    public R<LastRepairRes> selectLastRepair(@RequestBody LastRepairReq lastRepairReq) {
        return R.success(shouhouExtendService.selectLastRepair(lastRepairReq));
    }


    /**
     *  查询串号上一次使用记录
     * @param pageRepairImeiReq
     * @return
     */
    @PostMapping("/selectRepairImeiHis")
    public R<IPage<PageRepairImeiHisVo>> selectRepairImeiHis(@RequestBody PageRepairImeiReq pageRepairImeiReq) {
        return R.success(shouhouExtendService.selectRepairImeiHis(pageRepairImeiReq));
    }


    /**
     *  查询串号上一次使用记录
     * @param repairOrderLogVO
     * @return
     */
    @PostMapping("/updateRepairOrder/v1")
    public R<String> updateRepairOrder(@RequestBody @Valid RepairOrderLogVO repairOrderLogVO) {
        return R.success(shouhouExtendService.updateRepairOrder(repairOrderLogVO));
    }

    /**
     * 良品是否可以【15天无理由退货】
     *
     * @param userId 会员id
     * @return R
     * @see .net:oa999UI.Controllers.ShouhouController.HasLiangpinWuliyouTuikuanCount
     */
    @ApiOperation(value = "良品是否可以【15天无理由退货】", httpMethod = "GET")
    @GetMapping("/HasLiangpinWuliyouTuikuanCount")
    public R<Map<String, Object>> HasLiangpinWuliyouTuikuanCount(@RequestParam Long userId) {
        Map<String, Object> result = Maps.newHashMapWithExpectedSize(2);
        Integer count = shouhouExtendService.exchangeCount(userId, LocalDateTime.now().minusMonths(2));
        result.put("count", count);
        result.put("istui", count < 2);
        return R.success(result);
    }

    /**
     * 良品是否可以【15天无理由退货】
     *
     * @return R
     * @see .net:oa999UI.Controllers.ShouhouController.HasLiangpinWuliyouTuikuanCount
     */
    @ApiOperation(value = "HasShouhou;HasProductMKC;Has4GjiaTaocan", httpMethod = "GET")
    @GetMapping("/checkImeiAndMkc")
    public R<Map<String, Boolean>> checkImeiAndMkc(@RequestParam Integer mkcId, @RequestParam String imei) {
        Map<String, Boolean> result = Maps.newHashMapWithExpectedSize(2);
        Integer shouhouCount = shouhouExtendService.hasShouhou(mkcId, imei);
        Integer mkcCount = shouhouExtendService.hasProductMKC(mkcId, imei);
        Integer taocanCount = shouhouExtendService.has4GjiaTaocan(imei);
        result.put("hasShouhou", shouhouCount != null);
        result.put("hasProductMKC", mkcCount != null);
        result.put("has4GjiaTaocan", taocanCount != null);
        return R.success(result);
    }

    /**
     * 良品是否可以【15天无理由退货】
     *
     * @return R
     */
    @ApiOperation(value = "是否存在售后记录", httpMethod = "GET")
    @GetMapping("/hasShouhou")
    public R<Integer> hasShouhou(@RequestParam Integer mkcId, @RequestParam String imei) {
        if (StrUtil.isBlank(imei)) {
            return R.error("非法参数");
        }
        return R.success(shouhouExtendService.hasShouhou(mkcId, imei));
    }

    /**
     * 根据mkcId查询是否支持15天无理由
     *
     * @param mkcIdParam mkcId
     * @return
     */
    @GetMapping("/getReturnByMkcId")
    @ApiOperation(value = "根据mkcid查询是否支持15天无理由", httpMethod = "GET")
    public R<Boolean> getReturnByMkcId(@RequestParam("mkcId") String mkcIdParam) {
        Integer mkcId = Convert.toInt(mkcIdParam);
        if (CommenUtil.isNullOrZero(mkcId)) {
            return R.error("非法参数");
        }
        return shouhouExtendService.getReturnByMkcId(mkcId);
    }


    /**
     * 获取维修配置
     *
     * @param name 关键词
     * @return R
     * @see .net:oa999UI.Controllers.ShouhouController.GetWxCheck
     */
    @ApiOperation(value = "获取维修配置", httpMethod = "GET")
    @GetMapping("/getWxConfig")
    public R<WxConfigRes> getWxConfig(@RequestParam String name) {
        return R.success(wxconfigService.getByKeys(name));
    }

    /**
     * 根据串号读取新机配置
     *
     * @param imei imei
     * @return R
     * @see .net:oa999UI.Controllers.ShouhouController.GetConfigByImei
     */
    @ApiOperation(value = "根据串号读取新机配置", httpMethod = "GET")
    @GetMapping("/getConfigByImei")
    public R<String> getConfigByImei(@RequestParam String imei) {
        return R.success("请求成功！", productinfoService.getConfigByImei(imei));
    }

    @ApiOperation(value = "获取售后的orderId", httpMethod = "GET")
    @GetMapping("/getShouhouOrderId")
    public R<String> getShouhouOrderId() {
       return R.success("查询成功",shouhouService.getOrderIdsh());
    }

    /**
     * 接件保存
     *
     * @param shouhou   售后实体
     * @param iswaiguan iswaiguan
     * @param isweb     isweb
     * @param doReCb    doReCb
     * @return R
     * @see .net:oa999UI.Controllers.ShouhouController.FormSave
     */
    @ApiOperation(value = "接件保存", httpMethod = "POST")
    @PostMapping("/save")
    @RepeatSubmitCheck(seconds = 30)
    public R<String> formSave(@RequestBody ShouhouReceiveReq shouhou,
                              @RequestParam(required = false, defaultValue = "-1") String iswaiguan,
                              @RequestParam(required = false, defaultValue = "0") Integer isweb,
                              @RequestParam(required = false, defaultValue = "0") String doReCb) {
        return shouhouExtendService.formSave(shouhou, Convert.toInt(iswaiguan, -1), isweb, doReCb);
    }


    /**
     * 回收单生成维修单
     * @param recoveryCreateShouHouReq
     * @return
     */
    @PostMapping("/recoveryCreateShouHou")
    @RepeatSubmitCheck(seconds = 30)
    public R<String> recoveryCreateShouHou(@RequestBody @Valid RecoveryCreateShouHouReq recoveryCreateShouHouReq){
        R<String> stringR = null;
        long startTime = System.currentTimeMillis(); // 记录开始时间
        try {
            log.warn("回收单生成维修单传入参数：{}", JSONUtil.toJsonStr(recoveryCreateShouHouReq));
            stringR = shouhouExtendService.recoveryCreateShouHou(recoveryCreateShouHouReq);
            log.warn("回收单生成维修单返回结果：{}", JSONUtil.toJsonStr(stringR));
        } catch (Exception e) {
            RRExceptionHandler.logError("回收单生成维修单异常", recoveryCreateShouHouReq, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            throw new CustomizeException(e.getMessage());
        } finally {
            long endTime = System.currentTimeMillis(); // 记录结束时间
            long duration = endTime - startTime; // 计算执行时间
            log.warn("回收单生成维修单消耗时间 {} ms", duration); // 记录执行时间
            if (duration > 10000) {
                RRExceptionHandler.logError("回收单生成维修单情耗时"+duration, recoveryCreateShouHouReq, null, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            }
        }
        return stringR;
    }

    /**
     * 回收单生成维修单
     * @param recoveryCreateShouHouReq
     * @return
     */
    @PostMapping("/handleHuiShou")
    public R<String> handleHuiShou(@RequestBody @Valid RecoveryCreateShouHouReq recoveryCreateShouHouReq){
        shouhouExtendService.handleHuiShou(recoveryCreateShouHouReq);
        return R.success("操作成功");
    }

    /**
     * 接件保存
     *
     * @param shouhou   售后实体
     * @param iswaiguan iswaiguan
     * @param isweb     isweb
     * @param doReCb    doReCb
     * @return R
     * @see .net:oa999UI.Controllers.ShouhouController.FormSave
     */
    @ApiOperation(value = "接件保存", httpMethod = "POST")
    @PostMapping("/saveFast")
    @RepeatSubmitCheck(seconds = 30)
    public R<String> formSaveFast(@RequestBody ShouhouReceiveFastReq shouhou,
                              @RequestParam(required = false, defaultValue = "-1") Integer iswaiguan,
                              @RequestParam(required = false, defaultValue = "0") Integer isweb,
                              @RequestParam(required = false, defaultValue = "0") String doReCb) {

        return shouhouExtendService.formSaveFast(shouhou, iswaiguan, isweb, doReCb);
    }

    /**
     * 售后接件form表单页面获取初始化数据
     *
     * @param fm 请求参数
     * @return R
     */
    @ApiOperation(value = "售后接件form表单页面获取初始化数据", httpMethod = "POST")
    @PostMapping("/addForm")
    public R<Shouhou> addForm(@RequestParam ShouhouFM fm) {
        return shouhouExtendService.addForm(fm);
    }

    /**
     * 售后接件获取维修组列表
     *
     * @return R
     */
    @ApiOperation(value = "售后接件获取维修组列表", httpMethod = "GET")
    @GetMapping("/listRepairGroups")
    public R<List<EnumVO>> listRepairGroups(@RequestParam(value = "productId", required = false) Integer productId,
                                            @RequestParam(value = "ppid", required = false) Integer ppid) {
        if(productId == null && ppid != null){
            productId = productinfoService.lambdaQuery().eq(Productinfo::getPpriceid, ppid)
                    .select(Productinfo::getPpriceid, Productinfo::getProductId).list().stream().findFirst()
                    .map(Productinfo::getProductId).orElse(productId);
        }
        R<List<EnumVO>> result = R.success(shouhouExtendService.listRepairGroups(productId));
        //增加其他配置
        result.setExData(shouhouExtendService.listRepairEnum(productId));
        return result;
    }

    /**
     * 判断是否为返修
     *
     * @param imei imei
     * @return Boolean
     */
    @ApiOperation(value = "判断是否为返修", httpMethod = "GET")
    @GetMapping("/checkFanxiu")
    public R<Boolean> checkFanxiu(@RequestParam("imei") String imei) {
        return R.success(shouhouExtendService.checkFanxiu(imei));
    }

    /**
     * 发送imei消息
     *
     * @param link 唤起扫码的地址
     * @return Boolean
     */
    @ApiOperation(value = "发送imei消息", httpMethod = "GET")
    @PostMapping("/sendImeiMessage")
    public R<Boolean> sendImeiMessage(@RequestParam String link) throws UnsupportedEncodingException {
        smsService.sendOaMsg("点击打开OA扫码接件", URLDecoder.decode(link, "utf-8"), getCurrentUser().getUserId().toString(), OaMesTypeEnum.SHTZ);
        return R.success(true);
    }

    @ApiOperation(value = "发送imei消息", httpMethod = "GET")
    @GetMapping("/sendOaMsg")
    public R<Boolean> sendImeiMessage() {
        smsService.sendOaMsg("OA消息推送测试", "", "13685", OaMesTypeEnum.SHTZ);
        return R.success(true);
    }

    /**
     * 撤销优惠码
     *
     * @param shouhouId
     * @return Boolean
     */
    @ApiOperation(value = "撤销优惠码", httpMethod = "GET")
    @GetMapping("/cancelYouHuiMa")
    public R<Boolean> cancelYouHuiMa(@RequestParam("shouhouId") Integer shouhouId,@RequestParam(value = "code",required = false) String code) {
        return shouhouExtendService.cancelYouHuiMa(shouhouId,code);
    }


    /**
     * 良品订单退换原因修改备注进程记录
     *
     * @param req
     * @return Boolean
     */
    @ApiOperation(value = "良品订单退还备注添加", httpMethod = "GET")
    @PostMapping("/addShouhouLog")
    public R<Boolean> addShouhouLog(@RequestBody ShouhouLogAddReq req) {
        ValidatorUtil.validateEntity(req);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(req.getShouhouId()), MTableInfoEnum.SHOUHOU, req.getShouhouId());
        //Shouhou shouhou = shouhouService.getById(req.getShouhouId());
        if (shouhou == null) {
            return R.error("维修单号错误");
        }
        String wuLiYouText = StrUtil.format("{}{}", ShouhouExService.getWuLiYouDays(), ShouhouExService.WU_LI_YOU_TEXT);
        if(StrUtil.endWith(req.getContent(),ShouhouExService.WU_LI_YOU_TEXT) && ObjectUtil.notEqual(req.getContent(),wuLiYouText)){
            //校验无理由退货的时间
            return R.error(StrUtil.format("只支持{}",wuLiYouText));
        }
        if (!Objects.equals(shouhou.getWuliyou(), req.getContent())) {
            shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getWuliyou, req.getContent()).eq(Shouhou::getId, shouhou.getId()));
            shouhouService.saveShouhouLog(req.getShouhouId(), "退换原因由：【" + shouhou.getWuliyou() + "】修改为：【" + req.getContent() + "】", req.getOperator());
        }
        return R.success("操作成功");
    }

    /**
     * 撤销优惠码
     *
     * @param shouhouId
     * @return Boolean
     */
    @ApiOperation(value = "退换机管理——退款前校验", httpMethod = "GET")
    @GetMapping("/tuiCheck")
    public R<Boolean> tuiCheck(@RequestParam("shouhouId") Integer shouhouId) {
        return shouhouExService.tuiCheck(shouhouId);
    }

    /**
     * 获取小件信息
     *
     * @param shouhouId
     * @return Boolean
     */
    @ApiOperation(value = "获取小件单信息", httpMethod = "GET")
    @GetMapping("/getSmallProInfo")
    public R<List<SmallProInfo>> getSmallProInfo(@RequestParam("shouhouId") Integer shouhouId) {
        return shouhouExService.getSmallProInfo(shouhouId);
    }

    @ApiOperation(value = "根据小件单获取售后单", httpMethod = "GET")
    @GetMapping("/getBigProId")
    public R<Integer> getBigProId(@RequestParam("smallId") Integer smallId) {
        return shouhouExService.getBigProId(smallId);
    }

    @ApiOperation(value = "根据手机号码查询预约单信息", httpMethod = "GET")
    @GetMapping("/getYuYueListByMobile")
    public R<List<ShouhouYuYueBasicInfo>> getYuYueListByMobile(@RequestParam("mobile") String mobile) {
        return shouhouExService.getYuYueListByMobile(mobile);
    }


    @ApiOperation(value = "获取接件提示文案信息", httpMethod = "GET")
    @GetMapping("/getSomeUsefulTipsWhenAfter")
    public R<String> getSomeUsefulTipsWhenAfter(@RequestParam(value = "imei", required = false) String imei) {
        return shouhouExService.getSomeUsefulTipsWhenAfter(imei);
    }

    /**
     * 接件时设备名称、规格是否支持修改
     *
     * @return
     */
    @ApiOperation(value = "接件时设备名称、规格是否支持修改", httpMethod = "GET")
    @GetMapping("/isSupportModifyProductInfoWhenReceive")
    public R<Boolean> isSupportModifyProductInfoWhenReceive() {
        return shouhouExService.isSupportModifyProductInfoWhenReceive();
    }

    /**
     * 取机成功后消息推送：记录会员与员工的关系
     * @param id
     * @return
     */
    @ApiOperation(value = "取机成功后消息推送：记录会员与员工的关系", notes = "请求参数售后单号", httpMethod = "GET")
    @GetMapping("/recordStaffAndMemberRelationship")
    public R<String> recordStaffAndMemberRelationship(@RequestParam(value = "id") Integer id) {
        //Shouhou shouhou = shouhouService.getById(id);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(id), MTableInfoEnum.SHOUHOU, id);
        if (shouhou == null) {
            return R.error("售后单不存在");
        }
        Integer ch999Id = Optional.ofNullable(userInfoClient.getCh999UserByUserName(shouhou.getWeixiuren()).getData()).map(Ch999UserVo::getCh999Id).orElse(null);
        if (CommenUtil.isNullOrZero(ch999Id)) {
            return R.error("获取维修人信息失败");
        }
        smsService.pushMemberScanBind(Math.toIntExact(shouhou.getUserid()), ch999Id, BusinessTypeEnum.AFTER_ORDER.getCode(), id, BusinessNodeEnum.BUSINESS_FINISHED.getCode());
        return R.success("操作成功");
    }

    /**
     * imei的维修记录
     * @param imei
     * @return
     */
    @ApiOperation(value = "imei的维修记录", notes = "请求参数imei", httpMethod = "GET")
    @GetMapping("/countRepairRecord")
    public R<Integer> countRepairRecord(String imei){
        return shouhouExService.countRepairRecord(imei);
    }

    /**
     * 取机成功后消息推送：记录会员与员工的关系
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "取机成功后消息推送：增值机业务推送", notes = "请求参数售后单号", httpMethod = "GET")
    @GetMapping("/valueAddedMachineNotice")
    public R<String> valueAddedMachineNotice(@RequestParam(value = "id") Integer id) {
        //Shouhou shouhou = shouhouService.getById(id);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(id), MTableInfoEnum.SHOUHOU, id);
        OaUserBO currentUser = currentRequestComponent.getCurrentStaffId();
        //增值机业务取机推送
        if (CommenUtil.isNotNullZero(shouhou.getOrderSource()) && CommenUtil.isNotNullZero(shouhou.getMkcId())) {
            R<String> valueR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.IN_WCF_HOST, currentUser.getXTenant());
            if (valueR.getCode() == ResultCode.SUCCESS && valueR.getData() != null) {
                String url = MessageFormat.format(UrlConstant.VALUE_ADDED_MACHINE_QU_JI_URL, valueR.getData());
                MachineValueAddedReq req = new MachineValueAddedReq();
                req.setMkcId(shouhou.getMkcId())
                        .setMkc_id(shouhou.getMkcId())
                        .setUserName(currentUser.getUserName());
                String jsonStr = JSON.toJSONString(req);
                CompletableFuture.runAsync(() -> HttpUtil.post(url, jsonStr));
            }
        }
        return R.success("操作成功");
    }

    /**
     *亏损是否需要备注
     * @param shouhouId
     * @return
     */
    @ApiOperation(value = "亏损是否需要备注", notes = "取机前调用", httpMethod = "GET")
    @GetMapping("/qujiLossNeedRemark")
    public R<Boolean> qujiLossNeedRemark(Integer shouhouId){
        return shouhouExService.qujiLossNeedRemark(shouhouId);
    }

    /**
     *处理维修配件默认标签
     * @param maxPpirceId
     * @return
     */
    @ApiOperation(value = "处理维修配件默认标签", notes = "处理维修配件默认标签", httpMethod = "GET")
    @GetMapping("/handleDeFaultLabel/v1")
    public R<Boolean> handleDeFaultLabel(@RequestParam("maxPpirceId") Integer maxPpirceId) {
        Boolean flag = shouhouExService.handleDeFaultLabel(maxPpirceId);
        if (flag) {
            return R.success("操作成功");
        }
        return R.error("操作失败");
    }

    /**
     * 判断是否是大疆维修订单
     * @param productId 产品ID
     * @param shouhouId 售后ID
     * @return 是否是大疆维修订单
     */
    @ApiOperation(value = "判断是否是大疆维修订单", notes = "判断是否是大疆维修订单", httpMethod = "GET")
    @GetMapping("/isDJIRepairOrder")
    public R<Boolean> isDJIRepairOrder(@RequestParam(value = "productId", required = false) Integer productId,
                                      @RequestParam(value = "shouhouId", required = false) Integer shouhouId) {
        return R.success(shouhouExtendService.isDJIRepairOrder(productId, shouhouId));
    }

    /**
     * 判断是否应该跳过成本校验
     * 满足任意条件不进行校验：
     * （1）条件①：维修单内只有：服务工时费（硬件）[ppid：420051]，或 拆机调试与主板进液清洗 [ppid：414747] 商品
     * （2）条件②：维修单内只有：维修耗材（分类id：801）商品（但不能超过2个）
     * （3）条件③：维修单内既有条件①，也有条件②的商品，且没有其他商品
     * @param shouhouId 维修单ID
     * @return true-跳过成本校验，false-需要进行成本校验
     */
    @ApiOperation(value = "判断是否应该跳过成本校验", notes = "判断维修单是否应该跳过成本校验", httpMethod = "GET")
    @GetMapping("/shouldBypassCostCheck")
    public R<Boolean> shouldBypassCostCheck(@RequestParam(value = "shouhouId") Integer shouhouId) {
        if (shouhouId == null || shouhouId <= 0) {
            return R.error("维修单ID不能为空或无效");
        }
        Boolean result = shouhouExService.shouldBypassCostCheck(shouhouId);
        return R.success("查询成功", result);
    }
}

