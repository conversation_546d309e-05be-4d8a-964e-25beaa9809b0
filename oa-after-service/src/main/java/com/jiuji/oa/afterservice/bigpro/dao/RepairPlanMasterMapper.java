package com.jiuji.oa.afterservice.bigpro.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlanMaster;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 维修方案主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Mapper
public interface RepairPlanMasterMapper extends BaseMapper<RepairPlanMaster> {

    /**
     * 根据故障ID查询售后单ID
     * @param faultId 故障ID
     * @return 售后单ID
     */
    Integer getShouHouIdByFaultId(@Param("faultId") Integer faultId);

}