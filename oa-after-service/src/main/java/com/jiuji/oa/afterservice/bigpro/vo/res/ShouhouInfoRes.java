package com.jiuji.oa.afterservice.bigpro.vo.res;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.bo.discount.DiscountInfoBo;
import com.jiuji.oa.afterservice.bigpro.enums.RepairOrderTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.vo.InvoiceInfo;
import com.jiuji.oa.afterservice.bigpro.vo.ShouHouRiskNotificationVO;
import com.jiuji.oa.afterservice.bigpro.vo.WeixiuzuKindVo;
import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouAddressInfo;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 售后维修表单信息
 *
 * @author: gengjiaping
 * @date: 2020/3/31
 */
@Data
public class ShouhouInfoRes extends Shouhou {

    /**
     * 员工价剩余次数
     */
    private Integer employeePriceRemainingTimes;




    /**
     * 维修单类型
     * 现货
     * 退货
     * 换货
     * 维修
     * @see RepairOrderTypeEnum
     */
    private String repairOrderType;

    /**
     * 维修单单据类型 1 大疆维修单 0 普通维修单
     */
    private Integer orderType;

    /**
     * 售后风险
     */
    private List<ShouHouRiskNotificationVO> shouHouRiskNotificationVOList;
    /**
     * 发票信息
     */
    private InvoiceInfo invoiceInfo;

    /**
     * 是否历史订单
     */
    private Boolean isHistory;
    /**
     * 是否修改串号
     */
    private Boolean whetherFixImei;
    /**
     * 原因
     */
    private List<String> reasonList;
    /**
     * 预约来源
     */
    private Integer yuYueFromSource;

    /**
     * 展示改价按钮
     */
    private Boolean displayPriceChange;

    /**
     * 员工改价信息
     */
    private UpdatePriceInfoRes updatePriceInfo;

    private List<LockWxpjBo> wxpjList;

    @ApiModelProperty(value = "附件")
    private List<FileReq> files;

    @ApiModelProperty(value = "附件是否是新版的")
    private Boolean isNew;

    @ApiModelProperty(value = "维修配件")
    private List<HexiaoBo> hexiaoTabs;
    @ApiModelProperty(value = "会员等级")
    private Integer userClass;
    @ApiModelProperty(value = "会员等级名称")
    private String userClassName;
    @ApiModelProperty(value = "内部员工姓名")
    private String ch999User;

    /**
     * 是否为内部员工
     */
    private Boolean isStaffSub;
    /**
     * 职务名称
     */
    private String zhiwu;
    /**
     * 学生认证状态
     */
    private Integer studentCertificationStatus;
    @ApiModelProperty(value = "当前所在地")
    private String nowarea;
    @ApiModelProperty(value = "当前所在地id")
    private Integer nowareaId;
    @ApiModelProperty(value = "维修绑定url")
    private String wxBindUrl;
    @ApiModelProperty(value = "是否新机单")
    private String isxinjidan;
    @ApiModelProperty(value = "出险服务名称")
    private String serviceName;
    /**
     *shouHouId<newTestShouhouId 走原始逻辑 - 0
     *newTestShouhouId<shouHouId<jdxTestShouhouId 走友奎版逻辑 - 1
     *jdxTestShouhouId<shouHouId 走机大侠逻辑 - 2
     * 测试使用方式
     */
    private Integer testType;

    private Integer areaZh;
    private String areaZhName;
    private Integer toareaZh;
    private String toareaZhText;
    private String toareaZhName;
    @ApiModelProperty(value = "退换类别")
    private Integer tuihuanKind;
    @ApiModelProperty(value = "退换类别描述")
    private String tuihuanKindText;

    @ApiModelProperty(value = "重大办")
    private ShouHouImportantBo zdb;

    @ApiModelProperty(value = "已(未)关注")
    private Boolean isSubCollect;

    @ApiModelProperty(value = "是否提醒")
    private Boolean isTx;

    @ApiModelProperty(value = "售后提醒")
    private ShouhouTixing shouhouTx;

    @ApiModelProperty(value = "订单是否存在接待记录")
    private Boolean isSubReception;

    @ApiModelProperty(value = "容量升级物流信息")
    private ShouhouRomUpgrade expressInfo;

    @ApiModelProperty(value = "区域对象")
    private AreaInfo areaSubject;

    @ApiModelProperty(value = "区域对象(可以为空)")
    private AreaInfo areaInfoOne;

    @ApiModelProperty(value = "中邮送修人客户信息")
    private ShouhouBusinessinfo bItem;

    @ApiModelProperty(value = "维修单赠品")
    private ShouHouZengpinBo zengPin;

    @ApiModelProperty(value = "维修单赠品1")
    private ShouHouZengpinBo zengPin1;

    @ApiModelProperty(value = "取机审核信息")
    private String checkInfo;

    @ApiModelProperty(value = "判断取机审核")
    private String qjCheck;

    @ApiModelProperty(value = "库存ppid")
    private List<Integer> kcOutPpids;

    @ApiModelProperty(value = "服务出险记录")
    private List<ShouhouServiceOutBo> serviceOutInfo;

    @ApiModelProperty(value = "维修组列表")
    private List<WeixiuzuKindVo> wxzKind;

    @ApiModelProperty(value = "最后一条测试数据信息")
    private ShouhoutestInfo testInfo;
    /**
     * 最新测试记录
     */
    private ShouhouTestResultInfo shouhouTestResultInfo;
    /**
     * 是否新版测试流程
     */
    private Boolean isNewTestProcess;
    /**
     * 是否显示修后测试
     */
    private Boolean isShowAfterRepairTest;
    /**
     * 是否显示修前测试
     */
    private Boolean isShowBeforeRepairTest;

    @ApiModelProperty(value = "所有故障类型")
    private List<ShouhouTroubleListRes> troubleList;

    @ApiModelProperty(value = "选中troubleId")
    private List<Integer> troubleIds;

    @ApiModelProperty(value = "files2")
    List<FileReq> attachments;

    @ApiModelProperty(value = "服务展示类型")
    private Integer fuwuShowType;

    @ApiModelProperty(value = "fuwuDR")
    private ShouhouFuwuDrRes fuwuDR;

    @ApiModelProperty(value = "维修机型配置")
    private WxConfigRes wxconfig;

    @ApiModelProperty(value = "处理进程添加处发送sms")
    private String mSms7;

    @ApiModelProperty(value = "带有部门的售后日志")
    private List<ShouhouLogBo> shlogs;

    @ApiModelProperty(value = "购买售后服务")
    private List<ShouhouServiceConfig> configList;

    @ApiModelProperty(value = "统计金额信息")
    private HexiaoSum hexiaoSum;

    @ApiModelProperty(value = "费用信息（文本信息）")
    private String feiyongInfoText;

    @ApiModelProperty(value = "应付费用（金额处理）")
    private BigDecimal yingfuM;

    @ApiModelProperty(value = "超时天数计算（前端使用）")
    private Long offDays;

    @ApiModelProperty(value = "售后渠道信息")
    private ShouhouQudao qudaoInfo;

    @ApiModelProperty(value = "isquji_")
    private Integer isqujiE;


    //todo 以下字段在提交时需要前端传值,联调时需要注意
    @ApiModelProperty(value = "C# 1268，1631行赋值（submit）")
    private Integer isWaiguan;

    @ApiModelProperty(value = "串号修改，新串号值（submit）")
    private String imei1;

    @ApiModelProperty(value = "C# 3246行赋值（submit）")
    private Boolean isWeb;

    @ApiModelProperty("预约地址相关")
    private ShouhouAddressInfo addressInfo;

    @ApiModelProperty(value = "内部附件（submit）")
    private List<FileReq> files2;
    /**
     * 会员实时的用户名称(关联bbs_user获取)
     */
    @ApiModelProperty("用户名(新)")
    private String username2;

    @ApiModelProperty("用户手机号，二维码使用")
    private String userMobile;

    private Boolean printFlag = false;

    @ApiModelProperty("用户手机号归属地")
    private String userPhoneAddress;

    @ApiModelProperty(value = "维修前后附件信息")
    private List<FileReq> files3;

    /**
     * 折叠屏幕新加文件
     */
    private List<FileReq> filesFoldScreen;

    /**
     * 是否二维码加单
     */
    @ApiModelProperty("是否二维码加单")
    private Boolean isCodeSub;
    /**
     * 折扣信息
     */
    private DiscountInfoBo discountInfo;
    /**
     * 最后一次抖音团购优惠使用记录信息
     */
    private DouYinCouponLogRes lastDouYinCouponLog;

    /**
     * 抖音团购开关
     */
    private Boolean douyinGoupSwitch;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 商品类型描述
     */
    private String productTypeMsg;

    /**
     * 0 不需要上传  1 活动合同上传
     */
    private Integer uploadedContractType;


    @Data
    public static class HexiaoSum {
        @ApiModelProperty(value = "总价")
        private BigDecimal allPrice;

        @ApiModelProperty(value = "已优惠额度")
        private BigDecimal yhPrice;

        @ApiModelProperty(value = "剩余可优惠额度")
        private String syYouhuiM;
    }

    @Data
    public static class ShouhouQudao {
        @ApiModelProperty(value = "shqdid")
        @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
        private Integer shqdId;

        @ApiModelProperty(value = "shqd2id")
        private Integer shqd2Id;

        @ApiModelProperty(value = "shqd2name")
        private String shqd2Name;


        private String quDaoInUser;

        private LocalDateTime qudaoAddTime;

        private Integer insourceid;

    }
    @ApiModelProperty(value = "是否是九机服务出险标识")
    private Boolean isJiujiServiceFlag;

    @ApiModelProperty(value = "是否是后端员工")
    private Boolean isBackendStaff = Boolean.FALSE;

    @ApiModelProperty(value = "企业粉丝加粉状态")
    private Boolean isCorporateFans;

    @ApiModelProperty(value = "是否可以退换机")
    private Boolean isCanExchange;

    @ApiModelProperty(value = "退换机提示消息")
    private String exchangeMsg;
    /**
     * （2021年9月1日-2022年2月15日期间iPhone5—iPhone 11全系列设备维修，电池享两年质保，屏幕享180天人为损坏半价更换一次，请注意核实）
     */
    @ApiModelProperty(value = "文案标记")
    private String markCopy;

    /**
     * 是否显示风险告知书按钮
     */
    private Boolean isShowRiskNotice;

    /**
     * 返修单id
     */
    private String currentRepairOrderIds;

    public String toMd5Hex(){
        return DigestUtil.md5Hex(JSON.toJSONString(this));
    }

}
