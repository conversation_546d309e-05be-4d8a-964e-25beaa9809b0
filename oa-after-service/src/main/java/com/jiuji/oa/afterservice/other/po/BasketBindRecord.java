package com.jiuji.oa.afterservice.other.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 此表不同步迁移历史库
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("basketBindRecord")
public class BasketBindRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField(value = "basket_id")
    private Integer basketId;
    @TableField(value = "imei")
    private String imei;
    @TableField(value = "binddate")
    private LocalDateTime binddate;
    @TableField(value = "binduser")
    private String binduser;
    /**
     * @see BasketBindTypeEnum
     */
   @TableField(value = "bindType")
    private Integer bindType;
}
