package com.jiuji.oa.afterservice.smallpro.service.impl;

import com.jiuji.oa.afterservice.bigpro.dao.ProductinfoMapper;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.BasketSmallCountBo;
import com.jiuji.oa.afterservice.smallpro.dao.SmallLeWuMapper;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproBillMapper;
import com.jiuji.oa.afterservice.smallpro.service.SmallLeWuService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.SignConstant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 小件乐物特殊操作
 * <AUTHOR>
 * @since 2021/9/22 10:50
 */
@Service
public class SmallLeWuServiceImpl implements SmallLeWuService {
    @Resource
    private ProductinfoMapper productinfoMapper;
    @Resource
    private SmallLeWuMapper smallLeWuMapper;
    @Resource
    private SmallproBillMapper smallproBillMapper;
    /**乐物ppid集合*/
    private static final Set<Integer> LE_WU_PPIDS = Arrays.stream(new Integer[]{117_462,95144,154_874,154_876}).collect(Collectors.toSet());
    /**原装苹果充电器*/
    private static final Integer ORIGINAL_IPHONE_CHARGER = 96280;

    /**
     *
     * @param basketSmallBills 小件接件id
     * @return
     */
    public R<List<Productinfo>> listExchangeProduct(List<BasketSmallCountBo> basketSmallBills){
        //过滤出所有的乐物充电器
        List<BasketSmallCountBo> leWuList = basketSmallBills.stream().filter(spb -> LE_WU_PPIDS.contains(spb.getPpriceid())).collect(Collectors.toList());
        if(leWuList.isEmpty()){
            return R.success(Collections.emptyList());
        }
        List<BasketSmallCountBo> exchangeTimes = smallLeWuMapper.countExchange(leWuList.stream().map(BasketSmallCountBo::getBasketId).collect(Collectors.toSet()));
        //换货次数大于1的ppid集合
        Set<Integer> canChangeIphonePpids = exchangeTimes.stream().filter(bsc -> DecideUtil.isNull(bsc.getSmallCount(), 0) > 1)
                .map(BasketSmallCountBo::getPpriceid).collect(Collectors.toSet());
        if(!canChangeIphonePpids.isEmpty()){
            //可以换原装iphone的ppid
            canChangeIphonePpids.add(ORIGINAL_IPHONE_CHARGER);
            List<Productinfo> productinfos = productinfoMapper.getProductListByPpids(canChangeIphonePpids.stream().collect(Collectors.toList()));
            //商品排序
            productinfos.sort(Comparator.comparing(pi-> IntStream.range(0,basketSmallBills.size())
                    //原商品信息,按提交过来的顺序排序
                    .filter(i->Objects.equals(basketSmallBills.get(i).getPpriceid(),pi.getPpriceid())).findFirst()
                    //苹果原装排在最后
                    .orElse(basketSmallBills.size())));
            R<List<Productinfo>> result = R.success(productinfos);
            result.put("showMsg", String.format("根据采销政策，客户的%s商品已有两次以上换货记录，建议给客户更换以下商品", productinfos.stream()
                    .filter(pi->!Objects.equals(pi.getPpriceid(),ORIGINAL_IPHONE_CHARGER)).map(Productinfo::getProductName)
                    .collect(Collectors.joining(SignConstant.COMMA))));
            //换货商品是两个以上,进行提示,禁止选择
            if (basketSmallBills.size()>1){
                result.put("readonly",true);
                result.put("warnMsg","注意：该商品只能进行单个换货不支持多选， 请对单个商品进行接件才能选择换货商品");
            }
            return result;
        }
        return R.success(Collections.emptyList());
    }
}
