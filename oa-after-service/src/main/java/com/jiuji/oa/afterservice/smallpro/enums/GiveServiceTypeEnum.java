package com.jiuji.oa.afterservice.smallpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum GiveServiceTypeEnum implements CodeMessageEnumInterface {

    /**
     * 赠送年包服务 : 1  年包赠送出险 2  年包赠送质保换新
     */
    ONE(1,"年包赠送出险"),
    TWO(2,"年包赠送质保换新");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;
}
