package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.bo.ServiceFeeBO;
import com.jiuji.oa.afterservice.bigpro.dao.ServiceFeeConfigMapper;
import com.jiuji.oa.afterservice.bigpro.entity.ServiceFeeConfig;
import com.jiuji.oa.afterservice.bigpro.enums.MaintenanceLevelEnum;
import com.jiuji.oa.afterservice.bigpro.service.ServiceFeeConfigService;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.tc.utils.common.CommonUtils;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ServiceFeeConfigServiceImpl extends ServiceImpl<ServiceFeeConfigMapper, ServiceFeeConfig> implements ServiceFeeConfigService {



    @Override
    public Map<Integer, ServiceFeeBO> selectServiceFeeByPpidList(Set<Integer> ppidList) {
        if (CollUtil.isEmpty(ppidList)) {
            return new HashMap<>();
        }
        List<ServiceFeeBO> serviceFeeBOS = CommonUtils.bigDataInQuery(ppidList, ids -> this.baseMapper.selectServiceFeeByPpidList(ids));
        if (CollUtil.isEmpty(serviceFeeBOS)) {
            return new HashMap<>();
        }
        return serviceFeeBOS.stream().collect(Collectors.toMap(ServiceFeeBO::getPpriceid, Function.identity(), (n1, n2) -> n2));
    }

    /**
     * 获取配置了员工报价的分类
     * @return
     */
    @Override
    public Set<Integer> selectEmployeePrice() {
        Set<Integer> set = new HashSet<>();
        List<Integer> cidList = this.lambdaQuery()
                .ne(ServiceFeeConfig::getMaintenanceLevel, MaintenanceLevelEnum.NOT_GRABBING.getCode())
                .list()
                .stream().map(ServiceFeeConfig::getCid).collect(Collectors.toList());
        //查询相关子分类
        cidList.forEach(cid -> {
            List<Integer> childrenCidList = this.baseMapper.selectChildrenCid(cid);
            if(CollUtil.isNotEmpty(childrenCidList)){
                set.addAll(childrenCidList);
            }
        });
        return set;
    }
}
