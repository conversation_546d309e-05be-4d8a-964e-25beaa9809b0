package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.jiuji.oa.afterservice.api.bo.AddModel;
import com.jiuji.oa.afterservice.api.bo.GetMemberPointExchangeCoupon;
import com.jiuji.oa.afterservice.apollo.ApolloEntity;
import com.jiuji.oa.afterservice.bigpro.bo.RedeemContext;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.PointExchangeMaxCouponReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.rabbitmq.SmallproRabblitMq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.jiuji.tc.common.vo.R;

@Slf4j
@Service
public class RedeemServiceImpl implements RedeemService {


    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private ApolloEntity apolloEntity;
    @Resource
    private ShouhouService shouhouService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private ShouhouExService shouhouExService;
    @Resource
    private NumberCardService numberCardService;
    @Resource
    private BbsxpUsersService bbsxpUsersService;


    /**
     * 根据门店判断是否启用积分兑换券
     * @return
     */
    @Override
    public Boolean isUseRedeem() {
        if(XtenantJudgeUtil.isJiujiMore()){
            OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("请登录"));
            Integer areaId = userBO.getAreaId();
            String redeemAreaId = Optional.ofNullable(apolloEntity.getRedeemAreaId()).orElse("");
            //     * 第一：如果过为空那就是全部门店使用，
            //     * 第二：如果存在具体门店id那就这几个门店id使用
            //     * 第三：如果只存在-1 那就是所有门店不使用
            List<Integer> areaIds = Arrays.stream(redeemAreaId.split(",")).filter(StringUtils::isNotEmpty).map(Integer::valueOf).collect(Collectors.toList());
            if(CollUtil.isEmpty(areaIds)){
                return Boolean.TRUE;
            } else if(areaIds.contains(areaId)){
                return Boolean.TRUE;
            } else if(areaIds.contains(-1)){
                return Boolean.FALSE;
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public PointExchangeMaxCouponRes pointExchangeMaxCoupon(PointExchangeMaxCouponReq req) {
        PointExchangeMaxCouponRes pointExchangeMaxCouponRes = new PointExchangeMaxCouponRes();
        //如果不开启那就直接不查询
        if(!isUseRedeem()){
            return pointExchangeMaxCouponRes;
        }
        Shouhou shouhou = Optional.ofNullable(shouhouService.getById(req.getShouHouId())).orElse(null);
        if(ObjectUtil.isNull(shouhou) || ObjectUtil.isNull(shouhou.getUserid())){
            return pointExchangeMaxCouponRes;
        }
        //获取积分兑换券信息（根据维修单会员）
        List<GetMemberPointExchangeCoupon> memberPointExchangeCouponList = getMemberPointExchangeCouponList(Convert.toInt(shouhou.getUserid()));
        if(CollUtil.isEmpty(memberPointExchangeCouponList)){
            return pointExchangeMaxCouponRes;
        }
        //按照价格进行倒叙 （这样获取第一个的时候就是获取优惠力度最大的优惠码）
        memberPointExchangeCouponList = memberPointExchangeCouponList.stream()
            .sorted(Comparator.comparing(c -> Optional.ofNullable(c.getPrice()).orElse(BigDecimal.ZERO), Comparator.reverseOrder()))
            .collect(Collectors.toList());
        List<String> couponRuleList = memberPointExchangeCouponList.stream().map(GetMemberPointExchangeCoupon::getCouponRule).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        //获取规则码限制信息
        Map<String, AddModel> addModelMap = getAddModelMap(couponRuleList);
        GetMemberPointExchangeCoupon memberPointExchangeCoupon = memberPointExchangeCouponList.stream()
                .filter(item -> isCanUseCoupon(item.getCouponRule(), addModelMap, shouhou)).findFirst()
                .orElse(null);
        //如果没有合适的直接返回
        if(ObjectUtil.isNull(memberPointExchangeCoupon)){
            return pointExchangeMaxCouponRes;
        }
        pointExchangeMaxCouponRes.setCanExchange(true);
        pointExchangeMaxCouponRes.setPrice(memberPointExchangeCoupon.getPrice());
        pointExchangeMaxCouponRes.setPoint(memberPointExchangeCoupon.getPoint());
        pointExchangeMaxCouponRes.setExchangeUrl(memberPointExchangeCoupon.getExchangeUrl());
        pointExchangeMaxCouponRes.setCouponRule(memberPointExchangeCoupon.getCouponRule());
        pointExchangeMaxCouponRes.setLimitPrice(memberPointExchangeCoupon.getLimitPrice());
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.M_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String subDetailUrl = String.format("%s/after-service/detail/%s?showCoupons=1", host, shouhou.getId());
        pointExchangeMaxCouponRes.setSubDetailUrl(subDetailUrl);
        return pointExchangeMaxCouponRes;
    }

    /**
     * 判断积分兑换券是否可用
     * @param couponRule
     * @param addModelMap
     * @param shouhou
     * @return
     */
    private boolean isCanUseCoupon(String couponRule, Map<String, AddModel> addModelMap, Shouhou shouhou) {
        RedeemContext.clear();
        RedeemContext.clearFilterReason();
        AddModel addModel = addModelMap.get(couponRule);
        if (ObjectUtil.isNotNull(addModel)) {
            //如果是 limitids 为空那就说明 这个是通用券，不可以使用
            if(StrUtil.isEmpty(addModel.getLimitids())){
                if(ObjectUtil.isNotNull(RedeemContext.getFilterReason())){
                    Map<String, Object> filterReason = RedeemContext.getFilterReason();
                    filterReason.put(couponRule, "通用优惠码过滤");
                    RedeemContext.setFilterReason(filterReason);
                }
                return Boolean.FALSE;
            }
            NumberCard numberCard = new NumberCard();
            numberCard.setCardID("-1");
            numberCard.setTotal(Convert.toBigDecimal(addModel.getTotal()));
            numberCard.setAreas(addModel.getAreas());
            numberCard.setCh999Id(Convert.toInt(addModel.getCh999_id(), 0));
            //开始时间和结束时间需要计算一下
            LocalDate now = LocalDate.now();
            if(Optional.ofNullable(addModel.getAddcanuse()).orElse(Boolean.FALSE)){
                numberCard.setEndTime(now.plusDays(Convert.toInt(addModel.getExpiredays())));
                numberCard.setStartTime(now.plusDays(Convert.toInt(addModel.getStartOffset())));
            } else {
                numberCard.setEndTime( DateUtil.parse(addModel.getEndtime()).toSqlDate().toLocalDate());
                numberCard.setStartTime( DateUtil.parse(addModel.getStarttime()).toSqlDate().toLocalDate());
            }
            numberCard.setAddTime(now);
            numberCard.setGName(addModel.getGname());
            numberCard.setLimintClint(Convert.toInt(addModel.getLimintClint()));
            numberCard.setLimit(Convert.toInt(addModel.getLimit()));
            numberCard.setLimit1(Convert.toInt(addModel.getLimit1()));
            numberCard.setLimit2(Convert.toBool(addModel.getLimit2()));
            numberCard.setLimitids(addModel.getLimitids());
            numberCard.setLimitprice(Convert.toBigDecimal(addModel.getLimitprice()));
            numberCard.setLimitType(Convert.toInt(addModel.getLimitType()));
            numberCard.setExcludePpIds(addModel.getExcludePpIds());
            numberCard.setTakeMethod(Convert.toInt(addModel.getTakeMethod()));
            //积分兑换券进行 查询校验 （因为原始代码优惠码的校验分在两块  查询和提交的时候）
            TempNumberCard tempNumberCard = toTemp(addModel,shouhou);
            RedeemContext.setTempNumberCardList(Collections.singletonList(tempNumberCard));
            R<List<NumberCardRes>> kxYouhuimas = numberCardService.getKxYouhuimas(shouhou.getUserid(), shouhou.getIssoft(), shouhou.getId());
            List<String> ruleCodeList = new ArrayList<>();
            if(kxYouhuimas.isSuccess() && CollUtil.isNotEmpty(kxYouhuimas.getData())){
                ruleCodeList  = kxYouhuimas.getData().stream().filter(v->NumberConstant.ONE.equals(Optional.ofNullable(v.getStats()).orElse(NumberConstant.ONE)))
                        .map(NumberCardRes::getRulecode).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
            }
            if(!ruleCodeList.contains(tempNumberCard.getRulecode())){
                if(ObjectUtil.isNotNull(RedeemContext.getFilterReason())){
                    Map<String, Object> filterReason = RedeemContext.getFilterReason();
                    filterReason.put(couponRule, "查询过滤");
                    RedeemContext.setFilterReason(filterReason);
                }
                return Boolean.FALSE;
            }
            R<Boolean> youhuiMa = shouhouExService.isUseShouHouYouhuiMa(shouhou, numberCard);
            if(!youhuiMa.isSuccess()){
                if(ObjectUtil.isNotNull(RedeemContext.getFilterReason())){
                    Map<String, Object> filterReason = RedeemContext.getFilterReason();
                    filterReason.put(couponRule, youhuiMa.getUserMsg());
                    RedeemContext.setFilterReason(filterReason);
                }
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
           // return ruleCodeList.contains(tempNumberCard.getRulecode()) && shouhouExService.isUseShouHouYouhuiMa(shouhou, numberCard).isSuccess();
        }
        return Boolean.FALSE;
    }

    private TempNumberCard toTemp(AddModel addModel, Shouhou shouhou) {
        TempNumberCard tempNumberCard = new TempNumberCard();
        tempNumberCard.setGName(addModel.getGname());
        tempNumberCard.setTotal(Convert.toInt(addModel.getTotal(), 0));
        tempNumberCard.setLimitPrice(Convert.toBigDecimal(addModel.getLimitprice()));
        tempNumberCard.setLimit(Convert.toInt(addModel.getLimit()));
        tempNumberCard.setLimit1(Convert.toInt(addModel.getLimit1()));
        tempNumberCard.setLimit2(Convert.toInt(addModel.getLimit2(), 0));
        tempNumberCard.setLimitIds(addModel.getLimitids());
        tempNumberCard.setCh999Id(Convert.toInt(addModel.getCh999_id(), 0));
        tempNumberCard.setLimitType(Convert.toInt(addModel.getLimitType()));
        tempNumberCard.setExcludePpIds(addModel.getExcludePpIds());
        tempNumberCard.setRulecode(addModel.getRulecode());
        tempNumberCard.setUserId(Convert.toInt(shouhou.getUserid()));
        tempNumberCard.setTeamId(addModel.getTeam_id());
        tempNumberCard.setIsDel(Boolean.FALSE);
        tempNumberCard.setState(0);
        LocalDate now = LocalDate.now();
        tempNumberCard.setAddTime(now);
        if(Optional.ofNullable(addModel.getAddcanuse()).orElse(Boolean.FALSE)) {
            tempNumberCard.setStartTime(now.plusDays(Convert.toInt(addModel.getStartOffset())));
            tempNumberCard.setEndTime(now.plusDays(Convert.toInt(addModel.getExpiredays())));
        }else {
            tempNumberCard.setEndTime( DateUtil.parse(addModel.getEndtime()).toSqlDate().toLocalDate());
            tempNumberCard.setStartTime( DateUtil.parse(addModel.getStarttime()).toSqlDate().toLocalDate());
        }
        return tempNumberCard;
    }

    /**
     * 主站判断规则码是可以使用
     * @param req
     * @return
     */
    @Override
    public List<CanUseCouponOutputRes> CanUseCoupon(CanUseCouponOutputReq req) {
        List<String> couponRuleList = req.getCouponRuleList();
        if(CollUtil.isEmpty(couponRuleList)){
            return Collections.emptyList();
        }
        Shouhou shouhou = Optional.ofNullable(shouhouService.getById(req.getShouHouId())).orElse(null);
        if(ObjectUtil.isNull(shouhou) || ObjectUtil.isNull(shouhou.getUserid())){
            return Collections.emptyList();
        }
        // 模拟登录 系统用户信息
        simulateLogin(shouhou.getAreaid());
        Map<String, AddModel> addModelMap = getAddModelMap(couponRuleList);
        return couponRuleList.stream().map(item -> {
            CanUseCouponOutputRes canUseCouponOutputRes = new CanUseCouponOutputRes();
            //判断规则码是否可以使用
            boolean canUseCoupon = isCanUseCoupon(item, addModelMap, shouhou);
            canUseCouponOutputRes.setCanUse(canUseCoupon);
            canUseCouponOutputRes.setDiyCoupon(item);
            return canUseCouponOutputRes;
        }).collect(Collectors.toList());
    }

    @Override
    public MemberPointExchangeRes getMemberPointExchangeCouponByUserId(MemberPointExchangeCouponReq req) {
        MemberPointExchangeRes pointExchangeRes = new MemberPointExchangeRes();
        //如果不开启那就直接不查询
        if(!isUseRedeem()){
            return pointExchangeRes;
        }
        Shouhou shouhou = Optional.ofNullable(shouhouService.getById(req.getShouHouId())).orElse(null);
        if(ObjectUtil.isNull(shouhou) || ObjectUtil.isNull(shouhou.getUserid())){
            return pointExchangeRes;
        }
        //获取积分兑换券信息（根据维修单会员）
        List<MemberPointExchangeCouponModel> memberPointExchangeCouponModels = exchangeByUserId(Convert.toInt(shouhou.getUserid()));
        if(CollUtil.isEmpty(memberPointExchangeCouponModels)){
            return pointExchangeRes;
        }
        List<String> couponRuleList = memberPointExchangeCouponModels.stream().map(MemberPointExchangeCouponModel::getRuleCode).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        //获取规则码限制信息
        Map<String, AddModel> addModelMap = getAddModelMap(couponRuleList);
        List<MemberPointExchangeCouponModel> collect = memberPointExchangeCouponModels.stream().filter(item -> isCanUseCoupon(item.getRuleCode(), addModelMap, shouhou)).collect(Collectors.toList());
        pointExchangeRes.setList(collect);
        //会员积分获取
        Long userid = shouhou.getUserid();
            bbsxpUsersService.lambdaQuery().eq(BbsxpUsers::getId, Convert.toInt(userid)).select(BbsxpUsers::getPoints)
                .list().stream().findFirst().ifPresent(item->{
                    pointExchangeRes.setPoints(item.getPoints());
            });
        return pointExchangeRes;
    }


    private List<NumberCard> getNumberCards(Shouhou shouhou,Long userId){
        R<List<NumberCardRes>> kxYouhuimas = numberCardService.getKxYouhuimas(userId, shouhou.getIssoft(), shouhou.getId());
        if (!kxYouhuimas.isSuccess() || CollUtil.isEmpty(kxYouhuimas.getData())) {
            return Collections.emptyList();
        }
        List<String> cardIDList = kxYouhuimas.getData().stream().map(NumberCardRes::getCardID).collect(Collectors.toList());
        List<NumberCard> numberCards = CommonUtils.bigDataInQuery(cardIDList, ids -> numberCardService.lambdaQuery().in(NumberCard::getCardID, ids).list());
        return numberCards.stream().filter(item->shouhouExService.isUseShouHouYouhuiMa(shouhou, item).isSuccess()).collect(Collectors.toList());
    }

    @Override
    public List<YouHuiMaBySubRes> getYouHuiMaBySub(YouHuiMaBySubReq req) {
        Shouhou shouhou = shouhouService.getById(req.getSubId());
        // 模拟登录 系统用户信息
        simulateLogin(shouhou.getAreaid());
        List<NumberCard> numberCards = getNumberCards(shouhou,Convert.toLong(req.getUserId()));
        return numberCards.stream().map(obj->{
                    YouHuiMaBySubRes youHuiMaBySubRes = new YouHuiMaBySubRes();
                    youHuiMaBySubRes.setCardId(obj.getCardID());
                    youHuiMaBySubRes.setCardIdShow(obj.getCardID()); // Same as CardID
                    youHuiMaBySubRes.setEndTime(obj.getEndTime() != null ? obj.getEndTime().toString() : null);
                    youHuiMaBySubRes.setStartTime(obj.getStartTime() != null ? obj.getStartTime().toString() : null);
                    youHuiMaBySubRes.setState(obj.getState() != null ? obj.getState().toString() : null);
                    youHuiMaBySubRes.setAreaIds(obj.getAreaids());
                    youHuiMaBySubRes.setCh999Id(obj.getCh999Id());
                    youHuiMaBySubRes.setGname(obj.getGName());
                    youHuiMaBySubRes.setId(obj.getId());
                    youHuiMaBySubRes.setIsNewBuy(false); // Default value, adjust if needed
                    youHuiMaBySubRes.setIsdjq(obj.getIsdjq() != null ? obj.getIsdjq().toString() : null);
                    youHuiMaBySubRes.setLimintClint(obj.getLimintClint() != null ? obj.getLimintClint().toString() : null);
                    youHuiMaBySubRes.setLimintClintShow(obj.getLimintClint() != null ? obj.getLimintClint().toString() : null); // Same as limintClint
                    youHuiMaBySubRes.setLimit(obj.getLimit());
                    youHuiMaBySubRes.setLimit1(obj.getLimit1());
                    youHuiMaBySubRes.setLimit2(obj.getLimit2());
                    youHuiMaBySubRes.setLimitType(obj.getLimitType());
                    youHuiMaBySubRes.setLimitWay(obj.getLimitWay() != null ? obj.getLimitWay().toString() : null);
                    youHuiMaBySubRes.setLimitids(obj.getLimitids());
                    youHuiMaBySubRes.setLimitprice(obj.getLimitprice() != null ? obj.getLimitprice().doubleValue() : null);
                    youHuiMaBySubRes.setNames(obj.getGName());
                    //直接返回可以用 因为所有的过滤逻辑已经判断好了（走到这里的优惠券就是可以用的）
                    youHuiMaBySubRes.setStats(NumberConstant.ONE);
                    youHuiMaBySubRes.setTakeMethod(obj.getTakeMethod() != null ? obj.getTakeMethod().toString() : null);
                    youHuiMaBySubRes.setTakeMethodShow(obj.getTakeMethod() != null ? obj.getTakeMethod().toString() : null); // Same as takeMethod
                    youHuiMaBySubRes.setTotal(obj.getTotal() != null ? obj.getTotal().doubleValue() : null);
                    youHuiMaBySubRes.setType(obj.getCh999Id()); // No direct mapping found
                    youHuiMaBySubRes.setExcludePpIds(obj.getExcludePpIds());
                    youHuiMaBySubRes.setInput(obj.getInput());
                    youHuiMaBySubRes.setOldRuleCode(obj.getOldRuleCode());
                    youHuiMaBySubRes.setUserid(obj.getUserid());
                    youHuiMaBySubRes.setFirstOwnerId(obj.getFirstOwnerId());
                    youHuiMaBySubRes.setIsSelfUnavailable(obj.getIsSelfUnavailable());
                    youHuiMaBySubRes.setRewardPoints(obj.getRewardPoints());
                    return youHuiMaBySubRes;
                })
                .collect(Collectors.toList());
    }

    /**
     * 模拟登录
     * @param shouhou
     */
    @Override
    public void simulateLogin(Integer areaId) {
        AreaInfo currAreaInfo = CommonUtils.getResultData(SpringUtil.getBean(AreaInfoClient.class).getAreaInfoById(areaId), userMsg -> {
            throw new CustomizeException(StrUtil.format("获取org门店信息[{}]异常", userMsg));
        });
        OaUserBO user = SpringUtil.getBean(SmallproRabblitMq.class).simulateUser(currAreaInfo, "系统");
        SpringContextUtil.getRequest().ifPresent(request ->  request.setAttribute(RequestAttrKeys.REQUEST_ATTR_OA_USER, user));
    }

    @Override
    public List<GetPreferentialRecommendationOutputRes> getPreferentialRecommendation(Integer shouHouId) {
        Shouhou shouhou = Optional.ofNullable(shouhouService.getById(shouHouId)).orElseThrow(()->new CustomizeException("维修单查询为空"));
        Integer userId = Convert.toInt(shouhou.getUserid());
        List<GetPreferentialRecommendationOutputRes> list = new ArrayList<>();
        //获取积分兑换券信息
        List<GetMemberPointExchangeCoupon> memberPointExchangeCouponList = getMemberPointExchangeCouponList(userId);
        List<String> couponRuleList = memberPointExchangeCouponList.stream().map(GetMemberPointExchangeCoupon::getCouponRule).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        //获取规则码限制信息
        Map<String, AddModel> addModelMap = getAddModelMap(couponRuleList);
        //过滤出能用的积分兑换券
        memberPointExchangeCouponList = memberPointExchangeCouponList.stream().filter(item -> isCanUseCoupon(item.getCouponRule(), addModelMap, shouhou)).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(memberPointExchangeCouponList)){
            // 找出价格最高的优惠券，处理getPrice为null的情况
             memberPointExchangeCouponList.stream()
                    .max(Comparator.comparing(c -> Optional.ofNullable(c.getPrice()).orElse(BigDecimal.ZERO)))
                    .ifPresent(item->{
                        GetPreferentialRecommendationOutputRes res = new GetPreferentialRecommendationOutputRes();
                        res.setCouponPrice(item.getPrice());
                        res.setCouponLimitPrice(item.getLimitPrice());
                        res.setCouponMa(Convert.toStr(item.getConfId()));
                        res.setExChangePoints(item.getPoint());
                        res.setType(NumberConstant.TWO);
                        list.add(res);
                    });
        }
        //获取维修单可用优惠券
        List<NumberCard> numberCards = getNumberCards(shouhou, Convert.toLong(userId));
        if(CollUtil.isNotEmpty(numberCards)){
             numberCards.stream()
                    .max(Comparator.comparing(c -> Optional.ofNullable(c.getTotal()).orElse(BigDecimal.ZERO)))
                    .ifPresent(item->{
                        GetPreferentialRecommendationOutputRes res = new GetPreferentialRecommendationOutputRes();
                        res.setCouponPrice(item.getTotal());
                        res.setCouponLimitPrice(item.getLimitprice());
                        res.setCouponMa(item.getCardID());
                        res.setType(NumberConstant.ONE);
                        list.add(res);
                    });
        }
        if(CollUtil.isNotEmpty(list)){
            List<GetPreferentialRecommendationOutputRes> sortList = list.stream().sorted(Comparator.comparing(c -> Optional.ofNullable(c.getCouponPrice()).orElse(BigDecimal.ZERO), Comparator.reverseOrder())).collect(Collectors.toList());
            // 为每个元素赋予相应的排序值 ranks
            for (int i = 0; i < sortList.size(); i++) {
                sortList.get(i).setRanks(i + 1); // 排名从1开始
            }
            return sortList;
        }
        return list;
    }

    /**
     * 获取规则码限制信息
     * @param userId
     * @return
     */
    private Map<String, AddModel> getAddModelMap(List<String> couponRuleList){
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.M_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String evidenceUrl = host + "/web/api/youhuima/getCouponByCode/v2?ruleCodes=" + couponRuleList.stream().filter(Objects::nonNull).collect(Collectors.joining(","));
        HttpResponse evidenceResult = HttpUtil.createGet(evidenceUrl)
                .execute();
        if(evidenceResult.isOk()){
            log.warn("调用主站获取规则码限制信息接口传入参数：{}，返回结果：{}",evidenceUrl,evidenceResult.body());
            R result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
            if(result.isSuccess()){
                Object data = result.getData();
                if(ObjectUtil.isNull(data)){
                    throw new CustomizeException("调用主站获取规则码限制信息返回数据为空");
                } else {
                    List<AddModel> addModelList = JSONUtil.toList(JSONUtil.toJsonStr(data), AddModel.class);
                    Map<String, AddModel> map = addModelList.stream().collect(Collectors.toMap(AddModel::getRulecode, Function.identity()));
                    return map;
                }
            } else {
                throw new CustomizeException("调用主站获取规则码限制信息失败："+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
            }
        } else {
            log.warn("调用主获取规则码限制信息异常传入参数：{}",evidenceUrl);
            throw new CustomizeException("调用主站获取规则码限制信息口异常");
        }
    }

    /**
     * 获取积分兑券规则码
     * @param userId
     * @return
     */
    private List<GetMemberPointExchangeCoupon> getMemberPointExchangeCouponList(Integer userId){
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.M_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String evidenceUrl = host + "/web/api/memberPointExchangeCoupon/oa/listByUserId/v1?userId=" + userId;
        HttpResponse evidenceResult = HttpUtil.createGet(evidenceUrl)
                .execute();
        if(evidenceResult.isOk()){
            log.warn("调用主站会员积分兑换券接口传入参数：{}，返回结果：{}",evidenceUrl,evidenceResult.body());
            R result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
            if(result.isSuccess()){
                Object data = result.getData();
                if(ObjectUtil.isNull(data)){
                    throw new CustomizeException("调用主站会员积分兑换券返回数据为空");
                } else {
                    List<GetMemberPointExchangeCoupon> donationDocs = JSONUtil.toList(JSONUtil.toJsonStr(data), GetMemberPointExchangeCoupon.class);
                    return donationDocs;
                }
            } else {
                throw new CustomizeException("调用主站会员积分兑换券失败："+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
            }
        } else {
            log.warn("调用主站会员积分兑换券异常传入参数：{}",evidenceUrl);
            throw new CustomizeException("调用主站会员积分兑换券口异常");
        }
    }


    /**
     * OA查询积分兑换券
     * @param userId
     * @return
     */
    private List<MemberPointExchangeCouponModel> exchangeByUserId(Integer userId){
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.M_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String evidenceUrl = host + "/web/api/memberPointExchangeCoupon/oa/exchangeByUserId/v1?userId=" + userId;
        HttpResponse evidenceResult = HttpUtil.createGet(evidenceUrl)
                .execute();
        if(evidenceResult.isOk()){
            log.warn("调用OA查询积分兑换券接口传入参数：{}，返回结果：{}",evidenceUrl,evidenceResult.body());
            R result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
            if(result.isSuccess()){
                Object data = result.getData();
                if(ObjectUtil.isNull(data)){
                    throw new CustomizeException("调用OA查询积分兑换券返回数据为空");
                } else {
                    List<MemberPointExchangeCouponModel> donationDocs = JSONUtil.toList(JSONUtil.toJsonStr(data), MemberPointExchangeCouponModel.class);
                    return donationDocs;
                }
            } else {
                throw new CustomizeException("调用OA查询积分兑换券失败："+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
            }
        } else {
            log.warn("调用OA查询积分兑换券异常传入参数：{}",evidenceUrl);
            throw new CustomizeException("调用OA查询积分兑换券口异常");
        }
    }


    /**
     * 兑换优惠券
     * @param userId
     * @return
     */
    @Override
    public PointExchangeCouponOutput pointExchangeCoupon(PointExchangeCouponOutputReq req){
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("请登录"));
        req.setFrom(userBO.getUserName());
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.M_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String evidenceUrl = host + "/web/api/memberPointExchangeCoupon/oa/exchange/v1";
        HttpResponse evidenceResult = HttpUtil.createPost(evidenceUrl)
                .body(JSONUtil.toJsonStr(req))
                .execute();
        if(evidenceResult.isOk()){
            log.warn("兑换优惠券传入参数：{}，返回结果：{}",evidenceUrl,evidenceResult.body());
            R result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
            if(result.isSuccess()){
                Object data = result.getData();
                if(ObjectUtil.isNull(data)){
                    throw new CustomizeException("兑换优惠券返回数据为空");
                } else {
                    return JSONUtil.toBean(JSONUtil.toJsonStr(data), PointExchangeCouponOutput.class);
                }
            } else {
                throw new CustomizeException("兑换优惠券失败："+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
            }
        } else {
            log.warn("兑换优惠券异常传入参数：{}",evidenceUrl);
            throw new CustomizeException("兑换优惠券口异常");
        }
    }

    /**
     * 撤销优惠券
     * @param userId
     * @return
     */
    @Override
    public R cancelExchangeCoupon(CancelExchangeCouponOutputReq req){
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.M_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String evidenceUrl = host + "/web/api/memberPointExchangeCoupon/oa/cancel/v1";
        HttpResponse evidenceResult = HttpUtil.createPost(evidenceUrl)
                .body(JSONUtil.toJsonStr(req))
                .execute();
        if(evidenceResult.isOk()){
            log.warn("撤销优惠券传入参数：{}，返回结果：{}",evidenceUrl,evidenceResult.body());
            return JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
        } else {
            log.warn("撤销优惠券异常传入参数：{}",evidenceUrl);
            throw new CustomizeException("撤销优惠券口异常");
        }
    }

    @Override
    public void oneClickUse(OneClickUseReq req) {
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("请登录"));
        Integer type = req.getType();
        //如果是优惠码 直接使用
        if(NumberConstant.ONE.equals(type)){
            R<Boolean> booleanR = shouhouExService.useYouhuiMa(req.getShouHouId(), req.getCouponMa(), userBO.getUserName());
            if(!booleanR.isSuccess()){
                throw new CustomizeException("使用优惠码失败："+Optional.ofNullable(booleanR.getUserMsg()).orElse(booleanR.getMsg()));
            }
        }
        if(NumberConstant.TWO.equals(type)){
            PointExchangeCouponOutputReq outputReq = new PointExchangeCouponOutputReq();
            outputReq.setFrom(userBO.getUserName());
            outputReq.setUserId(req.getUserId());
            outputReq.setShouHouId(req.getShouHouId());
            outputReq.setConfId(Convert.toInt(req.getCouponMa()));
            //对换成为优惠券之后
            PointExchangeCouponOutput pointExchangeCouponOutput = pointExchangeCoupon(outputReq);
            //自动使用优惠券
            R<Boolean> booleanR = shouhouExService.useYouhuiMa(req.getShouHouId(), pointExchangeCouponOutput.getCouponMa(), userBO.getUserName());
            if(!booleanR.isSuccess()){
                throw new CustomizeException("使用优惠码失败："+Optional.ofNullable(booleanR.getUserMsg()).orElse(booleanR.getMsg()));
            }
        }
    }

}
