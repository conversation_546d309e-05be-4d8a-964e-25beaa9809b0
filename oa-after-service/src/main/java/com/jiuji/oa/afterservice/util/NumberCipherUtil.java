package com.jiuji.oa.afterservice.util;

public class NumberCipherUtil {

    /**
     * 最大值限制为 0x7fff_ffffL
     */
    private static final long OPERATOR_1 = 0x7abe_339fL;
    /**
     * 最大值限制为 0x7fff_ffffL
     */
    private static final long OPERATOR_2 = 0x26c1_23ffL;
    /**
     * 最大值限制为 0x7fff_ffffL
     */
    private static final long OPERATOR_3 = 0x7312_abcdL;

    /**
     * 对数值加密
     *
     * @param origin 原始值，取值范围 0 ~ 0x7fff_ffff
     */
    public static long encrypt(long origin) {
        return ((origin ^ OPERATOR_1 ^ OPERATOR_2) + OPERATOR_3);
    }

    /**
     * 对加密后的数值 解密
     */
    public static long decrypt(long encrypt) {
        return ((encrypt - OPERATOR_3) ^ OPERATOR_1 ^ OPERATOR_2);
    }
}