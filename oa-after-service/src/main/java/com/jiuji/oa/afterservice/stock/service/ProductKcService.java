package com.jiuji.oa.afterservice.stock.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.bo.OperateProductKcPara;
import com.jiuji.oa.afterservice.bigpro.bo.productkc.OperateProductKcRes;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import com.jiuji.oa.afterservice.stock.po.StockStatusCountBo;
import com.jiuji.tc.common.vo.R;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-17
 */
public interface ProductKcService extends IService<ProductKc> {
  String TRANS_ROLLBACK_COMMENT_START = "分布式事务性补偿：";

    Boolean lockKc(Integer ppid, Integer areaId, Integer orderCount);

    /**
   * 允许负数库存出库
   * @param ppid
   * @return
   */
  Boolean negativeInventory(Integer ppid);
    /**
     * 查询库存数量
     * @param ppid
     * @param areaId
     * @return
     */
  Integer getKcCount(Integer ppid,Integer areaId);



  /**
   * 查询库存数量
   * @param ppidList
   * @param areaId
   * @return
   */
  Map<Integer, Integer> listKcCount(Collection<Integer> ppidList, Integer areaId);

  Map<Integer, ProductKc> listKcCountMap(Collection<Integer> ppidList, Integer areaId);

    /**
     * 锁定库存
     * @param ppid
     * @param areaId
     * @return
     */
  Boolean lockKc(Integer ppid,Integer areaId);

  @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
  Boolean lockKcNewTrans(Integer ppid, Integer areaId);

  /**
     * 解锁库存占用
     * @param ppid
     * @param areaId
     * @return
     */
  Boolean unlockKc(Integer ppid,Integer areaId);

  @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
  Boolean unlockKcNewTrans(Integer ppid, Integer areaId);

  /**
   * C# oa999DAL/apiServices.cs.product_kc方法
   * 库存操作
   * @param para
   * @return
   */
  R<OperateProductKcRes> cSharpOperateProductKc(OperateProductKcPara para);

  /**
   * 改为调用C#的接口
   * @param para
   * @return
   */
  @Transactional(rollbackFor = Exception.class)
  R<OperateProductKcRes> operateProductKc(OperateProductKcPara para);

  /**
   * 根据imei或者mkcId查询是否有product_mkc记录
   *
   * @param mkcID mkcID
   * @param imei  imei
   * @return 有1 没有null
   */
  Integer hasProductMKC(@Param("mkcId") Integer mkcId, @Param("imei") String imei);


  /**
   * 锁单
   * @param count
   * @param kcid
   * @return
   */
  Boolean updateProductKcNew(Integer count, Long kcid);


  /**
   * 批量获取库存备货数量
   * @param basketIds
   * @param basketType
   * @return
   */
  List<StockStatusCountBo> listStockStatusCount(Collection<Integer> basketIds, Integer basketType);

}
