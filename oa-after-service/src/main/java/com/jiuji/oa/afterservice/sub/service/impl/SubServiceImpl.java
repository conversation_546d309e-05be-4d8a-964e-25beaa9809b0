package com.jiuji.oa.afterservice.sub.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.*;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouTuihuanReturnDataBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.service.ProductinfoService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.vo.MemberSubVO;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RequestCacheKeys;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.bo.AreaInfoSimpleBO;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.service.SmallproLogService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproService;
import com.jiuji.oa.afterservice.sub.bo.SaleOrderParamBO;
import com.jiuji.oa.afterservice.sub.dao.SubMapper;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @since 2020/3/21
 */
@Slf4j
@Service
public class SubServiceImpl extends ServiceImpl<SubMapper, Sub> implements SubService {

    @Autowired
    private ShouhouMapper shouhouMapper;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private  MemberClient memberClient;
    @Resource
    private SubLogsCloud subLogsCloud;
    @Resource
    private SmsService smsService;
    @Resource
    private SmallproLogService smallproLogService;
    @Resource
    private AreainfoService areainfoService;


    @Override
    public Integer createSubBySmallProId(Integer smallProId) {
        //查询小件单相关信息
        Smallpro smallpro = Optional.ofNullable(SpringUtil.getBean(SmallproService.class).getById(smallProId)).orElseThrow(() -> new CustomizeException("小件单查询为空"));
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息超时"));
        Integer userId = smallpro.getUserId();

//        MemberBasicRes memberBasicRes = Optional.ofNullable(memberClient.getMemberBasicInfo(userId))
//                .map(R::getData)
//                .filter(ObjectUtil::isNotEmpty)
//                .orElseThrow(() -> new CustomizeException("获取用户信息出异常"));
        //获取门店信息
        AreaInfoSimpleBO areaInfoSimpleBO = Optional.ofNullable(areainfoService.getAreaInfoSimpleByAreaId(smallpro.getAreaId())).orElseThrow(() -> new CustomizeException("接件门店查询为空"));
        //获取换货商品信息
        Productinfo productInfo = Optional.ofNullable(productinfoService.getProductinfoByPpid(smallpro.getChangePpriceid())).orElse(new Productinfo());
        SaleOrderParamBO saleOrderParamBO = new SaleOrderParamBO();
        //订单信息封装
        SaleOrderParamBO.SaleOrderParam saleOrderParam = new SaleOrderParamBO.SaleOrderParam();
        saleOrderParam.setUserId(userId);
        saleOrderParam.setInuser(userBO.getUserName());
        saleOrderParam.setSubTo(smallpro.getUserName());
        saleOrderParam.setSubMobile(smallpro.getMobile());
        saleOrderParam.setArea(smallpro.getAreaId().toString());
        saleOrderParam.setOpUser(userBO.getUserName());
        //到店自取
        saleOrderParam.setDelivery(NumberConstant.ONE);
        //到店支付
        saleOrderParam.setSubPay(10);
        // 小件换货 订单类型
        saleOrderParam.setSubType(31);
        saleOrderParam.setComment("小件换货生成销售单,小件单:"+smallProId);
        //商品明细封装
        List<SaleOrderParamBO.SaleProductParam> saleProductParamList = Lists.newArrayList();
        SaleOrderParamBO.SaleProductParam saleProductParam = new SaleOrderParamBO.SaleProductParam();
        Integer changePpriceid = smallpro.getChangePpriceid();
        saleProductParam.setPpriceid(changePpriceid);
        saleProductParam.setIsmobile(NumberConstant.ZERO);
        saleProductParam.setProductNum(NumberConstant.ONE);
        saleProductParam.setProductTitle(productInfo.getProductName());
        saleProductParam.setProductPrice1(productInfo.getMemberprice());
        //查询区域价格
        Map<Integer,BigDecimal> regionalPriceMap = getRegionalPrice(Collections.singletonList(changePpriceid), areaInfoSimpleBO.getCityId(), userBO.getXTenant());
        BigDecimal regionalPrice = Optional.ofNullable(regionalPriceMap.get(changePpriceid)).orElseThrow(() -> new CustomizeException("区域价格获取为空"));
        saleProductParam.setProductPrice(regionalPrice);
        saleProductParam.setSeller(userBO.getUserName());
        saleProductParam.setInuser(userBO.getUserName());
        //自动备货
        saleProductParam.setBeihuo(NumberConstant.TWO);

        saleProductParamList.add(saleProductParam);
        saleOrderParamBO.setSaleOrderParam(saleOrderParam);
        saleOrderParamBO.setSaleProductParamList(saleProductParamList);
        int subId = createSub(saleOrderParamBO);

        //订单详情的软件接件记录通知客户的订单日志
        CompletableFuture.runAsync(()-> {
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            //拼接小件单跳转连接
            String url = "/staticpc/#/small-refund/"+smallProId;
            subLogsNewReq.setComment("订单类型：小件换货 ，管理小件售后单"+"<a href="+url+">"+smallProId +"</a>");
            subLogsNewReq.setSubId(subId);
            subLogsNewReq.setInUser(userBO.getUserName());
            subLogsNewReq.setShowType(Boolean.TRUE);
            subLogsNewReq.setType(1);
            R<Boolean> addLogR = subLogsCloud.addSubLog(subLogsNewReq);
            if(!addLogR.isSuccess()){
                smsService.sendOaMsgTo9JiMan("小件换货单生成销售单添加日志:{}",addLogR.getUserMsg());
            }
            log.warn("小件换货单生成销售单添加日志:{}",JSON.toJSONString(addLogR));
        });
        //添加小件单日志
        CompletableFuture.runAsync(()-> {
        String url="/addOrder/editOrder?SubID="+subId;
        String comment="关联生成置换商品订单，单号"+"<a href="+url+">"+subId +"</a>";
        smallproLogService.addLogs(smallProId, comment, userBO.getUserName(), 0);
        });
        return subId;
    }


    /**
     * 获取区域价格
     * @param ppidList
     * @return
     */
    @Override
    public Map<Integer,BigDecimal> getRegionalPrice(Collection<Integer> ppidList, Integer cityid, Integer xtenant){
        Map <Integer,BigDecimal> resultMap = new HashMap<>();
        if(CollectionUtils.isEmpty(ppidList) || ObjectUtil.isNull(cityid) || ObjectUtil.isNull(xtenant)){
            return resultMap;
        }

        //获取订单域名
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.M_URL))
                .map(R::getData)
                .filter(StringUtils::isNotEmpty)
                .orElseThrow(() -> new CustomizeException("获取域名出错"));
        String url = host+"/cloudapi_nc/product/api/batchGetClientPrice/v2?xservicename=product-9ji";
        HashMap<String, Object> map = new HashMap<>();
        String joinedString = ppidList.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        map.put("ppids",joinedString);
        map.put("areaCode",cityid);
        map.put("xtenant",xtenant);
        map.put("shopId",0);
        map.put("clientType",2);
        map.put("needLimitBuyPrice",Boolean.TRUE);
        HttpResponse execute = HttpUtil.createPost(url).form(map).execute();
        if(execute.isOk()){
            log.warn("获取去区域价格传入参数：{}，返回结果：{}",JSONUtil.toJsonStr(map),execute.body());
            String body = execute.body();
            R r = JSONUtil.toBean(JSONUtil.toJsonStr(body), R.class);
            if(r.isSuccess()){

                Object data = r.getData();
                Map toBean = JSONUtil.toBean(JSONUtil.toJsonStr(data), Map.class);
                toBean.forEach((k,v)-> resultMap.put(Integer.parseInt(k.toString()),new BigDecimal(v.toString())));
                return resultMap;

            }
            throw new CustomizeException("获取区域价格异常"+Optional.ofNullable(r.getMsg()).orElse(r.getUserMsg()));
        }
        log.warn("获取去区域价格异常传入参数:{}",JSONUtil.toJsonStr(map));
        throw new CustomizeException("获取去区域价格异常");
    }

    @Override
    public Integer createSub(SaleOrderParamBO saleOrderParamBO){
        //获取订单域名
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST))
                .map(R::getData)
                .filter(StringUtils::isNotEmpty)
                .orElseThrow(() -> new CustomizeException("获取域名出错"));
        String url = host + "/oaApi.svc/rest/SubmitOrderEx";
        String createSaleOrderResult = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, ContentType.JSON.getValue())
                .header(Header.ACCEPT, ContentType.JSON.getValue())
                .body(JSON.toJSONString(saleOrderParamBO))
                .execute()
                .body();
        log.warn("创建销售单，请求参数：{}, 返回结果：{},调用url:{}", JSON.toJSONString(saleOrderParamBO), createSaleOrderResult,url);
        if (StringUtils.isBlank(createSaleOrderResult)) {
            throw new CustomizeException("调用OA生成销售单异常");
        }
        R result = JSONUtil.toBean(createSaleOrderResult, R.class);
        if (!result.isSuccess()) {
            throw new CustomizeException("调用OA生成销售单返回状态错误,错误信息：" + Optional.ofNullable(result.getMsg()).orElse(result.getUserMsg()));
        }
        return Integer.parseInt(result.getData().toString());
    }

    @Override
    public Sub splitSub(Sub oldSub, Integer subCheck,
                        BigDecimal yingfuM, BigDecimal yifuM) {
        return splitSub(oldSub, subCheck, yingfuM, yifuM
                , BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
    }

    @Override
    public Sub splitSub(Sub oldSub, Integer subCheck
            , BigDecimal yingfuM, BigDecimal yifuM, BigDecimal feeM, BigDecimal youhui1M
            , BigDecimal shouxuM, BigDecimal jidianM) {
        Sub splitSub = new Sub();
        BeanUtils.copyProperties(oldSub, splitSub);
        splitSub.setSubId(null)
                .setReturnDate(LocalDateTime.now())
                .setSubApartDate(LocalDateTime.now())
                .setSubPid(oldSub.getSubId())
                .setSubCheck(subCheck)
                .setYingfuM(yingfuM)
                .setYifuM(yifuM)
                .setFeeM(feeM)
                .setYouhui1M(youhui1M)
                .setShouxuM(shouxuM)
                .setJidianM(jidianM);
        boolean saveSuccess = save(splitSub);
        if (saveSuccess) {
            return splitSub;
        } else {
            return null;
        }
    }

    @Override
    public Sub getByIdSqlServer(Integer id) {
        return SpringContextUtil.reqCache(()-> baseMapper.getByIdSqlServer(id), RequestCacheKeys.SUB_SERVICE_GET_BY_ID_SQL_SERVER, id);
    }

    @Override
    public List<Sub> listSqlServer(Wrapper wrapper) {
        return baseMapper.listSqlServer(wrapper);
    }

    @Override
    public BigDecimal getSubDJQPrice(Integer subId) {
        List<Sub> subList = this.list(new LambdaQueryWrapper<Sub>().eq(Sub::getSubId, subId).eq(Sub::getAreaId, 280));
        if (CollectionUtils.isEmpty(subList)) {
            return BigDecimal.ZERO;
        } else {
            return baseMapper.getSubDJQPrice(subId);
        }

    }

    @Override
    public Integer getUserIdBySubId(Integer subId) {

        Sub sub = super.getById(subId);
        if (sub == null || sub.getUserId() == null) {
            return null;
        } else {
            return sub.getUserId().intValue();
        }
    }

    @Override
    public Integer getHisUserIdBySubId(Integer subId) {
        Sub sub = super.getById(subId);
        if (sub == null || sub.getUserId() == null) {
            return null;
        } else {
            return sub.getUserId().intValue();
        }
    }

    @Override
    public ShouhouTuihuanReturnDataBo getReturnDataTable(Integer subId, String type) {
        Integer returnType = 1;
        MTableInfoEnum tableInfo = null;
        if ("6".contains(type)) {
            returnType = 1;
            tableInfo = MTableInfoEnum.SUB;
        } else if ("8".contains(type)) {
            returnType = 2;
        } else if ("11,5".contains(type)) {
            returnType = 4;
        } else if ("3,4".contains(type)) {
            returnType = 3;
        } else if ("7".contains(type)) {
            returnType = 5;
            tableInfo = MTableInfoEnum.SUB;
        }

        Integer ishuishou = null;
        Integer tmpDtCount = null;
        if (returnType == 3) {
            Shouhou shouhou = shouhouMapper.selectById(subId);
            if (shouhou != null) {
                tmpDtCount = 1;
                ishuishou = shouhou.getIshuishou();
                if (ishuishou != null && ishuishou > 0) {
                    subId = shouhou.getSubId();
                    tableInfo = MTableInfoEnum.RECOVER_MARKET_INFO;
                } else {
                    if (shouhou.getSubId() == null) {
                        subId = null;
                    } else {
                        subId = shouhou.getSubId();
                        tableInfo = MTableInfoEnum.SUB;
                    }
                }
            }
        }
        Integer finalSubId = subId;
        Integer finalReturnType = returnType;
        Integer finalIshuishou = ishuishou;
        Integer finalTmpDtCount = tmpDtCount;
        return CommenUtil.autoQueryHist(()-> baseMapper.getReturnDataTable(finalSubId, finalReturnType, finalIshuishou,
                null, finalTmpDtCount), tableInfo, subId);
    }

    @Override
    public List<Sub> list(Wrapper<Sub> queryWrapper) {
        return listSqlServer(queryWrapper);
    }

    @Override
    public List<MemberSubVO> searchSubInfoByUserId(Integer userId) {
        return baseMapper.searchSubInfoByUserId(userId);
    }

    @Override
    public List<MemberSubVO> searchLpInfoByUserId(Integer userId) {
        return baseMapper.searchLpInfoByUserId(userId);
    }

    @Override
    public List<MemberSubVO> searchSubInfoByImei(String imei) {
        return baseMapper.searchSubInfoByImei(imei);
    }

    @Override
    @DS("ch999oanew")
    public Sub getServiceSub(Integer basketId, Integer ppid) {
        return baseMapper.getServiceSub(basketId,ppid);
    }

    @Override
    @DS("oanew_his")
    public Sub getHistoryServiceSub(Integer basketId, Integer ppid) {
        return baseMapper.getServiceSub(basketId,ppid);
    }

    /**
     * 根据手机号码查询九机盾服务
     * @param key 手机号码
     * @return
     */
    @Override
    public List<MemberSubVO> getShieldService(String key) {
        return baseMapper.getShieldService(key);
    }


    @Override
    public boolean judgeSubType(Integer subId, List<Integer> subTypeList) {
        if(null == subId || CollUtil.isEmpty(subTypeList)){
            return false;
        }
        Sub sub = this.getByIdSqlServer(subId);
        if(null == sub){
            return false;
        }
        return subTypeList.contains(sub.getSubtype());
    }

}
