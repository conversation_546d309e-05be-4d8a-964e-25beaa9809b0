package com.jiuji.oa.afterservice.bigpro.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 维修故障VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@ApiModel(value = "RepairFaultVO对象", description = "维修故障VO")
public class RepairFaultVO implements Serializable {


    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "故障名称")
    private String faultName;

    @ApiModelProperty(value = "是否必填(0:否 1:是)")
    private Integer isRequired;

    
    @ApiModelProperty(value = "维修方案列表")
    private List<RepairPlanVO> planList;
} 