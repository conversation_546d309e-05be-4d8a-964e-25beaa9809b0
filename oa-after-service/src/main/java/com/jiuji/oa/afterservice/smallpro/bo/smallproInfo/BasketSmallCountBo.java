package com.jiuji.oa.afterservice.smallpro.bo.smallproInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 商品小件接件数量
 * <AUTHOR>
 * @since 2021/9/22 15:49
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@ApiModel("小件单的商品")
public class BasketSmallCountBo {
    /**商品id*/
    @ApiModelProperty("商品id")
    private Integer basketId;
    /**商品ppid*/
    @ApiModelProperty("商品ppid")
    private Integer ppriceid;
    /**小件接件数量统计*/
    @ApiModelProperty(value = "小件接件数量统计",hidden = true)
    private Integer smallCount;
}
