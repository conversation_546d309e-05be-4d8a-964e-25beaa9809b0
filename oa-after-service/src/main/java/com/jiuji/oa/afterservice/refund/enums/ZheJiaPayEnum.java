package com.jiuji.oa.afterservice.refund.enums;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * 折价支付枚举
 * <AUTHOR>
 * @since 2024/12/30 20:08
 */
@Getter
@AllArgsConstructor
public enum ZheJiaPayEnum implements CodeMessageEnumInterface {
    // 国家补贴
    GUO_JIA_BUTIE(1, "此订单为享受政府补贴订单，需在维修单内收取折价金额{payPrice}元后才能办理退换。", "国家补贴"),
    GUARANTEED_VALUE(2, "因涉及到保值退换折价，需在维修单内收取差价金额{payPrice}元后才可提交退换办理。", "保值退换")
    ;

    /**
     * 编码
     */
    private Integer code;
    /**
     * 折价原因模板
     */
    private String reasonFormat;
    /**
     * 编码对应信息
     */
    private String message;

    public String format(BigDecimal payPrice){
        return StrUtil.format(this.getReasonFormat(), Dict.create().set("payPrice", payPrice.setScale(2, BigDecimal.ROUND_DOWN)));
    }
}
