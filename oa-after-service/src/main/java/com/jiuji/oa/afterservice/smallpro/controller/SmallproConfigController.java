package com.jiuji.oa.afterservice.smallpro.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.cloud.after.vo.res.ChangeInfoRes;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.shouhou.vo.req.SelectConfigToWebReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.SelectConfigToWebRes;
import com.jiuji.oa.afterservice.smallpro.enums.ExchangeGoodsEnums;
import com.jiuji.oa.afterservice.smallpro.enums.RefundGoodsEnums;
import com.jiuji.oa.afterservice.smallpro.repository.SmallExchangeConfigLogRepository;
import com.jiuji.oa.afterservice.smallpro.repository.SmallRefundConfigLogRepository;
import com.jiuji.oa.afterservice.smallpro.repository.document.SmallExchangeConfigLogDocument;
import com.jiuji.oa.afterservice.smallpro.repository.document.SmallRefundConfigLogDocument;
import com.jiuji.oa.afterservice.smallpro.service.SmallReasonConfigBusService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproDetailsExService;
import com.jiuji.oa.afterservice.smallpro.service.smallpro.SmallProConfigService;
import com.jiuji.oa.afterservice.smallpro.vo.req.*;
import com.jiuji.oa.afterservice.smallpro.vo.res.ExchangeGoodsConfigRes;
import com.jiuji.oa.afterservice.smallpro.vo.res.ExchangeGoodsConfigResV2;
import com.jiuji.oa.afterservice.smallpro.vo.res.SmallReasonConfigListRes;
import com.jiuji.oa.afterservice.smallpro.vo.res.RefundGoodsConfigRes;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Api(tags = "小件接件配置")
@RequestMapping("/api/SmallproConfig")
public class SmallproConfigController {
    @Resource
    private SmallProConfigService smallProConfigService;
    @Resource
    private SmallExchangeConfigLogRepository smallExchangeConfigLogRepository;
    @Resource
    private SmallRefundConfigLogRepository smallRefundConfigLogRepository;
    @Resource
    private SmallReasonConfigBusService smallReasonConfigBusService;
    @Resource
    private SmallproDetailsExService detailsExService;


    /**
     * 小件售后配置查询接口
     * @return 小件换货配置列表
     */
    @PostMapping("/getExchangeGoodsConfigInfo")
    @ApiOperation("小件接件换货配置查询接口")
    public R<Page<ExchangeGoodsConfigRes>> getExchangeGoodsConfigInfo(@RequestBody ExchangeGoodsConfigReq req) {
        return smallProConfigService.getExchangeGoodsConfigInfo(req);
    }


    /**
     * 小件配置查询提供给网站
     * @param req
     * @return
     */
    @PostMapping("/selectConfigToWeb")
    public R<SelectConfigToWebRes> selectConfigToWeb(@RequestBody SelectConfigToWebReq req) {
        return R.success(smallProConfigService.selectConfigToWeb(req));
    }


    /**
     * 小件配置查询提供给网站
     * @param req
     * @return
     */
    @GetMapping("/selectChangeInfoRes")
    public R<List<ChangeInfoRes>> selectChangeInfoRes(@RequestParam (value = "smallProId")Integer smallProId) {
        return R.success(detailsExService.selelctChangeInfoList(smallProId));
    }
    
    /**
     * 小件售后配置枚举接口
     * @return 小件换货枚举值
     */
    @GetMapping("/getExchangeGoodsEnums")
    @ApiOperation("小件接件换货配置枚举")
    public R<List<ExchangeGoodsEnums>> getExchangeGoodsEnums() {
        return smallProConfigService.getExchangeGoodsEnums();
    }

    /**
     * 小件接件换货配置保存接口
     * @return
     */
    @PostMapping("/saveExchangeGoodsConfig")
    @ApiOperation("小件接件换货配置保存接口")
    public R<Boolean> saveExchangeGoodsConfig(@RequestBody ExchangeGoodsConfigSaveReq req) {
        if (CommenUtil.isNotNullZero(req.getId())){
            //更新
            return smallProConfigService.updateExchangeGoodsConfig(req);
        }else {
            //新增
            return smallProConfigService.addExchangeGoodsConfig(req);
        }
    }

    /**
     * 小件接件换货配置保存接口
     * @return
     */
    @PostMapping("/saveExchangeGoodsConfig/v2")
    @ApiOperation("小件接件换货配置保存接口v2")
    public R<Boolean> saveExchangeGoodsConfigV2(@RequestBody ExchangeGoodsConfigSaveReqV2 req) {
        if (CommenUtil.isNotNullZero(req.getId())){
            //更新
            return smallProConfigService.updateExchangeGoodsConfigV2(req);
        }else {
            //新增
            return smallProConfigService.addExchangeGoodsConfigV2(req);
        }
    }

    /**
     * 小件接件换货配置保存接口
     * @return
     */
    @GetMapping("/getExchangeGoodsConfigById/v2")
    @ApiOperation("查询小件接件换货配置详情")
    public R<ExchangeGoodsConfigResV2> getExchangeGoodsConfigById(Integer id) {
        return R.success(smallProConfigService.getExchangeGoodsConfigById(id));
    }

    /**
     * 小件接件换货配置保存接口
     * @return
     */
    @GetMapping("/delExchangeGoodsConfig")
    @ApiOperation("删除小件换货配置")
    public R<Boolean> delExchangeGoodsConfig(@RequestParam(value = "id") Integer id) {
        return smallProConfigService.delExchangeGoodsConfig(id);
    }

    /**
     * 小件换货日志接口
     */
    @GetMapping("/getListSmallProConfigLog/{configId}")
    @ApiOperation(value = "根据configId查询日志")
    public R<List<SmallExchangeConfigLogDocument.Conts>> listSmallProConfigLog(@PathVariable("configId") @NotNull Long configId) {
        SmallExchangeConfigLogDocument smallProConfigLogDocument = smallExchangeConfigLogRepository.findById(configId).orElse(new SmallExchangeConfigLogDocument());
        return R.success(smallProConfigLogDocument.getCons().stream().sorted(Comparator.comparing(SmallExchangeConfigLogDocument.Conts::getDTime).reversed()).collect(Collectors.toList()));
    }

    /**
     * 小件售后配置查询接口
     * @return 小件退货配置列表
     */
    @PostMapping("/getRefundGoodsConfigInfo")
    @ApiOperation("小件接件退货配置查询接口")
    public R<Page<RefundGoodsConfigRes>> getRefundGoodsConfigInfo(@RequestBody RefundGoodsConfigReq req) {
        return smallProConfigService.getRefundGoodsConfigInfo(req);
    }

    /**
     * 小件售后配置枚举接口
     * @return 小件退货枚举值
     */
    @GetMapping("/getRefundGoodsEnums")
    @ApiOperation("小件接件退货配置枚举")
    public R<List<RefundGoodsEnums>> getRefundGoodsEnums() {
        return smallProConfigService.getRefundGoodsEnums();
    }

    /**
     * 小件接件退货配置保存接口
     * @return
     */
    @PostMapping("/saveRefundGoodsConfig")
    @ApiOperation("小件接件退货配置保存接口")
    public R<Boolean> saveRefundGoodsConfig(@RequestBody RefundGoodsConfigSaveReq req) {
        if (CommenUtil.isNotNullZero(req.getId())){
            //更新
            return smallProConfigService.updateRefundGoodsConfig(req);
        }else {
            //新增
            return smallProConfigService.addRefundGoodsConfig(req);
        }
    }

    /**
     * 小件接件退货配置保存接口
     * @return
     */
    @GetMapping("/delRefundGoodsConfig")
    @ApiOperation("删除小件退货配置")
    public R<Boolean> delRefundGoodsConfig(@RequestParam(value = "id") Integer id) {
        return smallProConfigService.delRefundGoodsConfig(id);
    }

    /**
     * 小件退货日志接口
     */
    @GetMapping("/getListSmallRefundConfigLog/{configId}")
    @ApiOperation(value = "根据configId查询日志")
    public R<List<SmallRefundConfigLogDocument.Conts>> listSmallRefundConfigLog(@PathVariable("configId") @NotNull Long configId) {
        SmallRefundConfigLogDocument smallProConfigLogDocument = smallRefundConfigLogRepository.findById(configId).orElse(new SmallRefundConfigLogDocument());
        return R.success(smallProConfigLogDocument.getCons().stream().sorted(Comparator.comparing(SmallRefundConfigLogDocument.Conts::getDTime).reversed()).collect(Collectors.toList()));
    }

    /**
     * 小件接件故障快捷原因配置查询
     */
    @PostMapping("/getSmallReasonConfigList/v1")
    public R<List<SmallReasonConfigListRes>> getSmallReasonConfigList(@RequestBody SmallReasonConfigListReq req) {
        return smallReasonConfigBusService.getSmallReasonConfigList(req);
    }
}

