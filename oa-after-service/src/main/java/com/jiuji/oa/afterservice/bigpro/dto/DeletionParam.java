package com.jiuji.oa.afterservice.bigpro.dto;

import com.jiuji.oa.afterservice.bigpro.po.RepairFault;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlan;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

/**
 * 处理删除的参数类
 */
@Data
public class DeletionParam {
    private String currentUser;
    private LocalDateTime now;
    private Integer shouHouId;
    private Map<Integer, RepairFault> existingFaultMap;
    private Map<Integer, Map<Integer, RepairPlan>> existingPlanMap;
    private Set<Integer> processedFaultIds;
    private Set<Integer> processedPlanIds;

    public DeletionParam(String currentUser, LocalDateTime now, Integer shouHouId,
                    Map<Integer, RepairFault> existingFaultMap,
                    Map<Integer, Map<Integer, RepairPlan>> existingPlanMap,
                    Set<Integer> processedFaultIds, Set<Integer> processedPlanIds) {
        this.currentUser = currentUser;
        this.now = now;
        this.shouHouId = shouHouId;
        this.existingFaultMap = existingFaultMap;
        this.existingPlanMap = existingPlanMap;
        this.processedFaultIds = processedFaultIds;
        this.processedPlanIds = processedPlanIds;
    }


} 