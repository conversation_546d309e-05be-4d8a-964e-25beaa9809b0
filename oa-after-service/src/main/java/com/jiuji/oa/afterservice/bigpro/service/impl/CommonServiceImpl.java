package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.ch999.common.util.atlas.AtlasUtil;
import com.ch999.common.util.vo.atlas.TencentMapResultVO;
import com.jiuji.oa.afterservice.bigpro.service.CommonService;
import com.jiuji.oa.afterservice.bigpro.vo.res.PositionGpsRes;
import com.jiuji.oa.afterservice.common.bo.XtenantSubjectBo;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.ExtenAntUrlTypeEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.afterservice.util.TencentMapUtil;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @author: gengjiaping
 * @date: 2020/4/26
 */
@Slf4j
@Service
public class CommonServiceImpl implements CommonService {

    private final double pi = 3.1415926535897932384626;
    private final double a = 6378245.0;
    private final double ee = 0.00669342162296594323;

    @Resource
    private SysConfigClient sysConfigClient;
    @Autowired
    private SysConfigService sysConfigService;

    @Override
    public PositionGpsRes getPositionGps(String location) {
        if (StringUtils.isEmpty(location)) {
            return null;
        }

        if (location.length() > 40) {
            location = location.substring(0, 40);
        }
        CompletableFuture.runAsync(()-> TencentMapUtil.upload("getPositionGps","getMapInfoUsingTencent"));
        JSONObject mapInfoUsingBaidu = AtlasUtil.getMapInfoUsingBaidu(location, null);
        log.warn("参数: {}, 地图结果: {}", location, mapInfoUsingBaidu);
        if(ObjectUtil.equals(ResultCode.SUCCESS, mapInfoUsingBaidu.getInteger("status"))){
            TencentMapResultVO.Result.Location locationObj = mapInfoUsingBaidu.getJSONObject("result")
                    .getObject("location", TencentMapResultVO.Result.Location.class);
            return new PositionGpsRes(locationObj.getLat(), locationObj.getLng());
        }else{
            throw new CustomizeException(mapInfoUsingBaidu.getString("msg"));
        }

    }


    @Override
    public Integer getSmsChannelByXtenant(Long xtenant, Integer eSmsChannelType) {
        if (eSmsChannelType == null) {
            eSmsChannelType = SysConfigConstant.VERIFICATION_CODE_CHANNEL;
        }
        String res = "";
        if (eSmsChannelType.equals(ESmsChannelTypeEnum.YZMTD.getCode())) {
            res = sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.VERIFICATION_CODE_CHANNEL, Math.toIntExact(xtenant));
        } else if (eSmsChannelType.equals(ESmsChannelTypeEnum.YXTD.getCode())) {
            res = sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.MARKETING_CHANNEL, Math.toIntExact(xtenant));
        }
        return StringUtils.isEmpty(res) || !NumberUtil.isInteger(res) ? null : Integer.valueOf(res);
    }

    @Override
    public String getUrlByXtenant(Long xtenant, Integer urlType) {
        R<String> urlR;
        if (ExtenAntUrlTypeEnum.MURL.getCode().equals(urlType)) {
            urlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, Math.toIntExact(xtenant));
        } else if (ExtenAntUrlTypeEnum.WEBURL.getCode().equals(urlType)) {
            urlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.WEB_URL, Math.toIntExact(xtenant));
        } else if (ExtenAntUrlTypeEnum.HSURL.getCode().equals(urlType)) {
            urlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.HS_URL, Math.toIntExact(xtenant));
        } else {
            urlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, Math.toIntExact(xtenant));
        }
        return CommonUtils.isRequestSuccess(urlR) ? urlR.getData() : "";
    }

    @Override
    public XtenantSubjectBo getXtenantSubject(Long xtenant) {
        XtenantSubjectBo xtenantSubject = new XtenantSubjectBo();
        String smsChannel = Optional.ofNullable(this.getSmsChannelByXtenant(xtenant, ESmsChannelTypeEnum.YZMTD.getCode())).map(String::valueOf).orElse(null);
        String smsChannelMarketing = Optional.ofNullable(this.getSmsChannelByXtenant(xtenant, ESmsChannelTypeEnum.YXTD.getCode())).map(String::valueOf).orElse(null);
        R<String> mUrlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, Math.toIntExact(xtenant));
        R<String> webUrlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.WEB_URL, Math.toIntExact(xtenant));
        R<String> hsUrlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.HS_URL, Math.toIntExact(xtenant));
        String mUrl = CommonUtils.isRequestSuccess(mUrlR) ? mUrlR.getData() : "";
        String webUrl = CommonUtils.isRequestSuccess(webUrlR) ? webUrlR.getData() : "";
        String hsUrl = CommonUtils.isRequestSuccess(hsUrlR) ? hsUrlR.getData() : "";
        xtenantSubject.setSmsChannel(smsChannel);
        xtenantSubject.setSmsChannelMarketing(smsChannelMarketing);
        xtenantSubject.setMUrl(mUrl);
        xtenantSubject.setWebUrl(webUrl);
        xtenantSubject.setHsUrl(hsUrl);
        return xtenantSubject;
    }

    /**
     * (BD-09)-->84 百度坐标转GPS坐标
     *
     * @param bd_lat
     * @param bd_lon
     * @return
     */
    private PositionGpsRes bd09ToGps84(double bd_lat, double bd_lon) {
        PositionGpsRes gcj02 = bd09ToGcj02(bd_lon, bd_lat);
        return gcjToGps84(gcj02.getLat(), gcj02.getLng());
    }

    /**
     * * 火星坐标系 (GCJ-02) to 84 * * @param lon * @param lat * @return
     */
    private PositionGpsRes gcjToGps84(double lat, double lon) {
        PositionGpsRes gps = transform(lat, lon);
        double lontitude = lon * 2 - gps.getLng();
        double latitude = lat * 2 - gps.getLat();
        return new PositionGpsRes(latitude, lontitude);
    }

    public PositionGpsRes transform(double lat, double lon) {
        if (outOfChina(lat, lon)) {
            return new PositionGpsRes(lat, lon);
        }
        double dLat = transformLat(lon - 105.0, lat - 35.0);
        double dLon = transformLon(lon - 105.0, lat - 35.0);
        double radLat = lat / 180.0 * pi;
        double magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
        dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
        double mgLat = lat + dLat;
        double mgLon = lon + dLon;
        return new PositionGpsRes(mgLat, mgLon);
    }

    private Boolean outOfChina(double lat, double lon) {
        if (lon < 72.004 || lon > 137.8347) {
            return true;
        }
        if (lat < 0.8293 || lat > 55.8271) {
            return true;
        }
        return false;
    }

    private double transformLat(double x, double y) {
        double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y
                + 0.2 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(y * pi) + 40.0 * Math.sin(y / 3.0 * pi)) * 2.0 / 3.0;
        ret += (160.0 * Math.sin(y / 12.0 * pi) + 320 * Math.sin(y * pi / 30.0)) * 2.0 / 3.0;
        return ret;
    }

    private double transformLon(double x, double y) {
        double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1
                * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(x * pi) + 40.0 * Math.sin(x / 3.0 * pi)) * 2.0 / 3.0;
        ret += (150.0 * Math.sin(x / 12.0 * pi) + 300.0 * Math.sin(x / 30.0
                * pi)) * 2.0 / 3.0;
        return ret;
    }

    /**
     * *火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换算法 * * 将 BD-09 坐标转换成GCJ-02 坐标 * * @param
     *
     * @param bd_lon
     * @param bd_lat
     * @return
     */
    private PositionGpsRes bd09ToGcj02(double bd_lon, double bd_lat) {
        double x = bd_lon - 0.0065, y = bd_lat - 0.006;
        double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * pi);
        double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * pi);
        double gg_lon = z * Math.cos(theta);
        double gg_lat = z * Math.sin(theta);
        return new PositionGpsRes(gg_lat, gg_lon);
    }
}
