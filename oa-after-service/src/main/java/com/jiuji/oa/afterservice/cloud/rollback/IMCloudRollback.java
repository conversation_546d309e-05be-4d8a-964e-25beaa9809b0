package com.jiuji.oa.afterservice.cloud.rollback;

import com.ch999.common.util.vo.Result;
import com.jiuji.oa.afterservice.cloud.service.IMCloud;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmAccessoriesV2Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class IMCloudRollback implements IMCloud {

    @Override
    public Result<String> sendServicesCompleteMsg(@RequestParam(value = "openId") String openId,
                                                  @RequestParam(value = "url") String url,
                                                  @RequestParam(value = "first") String first,
                                                  @RequestParam(value = "keyword1") String keyword1,
                                                  @RequestParam(value = "keyword2") String keyword2,
                                                  @RequestParam(value = "keyword3") String keyword3,
                                                  @RequestParam(value = "remark") String remark,
                                                  @RequestHeader(value = "City") Integer cityId,
                                                  @RequestHeader(value = "xtenant") Long xtenant) {
        log.error("WebCloud.sendServicesCompleteMsg 调用失败进入降级服务");
        return Result.error();
    }

    @Override
    public Result<String> sendServicesCompleteMsgV2(String openId, String url, String serviceNo, String completeTime) {
        log.error("WebCloud.sendServicesCompleteMsgV2 调用失败进入降级服务");
        return Result.error();
    }


    @Override
    public Result<String> sendAfterServiceProgressMsg(@RequestParam(value = "openId") String openId,
                                                      @RequestParam(value = "url") String url,
                                                      @RequestParam(value = "first") String first, String HandleType,
                                                      String Status, String RowCreateDate, String LogType,
                                                      String remark,
                                                      @RequestHeader(value = "City") Integer cityId,
                                                      @RequestHeader(value = "xtenant") Long xtenant) {
        log.error("WebCloud.sendAfterServiceProgressMsg 调用失败进入降级服务");
        return Result.error();
    }

    @Override
    public Result<String> sendAfterServicesProcessingProgress(@RequestParam(value = "openId") String openId,
                                                              @RequestParam(value = "url", required = false) String url,
                                                              @RequestParam(value = "orderNo", required = false) String orderNo,
                                                              @RequestParam(value = "serviceType", required = false) String serviceType,
                                                              @RequestParam(value = "processingStatus", required = false) String processingStatus,
                                                              @RequestParam(value = "submitTime", required = false) String submitTime,
                                                              @RequestParam(value = "progressResult", required = false) String progressResult,
                                                              @RequestHeader(value = "City") Integer cityId,
                                                              @RequestHeader(value = "xtenant") Long xtenant){
        log.error("WebCloud.sendAfterServicesProcessingProgress 调用失败进入降级服务");
        return Result.error();
    }

    @Override
    public Result<String> sendServiceConfirmMsg(String openId, String url, String first, String keyword1, String keyword2, String remark) {
        log.error("WebCloud.sendServiceConfirmMsg 调用失败进入降级服务");
        return Result.error();
    }

    @Override
    public Result<String> sendPointsReminderMsg(String openId, String url, String first, String account, String time, String type, String creditChange, String number, String creditName, String amount, String remark) {
        log.error("WebCloud.sendPointsReminderMsg 调用失败进入降级服务");
        return Result.error();
    }

    @Override
    public Result<List<FilmAccessoriesV2Data>> getFilmAccessoriesV2(Integer ppid,Integer type) {
        log.error("WebCloud.getFilmAccessoriesV2 调用失败进入降级服务");
        return Result.error();
    }
}
