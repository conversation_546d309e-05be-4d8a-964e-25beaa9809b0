package com.jiuji.oa.afterservice.refund.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退换数据对象
 *
 * <AUTHOR> ji<PERSON>
 * @since 2022/11/23 16:57
 */
@Data
@Accessors(chain = true)
@ApiModel("退换数据对象Bo")
public class RefundDataBo {

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private Integer subId;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private Integer subCheck;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private Integer areaId;

    /**
     * 订单所属的用户id
     */
    @ApiModelProperty(value = "订单所属的用户id")
    private Integer userId;

    /**
     * 订单商品id
     */
    @ApiModelProperty(value = "订单商品id")
    private Integer basketId;

    /**
     * 成本价
     */
    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    /**
     * 订单商品类型
     */
    @ApiModelProperty(value = "订单商品类型")
    private Integer type;

    /**
     * 订单商品原价(售后可退金额)
     */
    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    /**
     * 订单商品销售原价
     */
    @ApiModelProperty(value = "当时销售原价")
    private BigDecimal price1;

    /**
     * 订单九机币抵扣金额
     */
    @ApiModelProperty(value = "订单九机币抵扣金额")
    private BigDecimal coinMoney;

    /**
     * 订单应付金额
     */
    @ApiModelProperty(value = "订单应付金额")
    private BigDecimal yingfuMoney;

    /**
     * 订单已付金额
     */
    @ApiModelProperty(value = "已付金额")
    private BigDecimal yifuMoney;

    /**
     * 订单交易完成时间
     */
    @ApiModelProperty(value = "订单交易完成时间")
    private LocalDateTime tradeDate1;

    /**
     * 商品配置
     */
    @ApiModelProperty(value = "商品配置")
    private String productPeizhi;

}
