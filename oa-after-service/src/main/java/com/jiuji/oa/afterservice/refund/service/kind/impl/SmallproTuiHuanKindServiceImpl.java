package com.jiuji.oa.afterservice.refund.service.kind.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.vo.res.DouYinCouponLogRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.RankEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.po.ReturnsDetail;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.other.service.ReturnsDetailService;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import com.jiuji.oa.afterservice.refund.bo.DetailParamBo;
import com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo;
import com.jiuji.oa.afterservice.refund.po.OperatorBasket;
import com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanDetailPo;
import com.jiuji.oa.afterservice.refund.service.OperatorBasketService;
import com.jiuji.oa.afterservice.refund.service.kind.SmallproTuiHuanKindService;
import com.jiuji.oa.afterservice.refund.service.way.WechatAlipaySecondsRefundService;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiHuanCheckVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProKindEnum;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.po.SmallproBill;
import com.jiuji.oa.afterservice.smallpro.service.*;
import com.jiuji.oa.afterservice.smallpro.vo.req.SmallproAddLogReq;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.BasketTypeEnum;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 小件退换的处理类
 * <AUTHOR>
 * @since 2022/12/1 10:41
 */
@Service
@Slf4j
public class SmallproTuiHuanKindServiceImpl extends ParentTuiHuanKindServiceImpl implements SmallproTuiHuanKindService {

    @Resource
    private SmallproService smallproService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private OperatorBasketService operatorBasketService;
    @Resource
    private SmallproBillService smallproBillService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private SmsService smsService;


    @Override
    public R<Integer> assertCheckSaveAndSet(GroupTuihuanFormVo tuihuanForm) {
        SpringUtil.getBean(SmallproRefundExService.class).assertSmallproRefundCheckAndSet(tuihuanForm);
        return R.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(GroupTuihuanFormVo tuihuanForm) {
        // 获取小件订单关联ppriceId和basketId
        Integer smallproId = tuihuanForm.getShouhouId();
        SpringUtil.getBean(SmallproBillService.class).lambdaQuery().eq(SmallproBill::getSmallproID, smallproId).list()
                        .stream()
                        .map(bo -> {
                            ReturnsDetail returnsDetail = new ReturnsDetail();
                            return returnsDetail.setShthid(tuihuanForm.getTuihuanId().longValue())
                                    .setBasketCount(bo.getCount())
                                    .setBasketID(bo.getBasketId().longValue());
                        }).forEach(SpringUtil.getBean(ReturnsDetailService.class)::save);
        BigDecimal zhejiaM = tuihuanForm.getSubInfoBo().getZheJiaM();
        String comment = StrUtil.format("提交退款申请，退款金额{}，折价金额{}，原折价金额{}",
                NumberUtil.round(tuihuanForm.getRefundPrice(),2, RoundingMode.HALF_DOWN),
                NumberUtil.round(tuihuanForm.getZhejiaM(),2, RoundingMode.HALF_DOWN),
                NumberUtil.round(zhejiaM,2, RoundingMode.HALF_DOWN));
        SpringUtil.getBean(SmallproService.class).addSmallproLogWithPush(LambdaBuild.create(new SmallproAddLogReq())
                .set(SmallproAddLogReq::setSmallproId, smallproId)
                .set(SmallproAddLogReq::setToSms, 0)
                .set(SmallproAddLogReq::setToEmail, 0)
                .set(SmallproAddLogReq::setToWeixin, 0)
                .set(SmallproAddLogReq::setShowType, 0)
                .set(SmallproAddLogReq::setComment, comment)
                .build(), Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class)
                .getCurrentStaffId()).map(OaUserBO::getUserName).orElse("系统"));
    }

    @Override
    public List<TuihuanKindEnum> myKind() {
        return Arrays.asList(TuihuanKindEnum.TPJ, TuihuanKindEnum.SMALL_PRO_HQTXH);
    }

    @Override
    protected RefundSubInfoBo getSuInfoWithMaxRefundPrice(Integer subId, TuihuanKindEnum tuihuanKindEnum) {
        return SpringUtil.getBean(SmallproDetailsExService.class).getSmallproMaxRefundPrice(subId, tuihuanKindEnum);
    }

    @Override
    public Optional<RefundSubInfoBo> getSuInfoWithMaxRefundPrice(Integer subId, Integer tuihuanKind) {
        return super.getSuInfoWithMaxRefundPrice(subId, tuihuanKind)
                // 逻辑调整,小件退折价金额不影响最大可退金额,最大可退改回总的可退金额,由 assertCheckSaveAndSet 来控制改折价的权限
                .map(swr -> swr.setMaxRefundPrice(SpringContextUtil.getRequest()
                        .map(req -> req.getAttribute(RequestAttrKeys.SMALLPRO_MAX_REFUND_PRICE))
                            .map(smrp -> (BigDecimal)smrp).orElse(swr.getTotalPrice())));
    }

    @Override
    public R<Boolean> cancelRefund(ShouhouTuiHuanPo tuiHuanPo) {
        SpringUtil.getBean(SmallproRefundExService.class).cancelGroupRefund(tuiHuanPo);
        return R.success(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Integer> checkSubmitCheckAndSet(TuiHuanCheckVo tuiHuanCheckVo) {
        ShouhouTuiHuanPo tuiHuanPo = tuiHuanCheckVo.getTuiHuanPo();
        Integer smallProId = Optional.ofNullable(tuiHuanPo.getSmallproid()).orElseGet(tuiHuanPo::getShouhouId);
        Optional<OaUserBO> oaUserOpt = Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId());
        SmallproBillService smallproBillService = SpringUtil.getBean(SmallproBillService.class);

        switch (tuiHuanCheckVo.getProcessStatusEnum()){
            case CHECK2:
                SpringUtil.getBean(SmallproExchangePurchaseService.class).virtualProductAutoScrap(smallProId);
                break;
            case CHECK3:
                R<Boolean> srlCheck = SpringUtil.getBean(WechatAlipaySecondsRefundService.class).secondsRefundLimit(tuiHuanCheckVo);
                if(!srlCheck.isSuccess()){
                    //校验不同过记录日志
                    SpringUtil.getBean(SmallproLogService.class).addLogs(smallProId,srlCheck.getUserMsg(), oaUserOpt.map(OaUserBO::getUserName)
                            .orElse("系统"), 0);
                    return R.error(srlCheck.getUserMsg());
                }
                if (Stream.of(TuihuanKindEnum.TPJ, TuihuanKindEnum.SMALL_PRO_HQTXH).anyMatch(tEnum -> tEnum.equals(tuiHuanCheckVo.getTuihuanKindEnum()))){
                    List<Integer> billBasketIds = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, smallProId).select(SmallproBill::getBasketId).list()
                            .stream().map(SmallproBill::getBasketId).collect(Collectors.toList());
                    smallproService.assertCheckFilmCard(billBasketIds);
                }

                break;
            default:
                break;
        }
        return R.success(null);
    }

    @Override
    public boolean isAutoCheck3(TuiHuanCheckVo tuiHuanCheckVo) {
        return XtenantEnum.isJiujiXtenant();
    }

    @Override
    public void forEachThirdRefund(ThirdOriginRefundVo tor, TuihuanKindEnum tuihuanKindEnum) {
        super.forEachThirdRefund(tor, tuihuanKindEnum);
        DouYinCouponLogRes.SubKindEnum subKindsEnum = null;
        switch (tuihuanKindEnum){
            case TPJ:
            case SMALL_PRO_HQTXH:
                subKindsEnum = DouYinCouponLogRes.SubKindEnum.ORDINARY;
                break;
            default:
                break;
        }
        setDouYinNotRefund(tor, subKindsEnum, true);
    }

    @Override
    public boolean isEnable(Integer orderId, Integer kind, boolean isEnable) {
        Smallpro smallpro = smallproService.getByIdSqlServer(orderId);
        if(!isEnable){
            //未启用, 不用往下判断了
            return false;
        }
        //非组合退类型
        if(Stream.of(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE, SmallProKindEnum.SMALL_PRO_KIND_RETURN)
                .noneMatch(spEnum -> Objects.equals(spEnum.getCode(), smallpro.getKind()))){
            return false;
        }
        if(TuihuanKindEnum.TPJ.getCode().equals(kind)){
            //退配件,只需要判断是否为退配件类型
            return SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode().equals(smallpro.getKind());
        }
        //换其他型号
        return smallproService.isHqtxh(orderId);
    }

    @Override
    public ShouhouRefundDetailVo.ProcessStatus processStatusRank(ShouhouRefundDetailVo.ProcessStatus nextStatus,
                                                                 ShouhouTuiHuanPo tuiHuanPo,
                                                                 List<ShouhouTuihuanDetailPo> tuihuanDetailPos) {
        List<String> ranks = Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId())
                .map(OaUserBO::getRank).orElseGet(Collections::emptyList);
        switch (nextStatus) {
            case CHECK2:

                if(XtenantEnum.isJiujiXtenant()){
                    Smallpro smallpro = smallproService.getByIdSqlServer(tuiHuanPo.getShouhouId());
                    if(smallpro == null){
                        throw new CustomizeException(StrUtil.format("小件单[{}]信息获取异常", tuiHuanPo.getShouhouId()));
                    }
                    // 退配件:运营商抵扣
                    if (Stream.of(TuihuanKindEnum.TPJ).map(TuihuanKindEnum::getCode).anyMatch(thk -> ObjectUtil.equal(thk, tuiHuanPo.getTuihuanKind()))) {
                        //接件商品是否存在 运营商抵扣
                        if(XtenantEnum.isJiujiXtenant() && operatorBasketService.isCarrierDeduction(smallpro.getId())
                                && !SpringUtil.getBean(SmallproDetailsExService.class).isRecentMonthSub(Convert.toLong(smallpro.getSubId()))){
                            if(RankEnum.XJT_YYTK.noHasAuthority(ranks)){
                                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.NOT_WORK_RANK, "需要权值[{}]", RankEnum.XJT_YYTK.getCode());
                                return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                            }
                            return super.processStatusRank(nextStatus, tuiHuanPo, tuihuanDetailPos);
                        }
                    }
                    // 退配件或者换其他
                    if(Stream.of(TuihuanKindEnum.TPJ, TuihuanKindEnum.SMALL_PRO_HQTXH).map(TuihuanKindEnum::getCode).anyMatch(thk -> ObjectUtil.equal(thk, tuiHuanPo.getTuihuanKind()))){
                        if(Boolean.TRUE.equals(smallpro.getIsSpecialTreatment()) && RankEnum.XJT_SPECIAL_9JI.noHasAuthority(ranks)){
                            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.NOT_WORK_RANK, "需要权值[{}]", RankEnum.XJT_SPECIAL.getCode());
                            return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                        }else if(!Boolean.TRUE.equals(smallpro.getIsSpecialTreatment()) && RankEnum.XJT_NORMAL_9JI.noHasAuthority(ranks)){
                            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.NOT_WORK_RANK, "需要权值[{}]", RankEnum.XJT_NORMAL.getCode());
                            return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                        }
                    }
                }

                break;
            default:
                break;
        }
        return super.processStatusRank(nextStatus, tuiHuanPo, tuihuanDetailPos);
    }

    @Override
    public void doDetailChain(DetailParamBo detailParamBo, R<RefundMoneyDetailVo> result) {
        //显示折价审批
        if(XtenantEnum.isJiujiXtenant()) {
            Optional.ofNullable(result.getData()).ifPresent(item->{
                OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前登录人失效"));
                //是否提交服务不折价退款审请
                Boolean hasKey = stringRedisTemplate.hasKey(StrUtil.format(RedisKeys.SERVICE_NOT_DISCOUNT_REFUND_KEY, detailParamBo.getOrderId()));
                //有777权限
                if(Boolean.TRUE.equals(hasKey) && RankEnum.hasAuthority(userBO.getRank(),RankEnum.SMALL_PRO_CALL_CENTER)){
                    //提交替换为 折价审批
                    if (Objects.equals(ShouhouRefundDetailVo.ProcessStatus.SUBMIT.getCode(),item.getProcessStatus())) {
                        item.setProcessName(ShouhouRefundDetailVo.ProcessStatus.DISCOUNT_ALLOW_WORK.getMessage());
                        item.setProcessStatus(ShouhouRefundDetailVo.ProcessStatus.DISCOUNT_ALLOW_WORK.getCode());
                    }
                }
            });
        }
    }

    @Override
    public void submitCheck3Success(TuiHuanCheckVo tuiHuanCheckVo) {
        //如果是输出不进行下面
        if(XtenantEnum.isJiujiXtenant()){
            //如果是运营商抵扣的小件退款那就进行返销处理  调用C#的接口进行返销
            Integer tuihuanId = Optional.ofNullable(tuiHuanCheckVo.getTuihuanId()).orElse(NumberConstant.ZERO);
            ShouhouTuihuan shouhouTuiHuanPo = SpringUtil.getBean(ShouhouTuihuanService.class).getByIdSqlServer(tuihuanId);
            log.warn("小件运营商抵扣退款成功,查询参数：{},查询结果：{}",tuihuanId,JSONUtil.toJsonStr(shouhouTuiHuanPo));
            Optional.ofNullable(shouhouTuiHuanPo.getSmallproid()).ifPresent(item->{
                if(operatorBasketService.isCarrierDeduction(item)){
                    QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
                    smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, item);
                    List<SmallproBill> smallproBillList = smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
                    List<Integer> basketIds = smallproBillList.stream().map(SmallproBill::getBasketId).collect(Collectors.toList());
                    List<OperatorBasket> operatorBasketList = operatorBasketService.lambdaQuery().in(OperatorBasket::getBasketId,basketIds).list();
                    if(CollUtil.isNotEmpty(operatorBasketList)){
                        String operatorBasketIds = operatorBasketList.stream().map(obj -> Convert.toStr(obj.getId())).collect(Collectors.joining(","));
                        returnSale(operatorBasketIds);
                    }
                }
            });
        }

    }


    private void returnSale(String operatorBasketIds){
        try {
            String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).map(R::getData).filter(StrUtil::isNotEmpty)
                    .orElseThrow(() -> new CustomizeException("获取域名出错"));
            OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前登录人失效"));
            Map<String, Object> parms = new HashMap<>();
            parms.put("ids", operatorBasketIds);
            parms.put("comment", "（退款操作快捷返销）");
            parms.put("noCheckRank", 1);
            String evidenceUrl = host + "/commonApi/BatchReturnOperatorBasket" ;
            HttpResponse evidenceResult = HttpUtil.createPost(evidenceUrl)
                    .header("authorization", userBO.getToken())
                    .body(JSONUtil.toJsonStr(parms))
                    .execute();
            log.warn("调用运营商返销接口传入参数：{}，返回结果：{}",JSONUtil.toJsonStr(parms),evidenceResult.body());
            if(evidenceResult.isOk()){
                R result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
                if(!result.isSuccess()){
                    throw new CustomizeException("调用运营商返销接口失败："+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
                }
            } else {
                log.warn("调用运营商返销接口异常传入参数：{}",evidenceUrl);
                throw new CustomizeException("调用运营商返销接口异常");
            }
        }catch (Exception e){
            RRExceptionHandler.logError("批量退款办理接口异常",operatorBasketIds,e,smsService::sendOaMsgTo9JiMan);
        }

    }



}
