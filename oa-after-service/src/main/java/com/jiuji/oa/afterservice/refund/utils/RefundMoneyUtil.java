package com.jiuji.oa.afterservice.refund.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jiuji.oa.afterservice.bigpro.po.RecoverMarketsubinfo;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.service.IRecoverMarketsubinfoService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.RequestCacheKeys;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.po.IBasket;
import com.jiuji.oa.afterservice.other.po.Shouying;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.other.service.ShouyingService;
import com.jiuji.oa.afterservice.refund.bo.NotRefundBo;
import com.jiuji.oa.afterservice.refund.bo.ParentChildSubBo;
import com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo;
import com.jiuji.oa.afterservice.refund.dao.way.NetPayOriginWayMapper;
import com.jiuji.oa.afterservice.refund.enums.BeDirectPayBussinessTypeEnum;
import com.jiuji.oa.afterservice.refund.enums.ThirdRefundTypeEnum;
import com.jiuji.oa.afterservice.refund.po.PaymentConfigPo;
import com.jiuji.oa.afterservice.refund.service.way.ThirdOriginWayService;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.SubCheckStatusEnum;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 退款工具类
 * <AUTHOR>
 * @since 2022/8/10 16:01
 */
public abstract class RefundMoneyUtil {

    /**
     * 通过退款分类获取业务类型
     * @param tuiHuanKind
     * @return
     */
    public static BusinessTypeEnum getBusinessType(TuihuanKindEnum tuiHuanKind) {
        if (tuiHuanKind == null){
            return null;
        }
        switch (tuiHuanKind){
            case TDJ_LP:
            case TK_LP:
                return BusinessTypeEnum.LP_ORDER;
            case SMALL_PRO_REFUND_REPAIR_FEE:
                return BusinessTypeEnum.SMALL_ORDER;
            case TWXF:
            case TDJ_WXF:
                return BusinessTypeEnum.REPAIR_ORDER;
            default:
                return BusinessTypeEnum.SALE_ORDER;

        }
    }

    /**
     * 通过退款类型获取相关的收银type
     * @param tuiHuanKind
     * @return
     */
    public static Set<String> getShouyingTypes(TuihuanKindEnum tuiHuanKind) {
        LambdaBuild<Set<String>> shouyingTypeBuild = LambdaBuild.create(CollUtil.newHashSet());
        if(tuiHuanKind == null){
            return shouyingTypeBuild.build();
        }
        switch (tuiHuanKind){
            case BATCH_TK:
            case TDJ:
            case TPJ:
            case TK:
            case HQTXH:
            case SMALL_PRO_HQTXH:
            case HJT:
            case HZB:
                shouyingTypeBuild.set(Set::add,"订金").set(Set::add,"交易");
                break;
            case TDJ_LP:
            case TK_LP:
                shouyingTypeBuild.set(Set::add,"订金2").set(Set::add,"交易2");
                break;
            case TWXF:
                shouyingTypeBuild.set(Set::add,"售后").set(Set::add,"订金3");
                break;
            case SMALL_PRO_REFUND_REPAIR_FEE:
                shouyingTypeBuild.set(Set::add,"售后小件");
                break;
            case TDJ_WXF:
                shouyingTypeBuild.set(Set::add,"售后").set(Set::add,"订金3");
                break;
        }
        Set<String> shouyingTypes = shouyingTypeBuild.build();
        return shouyingTypes;
    }


    /**
     *  @param shouyingOrderId
     * @param tuiHuanKind
     */
    public static Set<Integer> parentChildOrderIds(Integer shouyingOrderId, TuihuanKindEnum tuiHuanKind) {
        return parentChildOrderIdList(shouyingOrderId, tuiHuanKind).stream().map(ParentChildSubBo::getSubId).collect(Collectors.toSet());
    }


    /**
     *  @param shouyingOrderId
     * @param tuiHuanKind
     */
    public static Set<ParentChildSubBo> parentChildOrderIdList(Integer shouyingOrderId, TuihuanKindEnum tuiHuanKind) {
        return SpringContextUtil.reqCache(() -> invokeParentChildOrderIds(shouyingOrderId, tuiHuanKind), RequestCacheKeys.PARENT_CHILD_ORDER_IDS, shouyingOrderId, tuiHuanKind);
    }

    /**
     *  @param shouyingOrderId
     * @param tuiHuanKind
     */
    private static Set<ParentChildSubBo> invokeParentChildOrderIds(Integer shouyingOrderId, TuihuanKindEnum tuiHuanKind) {
        //默认为新机单号
        Set<ParentChildSubBo> subIds = CollUtil.newHashSet(new ParentChildSubBo().setSubId(shouyingOrderId)
                .setIdType(ParentChildSubBo.IdTypeEnum.CURR_ORDER.getCode()).setSubCheck(null).setYifuM(BigDecimal.ZERO));
        // 需要添加父订单
        switch (tuiHuanKind) {
            case TDJ:
            case TK:
            case TPJ:
            case BATCH_TK:
            case HQTXH:
            case SMALL_PRO_HQTXH:
                //订单需要考虑父订单的情况
                Optional.ofNullable(CommenUtil.autoQueryHist(()-> SpringUtil.getBean(NetPayOriginWayMapper.class).getSubParentId(shouyingOrderId), MTableInfoEnum.SUB, shouyingOrderId))
                        .ifPresent(subIds::add);
                //订单需要考虑子订单的情况
                CommenUtil.autoQueryHist(()-> SpringUtil.getBean(NetPayOriginWayMapper.class).listSubChildId(shouyingOrderId), MTableInfoEnum.SUB, shouyingOrderId).forEach(subIds::add);
                break;
            case TK_LP:
            case TDJ_LP:
                //订单需要考虑父订单的情况
                Optional.ofNullable(CommenUtil.autoQueryHist(()-> SpringUtil.getBean(NetPayOriginWayMapper.class)
                        .getLpSubParentId(shouyingOrderId), MTableInfoEnum.RECOVER_MARKET_INFO, shouyingOrderId)).ifPresent(subIds::add);
                //订单需要考虑子订单的情况
                CommenUtil.autoQueryHist(()-> SpringUtil.getBean(NetPayOriginWayMapper.class).listLpSubChildId(shouyingOrderId),  MTableInfoEnum.RECOVER_MARKET_INFO, shouyingOrderId)
                        .forEach(subIds::add);
                break;
            default:
                break;

        }
        return subIds;
    }


    /**
     * 通过退款类型获取相关的收银type
     * @param businessTypeEnum
     * @return
     */
    public static Set<String> getShouyingTypes(BusinessTypeEnum businessTypeEnum) {
        LambdaBuild<Set<String>> shouyingTypeBuild = LambdaBuild.create(CollUtil.newHashSet());
        if(businessTypeEnum == null){
            return shouyingTypeBuild.build();
        }
        switch (businessTypeEnum){
            case SALE_ORDER:
                shouyingTypeBuild.set(Set::add,"订金").set(Set::add,"交易");
                break;
            case LP_ORDER:
                shouyingTypeBuild.set(Set::add,"订金2").set(Set::add,"交易2");
                break;
            case REPAIR_ORDER:
                shouyingTypeBuild.set(Set::add,"售后").set(Set::add,"售后小件").set(Set::add,"订金3");
                break;
            case SMALL_ORDER:
                shouyingTypeBuild.set(Set::add,"售后小件");
                break;
        }
        Set<String> shouyingTypes = shouyingTypeBuild.build();
        return shouyingTypes;
    }

    /**
     * 获取客户实付最大可退金额
     * @param refundSubInfoBo 订单信息
     * @param tuihuanKindEnum 退款类型
     * @param price 退款商品的最大可退金额
     * @param zheJiaM 折价金额
     * @return
     */
    public static BigDecimal getCustomerMaxRefundPrice(RefundSubInfoBo refundSubInfoBo, TuihuanKindEnum tuihuanKindEnum, BigDecimal price, BigDecimal zheJiaM) {
        List<ThirdOriginRefundVo> thirdOriginRefundVos = SpringUtil.getBean(ThirdOriginWayService.class).listAll(refundSubInfoBo.getOrderId(), tuihuanKindEnum);
        AtomicReference<BigDecimal> onlyNoCheck3RefundedRef = new AtomicReference<>(BigDecimal.ZERO);
        BigDecimal onlyOriginRefundPrice = thirdOriginRefundVos.stream()
                // 所有原路径包含参与折价的
                .filter(tor -> ThirdRefundTypeEnum.onlyOriginRefund(tor.getRefundType(), null))
                // 排除国补
                .filter(tor -> {
                    List<Integer> nationalSupplements = SpringUtil.getBean(ThirdOriginWayService.class).listNationalSupplement(tor.getSubId());
                    return CollUtil.isEmpty(nationalSupplements) || !nationalSupplements.contains(tor.getOtherType());
                })
                // 累加没退款办理的原路径退款金额
                .peek(tor -> onlyNoCheck3RefundedRef.set(onlyNoCheck3RefundedRef.get().add(ObjectUtil.defaultIfNull(tor.getNoCheck3RefundedPrice(), BigDecimal.ZERO))))
                .map(ThirdOriginRefundVo::getRefundPrice).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        NotRefundBo notRefundBo = RefundMoneyUtil.getNotRefundMoney(tuihuanKindEnum, refundSubInfoBo.getBusinessType(), refundSubInfoBo.getOrderId());
        BigDecimal notRefund = NumberUtil.null2Zero(notRefundBo.getNotRefundMoney());
        BigDecimal subMaxRefundPrice = NumberUtil.max(refundSubInfoBo.getAllYifuM().subtract(notRefund), BigDecimal.ZERO);
        BigDecimal customerMaxRefundPrice = NumberUtil.max(NumberUtil.max(NumberUtil.min(subMaxRefundPrice.subtract(onlyOriginRefundPrice)
                .subtract(onlyNoCheck3RefundedRef.get()).subtract(zheJiaM), price), BigDecimal.ZERO));
        return customerMaxRefundPrice;
    }



    /**
     * 获取不能退的金额
     * @param tuihuanKindEnumParam
     * @param businessType
     * @param orderId
     * @return
     */
    public static NotRefundBo getNotRefundMoney(TuihuanKindEnum tuihuanKindEnumParam, Integer businessType, Integer orderId) {
        return SpringContextUtil.reqCache(() -> RefundMoneyUtil.invokeGetNotRefundMoney(tuihuanKindEnumParam, businessType, orderId)
                , RequestCacheKeys.GET_NOT_REFUND_MONEY, tuihuanKindEnumParam, businessType, orderId);
    }

    /**
     * 获取不能退的金额
     * @param tuihuanKindEnumParam
     * @param businessType
     * @param orderId
     * @return
     */
    private static NotRefundBo invokeGetNotRefundMoney(TuihuanKindEnum tuihuanKindEnumParam, Integer businessType, Integer orderId) {
        NotRefundBo notRefundBo = new NotRefundBo().setNotRefundMoney(BigDecimal.ZERO).setBusinessType(businessType)
                .setShouYingNotRefunds(new LinkedList<>()).setThirdNotRefunds(new LinkedList<>());
        TuihuanKindEnum tuihuanKindEnum = tuihuanKindEnumParam;
        if(tuihuanKindEnum == TuihuanKindEnum.TK && BusinessTypeEnum.LP_ORDER.getCode().equals(businessType)){
            tuihuanKindEnum = TuihuanKindEnum.TK_LP;
        }
        //需要减去不支持退款的三方退款金额
        //所有三方配置的id
        Collection<Integer> torSyIds = new HashSet<>();
        List<ThirdOriginRefundVo> thirdOriginRefundVos = SpringUtil.getBean(ThirdOriginWayService.class).listAll(orderId, tuihuanKindEnum);
        BigDecimal notRefund = thirdOriginRefundVos.stream()
                .peek(tor -> torSyIds.add(tor.getShouyingId()))
                .filter(tor -> !ThirdRefundTypeEnum.canRefund(tor.getRefundType()))
                .peek(notRefundBo.getThirdNotRefunds()::add)
                .peek(tor -> SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"三方配置id[{}][{}]收银id[{}]已退金额[{}],金额[{}]不可退",
                        tor.getSysConfigId(),tor.getReturnWayName(),tor.getShouyingId(),tor.getRefundedPrice(),tor.getRefundPrice()))
                .map(ThirdOriginRefundVo::getRefundPrice)
                .filter(ObjectUtil::isNotNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //需要减去不能退款的收银
        notRefund = SpringUtil.getBean(ShouyingService.class).lambdaQuery().eq(Shouying::getSubId, orderId)
                .in(Shouying::getShouyingType, RefundMoneyUtil.getShouyingTypes(tuihuanKindEnum))
                //排除三方配置的收银
                .notIn(!torSyIds.isEmpty(),Shouying::getId,torSyIds)
                .eq(Shouying::getIsNotRefundable,Boolean.TRUE)
                .list().stream()
                .peek(notRefundBo.getShouYingNotRefunds()::add)
                .map(sy -> {
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"[{}]收银id[{}]已退金额[{}],金额[{}]不可退",
                            sy.getInuser(),sy.getId(),BigDecimal.ZERO,sy.getHejim());
                    return sy.getHejim();
                })
                .filter(ObjectUtil::isNotNull)
                .reduce(notRefund,BigDecimal::add);
        notRefundBo.setNotRefundMoney(notRefund);
        //保证不能为负数
        notRefundBo.setNotRefundMoney(NumberUtil.max(notRefundBo.getNotRefundMoney(), BigDecimal.ZERO));
        return notRefundBo;
    }

    /**
     * 分摊后是单个商品分摊金额
     * @param refundBasketIds
     * @param notRefundBo
     * @param orderId
     * @return {basketId: [Basket, splitedPrice]}
     */
    public static Map<Integer, Tuple> splitNotRefundPrice(Set<Integer> refundBasketIds, NotRefundBo notRefundBo, Integer orderId){
        if(CollUtil.isEmpty(refundBasketIds)){
            return Collections.emptyMap();
        }
        Set<Integer> basketIds = CollUtil.newHashSet(refundBasketIds);
        Predicate<String> filterAndAddFun = (basketIdStr) -> {
            List<String> basketIdList = StrUtil.splitTrim(basketIdStr, StringPool.COMMA);
            if (refundBasketIds.stream().anyMatch(basketId -> basketIdList.contains(Convert.toStr(basketId)))){
                basketIdList.stream().map(Convert::toInt).forEach(basketIds::add);
                return true;
            }else{
                return false;
            }
        };
        List<ThirdOriginRefundVo> basketTorNotRefunds = notRefundBo.getThirdNotRefunds().stream()
                .filter(tor -> filterAndAddFun.test(tor.getBasketId())).collect(Collectors.toList());
        List<Shouying> basketSyNotRefunds = notRefundBo.getShouYingNotRefunds().stream()
                .filter(sy -> filterAndAddFun.test(sy.getBasketId())).collect(Collectors.toList());
        //获取当前商品不能退的金额
        int basketIndex = 0;
        int splitNotRefundIndex = 1;
        List<IBasket> baskets;
        Integer businessType = notRefundBo.getBusinessType();
        baskets = getBaskets(basketIds, businessType, orderId);

        Map<Integer, Tuple> splitBasketMap = baskets.stream().collect(Collectors.toMap(IBasket::getBasketId, basket -> new Tuple(basket, BigDecimal.ZERO), (v1,v2) -> v1));
        BiConsumer<String, BigDecimal> splitNotRefundPriceFun = (basketIdStr, totalSplitPrice) -> {
            List<Tuple> tuples = StrUtil.splitTrim(basketIdStr, StringPool.COMMA).stream().map(Convert::toInt)
                    .map(splitBasketMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            CommonUtils.splitPrice(totalSplitPrice, 2,
                    tuples, tuple -> NumberUtil.null2Zero(((IBasket) tuple.get(basketIndex)).getPrice()),
                    tuple -> ObjectUtil.defaultIfNull(((IBasket) tuple.get(basketIndex)).getBasketCount(), 0), tuple -> tuple.get(splitNotRefundIndex),
                    (tuple, splitNotRefund) -> tuple.getMembers()[splitNotRefundIndex] = splitNotRefund);
        };
        //逐个进行分摊
        for (ThirdOriginRefundVo basketTorNotRefund : basketTorNotRefunds) {
            splitNotRefundPriceFun.accept(basketTorNotRefund.getBasketId(), basketTorNotRefund.getActualPayPrice());
        }
        for (Shouying basketSyNotRefund : basketSyNotRefunds) {
            splitNotRefundPriceFun.accept(basketSyNotRefund.getBasketId(), NumberUtil.null2Zero(basketSyNotRefund.getHejim()));
        }
        splitBasketMap.entrySet().forEach(entry -> SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                "[{}]商品分摊不可退金额：{}", entry.getKey(),entry.getValue().get(splitNotRefundIndex)));
        return splitBasketMap;
    }

    /**
     *
     * @param basketIds
     * @param businessType
     * @param orderId
     * @see BusinessTypeEnum
     * @return
     */
    public static List<IBasket> getBaskets(Set<Integer> basketIds, Integer businessType, Integer orderId) {
        List<IBasket> baskets;
        if(BusinessTypeEnum.LP_ORDER.getCode().equals(businessType)){
            baskets = SpringContextUtil.reqCache(()-> CommenUtil.autoQueryHist(() -> SpringUtil.getBean(IRecoverMarketsubinfoService.class).lambdaQuery()
                    .in(RecoverMarketsubinfo::getBasketId, basketIds).list().stream().collect(Collectors.toList()), MTableInfoEnum.RECOVER_MARKET_INFO, orderId),
                    RequestCacheKeys.LIST_LP_BASKETS, basketIds);
        }else{
            baskets = SpringContextUtil.reqCache(()-> CommenUtil.autoQueryHist(() -> SpringUtil.getBean(BasketService.class).lambdaQuery()
                    .in(Basket::getBasketId, basketIds).list().stream().collect(Collectors.toList()), MTableInfoEnum.SUB, orderId),
                    RequestCacheKeys.LIST_SUB_BASKETS, basketIds);
        }
        return baskets;
    }


    /**
     * 获取订单所有支付金额
     * @param orderId
     * @param subCheck
     * @param yifuM
     * @param tuihuanKindEnum
     * @return
     */
    public static BigDecimal getAllYifuM(Integer orderId, Integer subCheck, BigDecimal yifuM, TuihuanKindEnum tuihuanKindEnum){
        Set<ParentChildSubBo> parentChildSubs = RefundMoneyUtil.parentChildOrderIdList(orderId, tuihuanKindEnum);
        parentChildSubs.stream().filter(pcs -> ParentChildSubBo.IdTypeEnum.CURR_ORDER.getCode().equals(pcs.getIdType()))
                .findFirst().ifPresent(pcs -> pcs.setYifuM(yifuM).setSubCheck(subCheck));
        BigDecimal allYifuM = parentChildSubs.stream()
                .filter(pcs -> Stream.of(SubCheckStatusEnum.DELETED, SubCheckStatusEnum.RETURN, SubCheckStatusEnum.REFUND)
                        .allMatch(scEnum -> !scEnum.getCode().equals(pcs.getSubCheck()))).map(ParentChildSubBo::getYifuM)
                .filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"所有父子订单总的已付金额: {}, 相关单号: {}",
                allYifuM, parentChildSubs.stream().map(pcs -> StrUtil.format("{} {} 金额: {}",
                        EnumUtil.getMessageByCode(ParentChildSubBo.IdTypeEnum.class, pcs.getIdType()), pcs.getSubId(), pcs.getYifuM()))
                        .collect(Collectors.joining(StringPool.COMMA)));
        return allYifuM;
    }

    /**
     * 银企直连业务类型
     * @param tuihuanKindEnum
     * @return
     */
    public static Integer getBeDirectPayBussinessType(TuihuanKindEnum tuihuanKindEnum) {
        if (tuihuanKindEnum == null) {
            return null;
        }
        switch (tuihuanKindEnum) {
            case HJT:
            case HZB:
            case TK:
            case HQTXH:
            case TDJ:
            case TPJ:
            case BATCH_TK:
            case SMALL_PRO_HQTXH:
                return BeDirectPayBussinessTypeEnum.ORDER.getCode();
            case TWXF:
            case TDJ_WXF:
                return BeDirectPayBussinessTypeEnum.WEIXIU.getCode();
            case TDJ_LP:
            case TK_LP:
                return BeDirectPayBussinessTypeEnum.LIANGPIN.getCode();
            case SMALL_PRO_REFUND:
            case SMALL_PRO_REFUND_REPAIR_FEE:
                return BeDirectPayBussinessTypeEnum.SHOUHOU_XIAOJIAN.getCode();
            default:
                return null;
        }
    }

    /**
     * 获取真实的退款类型
     * @param shouhouId
     * @param tuihuanKindEnum
     * @return
     */
    public static TuihuanKindEnum getRealTuihuanKind(Integer shouhouId, TuihuanKindEnum tuihuanKindEnum){
        return SpringContextUtil.reqCache(() -> {
            if(ObjectUtil.equals(tuihuanKindEnum, TuihuanKindEnum.TK)){
                Optional<Integer> isHuishouOpt = SpringUtil.getBean(ShouhouService.class).lambdaQuery().eq(Shouhou::getId, shouhouId)
                        .select(Shouhou::getId, Shouhou::getIshuishou).list().stream().map(Shouhou::getIshuishou)
                        .filter(Objects::nonNull).findFirst()
                        .filter(isHuishou -> ObjectUtil.defaultIfNull(isHuishou,0)>0);
                if(isHuishouOpt.isPresent()){
                    return TuihuanKindEnum.TK_LP;
                }
            }
            return tuihuanKindEnum;
        }, RequestAttrKeys.GET_REALTUIHUAN_KIND, shouhouId, tuihuanKindEnum);
    }

    private RefundMoneyUtil(){

    }

    /**
     * 参与换购冲抵, 三方原路径
     */
    public static Stream<ThirdOriginRefundVo> filterChangeThirdOrigin(List<ThirdOriginRefundVo> thirdOriginRefundVos,
                                                                           TuihuanKindEnum tuihuanKindEnum, List<Integer> basketIds) {
        if(XtenantEnum.isJiujiXtenant() && Stream.of(TuihuanKindEnum.HQTXH, TuihuanKindEnum.SMALL_PRO_HQTXH)
                .anyMatch(tkEnum -> tkEnum.equals(tuihuanKindEnum))){
            List<String> effBasketIdList = basketIds.stream().filter(Objects::nonNull).map(Convert::toStr).collect(Collectors.toList());
            return thirdOriginRefundVos.stream()
                    .filter(tor -> SysConfigConstant.VOUCHER_PAYMENT_METHOD.equals(tor.getSysConfigCode()))
                    .filter(tor -> PaymentConfigPo.ActivityTypeEnum.NON_MALL_ACTIVITY.getCode().equals(tor.getActivityType()))
                    .filter(tor -> ThirdRefundTypeEnum.onlyOriginRefund(tor.getRefundType(), null))
                    .filter(tor -> CollUtil.isEmpty(effBasketIdList) || StrUtil.isBlank(tor.getBasketId())
                            || !CollUtil.intersection(effBasketIdList, StrUtil.splitTrim(tor.getBasketId(), StringPool.COMMA)).isEmpty());
        }
        return Stream.empty();
    }


}
