package com.jiuji.oa.afterservice.statistics.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.cloud.org.vo.response.RoleTermRes;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.BusinessUtil;
import com.jiuji.oa.afterservice.common.vo.CheckDataViewScopeReq;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProKindEnum;
import com.jiuji.oa.afterservice.statistics.bo.smallpro.LossReportedAndCashAmountBO;
import com.jiuji.oa.afterservice.statistics.bo.smallpro.PieceQuantityBO;
import com.jiuji.oa.afterservice.statistics.bo.smallpro.SmallSalesBO;
import com.jiuji.oa.afterservice.statistics.bo.smallpro.SmallproSstaticsWithFilterBO;
import com.jiuji.oa.afterservice.statistics.dao.SmallproStatisticsDao;
import com.jiuji.oa.afterservice.statistics.enums.SmallproStatisticsFieldEnum;
import com.jiuji.oa.afterservice.statistics.enums.SmallproStatisticsFieldResEnum;
import com.jiuji.oa.afterservice.statistics.enums.SmallproStatisticsFilterEnum;
import com.jiuji.oa.afterservice.statistics.enums.SmallproStatisticsTableEnum;
import com.jiuji.oa.afterservice.statistics.service.SmallproStatisticsService;
import com.jiuji.oa.afterservice.statistics.vo.req.SmallproStatisticsFilterReq;
import com.jiuji.oa.afterservice.statistics.vo.res.SmallproStatisticsFieldRes;
import com.jiuji.oa.afterservice.statistics.vo.res.SmallproStatisticsFilterRes;
import com.jiuji.oa.afterservice.statistics.vo.res.SmallproStatisticsRes;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * description: <小件统计服务实现类>
 * translation: <Smallware statistical service implementation class>
 *
 * <AUTHOR>
 * @date 2020/4/28
 * @since 1.0.0
 */
@Service
public class SmallproStatisticsServiceImpl implements SmallproStatisticsService {

    @Resource
    private SmallproStatisticsDao smallproStatisticsDao;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    // region 逻辑方法

    // region 获取小件统计筛选项列表 getSmallproStatisticsFilterSelection

    @Override
    public SmallproStatisticsFilterRes getSmallproStatisticsFilterSelection() {
        SmallproStatisticsFilterRes result = new SmallproStatisticsFilterRes();
        LinkedHashMap<Integer, String> areaLevel = SmallproStatisticsFilterEnum.getMapByFlag(1);
        LinkedHashMap<Integer, String> areaKind = SmallproStatisticsFilterEnum.getMapByFlag(2);
        LinkedHashMap<Integer, String> keyType = SmallproStatisticsFilterEnum.getMapByFlag(3);
        LinkedHashMap<Integer, String> groupType = SmallproStatisticsFilterEnum.getMapByFlag(4);
        SmallproStatisticsFilterRes.Point areaLevelPoint = new SmallproStatisticsFilterRes.Point();
        SmallproStatisticsFilterRes.Point areaKindPoint = new SmallproStatisticsFilterRes.Point();
        SmallproStatisticsFilterRes.Point keyTypePoint = new SmallproStatisticsFilterRes.Point();
        SmallproStatisticsFilterRes.Point groupTypePoint = new SmallproStatisticsFilterRes.Point();
        List<SmallproStatisticsFilterRes.PointValue> options = new ArrayList<>();
        SmallproStatisticsFilterRes.PointValue pointValue;
        areaLevelPoint.setTitle("店面级别");
        for (Map.Entry<Integer, String> temp : areaLevel.entrySet()) {
            pointValue = new SmallproStatisticsFilterRes.PointValue();
            pointValue.setLabel(temp.getValue()).setValue(temp.getKey());
            options.add(pointValue);
        }
        areaLevelPoint.setOptions(options);
        result.setAreaLevel(areaLevelPoint);
        options = new ArrayList<>();
        areaKindPoint.setTitle("店面类别");
        for (Map.Entry<Integer, String> temp : areaKind.entrySet()) {
            pointValue = new SmallproStatisticsFilterRes.PointValue();
            pointValue.setLabel(temp.getValue()).setValue(temp.getKey());
            options.add(pointValue);
        }
        areaKindPoint.setOptions(options);
        result.setAreaKind(areaKindPoint);
        options = new ArrayList<>();
        keyTypePoint.setTitle("关键字类别");
        for (Map.Entry<Integer, String> temp : keyType.entrySet()) {
            pointValue = new SmallproStatisticsFilterRes.PointValue();
            pointValue.setLabel(temp.getValue()).setValue(temp.getKey());
            options.add(pointValue);
        }
        keyTypePoint.setOptions(options);
        result.setKeyType(keyTypePoint);
        options = new ArrayList<>();
        groupTypePoint.setTitle("聚合方式");
        for (Map.Entry<Integer, String> temp : groupType.entrySet()) {
            pointValue = new SmallproStatisticsFilterRes.PointValue();
            pointValue.setLabel(temp.getValue()).setValue(temp.getKey());
            options.add(pointValue);
        }
        groupTypePoint.setOptions(options);
        result.setGroupType(groupTypePoint);
        return result;
    }

    // endregion

    // region 获取小件统计字段相关 getSmallproStatisticsField

    @SneakyThrows
    @Override
    public SmallproStatisticsFieldRes getSmallproStatisticsField() {
        SmallproStatisticsFieldRes result = new SmallproStatisticsFieldRes();
        LinkedHashMap<String, String> fieldMap = SmallproStatisticsFieldResEnum.getAllField();
        LinkedHashMap<String, String> fieldCommonMap = SmallproStatisticsFieldEnum.getAllField();
        for (Map.Entry<String, String> temp : fieldMap.entrySet()) {
            SmallproStatisticsFieldRes.Point tempPoint = new SmallproStatisticsFieldRes.Point();
            tempPoint.setFieldName(temp.getValue());
            String comment = fieldCommonMap.get(temp.getKey()) == null ? "" : fieldCommonMap.get(temp.getKey());
            tempPoint.setFieldComment(comment);
            Field field = SmallproStatisticsFieldRes.class.getDeclaredField(temp.getKey());
            field.setAccessible(true);
            field.set(result, tempPoint);
        }
        return result;
    }

    // endregion

    @SuppressWarnings("SingleStatementInBlock")
    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public SmallproStatisticsRes getSmallproStatisticsRes(SmallproStatisticsFilterReq req) {
        if (null == req.getStartTime()) {
            req.setStartTime(LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth()));
        }
        if (null == req.getEndTime()) {
            req.setEndTime(LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()));
        }
        //角色数据查询
        AtomicReference<RoleTermRes> roleTermRes = new AtomicReference<>();
        R dataViewRes = BusinessUtil.checkDataViewScope(CheckDataViewScopeReq.builder().moduleEnum(RoleTermModuleEnum.AFTER_SALES)
                .getStartTimeFun(req::getStartTime)
                .getEndTimeFun(req::getEndTime)
                .setStartTimeFun(req::setStartTime)
                .setEndTimeFun(req::setEndTime)
                .build(), roleTermRes::set);
        if (!dataViewRes.isSuccess()) {
            throw new CustomizeException(dataViewRes.getUserMsg());
        }
        if (roleTermRes.get() == null) {
            // 限制没有权限的只查最近两个月的数据
            String startTime = filterDate(req.getStartTime(), 1);
            String endtTime = filterDate(req.getEndTime(), 2);
            req.setStartTimeStr(startTime);
            req.setEndTimeStr(endtTime);
        } else {
            req.setStartTimeStr(DateUtil.format(req.getStartTime(), DatePattern.NORM_DATE_PATTERN));
            req.setEndTimeStr(DateUtil.format(req.getEndTime(), DatePattern.NORM_DATE_PATTERN));
        }
        if (CollectionUtils.isNotEmpty(req.getAreaIdList())) {
            req.setAreaIdList(req.getAreaIdList().stream().filter(e -> !e.contains("a")).collect(Collectors.toList()));
        }
        SmallproStatisticsRes res = new SmallproStatisticsRes();
        List<SmallproSstaticsWithFilterBO> list = new ArrayList<>();
        if (SmallproStatisticsFilterEnum.SMALLPRO_STATISTICS_FILTER_GROUP_KIND_0.getCode().equals(req.getGroupType())) {
            list = smallproStatisticsDao.getAllDataByArea(req);
            // 获取接件量
            List<PieceQuantityBO> pieceQuantityBO;
            if(XtenantEnum.isJiujiXtenant()){
                pieceQuantityBO = smallproStatisticsDao.getPieceQuantityByAreaJiuJi(req, null, null);
            } else {
                pieceQuantityBO= smallproStatisticsDao.getPieceQuantityByArea(req, null, null);
            }
            // 获取售前量
            List<PieceQuantityBO> preSales = smallproStatisticsDao.getPieceQuantityByArea(req,
                    SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode()
                    , null);
            // 获取维修量
            List<PieceQuantityBO> maintenance = smallproStatisticsDao.getPieceQuantityByArea(req,
                    SmallProKindEnum.SMALL_PRO_KIND_SERVICE.getCode()
                    , null);
            // 获取待处理
            List<PieceQuantityBO> toBeProcessed = smallproStatisticsDao.getPieceQuantityByArea(req,
                    null, 1);
            // 获取报损数量(3), 转现量(2)
            List<LossReportedAndCashAmountBO> lossReportedAndCashAmount =
                    smallproStatisticsDao.getLossReportedAndCashAmountByArea(req);
            // 获取小件销量
            List<SmallSalesBO> smallSales = smallproStatisticsDao.getSmallSalesByArea(req);
            for (SmallproSstaticsWithFilterBO bo : list) {
                PieceQuantityBO pieceQuantityBO1 =
                        pieceQuantityBO.stream().filter(e -> null != e.getId() && null != bo.getId() && e.getId().equals(bo.getId())).findFirst().orElse(null);
                if (null != pieceQuantityBO1) {
                    bo.setReceiveCount(pieceQuantityBO1.getPieceQuantity());
                }
                setNewIndex(preSales, maintenance, toBeProcessed, lossReportedAndCashAmount, smallSales, bo);
            }
            res.setTableFiled(SmallproStatisticsTableEnum.getClassTable(1));
        } else if (SmallproStatisticsFilterEnum.SMALLPRO_STATISTICS_FILTER_GROUP_KIND_1.getCode().equals(req.getGroupType())) {
            // 获取接件量
            List<PieceQuantityBO> pieceQuantityBO;
            if (XtenantEnum.isJiujiXtenant()) {
                pieceQuantityBO = smallproStatisticsDao.getPieceQuantityJiuJi(req, null, null);
            } else{
                pieceQuantityBO = smallproStatisticsDao.getPieceQuantity(req, null, null);
            }
            // 获取售前量
            List<PieceQuantityBO> preSales = smallproStatisticsDao.getPieceQuantity(req,
                    SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode()
                    , null);
            // 获取维修量
            List<PieceQuantityBO> maintenance = smallproStatisticsDao.getPieceQuantity(req,
                    SmallProKindEnum.SMALL_PRO_KIND_SERVICE.getCode()
                    , null);
            // 获取待处理
            List<PieceQuantityBO> toBeProcessed = smallproStatisticsDao.getPieceQuantity(req,
                    null, 1);
            // 获取报损数量(3), 转现量(2)
            List<LossReportedAndCashAmountBO> lossReportedAndCashAmount =
                    smallproStatisticsDao.getLossReportedAndCashAmount(req);
            // 获取小件销量
            List<SmallSalesBO> smallSales = smallproStatisticsDao.getSmallSales(req);
            for (PieceQuantityBO quantityBO : pieceQuantityBO) {
                SmallproSstaticsWithFilterBO bo = new SmallproSstaticsWithFilterBO();
                bo.setId(quantityBO.getId());
                bo.setName(quantityBO.getName());
                bo.setProductName(quantityBO.getName());
                bo.setReceiveCount(quantityBO.getPieceQuantity());
                list.add(bo);
            }
            for (SmallproSstaticsWithFilterBO bo : list) {
                setNewIndex(preSales, maintenance, toBeProcessed, lossReportedAndCashAmount, smallSales, bo);
            }
            res.setTableFiled(SmallproStatisticsTableEnum.getClassTable(2));
        } else if (SmallproStatisticsFilterEnum.SMALLPRO_STATISTICS_FILTER_GROUP_KIND_2.getCode().equals(req.getGroupType())) {
            list = smallproStatisticsDao.getAllDataByCustomer(req);
            res.setTableFiled(SmallproStatisticsTableEnum.getClassTable(3));
        }
        for (SmallproSstaticsWithFilterBO bo : list) {
            if(XtenantEnum.isJiujiXtenant()){
                Integer saleCount = bo.getSaleCount();
                if(Objects.isNull(saleCount) || saleCount ==0){
                    bo.setAfterProportion("0.00%");
                } else {
                    bo.setAfterProportion(getDoublePercentage(Optional.ofNullable(bo.getReceiveCount()).orElse(NumberConstant.ZERO),saleCount));
                }
            } else {
                bo.setAfterProportion((null == bo.getSaleCount() || bo.getSaleCount() == 0 || null == bo.getReceiveCount() || 0 == bo.getReceiveCount()) ? "0" :
                        getDoublePercentage(bo.getReceiveCount() - (bo.getBeforeCount() == null ? 0 : bo.getBeforeCount()), bo.getSaleCount()));
            }
            bo.setBeforeProportion((null == bo.getBeforeCount() || null == bo.getSaleCount() || 0 == bo.getSaleCount()) ? "0" : getDoublePercentage(bo.getBeforeCount(), bo.getSaleCount()));
            bo.setMaintainProportion((null == bo.getMaintainCount() || null == bo.getSaleCount() || 0 == bo.getSaleCount()) ? "0" : getDoublePercentage(bo.getMaintainCount(), bo.getSaleCount()));
            bo.setUserName(bo.getName());
        }
        res.setList(list);
        return res;
    }

    private String getDoublePercentage(Integer a, Integer b) {
        return String.format("%.2f", a / (b * 1.00d) * 100) + "%";
    }

    private String filterDate(LocalDateTime time, int type) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        List<String> rank = oaUserBO.getRank();
        DateTimeFormatter dateTimeFormatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String timeTemp = time.format(dateTimeFormatter2);
        if (!rank.contains("99") && !rank.contains("9o9") && getCurUserDefaultKind1(oaUserBO.getUserId()) == 1) {
            if (time.isBefore(LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth()).plusMonths(-2))) {
                if (1 == type) {
                    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00");
                    timeTemp =
                            LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth()).plusMonths(-2).format(dateTimeFormatter);
                } else if (type == 2) {
                    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd 23:59:59");
                    timeTemp =
                            LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth()).plusMonths(-2).format(dateTimeFormatter);
                }
            }
        }
        return timeTemp;
    }

    /**
     * 获取工作人员默认类别（加盟、自营）
     *
     * @param userId
     * @return
     */
    private int getCurUserDefaultKind1(Integer userId) {
        int kind1 = smallproStatisticsDao.getCurUserDefaultKind1(userId);
        return kind1;
    }

    private void setNewIndex(List<PieceQuantityBO> preSales, List<PieceQuantityBO> maintenance,
                             List<PieceQuantityBO> toBeProcessed,
                             List<LossReportedAndCashAmountBO> lossReportedAndCashAmount,
                             List<SmallSalesBO> smallSales, SmallproSstaticsWithFilterBO bo) {
        PieceQuantityBO pieceQuantityBO2 =
                preSales.stream().filter(e -> null != e.getId() && null != bo.getId() && e.getId().equals(bo.getId())).findFirst().orElse(null);
        if (null != pieceQuantityBO2) {
            bo.setBeforeCount(pieceQuantityBO2.getPieceQuantity());
        }
        PieceQuantityBO pieceQuantityBO3 =
                maintenance.stream().filter(e -> null != e.getId() && null != bo.getId() && e.getId().equals(bo.getId())).findFirst().orElse(null);
        if (null != pieceQuantityBO3) {
            bo.setMaintainCount(pieceQuantityBO3.getPieceQuantity());
        }
        PieceQuantityBO pieceQuantityBO4 =
                toBeProcessed.stream().filter(e -> null != e.getId() && null != bo.getId() && e.getId().equals(bo.getId())).findFirst().orElse(null);
        if (null != pieceQuantityBO4) {
            bo.setProcessedCount(pieceQuantityBO4.getPieceQuantity());
        }
        setAllData(lossReportedAndCashAmount, smallSales, bo);
    }

    private void setAllData(List<LossReportedAndCashAmountBO> lossReportedAndCashAmount,
                            List<SmallSalesBO> smallSales, SmallproSstaticsWithFilterBO bo) {
        SmallSalesBO salesBO =
                smallSales.stream().filter(e -> e.getId().equals(bo.getId())).findFirst().orElse(null);
        if (null != salesBO) {
            bo.setSaleCount(salesBO.getSmallSaleNums());
            bo.setSaleAmount(salesBO.getSmallSales());
            bo.setSaleProfit(salesBO.getSmallSalesProfit());
        }
        LossReportedAndCashAmountBO lossReportedAndCashAmountBO =
                lossReportedAndCashAmount.stream().filter(e -> null != e.getId() && null != bo.getId() && e.getId().equals(bo.getId())).findFirst().orElse(null);
        if (null != lossReportedAndCashAmountBO) {
            bo.setDamagedCount(lossReportedAndCashAmountBO.getLossReported());
            bo.setDamagedAmount(lossReportedAndCashAmountBO.getAmountReported() == null ? 0 :
                    lossReportedAndCashAmountBO.getAmountReported());
            bo.setConvertInventoryCount(lossReportedAndCashAmountBO.getCashAmount());
            bo.setConvertInventoryAmount(lossReportedAndCashAmountBO.getCashTransfer() == null ? 0 :
                    lossReportedAndCashAmountBO.getCashTransfer());
        }
    }
}
