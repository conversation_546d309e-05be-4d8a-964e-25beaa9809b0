package com.jiuji.oa.afterservice.bigpro.service.strategy;

import com.jiuji.oa.afterservice.bigpro.enums.PlanTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlanMasterType;
import com.jiuji.oa.afterservice.bigpro.service.RepairPlanMasterService;
import com.jiuji.oa.afterservice.bigpro.service.RepairPlanMasterTypeService;
import com.jiuji.oa.afterservice.bigpro.service.strategy.impl.DJIRepairStrategy;
import com.jiuji.oa.afterservice.bigpro.service.strategy.impl.OldForNewStrategy;
import com.jiuji.oa.afterservice.bigpro.service.strategy.impl.ReplaceInsteadRepairStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 维修方案确认策略工厂
 */
@Component
public class RepairPlanConfirmStrategyFactory {
    
    @Resource
    private DJIRepairStrategy djiRepairStrategy;
    
    @Resource
    private ReplaceInsteadRepairStrategy replaceInsteadRepairStrategy;
    
    @Resource
    private OldForNewStrategy oldForNewStrategy;

    @Resource
    private RepairPlanMasterTypeService masterTypeService;
    
    /**
     * 根据维修方案类型获取对应的策略
     * 
     * @param repairPlanMasterTypeId 维修方案类型
     * @return 对应的策略实现
     */
    public RepairPlanConfirmStrategy getStrategy(Integer repairPlanMasterTypeId) {
        RepairPlanMasterType planMasterType = Optional.ofNullable(masterTypeService.getById(repairPlanMasterTypeId)).orElseThrow(() -> new RuntimeException("不存在的维修方案类型"));
        Integer planType = planMasterType.getPlanType();
        if (PlanTypeEnum.DJI_REPAIR.getCode().equals(planType)) {
            return djiRepairStrategy;
        } else if (PlanTypeEnum.REPLACE_INSTEAD_REPAIR.getCode().equals(planType)) {
            return replaceInsteadRepairStrategy;
        } else if (PlanTypeEnum.OLD_FOR_NEW.getCode().equals(planType)) {
            return oldForNewStrategy;
        } else {
            throw new RuntimeException("不支持的维修方案类型");
        }
    }
}
