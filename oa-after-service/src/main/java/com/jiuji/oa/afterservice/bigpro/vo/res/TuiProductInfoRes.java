package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 获取可接件商品信息响应
 */
@Data
public class TuiProductInfoRes {

    @ApiModelProperty(value = "订单号")
    private Integer subId;
    /**
     * 订单类型
     */
    private String subIdTypeStr;
    /**
     * 购买时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime subDate;

    private List<YuyueProductInfoRes> productInfoList;
} 