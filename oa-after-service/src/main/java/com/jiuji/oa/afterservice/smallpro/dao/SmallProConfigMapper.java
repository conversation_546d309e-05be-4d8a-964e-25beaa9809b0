package com.jiuji.oa.afterservice.smallpro.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.cloud.after.vo.req.SmallExchangeReq;
import com.jiuji.oa.afterservice.bigpro.po.SmallConfigProductInfoPo;
import com.jiuji.oa.afterservice.shouhou.vo.req.SelectConfigToWebReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.SmallExchangeConfigRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.SmallExchangeProductConfigRes;
import com.jiuji.oa.afterservice.smallpro.po.exchange.ProductExchangePo;
import com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeConfigPo;
import com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeProductConfigPo;
import com.jiuji.oa.afterservice.smallpro.po.refund.ProductRefundPo;
import com.jiuji.oa.afterservice.smallpro.po.refund.SmallRefundConfigPo;
import com.jiuji.oa.afterservice.smallpro.po.refund.SmallRefundProductConfigPo;
import com.jiuji.oa.afterservice.smallpro.vo.req.ExchangeGoodsConfigReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.ExchangeGoodsConfigSaveReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.RefundGoodsConfigReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.RefundGoodsConfigSaveReq;
import com.jiuji.oa.afterservice.smallpro.vo.res.ExchangeGoodsConfigRes;
import com.jiuji.oa.afterservice.smallpro.vo.res.RefundGoodsConfigRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 小件配置服务类
 * <AUTHOR>
 * @since 2022/4/28 10:22
 */
@Mapper
public interface SmallProConfigMapper {
    /**
     * 获取配置信息
     *
     * @param serviceType
     * @param configTypeAndValues
     * @param wordKey oa的搜索关键词
     * @param webKeyword 来自web的搜索关键词
     * @return
     */
    SmallExchangeConfigPo getConfig(@Param("configType") Integer configType, @Param("serviceType") Integer serviceType,
                                    @Param("configTypeAndValues") List<SmallExchangeConfigPo.ConfigTypeAndValue> configTypeAndValues,
                                    @Param("wordKey") Long wordKey,
                                    @Param("webKeyword") String webKeyword,
                                    @Param("ppids") Collection<Integer> ppids
    );


    List<SmallExchangeConfigRes> selectConfigToWeb(@Param("req") SelectConfigToWebReq req);

    List<SmallExchangeProductConfigRes> selectConfigProductByIds(@Param("ids") List<Integer> ids);

    /**
     * 根据id获取小件换货配置的商品
     * @param configId configId
     * @return
     */
    List<SmallExchangeProductConfigPo> listConfigProduct(@Param("configId") Integer configId);

    /**
     * 获取商品信息
     *
     * @param smallConfigProductInfo
     * @param wordKey
     * @param key
     * @param ppids
     * @return
     */
    List<ProductExchangePo> listSmallProductInfo(@Param("smallConfigProductInfo") SmallConfigProductInfoPo smallConfigProductInfo,
                                                 @Param("wordKey") Long wordKey, @Param("webKeyword") String key,
                                                 @Param("ppids") Collection<Integer> ppids);

    /**
     * 获取商品信息
     * @param configInfo
     * @param req 分页请求参数
     * @return
     */
    List<ProductExchangePo> listSmallProductInfoForWeb(@Param("config") SmallConfigProductInfoPo configInfo,
                                                       @Param("req") SmallExchangeReq req);

    /**
     * 获取商品信息
     * @param configInfo
     * @param req 分页请求参数
     * @return
     */
    Integer countSmallProductInfoForWeb(@Param("config") SmallConfigProductInfoPo configInfo,
                                        @Param("req") SmallExchangeReq req);

    /**
     * 分页获取小件换货配置
     * @param page
     * @param req
     * @return
     */
    Page<ExchangeGoodsConfigRes> getConfigList(Page<ExchangeGoodsConfigReq> page,@Param("req") ExchangeGoodsConfigReq req);

    /**
     * 根据configId批量查询
     * @param exchangeGoodsConfigIds fkConfigIds
     * @return
     */
    List<SmallExchangeProductConfigPo> listConfigProductByIds(@Param("configIds") List<Integer> exchangeGoodsConfigIds);

    /**
     * 根据id删除小件配置信息
     * @param id id
     * @return
     */
    int delExchangeGoodsConfig(@Param("id") Integer id);

    /**
     * 根据id删除小件配置信息
     * @param configId id
     * @return
     */
    int delExchangeProductConfigByConfigId(@Param("configId") Integer configId);

    /**
     * 添加配置
     * @param req
     * @return
     */
    int addExchangeConfig(@Param("req") ExchangeGoodsConfigSaveReq req ,@Param("userName") String userName);

    /**
     * 添加商品配置
     * @param id fk_id
     * @param configTypeAndValues configTypeAndValues
     * @param userName userName
     */
    int addExchangeProductConfig(@Param("id") int id,@Param("configTypeAndValues") List<SmallExchangeConfigPo.ConfigTypeAndValue> configTypeAndValues,@Param("userName") String userName);

    /**
     * 根据id查询换货配置
     * @param id
     * @return
     */
    SmallExchangeConfigPo getConfigById(@Param("id") Integer id);

    /**
     * 更新配置
     * @param req req
     * @param userName userName
     * @return
     */
    int updateExchangeConfig(@Param("req") ExchangeGoodsConfigSaveReq req,@Param("userName") String userName);

    /**
     * 更新商品配置
     * @param configId id
     * @param smallExchangeProductConfigPoList smallExchangeProductConfigPoList
     * @param userName userName
     * @return
     */
    int updateExchangeProductConfig(@Param("configId") Integer configId,@Param("smallExchangeProductConfigPoList") List<SmallExchangeProductConfigPo> smallExchangeProductConfigPoList, @Param("userName") String userName);


    /**
     * 删除配置
     * @param configId configId
     * @param smallExchangeProductConfigPoList smallExchangeProductConfigPoList
     * @param userName userName
     * @return
     */
    int delExchangeProductConfig(@Param("configId") Integer configId,@Param("smallExchangeProductConfigPoList") List<SmallExchangeProductConfigPo> smallExchangeProductConfigPoList, @Param("userName") String userName);

    /**
     * 分页获取小件退货配置
     * @param page
     * @param req
     * @return
     */
    Page<RefundGoodsConfigRes> getRefundConfigList(Page<RefundGoodsConfigReq> page, RefundGoodsConfigReq req);

    /**
     * 添加小件退货配置
     * @param req
     * @param userName
     * @return
     */
    int addRefundConfig(@Param("req") RefundGoodsConfigSaveReq req, @Param("userName") String userName);

    /**
     * 添加小件商品配置
     * @param id fk_config_id
     * @param configTypeAndValues configTypeAndValues
     * @param userName  userName
     * @return
     */
    int addRefundProductConfig(@Param("id") Integer id, @Param("configTypeAndValues") List<SmallRefundConfigPo.ConfigTypeAndValue> configTypeAndValues, @Param("userName") String userName);

    /**
     * 根据configId批量查询
     * @param refundGoodsConfigIds fkConfigIds
     * @return
     */
    List<SmallRefundProductConfigPo> listRefundConfigProductByIds(@Param("refundGoodsConfigIds") List<Integer> refundGoodsConfigIds);

    /**
     * 根据id获取小件退货配置
     * @param id
     * @return
     */
    SmallRefundConfigPo getRefundConfigById(@Param("id") Integer id);

    /**
     * 根据id更新小件退货配置删除状态
     * @param id
     * @return
     */
    int delRefundGoodsConfig(@Param("id") Integer id);

    /**
     * 根据id更新小件退货配置商品删除状态
     * @param id
     * @return
     */
    int delRefundProductConfigByConfigId(Integer id);

    /**
     * 根据小件退货配置的id  查询小件退货配置的相关商品信息
     * @param id 小件退货配置的id
     * @return
     */
    List<SmallRefundProductConfigPo> listRefundConfigProduct(@Param("configId") Integer id);

    /**
     * 更新小件退货配置
     * @param req req
     * @param userName userName
     */
    void updateRefundConfig(@Param("req") RefundGoodsConfigSaveReq req,@Param("userName") String userName);

    /**
     * 更新配置
     * @param exchangeProductConfigPo
     * @return
     */
    int updateExchangeProductConfigById(@Param("req") SmallExchangeConfigPo exchangeProductConfigPo);

    /**
     * 添加配置
     * @param exchangeConfigPo
     * @return
     */
    int saveExchangeConfig(@Param("req") SmallExchangeConfigPo exchangeConfigPo);

    /**
     * 批量换货商品配置信息
     * @param productConfigPoList
     * @return
     */
    int saveExchangeProductConfigBatch(@Param("req") List<SmallExchangeProductConfigPo> productConfigPoList);

    /**
     * 批量删除商品配置信息
     * @param ids
     * @return
     */
    int delExchangeProductConfigByIds(@Param("ids") List<Integer> ids);

    /**
     * 更新小件退货配置的商品
     * @param id id
     * @param addProductConfigPoList addProductConfigPoList
     * @param userName userName
     */
    void updateRefundProductConfig(@Param("configId")Integer id,@Param("addProductConfigPoList") List<SmallRefundProductConfigPo> addProductConfigPoList,@Param("userName") String userName);

    /**
     * 删除小件退货配置的商品
     * @param id id
     * @param delProductConfigPoList delProductConfigPoList
     * @param userName userName
     */
    void delRefundProductConfig(@Param("configId") Integer id,@Param("delProductConfigPoList") List<SmallRefundProductConfigPo> delProductConfigPoList,@Param("userName") String userName);

    /**
     * 根据ppid查询分类 品牌等数据
     * @param ppid
     * @return
     */
    ProductRefundPo listRefundSmallProductInfo(@Param("ppid") Integer ppid);

    /**
     * 根据配置的值来获取所有匹配的规则
     * @param configTypeAndValue configTypeAndValue
     * @return
     */
    SmallRefundConfigPo getRefundConfig(@Param("configTypeAndValue") SmallRefundConfigPo.ConfigTypeAndValue configTypeAndValue,
                                        @Param("days") int days,@Param("xtenant") int xtenant,
                                        @Param("smallRefundServiceType") int smallRefundServiceType);
}