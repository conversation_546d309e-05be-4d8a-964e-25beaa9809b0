package com.jiuji.oa.afterservice.bigpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 维修方案类型枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Getter
@AllArgsConstructor
public enum PlanTypeEnum implements CodeMessageEnumInterface {
    
    /**
     * 以旧换新
     */
    OLD_FOR_NEW(1, "以旧换新",8),
    
    /**
     * 以换代修
     */
    REPLACE_INSTEAD_REPAIR(2, "以换代修",15),
    
    /**
     * 大疆维修
     */
    DJI_REPAIR(3, "大疆维修",3);
    
    private final Integer code;
    private final String message;
    private final Integer rank;

    
    /**
     * 根据code获取枚举
     * @param code 代码
     * @return 枚举
     */
    public static PlanTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PlanTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取描述
     * @param code 代码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        PlanTypeEnum enumItem = getByCode(code);
        return enumItem == null ? "" : enumItem.getMessage();
    }

    /**
     * 根据code获取rank
     * @param code 代码
     * @return rank值
     */
    public static Integer getRankByCode(Integer code) {
        PlanTypeEnum enumItem = getByCode(code);
        return enumItem != null ? enumItem.getRank() : Integer.MAX_VALUE;
    }
    
    /**
     * 获取所有枚举值的列表
     * @return 枚举值列表
     */
    public static Map<Integer, String> getAll() {
        Map<Integer, String> map = new HashMap<>();
        for (PlanTypeEnum item : values()) {
            map.put(item.getCode(), item.getMessage());
        }
        return map;
    }
} 