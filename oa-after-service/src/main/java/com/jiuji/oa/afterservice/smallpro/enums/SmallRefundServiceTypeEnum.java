package com.jiuji.oa.afterservice.smallpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2022/6/1 10:00
 * @Description 小件退货类型
 */
@Getter
@AllArgsConstructor
public enum SmallRefundServiceTypeEnum implements CodeMessageEnumInterface {
    SMALL_REGULAR_SMALL_PARTS(1, "常规小件"),
    SMALL_HALF_YEAR_SERVICE(2, "半年服务"),
    SMALL_ONE_YEAR_SERVICE(3, "一年服务"),
    SMALL_TWO_YEARS_AND_ABOVE_SERVICE(4, "两年及以上服务");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;

}