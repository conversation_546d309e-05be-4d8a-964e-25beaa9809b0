package com.jiuji.oa.afterservice.common.enums;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.sys.dao.SqlServerColumnMapper;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.Duration;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 历史表信息枚举
 * Enum for Table Information
 *
 * <AUTHOR>
 * @date 2023/11/27
 * @since 1.0.0
 */
@RequiredArgsConstructor
@Getter
@Slf4j
public enum MTableInfoEnum implements CodeMessageEnumInterface {

    SUB("sub", "sub_id","sub_date", "销售订单表"),
    BASKET("basket", "basket_id","basket_date", "销售商品明细表"),
    RECOVER_MARKET_INFO("recover_marketInfo", "sub_id","sub_date", "良品订单表"),
    RECOVER_MARKET_SUB_INFO("recover_marketSubInfo", "basket_id","basket_date", "良品订单明细表"),
    SHOUYING_SUB_ID("shouying", "sub_id","dtime","shouying_type", "收银表"),
    SHOUYIN_OTHER("shouyin_other", "id", null, "收银表"),
    NET_PAY_RECORD("netpay_record", "sub_number", true,"dtime","type", "网络支付表"),
    RECOVER_SUB("recover_sub", "sub_id","dtime", "回收单表"),
    RECOVER_BASKET("recover_basket", "id","intime", "回收单商品信息表"),
    SERVICERECORD("ServiceRecord", "id","tradedate", "服务绑定表"),

    SHOUHOU("shouhou", "id","modidate", "维修单主表"),
    wxkcoutput("wxkcoutput", "id","dtime", "维修单配件表"),
    SHOUHOU_SERVICE_REPORT("shouhou_service_report", "id","create_time", "维修单检测报告表"),
    SHOUHOU_TUIHUAN("shouhou_tuihuan", "id","dtime", "售后退还表"),
    SHOUHOU_OTHER("shouhou_other", "shouhouid","timeoutdate", "售后进程通知表"),
    SHOUHOU_TEST_RESULT_INFO("shouhou_test_result_info", "shouhou_id","create_time", "维修单测试主表（新）"),
    SHOUHOUTESTINFO("shouhoutestInfo", "shouhou_id","dtime", "维修单测试主表（老）"),
    MACHINE_HERO_REPORT("machine_hero_report", "shouhou_id","create_time", "机大侠测试主表"),
    MACHINE_HERO_REPORT_DETAIL("machine_hero_report_detail", "shouhou_id","create_time", "机大侠测试从表"),
    WXKCOUTPUT_WXID("wxkcoutput", "wxid","dtime", "维修单配件表"),
    WXKCOUTPUT_BIND_ID("wxkcoutput", "bind_id","dtime", "维修单配件表"),
    SHOUHOU_RISK_NOTIFICATION_WXID("shouhou_risk_notification", "shouhou_id","create_time", "售后大件风险告知"),


    ;
    /**
     * 表名
     */
    private final String code;
    /**
     * 最大或最小列名
     */
    private final String mmColumnName;
    /**
     * 最大或最小列是varchar类型
     */
    private Boolean mmIsVarChar;
    /**
     * 创建时间列名
     */
    private final String creatTimeColumnName;
    /**
     * 表类类型名称
     */
    private String typeColumnName;
    /**
     * 表描述
     */
    private final String message;

    MTableInfoEnum(String code, String mmColumnName,String creatTimeColumnName, String typeColumnName, String message) {
        this.code = code;
        this.mmColumnName = mmColumnName;
        this.creatTimeColumnName= creatTimeColumnName;
        this.typeColumnName = typeColumnName;
        this.message = message;
    }



    MTableInfoEnum(String code, String mmColumnName,boolean mmIsVarChar,String creatTimeColumnName, String typeColumnName, String message) {
        this(code, mmColumnName, creatTimeColumnName, typeColumnName, message);
        this.mmIsVarChar = mmIsVarChar;
    }

    public boolean isHistory(Long tableIdParam, Object ... tableTypes){
        Long tableId = ObjectUtil.defaultIfNull(tableIdParam, 0L);
        RedisTemplate<String, String> redisTemplate = SpringUtil.getBean("redisTemplate", RedisTemplate.class);
        String typeColumnName = ObjectUtil.defaultIfBlank(this.typeColumnName, StringPool.EMPTY);
        String typeStr = Arrays.stream(tableTypes).sorted().map(Convert::toStr).collect(Collectors.joining(StringPool.COMMA));
        String ishistoryKey = StrUtil.format(RedisKeys.CH999OANEW_TABLE_ID_IS_HISTORY_KEY,
                this.code,this.mmColumnName, tableId,typeColumnName, typeStr);

        if(redisTemplate.hasKey(ishistoryKey)){
            return Convert.toBool(redisTemplate.opsForValue().get(ishistoryKey), Boolean.FALSE);
        }

        String maxCachekey = StrUtil.format(RedisKeys.SQLSERVER_DB_TABLE_MAX_ID_KEY, DataSourceConstants.OA_NEW_HIS,this.code, this.mmColumnName
                , typeColumnName, typeStr);
        Long maxId;
        Duration cacheDuration = Duration.ofMinutes(RandomUtil.randomInt(100, 120));
        if(redisTemplate.hasKey(maxCachekey)){
            maxId = Convert.toLong(redisTemplate.opsForValue().get(maxCachekey), 0L);
        }else{
            maxId = ObjectUtil.defaultIfNull(MultipleTransaction.query( DataSourceConstants.OA_NEW_HIS, () -> SpringUtil.getBean(SqlServerColumnMapper.class)
                    .selectMax(this, Arrays.asList(tableTypes))), 0L);
            redisTemplate.opsForValue().set(maxCachekey, Convert.toStr(maxId), cacheDuration);
        }
        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "历史标记最大值缓存key: {}, 最大值: {},是否历史key: {}", maxCachekey, maxId, ishistoryKey);
        boolean isHist = false;
        if(tableId <= maxId){
            Object qTableId;
            if(Boolean.TRUE.equals(this.mmIsVarChar)){
                qTableId = Convert.toStr(tableId);
            }else{
                qTableId = tableId;
            }
            isHist = MultipleTransaction.query( DataSourceConstants.DEFAULT, () -> SpringUtil.getBean(SqlServerColumnMapper.class)
                    .selectId(this, qTableId, Arrays.asList(tableTypes))) == null;
            redisTemplate.opsForValue().set(ishistoryKey, Convert.toStr(isHist), cacheDuration);
        }

        return isHist;
    }

}
