package com.jiuji.oa.afterservice.sys.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouExService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouRefundService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.vo.refund.TuihuanFormVo;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeV1Enum;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.smallpro.service.SmallproExchangePurchaseService;
import com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes;
import com.jiuji.oa.afterservice.sys.bo.ValidBO;
import com.jiuji.oa.afterservice.sys.dao.ValidMemberMapper;
import com.jiuji.oa.afterservice.sys.service.UserTokenService;
import com.jiuji.oa.afterservice.sys.service.ValidMemberService;
import com.jiuji.oa.afterservice.sys.vo.req.ValidMemberReq;
import com.jiuji.oa.afterservice.sys.vo.req.ValidReq;
import com.jiuji.oa.oacore.csharp.cloud.CsharpCloud;
import com.jiuji.oa.oacore.csharp.vo.res.MemberShipCodeModelRes;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.res.ResultModel;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * 验证码验证服务(短信验证码 会员识别码 支付密码验证)
 *
 * <AUTHOR>
 * @since 2021/11/30 16:53
 */
@Service
@Slf4j
public class ValidMemberServiceImpl implements ValidMemberService {
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MemberClient memberClient;
    @Resource
    private ValidMemberMapper validMemberMapper;
    @Resource
    private ShouhouExService shouhouExService;
    @Resource
    private ShouhouRefundService shouhouRefundService;
    @Resource
    private SmallproExchangePurchaseService smallproExchangePurchaseService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private ShouhouService shouhouService;
    @Resource
    private UserTokenService userTokenService;
    @Resource
    private CsharpCloud csharpCloud;

    @Override
    public R<Boolean> sendCode(ValidMemberReq req) {
        ValidBO mobileAndUserId = getMobileAndUserId(req.getBusinessType(), req.getOrderId());
        //不同的场景采用不同验证码的发放
        //
        if (Objects.equals(req.getBusinessType(), BusinessTypeV1Enum.AFTER_OUT.getCode())) {
            if (CommenUtil.isNullOrZero(mobileAndUserId.getShouhouId())) {
                return R.success("发送验证码失败,请检查维修单号或订单号！");
            }
            //维修取机操作
            return shouhouExService.sendSrcode(mobileAndUserId.getShouhouId(), mobileAndUserId.getSubId());
        }
        //维修费用退款操作校验
        if (Objects.equals(req.getBusinessType(), BusinessTypeV1Enum.AFTER_REFUND.getCode())) {
            if (CommenUtil.isNullOrZero(mobileAndUserId.getShouhouId()) || Objects.isNull(mobileAndUserId.getTuihuanKind())) {
                return R.success("发送验证码失败,参数是否正确！");
            }
            return shouhouRefundService.sendCode(mobileAndUserId.getShouhouId(), mobileAndUserId.getTuihuanId(), mobileAndUserId.getTuihuanKind());
        }
        //小件退货操作校验
        if (Objects.equals(req.getBusinessType(), BusinessTypeV1Enum.AFTER_SMALL_RETURN.getCode())) {
            mobileAndUserId.setType(NumberConstant.TWO);
            return getBooleanR(mobileAndUserId);
        }
        //小件换货操作校验
        if (Objects.equals(req.getBusinessType(), BusinessTypeV1Enum.AFTER_SMALL_CHANGE.getCode())) {
            mobileAndUserId.setType(NumberConstant.ONE);
            return getBooleanR(mobileAndUserId);
        }
        //以后的短信验证 统一走下面的接口
        BusinessTypeV1Enum enumByCode = Optional.ofNullable(EnumUtil.getEnumByCode(BusinessTypeV1Enum.class, req.getBusinessType())).orElse(BusinessTypeV1Enum.UNKNOWN);
        if (CommenUtil.isNullOrZero(enumByCode.getCode())) {
            return sendSmsCode(enumByCode, req.getOrderId(), mobileAndUserId.getMobile());
        }
        return R.error("发送验证码失败!");
    }

    private R<Boolean> getBooleanR(ValidBO mobileAndUserId) {
        if (CommenUtil.isNullOrZero(mobileAndUserId.getSmallproId()) || CommenUtil.isNullOrZero(mobileAndUserId.getAreaId()) || CommenUtil.isNullOrZero(mobileAndUserId.getSubId())) {
            return R.success("发送验证码失败,参数是否正确！");
        }
        SmallproNormalCodeMessageRes result = smallproExchangePurchaseService.pushCodeMessageToUser(mobileAndUserId.getSmallproId(), mobileAndUserId.getAreaId(), mobileAndUserId.getSubId(), mobileAndUserId.getType());
        if (result.getCode() != 0) {
            return R.error(result.getMessage());
        }
        return R.success(true);
    }

    private R<Boolean> sendSmsCode(BusinessTypeV1Enum businessType, Long subId, String mobile) {
        int code = CommonUtils.getRandom4Code();
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        SmsService smsService = SpringUtil.getBean(SmsService.class);
        String msg = StrUtil.format("您正在进行[{}]操作，验证码{}，5分钟内有效，售后单号：{}。如非本人操作请忽略。", businessType.getMessage(),code, subId);
        R<Boolean> result = smsService.sendSms(mobile,msg
                , DateUtil.localDateTime2LocalDate(LocalDateTime.now().minusMinutes(NumberConstant.TWO))
                , oaUser.getUserName(), smsService.getSmsChannelByTenant(oaUser.getAreaId(), ESmsChannelTypeEnum.VERIFICATION_CODE));
        if (result.isSuccess()) {
            log.info("{}id:{},验证码:{}", businessType.getMessage(), subId, code);
            stringRedisTemplate.opsForValue().set(StrUtil.format(RedisKeys.AFTER_SMS_CODE_KEY, businessType.getCode(), subId)
                    , String.valueOf(code), Duration.ofMinutes(NumberConstant.FIVE));
            return R.success("验证码发送成功", Boolean.TRUE);
        } else {
            return new R(ResultCode.SERVER_ERROR, result.getMsg(), result.getUserMsg()).addAllBusinessLog(result.businessLogs());
        }
    }

    @Override
    public R<Boolean> validByAssertCheck(ValidReq req, TuihuanFormVo tuihuanForm) {
        //维修费用退款 专属接口
        if (Objects.equals(req.getBusinessType(), BusinessTypeV1Enum.AFTER_REFUND.getCode())) {
            ValidBO mobileAndUserId = getMobileAndUserId(req.getBusinessType(), req.getOrderId());
            return validCode(tuihuanForm.getShouhouId(), tuihuanForm.getTuihuanId(), tuihuanForm.getTuihuanKind(), req.getValidType(), req.getValidCode(), mobileAndUserId.getUserid());
        }
        return R.error("验证失败!");
    }

    private ValidBO getMobileAndUserId(@Param("businessType") Integer businessType, @Param("orderId") Long orderId){
        ValidBO validBO = new ValidBO();
        if(Arrays.asList(NumberConstant.ONE, NumberConstant.TWO).contains(businessType)){
            //如果是查询 shouhou表和shouhou_tuihuan表那就进行历史库处理
            validBO = CommenUtil.autoQueryHist(() -> validMemberMapper.getMobileAndUserId(businessType, orderId), MTableInfoEnum.SHOUHOU, orderId);
        } else {
            //如果是查询小件那就不进行历史库查询
            validBO = validMemberMapper.getMobileAndUserId(businessType, orderId);
        }
        return validBO;
    }

    @Override
    public R<Boolean> valid(ValidReq req) {
        if (req.getValidCode() == null || req.getValidType() == null || req.getBusinessType() == null || req.getOrderId() == null) {
            return R.error("参数不能为空！");
        }
        ValidBO mobileAndUserId = getMobileAndUserId(req.getBusinessType(), req.getOrderId());
        if (ObjectUtil.isNull(mobileAndUserId)) {
            return R.error("请核对参数是否正确！");
        }
        //维修取机操作
        if (Objects.equals(req.getBusinessType(), BusinessTypeV1Enum.AFTER_OUT.getCode())) {
            return saveSrcode(mobileAndUserId.getShouhouId(), req.getValidCode(), req.getValidType(), mobileAndUserId.getUserid());
        }
        //小件退货
        if (Objects.equals(req.getBusinessType(), BusinessTypeV1Enum.AFTER_SMALL_RETURN.getCode())) {
            mobileAndUserId.setType(NumberConstant.TWO);
            SmallproNormalCodeMessageRes result = smallproExchangePurchaseService.checkCodeMessage(mobileAndUserId.getSmallproId(), req.getValidCode(), mobileAndUserId.getType(), req.getValidType());
            if (result.getCode() != 0) {
                return R.error(result.getMessage());
            }
            return R.success(true);
        }
        //小件换货
        if (Objects.equals(req.getBusinessType(), BusinessTypeV1Enum.AFTER_SMALL_CHANGE.getCode())) {
            if (StringUtils.isBlank(req.getValidCode())) {
                return R.error("请输入验证码!");
            }
            if (req.getOrderId() == null || req.getOrderId() <= 0) {
                return R.error("小件id不能为空!");
            }
            if (req.getValidType() == null || req.getValidType() <= 0) {
                return R.error("请选择校验方式！");
            }
            OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
            String tempCode = req.getValidCode();
            if (Objects.equals("授权", req.getValidCode())) {
                if (!oaUserBO.getRank().contains("6f7")) {
                    return R.error("您没有权限！权值:6f7");
                }
                tempCode = MessageFormat.format("授权验证 【{0}】 {1}", oaUserBO.getUserName()
                        , LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            mobileAndUserId.setType(NumberConstant.ONE);
            SmallproNormalCodeMessageRes result = smallproExchangePurchaseService.checkCodeMessage(mobileAndUserId.getSmallproId(), tempCode, mobileAndUserId.getType(), req.getValidType());
            if (result.getCode() != 0) {
                return R.error(result.getMessage());
            }
            return R.success(true);
        }
        return R.error("验证失败!");
    }

    @Override
    public boolean support(BusinessTypeV1Enum businessTypeEnum) {
        if(businessTypeEnum == null){
            return false;
        }
        switch (businessTypeEnum){
            case AFTER_OUT:
            case AFTER_REFUND:
            case AFTER_SMALL_RETURN:
            case AFTER_SMALL_CHANGE:
                return true;
            default:
                return false;
        }
    }


    private R<Boolean> validCode(Integer shouhouId, Integer tuihuanId, Integer tuihuanKind, Integer validType, String validCode, Long userId) {
        //验证码验证 1 识别码 2 支付密码 3 短信
        try {
            if (Objects.equals(validType, TuihuanFormVo.ValidTypeEnum.SMS.getCode())) {
                String code = stringRedisTemplate.opsForValue().get(StrUtil.format(RedisKeys.SHOUHOU_TUIHUAN_VALID_CODE, shouhouId, tuihuanId, tuihuanKind));
                Assert.isFalse(StrUtil.isBlank(code), "请先发送验证码");
                Assert.isTrue(Objects.equals(validCode, code), "验证码错误！");
            } else if (Objects.equals(validType, TuihuanFormVo.ValidTypeEnum.MEMBER_CODE.getCode())) {
                assertValidMemberToken(validCode, userId);
            } else if (Objects.equals(validType, TuihuanFormVo.ValidTypeEnum.PAY_PASSWORD.getCode())) {
                //支付密码
                assertValidPayPwd(validCode, userId);
            } else {
                return R.error("验证码类型错误");
            }
        } catch (IllegalArgumentException e) {
            return R.error(ResultCode.NEED_VERIFY, e.getMessage());
        }
        return R.success(Boolean.TRUE);
    }

    /**
     * 验证会员识别码
     * @param paramValidCode validCode
     * @param userId userId
     */
    @Override
    public void assertValidMemberToken(String paramValidCode, Long userId) {
        //识别码的处理,如果首字符为A或a字母去掉
        String validCode;
        if(StrUtil.equalsCharAt(paramValidCode,0,'a') || StrUtil.equalsCharAt(paramValidCode,0,'A')){
            validCode = paramValidCode.substring(1);
        }else{
            validCode = paramValidCode;
        }
        if(StringUtils.isNotBlank(paramValidCode) && paramValidCode.length() > UserTokenServiceImpl.SEPARATING_VALUES && XtenantEnum.isJiujiXtenant()){
            validCode = paramValidCode;
            //以 membershipCode:  这个开头的走C#接口验证
            if (validCode.startsWith("membershipCode:")) {
                R<MemberShipCodeModelRes> memberShipCodeModelRes = csharpCloud.checkMemberShipCode(validCode);
                if (memberShipCodeModelRes != null && memberShipCodeModelRes.isSuccess() && memberShipCodeModelRes.getData() != null) {
                    MemberShipCodeModelRes memberShipCodeModelResData = memberShipCodeModelRes.getData();
                    if (Objects.nonNull(memberShipCodeModelResData.getExpireTime()) && memberShipCodeModelResData.getExpireTime().isAfter(LocalDateTime.now())) {
                        validCode = memberShipCodeModelResData.getCode();
                    } else {
                        throw new CustomizeException("识别码已失效，请输入新的识别码，重新验证");
                    }
                } else {
                    throw new CustomizeException(Optional.ofNullable(memberShipCodeModelRes).map(R::getUserMsg).orElse("识别码已失效，请输入新的识别码，重新验证"));
                }
            }
        }
        Integer userIdByToken = userTokenService.getUserIdByToken(validCode);
        if (CommenUtil.isNotNullZero(userIdByToken)) {
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "识别码会员id: {}", userIdByToken);
            boolean isValidSuccess = Objects.equals(Math.toIntExact(userId), userIdByToken);
            Assert.isTrue(isValidSuccess, "识别码与用当前户不匹配，请输入本人识别码，重新验证");
        } else {
            Assert.isTrue(false, "识别码已失效，请输入新的识别码，重新验证");
        }
        //清除缓存
        userTokenService.delUserIdByToken(validCode);
    }

    /**
     * 验证支付密码
     * @param validCode validCode
     * @param userId userId
     */
    @Override
    public void assertValidPayPwd(String validCode, Long userId) {
        Optional<R<ResultModel>> resultModelR = Optional.ofNullable(SpringUtil.getBean(MemberClient.class)
                .validtPayPwd(Convert.toInt(userId),validCode));
        Assert.isTrue(resultModelR.map(R::isSuccess).orElse(Boolean.FALSE), resultModelR.map(R::getUserMsg).orElse("支付密码验证接口返回空"));
        Assert.isTrue(resultModelR.map(R::getData).map(d -> Objects.equals(d.getStats(),NumberConstant.ONE)).orElse(Boolean.FALSE),resultModelR.map(R::getData)
                .map(ResultModel::getResult).orElse("支付密码错误"));
    }

    private R<Boolean> saveSrcode(Integer shouhouId, String validCode, Integer validType, Long userId) {
        try {
            Assert.isFalse(Objects.isNull(userId), "会员id不能为空");
            if (Objects.equals(validType, TuihuanFormVo.ValidTypeEnum.SMS.getCode())) {
                String code = stringRedisTemplate.opsForValue().get("srCode" + shouhouId);
                Assert.isFalse(StrUtil.isBlank(code), "请先发送验证码");
                Assert.isTrue(Objects.equals(validCode, code), "验证码错误！");
                //验证通过时存储数据库
                boolean flag = shouhouService.update(new UpdateWrapper<Shouhou>().lambda().set(Shouhou::getCodeMsg, validCode).eq(Shouhou::getId, shouhouId));
                Assert.isTrue(flag, "保存失败！");
                return R.success("保存成功！");
            } else if (Objects.equals(validType, TuihuanFormVo.ValidTypeEnum.MEMBER_CODE.getCode())) {
                //会员识别码校验
                assertValidMemberToken(validCode, userId);
                //验证通过时存储数据库
                boolean flag = shouhouService.update(new UpdateWrapper<Shouhou>().lambda().set(Shouhou::getCodeMsg, validCode).eq(Shouhou::getId, shouhouId));
                Assert.isTrue(flag, "保存失败！");
                return R.success("保存成功！");
            } else if (Objects.equals(validType, TuihuanFormVo.ValidTypeEnum.PAY_PASSWORD.getCode())) {
                //支付密码验证
                assertValidPayPwd(validCode, userId);
                return R.success("验证成功！");
            } else {
                return R.error("验证码类型错误！");
            }
        } catch (IllegalArgumentException e) {
            return R.error(ResultCode.NEED_VERIFY, e.getMessage());
        }
    }
}
