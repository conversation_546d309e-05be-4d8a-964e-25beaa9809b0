package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jiuji.oa.afterservice.bigpro.dao.KaoqinMapper;
import com.jiuji.oa.afterservice.bigpro.po.Kaoqin;
import com.jiuji.oa.afterservice.bigpro.service.KaoqinService;
import com.jiuji.tc.utils.common.CommonUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-18
 */
@Service
public class KaoqinServiceImpl extends ServiceImpl<KaoqinMapper, Kaoqin> implements KaoqinService {

    @Override
    public List<Integer> getCurrentWork(List<Integer> ch999Ids) {
        if(CollectionUtils.isEmpty(ch999Ids)){
            return null;
        }
        return CommonUtils.bigDataInQuery(ch999Ids,baseMapper::getCurrentWork);
    }

    @Override
    public List<Integer> getCurrentWorkByAreaIdAndCh999Ids(Integer areaId, List<Integer> ch999Ids) {
        return CommonUtils.bigDataInQuery(ch999Ids,ids -> baseMapper.getCurrentWorkByAreaIdAndCh999Ids(areaId,ids));
    }

    @Override
    public List<Integer> getCurrentWorkByAreaIdAndRoleIds(Integer areaId, List<Integer> roleIds) {
        return CommonUtils.bigDataInQuery(roleIds,ids -> baseMapper.getCurrentWorkByAreaIdAndRoleIds(areaId,ids));
    }

    @Override
    public List<Integer> getCurrentWorkByAreaIdAndZhiWuIds(Integer areaId, List<Integer> zhiWuIds) {
        return CommonUtils.bigDataInQuery(zhiWuIds, ids -> baseMapper.getCurrentWorkByAreaIdAndZhiWuIds(areaId,ids));
    }

    @Override
    public List<Integer> getCh999IdsByRole(List<Integer> roleIds) {
        if(CollUtil.isEmpty(roleIds)){
            return Lists.newArrayList();
        }
        return CommonUtils.bigDataInQuery(roleIds,ids -> baseMapper.getCh999IdsByRole(ids));
    }

    @Override
    public List<Integer> getCh999IdsByzhiWu(List<Integer> zhiWuIds) {
        if(CollUtil.isEmpty(zhiWuIds)){
            return Lists.newArrayList();
        }
        return CommonUtils.bigDataInQuery(zhiWuIds,ids -> baseMapper.getCh999IdsByzhiWu(ids));
    }
}
