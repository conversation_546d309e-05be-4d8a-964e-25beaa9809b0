package com.jiuji.oa.afterservice.bigpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单类型 枚举
 * @author: <PERSON>
 * @date: 2020-12-24
 */
@Getter
@AllArgsConstructor
public enum SubSubTypeEnum implements CodeMessageEnumInterface {
    DAO_DIAN_XIA_DAN(1, "到店下单")
    ,WANG_LUO_DING_DAN(2, "网络订单")
    ,DIAN_HUA_DING_GOU(3, "电话订购")
    ,QQ_DING_GOU(4, "QQ订购")
    ,M_BAN(5, "m版")
    ,APP(6, "app")
    ,MOA(7, "MOA")
    ,SAO_MA_JIA_DAN(8, "扫码加单")
    ,WEI_XIN_XIAO_CHENG_XU(11, "微信小程序")
    ,XUAN_JI_DIAN_NAO(12, "选机电脑")
    ,JING_DONG_DAO_JIA(18, "京东到家")
    ,MEI_TUAN_SHAN_GOU(19, "美团闪购")
    ,APP_AN_ZHUO(16, "APP-安卓")
    ,APP_IOS(17, "APP-IOS")
    ,PEI_JIAN_ZENG_SONG(9, "配件赠送")
    ,FEN_XIAO(10, "分销")
    ,FEI_FEN_XIAO_DING_DAN(-10, "非分销订单")
    ,BAO_ZUN_DING_DAN(21, "宝尊其他订单")
    ,DOU_YIN_DING_DAN(22, "抖音订单")
    ,DOU_YIN_HOUR(32, "抖音小时达")
    ,TAO_BAO(38, "淘宝小时达")
    ;
    /**
     * 状态
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;
}
