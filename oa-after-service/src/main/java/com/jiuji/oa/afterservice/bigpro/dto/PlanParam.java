package com.jiuji.oa.afterservice.bigpro.dto;

import com.jiuji.oa.afterservice.bigpro.po.RepairPlan;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

/**
 * 处理方案的参数类
 */
@Data
public class PlanParam {
    private String currentUser;
    private LocalDateTime now;
    private Integer shouHouId;
    private Map<Integer, RepairPlan> faultPlanMap;
    private Set<Integer> processedPlanIds;

    public PlanParam(String currentUser, LocalDateTime now, Integer shouHouId, 
               Map<Integer, RepairPlan> faultPlanMap, Set<Integer> processedPlanIds) {
        this.currentUser = currentUser;
        this.now = now;
        this.shouHouId = shouHouId;
        this.faultPlanMap = faultPlanMap;
        this.processedPlanIds = processedPlanIds;
    }


} 