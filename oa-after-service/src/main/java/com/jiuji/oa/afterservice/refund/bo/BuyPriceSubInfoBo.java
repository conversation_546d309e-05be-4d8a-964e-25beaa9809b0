package com.jiuji.oa.afterservice.refund.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 购买金额实体Bo
 *
 * <AUTHOR>
 * @since 2022/12/07
 */
@Data
@Accessors(chain = true)
@ApiModel("购买金额实体Bo")
public class BuyPriceSubInfoBo {
    /**
     * 单号(交易单号)
     */
    @ApiModelProperty(value = "单号")
    private Integer orderId;
    /**
     * 订单类型
     * @see com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum
     */
    @ApiModelProperty(value = "业务类型")
    private Integer businessType;
    /**
     * 订单门店id
     */
    @ApiModelProperty(value = "订单门店id")
    private Integer areaId;
    /**
     * 售前单会员编号
     */
    @ApiModelProperty(value = "售前单会员编号")
    private Integer userId;
    /**
     * 售后的门店id
     */
    @ApiModelProperty(value = "售后的门店id")
    private Integer shouhouAreaId;
    /**
     * 总金额(包含折价金额)
     */
    @ApiModelProperty(value = "总金额")
    private BigDecimal totalPrice;

    /**
     * 订单商品销售原价
     */
    @ApiModelProperty(value = "当时销售原价")
    private BigDecimal price1;
    /**
     * 已付金额
     */
    @ApiModelProperty(value = "已付金额")
    private BigDecimal yifuMoney;
    /**
     * 九机币抵扣金额
     */
    @ApiModelProperty(value = "九机币抵扣金额")
    private BigDecimal coinMoney;
    /**
     * 赠品金额
     */
    @ApiModelProperty(value = "总金额")
    private BigDecimal giftPrice;
    /**
     * 成本
     */
    @ApiModelProperty(value = "成本")
    private BigDecimal costPrice;
    /**
     * 交易天数
     */
    @ApiModelProperty(value = "交易天数")
    private Integer tradeDay;
    /**
     * 交易类型
     */
    @ApiModelProperty(value = "交易类型")
    private Integer tradeType;
    /**
     * 开票类型
     */
    @ApiModelProperty(value = "交易类型")
    private Integer piaoType;
    /**
     * 商品配置
     */
    @ApiModelProperty(value = "商品配置")
    private String peizhi;
    /**
     * 不同订单类型,状态不一样
     */
    @ApiModelProperty(value = "订单状态")
    private Integer subCheck;
    /**
     * 订单商品id
     */
    @ApiModelProperty(value = "订单商品id")
    private Integer basketId;
    /**
     * 订单交易时间
     */
    @ApiModelProperty(value = "订单交易时间")
    private LocalDateTime tradeDate;
}
