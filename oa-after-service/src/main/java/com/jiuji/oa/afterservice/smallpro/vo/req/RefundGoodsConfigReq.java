package com.jiuji.oa.afterservice.smallpro.vo.req;

import com.jiuji.oa.afterservice.common.vo.PageReq;
import com.jiuji.oa.afterservice.smallpro.enums.SmallRefundServiceTypeEnum;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/23 11:50
 * @Description 小件换货配置查询参数
 */
@Data
@Accessors(chain = true)
@ApiModel("小件换货配置查询参数")
public class RefundGoodsConfigReq extends PageReq implements Serializable{
    /**
     * 补差价百分比
     */
    @ApiModelProperty(value = "补差价百分比")
    private BigDecimal differentPricePercent;
    /**
     * 服务类型
     *
     * @see SmallRefundServiceTypeEnum
     */
    @ApiModelProperty(value = "服务类型")
    private Integer serviceType;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌ID")
    private List<Integer> brandIdList;
    /**
     * 分类
     */
    @ApiModelProperty(value = "分类ID")
    private List<Integer> cidList;

    /**
     * 配置名称 标题
     */
    @ApiModelProperty(value = "配置名称/标题")
    private String title;

    /**
     * 搜索类型 1-sku 2-spu
     */
    @ApiModelProperty(value = "搜索类型 1-sku 2-spu")
    private Integer searchType;

    /**
     * 搜索值
     */
    @ApiModelProperty(value = "searchValue")
    private String searchValue;

    /**
     * 搜索类型 1-sku 2-spu
     */
    @Getter
    @AllArgsConstructor
    public enum SearchTypeEnum implements CodeMessageEnumInterface {
        SKU(1,"SKU"),
        SPU(2,"SPU")
        ;

        /**
         * 编码
         */
        private Integer code;

        /**
         * 编码对应信息
         */
        private String message;
    }
}
