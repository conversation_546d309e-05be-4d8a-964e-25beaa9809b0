package com.jiuji.oa.afterservice.refund.vo.res.machine;

import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.oa.afterservice.refund.enums.PrioritySmallRefundEnum;
import com.jiuji.oa.afterservice.refund.enums.ZheJiaPayEnum;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;

/**
 * 退换机管理详情对象
 * <AUTHOR>
 * @since 2022/11/16 17:22
 */
@Data
@Accessors(chain = true)
@ApiModel("退换机详情实体vo")
public class ZheJiaMachineVo {

    @ApiModelProperty("当前设备折价金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal zhejiaM;

    @ApiModelProperty("折价支付金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal payZhejiaM;
    @ApiModelProperty("发票折价金额")
    @JSONField(format = "0.00@UP")
    private BigDecimal piaoPrice;
    @ApiModelProperty("支付的发票折价金额")
    @JSONField(format = "0.00@UP")
    private BigDecimal payPiaoPrice;
    @ApiModelProperty("附件折价金额")
    @JSONField(format = "0.00@DOWN")
    @DecimalMin(value = "0.00", message = "附件折价金额不能小于0")
    private BigDecimal peizhiPrice;
    @ApiModelProperty("附件折价金额")
    @JSONField(format = "0.00@DOWN")
    @DecimalMin(value = "0.00", message = "附件折价金额不能小于0")
    private BigDecimal payPeizhiPrice;
    @ApiModelProperty("折价后可退金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal refundPrice;
    @ApiModelProperty("换货信息对象")
    private HuanInfoVo huanInfo;
    @ApiModelProperty("换其他型号订单冲抵金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal subIdM;
    @ApiModelProperty("退款金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal tuikuanM;
    /**
     * 组合退款的信息对象
     */
    @ApiModelProperty("组合退款的信息对象")
    private RefundMoneyDetailVo refundMoneyDetailVo;

    /**
     * 附件折价
     */
    @ApiModelProperty("附件折价")
    private BigDecimal accessoriesPrice;

    @ApiModelProperty("是否显示组合退明细")
    private Boolean isShowGroupDetail;

    /**
     * 是否显示折价支付
     */
    @ApiModelProperty("是否显示折价支付")
    private Boolean isShowZheJiaPay;

    /**
     * @see ZheJiaPayEnum
     */
    @ApiModelProperty("是否显示折价支付")
    private Integer zheJiaPayCode;
    /**
     * 折价支付理由
     */
    @ApiModelProperty("折价支付理由")
    private String zheJiaPayReason;

    private List<ZheJiaPayVo> zheJiaPayList = new LinkedList<>();


    /**
     * 是否显示小件退款
     */
    @ApiModelProperty("是否显示小件退款")
    private Boolean isShowSmallRefund;

    /**
     * 小件退款订单号
     */
    @ApiModelProperty("小件退款订单号")
    private Integer smallRefundSubId;

    /**
     * 小件退款编码
     * @see PrioritySmallRefundEnum
     */
    @ApiModelProperty("小件退款编码")
    private Integer smallRefundCode;
    /**
     * 小件退款理由
     */
    @ApiModelProperty("小件退款理由")
    private String smallRefundReason;

    /**
     * 折价支付的对象
     */
    @Data
    @Accessors(chain = true)
    @ApiModel("折价支付的对象")
    public static class ZheJiaPayVo{
        /**
         * 是否显示折价支付
         */
        @ApiModelProperty("是否显示折价支付")
        private Boolean isShowZheJiaPay;

        /**
         * @see ZheJiaPayEnum
         */
        @ApiModelProperty("是否显示折价支付")
        private Integer zheJiaPayCode;
        /**
         * 折价支付理由
         */
        @ApiModelProperty("折价支付理由")
        private String zheJiaPayReason;
        /**
         * 请求接口需要提供的参数
         */
        private ZheJiaPayFormVo param;
    }


}
