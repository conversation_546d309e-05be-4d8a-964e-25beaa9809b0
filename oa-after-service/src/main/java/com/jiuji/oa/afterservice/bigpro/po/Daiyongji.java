package com.jiuji.oa.afterservice.bigpro.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @description 代用机信息表
 * 
 * <AUTHOR> quan
 * @date 2020-05-09 16:44:33
 */
@Data
@TableName("daiyongji")
public class Daiyongji implements Serializable {
	private static final long serialVersionUID = 1L;


	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	@ApiModelProperty("代用机名称")
	private String name;

	@ApiModelProperty("串号")
	private String imei;

	@ApiModelProperty("状况")
	private String jikuang;

	@ApiModelProperty("价格")
	private BigDecimal price;

	@ApiModelProperty("代用单号")
	private Integer pos;

	@ApiModelProperty("备注")
	private String beizhu;

	@ApiModelProperty(value = "是否可以继续使用")
	private Boolean isContinueUse;

	@ApiModelProperty(value = "是否有保护壳")
	private Boolean isProtectiveShell;

	@ApiModelProperty(value = "是否有保护膜")
	private Boolean isProtectiveFilm;

	@ApiModelProperty(value = "测试结果")
	private String testResult;

	@ApiModelProperty(value = "外观描述")
	private String waiGuan;

	@ApiModelProperty("是否已删除")
	private Boolean isdel;

	@ApiModelProperty("门店")
	private String area;

	@ApiModelProperty("添加时间")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime dtime;

	@ApiModelProperty("状态：1空闲，2代用中，3维修中，4损坏报废，5 预约")
	private Integer stats;

	@ApiModelProperty("盘点状态")
	private Boolean pandian;

	@ApiModelProperty("盘点时间")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime pandiandate;

	@ApiModelProperty("盘点用户")
	private String panUser;

	@ApiModelProperty("库存状态")
	private Integer kcCheck;

	@ApiModelProperty("门店id")
	private Integer areaid;

	@ApiModelProperty("代用机级别")
	private Integer leavel;

	@ApiModelProperty("配置")
	private String peizhi;

	@ApiModelProperty("使用次数")
	private Integer applycount;

	@ApiModelProperty("申请时间")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime applydate;

	@ApiModelProperty("成本价")
	private BigDecimal inprice;

	@ApiModelProperty("改价时间")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime pricetime;

	@ApiModelProperty("借出人")
	private String jiechuren;


	private String productColor;

	@ApiModelProperty("等级")
	private Integer grade;

	@ApiModelProperty("借出时间")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime jiechutime;

	@ApiModelProperty("预约id")
	private Integer yuyueid;

	@ApiModelProperty("归还时间")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime guihuandate;

	@ApiModelProperty("商品规格id")
	private Integer ppriceid;

	@TableField(exist = false)
	private Integer toareaStats;
	/**
	 * 附件id
	 */
	@ApiModelProperty(value = "附件id")
	private String fids;

}
