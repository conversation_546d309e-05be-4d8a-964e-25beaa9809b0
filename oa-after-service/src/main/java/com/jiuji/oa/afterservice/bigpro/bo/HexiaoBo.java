package com.jiuji.oa.afterservice.bigpro.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @author: gengjiaping
 * @date: 2020/3/31
 */
@ApiModel
@Data
public class HexiaoBo {

    private Integer id;

    @ApiModelProperty(value = "名称")
    private String wxpjName;

    @ApiModelProperty(value = "是否锁定库存")
    private Boolean islockc;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "原商品名称")
    private String originProductName;

    @ApiModelProperty(value = "商品规格")
    private String originProductColor;

    @ApiModelProperty(value = "标签")
    private Integer pLabel;

    private String plabelText;
    private Integer cid;
    private BigDecimal price;
    /**
     * 退款状态 1 仅退款 2 退货退款
     * @see Wxkcoutput.TuiStatusEnum
     */
    @ApiModelProperty(value = "退款状态 1 仅退款 2 退货退款")
    private Integer tuiStatus;
    @ApiModelProperty(value = "扣除优惠费用,可退款金额")
    private BigDecimal refundPrice;

    @ApiModelProperty(value = "已退金额")
    private BigDecimal refundedPrice;
    private BigDecimal price1;
    private BigDecimal priceYh;
    private BigDecimal inprice;
    private BigDecimal priceGs;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtime;
    private String inuser;
    private Integer stats;
    private BigDecimal hxprice;
    private Integer ishexiao;
    private Integer ppid;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lock1dtime;
    private Integer hsid;
    private Boolean issale;
    private BigDecimal hsprice;
    private Boolean isfan;
    private Integer dianping;
    //旧件回收的门店id
    private Integer hsAreaId;
    //旧件回收转门店id
    private Integer hsToAreaId;
    //旧件是否已返回
    private Boolean hsIsFan;
    //旧件是否已返回
    private Boolean hsIsSaleCheck;
    //旧件是否已返回
    private Boolean hsConfirm;
    //旧件是否已完成
    private Boolean hsComplete;
    private String productAttribute;
    private Integer dktype;
    private Integer ishuanhuo;

    private Integer hsjjSaletype;


    private Integer punishSub;

    private Integer specialQualityAssurance;

    /**
     * 撤销选项  0 没有选项 1 退款方式选项
     */
    @ApiModelProperty(value = "撤销选项  0 没有选项 1 退款方式选项")
    private Integer cancelSelect;

    /**
     * 外送保障id
     */
    @ApiModelProperty(value = "撤销选项  0 没有选项 1 退款方式选项")
    private Integer waiSongId;

    /**
     * 旧件的来源 1 维修配件退款
     * @Seee
     */
    private Integer hsFromSource;

    @ApiModelProperty(value = "返修审核")
    private Boolean hsFancheck;

    /**
     * 维修配件订购申请数据
     */
    private Boolean isShouhouApply;
    /**
     * 备货状态
     * 待审核 = 1,
     * 等待采购 = 2,
     * 调拨中 = 3,
     * 采购中 = 4,
     * 已完成 = 5,
     * 已删除 = 6,
     * 无法采购 = 7
     * 库存 = 8
     * 出库 = 9
     */
    private Integer beihuoStats;

    /**
     * 需要回收，返还的旧件的分类ID
     */
    private Boolean isRecoveryReturnCid;

    /**
     * 配件类型
     * @see Wxkcoutput.PartTypeEnum
     */
    private Integer partType;


    /**
     * 前端展示用！！！！！
     * 订购方式：集采：1，自采：2
     * 【这个字段给前端展示用，前端已经与c#协商好枚举，这里基于buyType坐下转换，】
     */
    private Integer orderType;

    private Boolean isSn ;
    private String sn;

    /**
     * 订购申请单号
     */
    private Integer applyId;

}
