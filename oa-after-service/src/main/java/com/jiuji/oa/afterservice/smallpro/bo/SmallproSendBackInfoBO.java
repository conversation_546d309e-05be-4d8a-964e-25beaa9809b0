package com.jiuji.oa.afterservice.smallpro.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * description: <小件接件单退还商品信息BO>
 * translation: <Smallpro of parts return commodity information BO>
 *
 * <AUTHOR>
 * @date 2019/11/25
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproSendBackInfoBO implements Serializable {

    private static final long serialVersionUID = 7216774777734612507L;
    /**
     * 第一审核标志[1已审核|0未审核]
     */
    @ApiModelProperty(value = "第一审核标志[1已审核|0未审核]")
    private Integer firstCheckFlag;
    /**
     * 第一审核人,old=check1user
     */
    @ApiModelProperty(value = "第一审核人,old=check1user")
    private String firstCheckUser;
    /**
     * 第一审核时间,old=check1dtime
     */
    @ApiModelProperty(value = "第一审核时间,old=check1dtime")
    private String firstCheckTime;
    /**
     * 第二审核标志[1已审核|0未审核]
     */
    @ApiModelProperty(value = "第二审核标志[1已审核|0未审核]")
    private Integer secondCheckFlag;
    /**
     * 第二审核人,old=check2user
     */
    @ApiModelProperty(value = "第二审核人,old=check2user")
    private String secondCheckUser;
    /**
     * 第二审核时间,old=check2user
     */
    @ApiModelProperty(value = "第二审核时间,old=check2user")
    private String secondCheckTime;


    /**
     * 退款办理标志[1已审核|0未审核]
     */
    @ApiModelProperty(value = "第二审核标志[1已审核|0未审核]")
    private Integer threeCheckFlag;
    /**
     * 退款办理审核人,old=check3user
     */
    @ApiModelProperty(value = "第二审核人,old=check2user")
    private String threeCheckUser;
    /**
     * 退款办理时间,old=check3user
     */
    @ApiModelProperty(value = "第二审核时间,old=check2user")
    private String threeCheckTime;

    /**
     * 提交人
     */
    @ApiModelProperty(value = "提交人")
    private String inUser;
    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private String dTime;
    /**
     * 退款金额
     */
    @ApiModelProperty(value = "退款金额")
    private Double tuikuanM;
    /**
     * 退款金额
     */
    @ApiModelProperty(value = "退款金额")
    private Double tuikuanM1;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String comment;
    /**
     * 退款方式
     */
    @ApiModelProperty(value = "退款方式")
    private String tuiWay;
    /**
     * shouhouTuihuanId
     */
    @ApiModelProperty(value = "shouhouTuihuanId")
    private Integer shouhouTuihuanId;

    /**
     * 微信用户验证openId
     */
    @ApiModelProperty(value = "微信用户验证openId")
    private String payOpenId;


    @ApiModelProperty(value = "bankName")
    private String bankName;

    @ApiModelProperty(value = "bankfuming")
    private String bankfuming;

    @ApiModelProperty(value = "banknumber")
    private String banknumber;

    private String kemuTui;
    /**
     * 是否有支付单号
     */
    @ApiModelProperty(value = "是否有支付单号")
    private Boolean isHavePayInfo;

    @ApiModelProperty(value = "故障类型")
    private String faultType;

    @ApiModelProperty(value = "审核类型")
    private String checkType;
}
