package com.jiuji.oa.afterservice.bigpro.bo;

import com.jiuji.oa.afterservice.bigpro.po.RepairFault;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 维修故障详情BO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@ApiModel(value = "RepairFaultDetailBO对象", description = "维修故障详情BO")
public class RepairFaultDetailBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "维修故障信息")
    private RepairFault fault;

    @ApiModelProperty(value = "维修方案列表")
    private List<RepairPlan> planList;
} 