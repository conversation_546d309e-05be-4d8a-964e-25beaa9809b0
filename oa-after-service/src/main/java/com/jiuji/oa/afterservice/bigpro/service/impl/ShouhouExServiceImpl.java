package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.cloud.after.enums.BaoXiuTypeEnum;
import com.jiuji.cloud.after.enums.ShouHouLogTypeEnum;
import com.jiuji.cloud.stock.service.WuliuStockCloud;
import com.jiuji.cloud.stock.vo.request.WuliuInvalidReqV2;
import com.jiuji.oa.afterservice.apollo.ShouhouTestInfoConfig;
import com.jiuji.oa.afterservice.batchreturn.dao.BatchReturnMkcDao;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.bo.productkc.OperateProductKcRes;
import com.jiuji.oa.afterservice.bigpro.bo.yuyue.ShouhouYuYueBasicInfo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouExMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper;
import com.jiuji.oa.afterservice.bigpro.enums.*;
import com.jiuji.oa.afterservice.bigpro.m.bo.RepairRecords;
import com.jiuji.oa.afterservice.bigpro.mapstruct.ShouhouTestMapStruct;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.MemberSubVO;
import com.jiuji.oa.afterservice.bigpro.vo.ShouhouTestParamsVo;
import com.jiuji.oa.afterservice.bigpro.vo.req.ChaoshiZengpinListReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ToAreaSetReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.WeixiuTestOptionReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.ShouhouConstants;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRException;
import com.jiuji.oa.afterservice.common.source.InwcfUrlSource;
import com.jiuji.oa.afterservice.common.util.*;
import com.jiuji.oa.afterservice.csharp.CsharpCommonService;
import com.jiuji.oa.afterservice.csharp.enums.MemberBlacklist;
import com.jiuji.oa.afterservice.csharp.vo.BlackListVo;
import com.jiuji.oa.afterservice.other.bo.*;
import com.jiuji.oa.afterservice.other.enums.SaveMoneyEkindEnum;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.po.NetPayRefundInfo;
import com.jiuji.oa.afterservice.other.po.ReturnsDetail;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.other.service.*;
import com.jiuji.oa.afterservice.other.vo.req.ShouhouTuihuanReq;
import com.jiuji.oa.afterservice.smallpro.enums.JiujiNumberCardTypeEnum;
import com.jiuji.oa.afterservice.smallpro.enums.JiujiTenantEnum;
import com.jiuji.oa.afterservice.smallpro.po.NetpayRecord;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.po.SmallproBill;
import com.jiuji.oa.afterservice.smallpro.service.NetpayRecordService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproBillService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproService;
import com.jiuji.oa.afterservice.smallpro.vo.req.oaApiReq.OaApiReq;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import com.jiuji.oa.afterservice.stock.po.ProductMkc;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.afterservice.stock.service.ProductMkcService;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.bo.KemuFzhsItem;
import com.jiuji.oa.afterservice.sys.enums.EKemuEnum;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.afterservice.sys.service.KeMuService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.coupon.LimitProTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description:
 * @author: Li Quan
 * @date: 2020/5/12 9:53
 */
@Service
@Slf4j
public class ShouhouExServiceImpl extends ServiceImpl<ShouhouExMapper, Shouhou> implements ShouhouExService {

    private final Integer THREAD_HOLD = 1000;

    @Lazy
    @Autowired
    private ShouhouQujishenheService shouhouQujishenheService;
    @Lazy
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private WxkcoutputService wxkcoutputService;
    @Autowired
    private ShouhouHuishouService shouhouHuishouService;
    @Autowired
    private ShouhouTuihuanService shouhouTuihuanService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Autowired
    private SubService subService;
    @Autowired
    private ShouhouJinshuibaoService shouhouJinshuibaoService;
    @Autowired
    private ShouhoutestInfoService shouhoutestInfoService;
    @Autowired
    private ShouhouMsgService shouhouMsgService;
    @Autowired
    private ShouhouFuwupicService shouhouFuwupicService;
    @Autowired
    private ShouhouPasswordlooklogService shouhouPasswordlooklogService;
    @Autowired
    private ShouhouWuLiuService shouhouWuLiuService;
    @Autowired
    private SmsService smsService;

    @Resource
    private AreainfoService areainfoService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ShouhouTroubleService shouhouTroubleService;
    @Autowired
    private MsoftService msoftService;
    @Autowired
    private BasketService basketService;
    @Autowired
    private ShouhouToareaService shouhouToareaService;
    @Autowired
    private ShouhouImeichangeService shouhouImeichangeService;
    @Autowired
    private NetpayRecordService netpayRecordService;
    @Autowired
    private NetPayRefundInfoService netPayRefundInfoService;
    @Autowired
    private SmallproService smallproService;
    @Autowired
    private SmallproBillService smallproBillService;
    @Autowired
    private ReturnsDetailService returnsDetailService;
    @Autowired
    private NumberCardService numberCardService;
    @Autowired
    private ProductinfoService productinfoService;
    @Autowired
    private ProductMkcService productMkcService;
    @Autowired
    private ShouhouChaoshizengpinService shouhouChaoshizengpinService;
    @Autowired
    private VoucherService voucherService;
    @Resource
    private ShouhouMapper shouhouMapper;
    @Autowired
    private ShouhouQudaoService shouhouQudaoService;
    @Autowired
    private CardLogsService cardLogsService;
    @Autowired
    private InsourceService insourceService;
    @Autowired
    private ShouHouPjService shouHouPjService;
    @Autowired
    private ProductKcService productKcService;
    @Autowired
    private ShouhouConstants shouhouConstants;
    @Autowired
    private AuthConfigService authConfigService;
    @Autowired
    private KeMuService keMuService;
    @Resource
    private InwcfUrlSource inwcfUrlSource;
    @Resource
    BatchReturnMkcDao batchReturnMkcDao;
    @Autowired
    private SysConfigService sysConfigService;
    @Resource
    private JiujiSystemProperties jiujiSystemProperties;
    @Autowired
    private ShouhouMemberDiscountService memberDiscountService;
    @Autowired
    private RabbitTemplate oaAsyncRabbitTemplate;
    @Resource
    private ShouhouTestInfoConfig shouhouTestInfoConfig;
    @Resource
    private ShouhouTestMapStruct shouhouTestMapStruct;
    @Resource
    private WuliuStockCloud wuliuStockCloud;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private CategoryService categoryService;


    private static final Long TWO = 2L;
    private static final String BLANK = "</span>&nbsp;&nbsp;";
    private static final String TITLE = "<span title='";
    private static final String SHEN_HE_PREFIX = "<a href='javascript:doqJshehe(";

    private Random random = new Random();



    @Override
    public ShouHouZengpinBo getZengpinByShouhouid(Integer shouhouId, Integer type) {
        if (type == null) {
            type = 0;
        }
        List<ShouHouZengpinBo> zpList = baseMapper.getZengpinByShouhouid(shouhouId, type);
        if (CollectionUtils.isNotEmpty(zpList)) {
            return zpList.get(0);
        } else {
            return null;
        }
    }

    @Override
    public String getCheckInfo(Integer shouhouId, ShouhouBo info) {
        StringBuilder sb = new StringBuilder();
        List<ShouhouQujishenhe> qjshList = shouhouQujishenheService.list(new QueryWrapper<ShouhouQujishenhe>().lambda()
                .eq(ShouhouQujishenhe::getShouhouid, shouhouId));

        if (CollectionUtils.isNotEmpty(qjshList)) {

            ShouhouQujishenhe shenheInfo = qjshList.get(0);
            BigDecimal hexiao = getHeXiaoJE(shouhouId);
            BigDecimal allhs = BigDecimal.ZERO;
            List<ShouhouHuishou> hsList = shouhouHuishouService.getHuishouListBy(shouhouId);
            if (CollectionUtils.isNotEmpty(hsList)) {
                for (ShouhouHuishou hs : hsList) {
                    BigDecimal price = hs.getPrice() == null ? BigDecimal.ZERO : hs.getPrice();
                    allhs = allhs.add(price);
                }
                AtomicReference<BigDecimal> lossPrice = new AtomicReference<>(info.getFeiyong().subtract(info.getCostprice()).subtract(allhs).subtract(hexiao));
                Optional.ofNullable(lossPrice.get().compareTo(BigDecimal.ZERO) > 0).filter(Boolean::booleanValue)
                        .ifPresent(k -> lossPrice.set(BigDecimal.ZERO));
                sb.append("<span style='border:1px solid #D6D6D6;padding:2px;color:Red;'>亏损：").append(lossPrice.get()).append(BLANK);
                sb.append(TITLE).append(DateUtil.localDateTimeToString(shenheInfo.getDtime()))
                        .append("' style='border:1px solid #D6D6D6;padding:2px;'>提交：").append(shenheInfo.getInuser()).append(BLANK);

                if (!shenheInfo.getCheckuser1().equals("未审核") && !shenheInfo.getCheckuser1().equals("免审")) {
                    sb.append(TITLE).append(DateUtil.localDateTimeToString(shenheInfo.getDtime1()))
                            .append("' style='border:1px solid #D6D6D6;padding:2px;'>1审：").append(shenheInfo.getCheckuser1()).append(BLANK);
                }
                if (!shenheInfo.getCheckuser2().equals("未审核") && !shenheInfo.getCheckuser2().equals("免审")) {
                    sb.append(TITLE).append(DateUtil.localDateTimeToString(shenheInfo.getDtime2()))
                            .append("' style='border:1px solid #D6D6D6;padding:2px;'>2审：").append(shenheInfo.getCheckuser2()).append(BLANK);
                }
                if (!shenheInfo.getCheckuser3().equals("未审核") && !shenheInfo.getCheckuser3().equals("免审")) {
                    sb.append(TITLE).append(DateUtil.localDateTimeToString(shenheInfo.getDtime3()))
                            .append("' style='border:1px solid #D6D6D6;padding:2px;'>3审：").append(shenheInfo.getCheckuser3()).append(BLANK);
                }
                if (!shenheInfo.getCheckuser4().equals("未审核") && !shenheInfo.getCheckuser4().equals("免审")) {
                    sb.append(TITLE).append(DateUtil.localDateTimeToString(shenheInfo.getDtime4()))
                            .append("' style='border:1px solid #D6D6D6;padding:2px;'>4审：").append(shenheInfo.getCheckuser4()).append(BLANK);
                }
            }

        }
        return sb.toString();
    }

    @Override
    public String getCheckIng(Integer shouhouId, ShouhouBo info) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        ShouhouQujishenhe shenheInfo = shouhouQujishenheService.getCheckIng(shouhouId);
        String msg = "";
        if (shenheInfo == null) {
            return msg;
        }
        BigDecimal hexiao = getHeXiaoJE(shouhouId);
        BigDecimal allhs = BigDecimal.ZERO;
        List<ShouhouHuishou> hsList = shouhouHuishouService.getHuishouListBy(shouhouId);
        if (CollectionUtils.isNotEmpty(hsList)) {
            for (ShouhouHuishou hs : hsList) {
                BigDecimal price = hs.getPrice() == null ? BigDecimal.ZERO : hs.getPrice();
                allhs = allhs.add(price);
            }
            //亏损的钱为负数,大于0取0元
            msg = "<span style='border:1px solid #D6D6D6;padding:2px;color:Red;'>亏损：" + Optional.of(info.getFeiyong()
                    .subtract(info.getCostprice()).subtract(allhs).subtract(hexiao)).filter(lossPrice -> lossPrice.compareTo(BigDecimal.ZERO)<=0).orElse(BigDecimal.ZERO) + BLANK;

            msg += TITLE + DateUtil.localDateTimeToString(shenheInfo.getDtime()) + "' style='border:1px solid #D6D6D6;padding:2px;'>提交：" + shenheInfo.getInuser() + BLANK;

            if (!shenheInfo.getCheckuser1().equals("未审核") && !shenheInfo.getCheckuser1().equals("免审")) {
                msg += TITLE + DateUtil.localDateTimeToString(shenheInfo.getDtime1()) + "' style='border:1px solid #D6D6D6;padding:2px;'>1审：" + shenheInfo.getCheckuser1() + BLANK;
            }
            if (!shenheInfo.getCheckuser2().equals("未审核") && !shenheInfo.getCheckuser2().equals("免审")) {
                msg += TITLE + DateUtil.localDateTimeToString(shenheInfo.getDtime2()) + "' style='border:1px solid #D6D6D6;padding:2px;'>2审：" + shenheInfo.getCheckuser2() + BLANK;
            }
            if (!shenheInfo.getCheckuser3().equals("未审核") && !shenheInfo.getCheckuser3().equals("免审")) {
                msg += TITLE + DateUtil.localDateTimeToString(shenheInfo.getDtime3()) + "' style='border:1px solid #D6D6D6;padding:2px;'>3审：" + shenheInfo.getCheckuser3() + BLANK;
            }
            if (!shenheInfo.getCheckuser4().equals("未审核") && !shenheInfo.getCheckuser4().equals("免审")) {
                msg += TITLE + DateUtil.localDateTimeToString(shenheInfo.getDtime4()) + "' style='border:1px solid #D6D6D6;padding:2px;'>4审：" + shenheInfo.getCheckuser4() + BLANK;
            }
            //继续
            if (shenheInfo.getCheckuser1().equals("未审核") && !shenheInfo.getCheckuser1().equals("免审") && oaUserBO.getRank().contains("6e1")) {
                msg += SHEN_HE_PREFIX + shenheInfo.getId() + ",1)'>审核一</a>";
            }
            if (shenheInfo.getCheckuser2().equals("未审核") && !shenheInfo.getCheckuser2().equals("免审") && oaUserBO.getRank().contains("6e2")) {
                msg += SHEN_HE_PREFIX + shenheInfo.getId() + ",2)'>审核二</a>";
            }
            if (shenheInfo.getCheckuser3().equals("未审核") && !shenheInfo.getCheckuser3().equals("免审") && oaUserBO.getRank().contains("6e3")) {
                msg += SHEN_HE_PREFIX + shenheInfo.getId() + ",3)'>审核三</a>";
            }
            if (shenheInfo.getCheckuser4().equals("未审核") && !shenheInfo.getCheckuser4().equals("免审") && oaUserBO.getRank().contains("6e4")) {
                msg += SHEN_HE_PREFIX + shenheInfo.getId() + ",4)'>审核四</a>";
            }
        }


        return msg;
    }

    @Override
    public List<ShouhouServiceOutBo> getServersList(String imei, Integer wxId) {
        if (StringUtils.isEmpty(imei)) {
            return new LinkedList<>();
        }
        return baseMapper.getServersList(imei, wxId);
    }

    @Override
    public R<JinshuibaoInfoRes> getJinshuibaoInfo(Integer shouhouId, Integer serviceType) {
        if (serviceType == null) {
            serviceType = BaoXiuTypeEnum.JSB.getCode();
        }

        JinshuibaoInfoRes info = new JinshuibaoInfoRes();
        Boolean isApple = false;
        Shouhou sh = shouhouService.getOne(new QueryWrapper<Shouhou>().lambda().eq(Shouhou::getId, shouhouId).eq(Shouhou::getXianshi, true));
        if (sh == null) {
            return R.error("单号信息不存在");
        }

        if (sh.getPpriceid() != null && sh.getPpriceid() != 0) {
            isApple = baseMapper.getPpidBy(sh.getPpriceid()) != null;
        }
        Integer basketId = sh.getBasketId();
        info.setShouhouId(shouhouId);
        info.setIsApple(isApple);
        if (basketId != null && basketId > 0) {
            List<ShouHouPriceAndTradeDateBo> subDt = baseMapper.getPriceAndTradeDate(basketId);
            if (CollectionUtils.isNotEmpty(subDt)) {
                info.setOrderTotal(subDt.get(0).getPrice());
                BigDecimal feiyong = getJinshuibaoFeiyong(subDt.get(0).getPrice(), subDt.get(0).getTradeDate1(), serviceType);
                info.setFeiYong(feiyong);
            }
        }
        Integer subId = sh.getSubId();
        if (subId != null && subId > 0) {
            if (serviceType.equals(BaoXiuTypeEnum.JSB.getCode())) {
                BigDecimal price = baseMapper.getPriceBySubIdAndPpid(subId, 55428);
                info.setFeiYong(price != null ? price : BigDecimal.ZERO);
            } else if (serviceType.equals(BaoXiuTypeEnum.YHDX.getCode())) {
                BigDecimal price = baseMapper.getPriceBySubIdAndPpid(subId, 67776);
                info.setFeiYong(price != null ? price : BigDecimal.ZERO);
            }
        }

        List<ShouhouJinshuibao> jinshuibaoList = shouhouJinshuibaoService.list(new QueryWrapper<ShouhouJinshuibao>().lambda()
                .eq(ShouhouJinshuibao::getShouhouid, shouhouId)
                .and(bo -> bo.eq(ShouhouJinshuibao::getServiceType, 6).or().isNull(ShouhouJinshuibao::getServiceType))
                .and(bo -> bo.eq(ShouhouJinshuibao::getIsdel, 0).or().isNull(ShouhouJinshuibao::getIsdel)));
        if (CollectionUtils.isNotEmpty(jinshuibaoList)) {
            ShouhouJinshuibao jinshuibaoInfo = jinshuibaoList.get(0);
            BeanUtils.copyProperties(jinshuibaoInfo, info);

        }

        return R.success(info);
    }

    @Override
    public BigDecimal getJinshuibaoFeiyong(BigDecimal price, LocalDateTime tradeDate, Integer serviceType) {
        BigDecimal feiyong = BigDecimal.ZERO;
        LocalDateTime nowTime = LocalDateTime.now();
        LocalDateTime tradeDateAtEndOfDay = CommonUtils.getEndOfDay(tradeDate);
        if (serviceType.equals(BaoXiuTypeEnum.JSB.getCode())) {
            //实际到期后延3天 算为服务截止时间
            Integer offsetday = 3;
            if (nowTime.isBefore(tradeDateAtEndOfDay.plusMonths(3).plusDays(offsetday))) {
            } else if (nowTime.isBefore(tradeDateAtEndOfDay.plusMonths(6).plusDays(offsetday))) {
                feiyong = price.multiply(BigDecimal.valueOf(0.32)).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else if (nowTime.isBefore(tradeDateAtEndOfDay.plusMonths(9).plusDays(offsetday))) {
                feiyong = price.multiply(BigDecimal.valueOf(0.27)).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else {
                feiyong = price.multiply(BigDecimal.valueOf(0.24)).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
        } else if (serviceType.equals(BaoXiuTypeEnum.YHDX.getCode())) {
            Duration duration = Duration.between(nowTime, tradeDateAtEndOfDay);
            Long month = duration.toDays() - 1;
            feiyong = price.multiply(BigDecimal.valueOf(0.8 - month * 0.02));
        }

        return feiyong;
    }

    @Override
    public ShouhoutestInfo getLastTestInfoByShId(Integer shouhouId) {
        if (XtenantEnum.isJiujiXtenant() && Optional.ofNullable(shouhouId).orElse(0) > shouhouTestInfoConfig.getNewTestShouhouId()) {
            ShouhouTestResultInfo testResultInfo = SpringUtil.getBean(ShouhouTestResultInfoService.class).getLastResultByShouhouId(shouhouId);
            return Objects.nonNull(testResultInfo) ? shouhouTestMapStruct.toShouhouTestResult(testResultInfo) : null;
        }
        List<ShouhoutestInfo> testInfoList = shouhoutestInfoService.list(new QueryWrapper<ShouhoutestInfo>().lambda().eq(ShouhoutestInfo::getShouhouId, shouhouId).orderByDesc(ShouhoutestInfo::getId));
        if (CollectionUtils.isNotEmpty(testInfoList)) {
            return testInfoList.get(0);
        } else {
            return null;
        }
    }

    @Override
    public ShouhouWxzqRes getShouHouWeiXiuZqByShId(Integer shouhouId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        ShouhouWxzqRes res = new ShouhouWxzqRes();
       // Shouhou shouhou = baseMapper.selectById(shouhouId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->baseMapper.selectById(shouhouId), MTableInfoEnum.SHOUHOU, shouhouId);
        ShouhouBo info = shouhouService.getOne(shouhouId);
        Integer zaituDay = 0;
        Integer wxzq = 0;
        List<ShouhouDaoJiShiBo> shouhouDaoJiShiList = null;
        Integer ppriceid = 0;
        if (info != null) {
            ppriceid = info.getPpriceid();
            if (ppriceid != null && ppriceid > 0) {
                shouhouDaoJiShiList = shouhouService.getShouhouTimeByPid(ppriceid);
                if (CollectionUtils.isNotEmpty(shouhouDaoJiShiList)) {
                    ShouhouDaoJiShiBo shouhouDaoJiShi = shouhouDaoJiShiList.get(0);
                    zaituDay = shouhouDaoJiShi.getZtday();
                    Integer wxkind = info.getWxkind() == null ? 0 : info.getWxkind();
                    if (wxkind.equals(ServicesWayEnum.YYDD.getCode())) {
                        wxzq = shouhouDaoJiShi.getXday();
                    } else if (wxkind.equals(ServicesWayEnum.SMQJ.getCode())) {
                        wxzq = shouhouDaoJiShi.getHday();
                    } else if (wxkind.equals(ServicesWayEnum.YJSX.getCode())) {
                        wxzq = shouhouDaoJiShi.getDday();
                    } else if (wxkind.equals(ServicesWayEnum.SMWX.getCode())) {
                        wxzq = shouhouDaoJiShi.getSday();
                    } else if (wxkind.equals(ServicesWayEnum.DMYY.getCode())) {
                        wxzq = shouhouDaoJiShi.getZday();
                    } else if (wxkind.equals(ServicesWayEnum.SMAZ.getCode())) {
                        wxzq = shouhouDaoJiShi.getKday();
                    } else {
                        wxzq = shouhouDaoJiShi.getXday();
                    }
                }
            }

            if (info.getDaojishi() != null) {
                wxzq = shouhou.getDaojishi();
            } else {
                //接件地非昆明市区倒计时要加上“在途时间“
                if (oaUserBO == null || (!shouhouService.isKunMingShi(info.getAreaid()) && oaUserBO.getXTenant().equals(0))) {
                    wxzq += zaituDay;
                }
            }
        }
        res.setWxzq(wxzq);
        res.setZaituDay(zaituDay);
        res = calcDate(wxzq, shouhou);
//        res.setTimeTextDjs(wxzqRes.getTimeTextDjs());
//        res.setTimeTextSum(wxzqRes.getTimeTextSum());
        return res;
    }


    @Override
    public R<String> bakDataOp(Integer stats, Integer shouhouId, String reason) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();

        Boolean flag = shouhouService.update(new UpdateWrapper<Shouhou>().lambda().set(Shouhou::getIsBakData, stats).eq(Shouhou::getId, shouhouId));
        if (flag) {
            if (stats == 2) {
                shouhouService.saveShouhouLog(shouhouId, "已为客户手机备份数据", oaUserBO.getUserName(), null, false);
                shouhouMsgService.sendCollectMsg(shouhouId, "已为客户还原数据", oaUserBO.getUserName());
            } else if (stats == 4) {
                if (StringUtils.isNotEmpty(reason)) {
                    shouhouService.saveShouhouLog(shouhouId, "无法备份数据，原因【" + reason + "】", oaUserBO.getUserName(), null, true);
                }
                shouhouService.saveShouhouLog(shouhouId, "您送修的设备因机身故障无法进行数据备份，请您悉知！", oaUserBO.getUserName(), null, false);
            } else {
                shouhouService.saveShouhouLog(shouhouId, "已为客户手机恢复数据并删除", oaUserBO.getUserName(), null, false);
                shouhouMsgService.sendCollectMsg(shouhouId, "已为客户备份数据", oaUserBO.getUserName());
            }

            return R.success("操作成功！");
        } else {
            return R.error("操作失败");
        }

    }

    @Override
    public List<ShouhouTroubleListRes> getTroubleList() {
        return baseMapper.getTroubleList();
    }

    @Override
    public List<Integer> getShouhouTroubles(Integer shouhouId) {
        return baseMapper.getShouhouTroubles(shouhouId);
    }

    @Override
    public R<String> setFuwuPic(String actName, ShouhouFuwupic fuwupic) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }
        Boolean flag = false;
        Boolean hasUpOk = false;
        ShouhouFuwupic pic = new ShouhouFuwupic();
        //无法上传图片
        if (actName.equals("wait")) {

            if (fuwupic.getFkind().equals(2)) {
                List<ShouhouFuwupic> fuwupicList = shouhouFuwupicService.list(new QueryWrapper<ShouhouFuwupic>().lambda().eq(ShouhouFuwupic::getShouhouid, fuwupic.getShouhouid()));
                if (CollectionUtils.isEmpty(fuwupicList)) {
                    pic.setShouhouid(fuwupic.getShouhouid());
                    pic.setFkind(1);
                    pic.setInuser(oaUserBO.getUserName());
                    pic.setIndate(LocalDateTime.now());
                    pic.setQujidate(LocalDateTime.now());
                    flag = shouhouFuwupicService.save(pic);
                } else {
                    hasUpOk = true;
                    flag = shouhouFuwupicService.update(new UpdateWrapper<ShouhouFuwupic>().lambda()
                            .set(ShouhouFuwupic::getFkind, 2)
                            .set(ShouhouFuwupic::getQujiuser, oaUserBO.getUserName())
                            .set(ShouhouFuwupic::getQujidate, LocalDateTime.now()));
                }

            } else if (fuwupic.getFkind().equals(1)) {
                flag = shouhouFuwupicService.remove(new QueryWrapper<ShouhouFuwupic>().lambda().eq(ShouhouFuwupic::getShouhouid, fuwupic.getShouhouid()));
            }
            if (flag) {
                String comment = "设备不开机，无法上传服务拍照图" + (fuwupic.getFkind() == 2 ? "（取机检测）" : "") + "";
                shouhouService.saveShouhouLog(fuwupic.getShouhouid(), comment, oaUserBO.getUserName(), null, false);
            }
        } else if (actName.equals("upok")) {
            List<ShouhouFuwupic> fuwupicList = shouhouFuwupicService.list(new QueryWrapper<ShouhouFuwupic>().lambda()
                    .eq(ShouhouFuwupic::getShouhouid, fuwupic.getShouhouid())
                    .isNotNull(ShouhouFuwupic::getFkind));
            if (CollectionUtils.isNotEmpty(fuwupicList)) {
                hasUpOk = true;
            }
        } else if (actName.equals("uppic")) {

            if (StringUtils.isNotEmpty(fuwupic.getFid())) {
                shouhouFuwupicService.remove(new LambdaQueryWrapper<ShouhouFuwupic>().eq(ShouhouFuwupic::getShouhouid, fuwupic.getShouhouid()));

                pic.setShouhouid(fuwupic.getShouhouid());
                pic.setFkind(2);
                pic.setPicid(fuwupic.getPicid());
                pic.setFid(fuwupic.getFid());
                pic.setInuser(oaUserBO.getUserName());
                pic.setIndate(LocalDateTime.now());
                pic.setQujiuser(oaUserBO.getUserName());
                pic.setQujidate(LocalDateTime.now());
                flag = shouhouFuwupicService.save(pic);
            } else {
                return R.error("没有上传图片");
            }
            if (flag) {
                String comment = "上传服务拍照图" + (fuwupic.getFkind() == 2 ? "（取机检测）" : "") + "";
                shouhouService.saveShouhouLog(fuwupic.getShouhouid(), comment, oaUserBO.getUserName(), null, false);
            }
        }

        return R.success("操作成功");
    }

    @Override
    public R<ShouhouDeviceInfoRes> getLockPwd(Integer shouhouId) {
        //Shouhou shouhou = shouhouService.getById(shouhouId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU,shouhouId);
        if (shouhou == null) {
            return R.error("单号有误");
        }

        ShouhouDeviceInfoRes res = new ShouhouDeviceInfoRes();
        res.setDeviceId(shouhou.getDeviceid());
        res.setDevicePwd(shouhou.getDevicepwd());
        res.setLockPwd(shouhou.getLockpwd());

        if (StringUtils.isEmpty(shouhou.getDeviceid()) && StringUtils.isEmpty(shouhou.getDevicepwd()) && StringUtils.isEmpty(shouhou.getLockpwd())) {
            return R.error("设备密码信息为空");
        }
        addShouhouPwdLookLog(shouhouId);
        return R.success("操作成功", res);

    }

    @Override
    public R<String> addShouhouPwdLookLog(Integer shouhouId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();

        ShouhouPasswordlooklog looklog = new ShouhouPasswordlooklog();
        looklog.setDtime(LocalDateTime.now());
        looklog.setInuser(oaUserBO.getUserName());
        looklog.setShouhouid(shouhouId);
        Boolean flag = shouhouPasswordlooklogService.save(looklog);

        return flag ? R.success("添加成功") : R.error("添加失败");
    }

    @Override
    public R<List<ShouhouPasswordlooklog>> getPwdlookLogsByShId(Integer shouhouId) {
        List<ShouhouPasswordlooklog> logList = Collections.emptyList();
        logList = shouhouPasswordlooklogService.list(new QueryWrapper<ShouhouPasswordlooklog>().lambda()
                .eq(ShouhouPasswordlooklog::getShouhouid, shouhouId)
                .orderByDesc(ShouhouPasswordlooklog::getId));
        if (CollectionUtils.isNotEmpty(logList) && logList.size() > 10) {
            return R.success(logList.subList(0, 10));
        }
        return R.success(logList);
    }

    @Override
    public R<List<ExpressDetailResVo>> queryExpress(String company, String postId) {
        if (company.equals("EMS")) {
            company = "ems";
        } else if (company.equals("圆通快递")) {
            company = "yuantong";
        } else if (company.equals("客运")) {
            company = "zhongtie";
        } else if (company.equals("申通快递")) {
            company = "shentong";
        } else if (company.equals("美团") || company.equals("美团(光速达)")) {
            company = "meituan";
        }
        //增益速递
        if (company.equals("zengyi")) {
            return shouhouWuLiuService.queryZengyiExpress(postId);
        }
        //中通
        if (company.equals("zhongtong") || company.equals("中通")) {
            return shouhouWuLiuService.queryZtoExpress(postId);
        }
        //顺丰
        if (company.equals("shunfeng") || company.equals("顺丰快递")) {
            return shouhouWuLiuService.querySfExpress(postId);
        }
        //美团
        if (company.equals("meituan") || company.equals("美团") || company.equals("meituanFastest") || company.equals("美团(光速达)")) {
            return shouhouWuLiuService.queryMeiTuanExpress(postId);
        }
        //其他
        return shouhouWuLiuService.queryOtherExpress(company, postId);

    }

    @Override
    public R<Boolean> sendSrcode(Integer shouhouId, Integer subId) {
        //Shouhou shouhou = shouhouService.getById(shouhouId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU,shouhouId);
        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shouhou.getAreaid());
        Integer code = RandomUtil.randomInt(1000,10000);
        String message = "尊敬的客户，您本次售后维修服务的取机验证码为：" + code + "。";
        //shouhou.getMobile()
        String mobile = Optional.ofNullable(subId).map(subService::getById).map(Sub::getSubMobile).orElse(shouhou.getMobile());
        R<Boolean> smsSendR = smsService.sendSms(mobile, message, DateUtil.localDateTimeToString(LocalDateTime.now().minusMinutes(TWO)), "系统 ", smsService.getSmsChannelByTenant(shouhou.getAreaid(), ESmsChannelTypeEnum.VERIFICATION_CODE));
        if (smsSendR.getCode() == ResultCode.SUCCESS) {
            redisTemplate.opsForValue().set("srCode" + shouhouId, code.toString(),Duration.ofDays(1));
            return R.success("发送成功");
        } else {
            return R.error("发送失败");
        }
    }

    @Override
    public R<Boolean> saveSrcode(Integer shouhouId, String code) {
        Object object = redisTemplate.opsForValue().get("srCode" + shouhouId);
        if (object != null && object.toString().equals(code)) {
            Boolean flag = shouhouService.update(new UpdateWrapper<Shouhou>().lambda().set(Shouhou::getCodeMsg, code).eq(Shouhou::getId, shouhouId));
            if (flag) {
                return R.success("保存成功！");
            } else {
                return R.error("保存失败！");
            }
        } else {
            return R.error("验证码错误！");
        }

    }
    @Override
    public R<Boolean> saveSrcodebyAdmin(Integer shouhouId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Boolean flag = shouhouService.update(new UpdateWrapper<Shouhou>().lambda()
                .set(Shouhou::getCodeMsg, oaUserBO.getUserName() + "【" + oaUserBO.getUserId() + "】").eq(Shouhou::getId, shouhouId));
        if (flag) {
            return R.success("保存成功！");
        } else {
            return R.error("保存失败！");
        }
    }

    @Override
    public void updateShouhouTroubles(Integer shouhouId, List<Integer> troubleIds) {
        shouhouTroubleService.update(new LambdaUpdateWrapper<ShouhouTrouble>().set(ShouhouTrouble::getIsValid, false).eq(ShouhouTrouble::getShouhouId, shouhouId));
        if (CollectionUtils.isEmpty(troubleIds)) {
            return;
        }
        List<ShouhouTrouble> troubleList = shouhouTroubleService.listAll();
        troubleIds.stream().forEach(e -> {
            Optional<ShouhouTrouble> optional = troubleList.stream().filter(t -> t.getShouhouId().equals(shouhouId) && t.getTroubleId().equals(e)).findFirst();
            ShouhouTrouble troubleEntity = null;
            if (optional.isPresent()) {
                troubleEntity = optional.get();
            }
            if (troubleEntity == null) {
                troubleEntity = new ShouhouTrouble();
                troubleEntity.setShouhouId(shouhouId);
                troubleEntity.setTroubleId(e);
                troubleEntity.setIsValid(true);
                shouhouTroubleService.save(troubleEntity);
            }
        });
    }

    @Override
    public void changeBuyAreaId(Integer shouhouId, Integer subId, Integer isHuishou) {
        if (isHuishou == null) {
            isHuishou = 0;
        }
        if (isHuishou > 0) {
            baseMapper.updateBuyAreaIdByRecoverMarketInfo(subId, shouhouId);
        } else {
            baseMapper.updateBuyAreaIdBySubInfo(subId, shouhouId);
        }

    }

    @Override
    public R<ShouhouHistoryRes> getHistory(String imei, Integer userId) {
        ShouhouHistoryRes res = new ShouhouHistoryRes();
        if (StringUtils.isNotEmpty(imei)) {

            List<ShouhouHardwareHistoryRes> hardwareHistoryList = this.getHardwareHistory(imei);
            res.setHardware(hardwareHistoryList);
            res.setHardwareHistoryCount(hardwareHistoryList.size());

            List<Msoft> softHistoryList = this.getMSoft(imei);
            res.setMsoft(softHistoryList);
            res.setMsoftHistoryCount(softHistoryList.size());
        }
        if (CommenUtil.isNotNullZero(userId)) {
            List<ShouhouUserHistoryRes> userHistoryList = this.getUserHistory(userId);
            res.setCustomerHistoryCount(userHistoryList.size());
            res.setCustomerWx(userHistoryList);
        }

        return R.success(res);
    }

    @Override
    public List<ShouhouHardwareHistoryRes> getHardwareHistory(String imei) {
        List<ShouhouHardwareHistoryRes> res = CommenUtil.autoQueryHist(()->baseMapper.getHardwareHistory(imei));
        if (CollectionUtils.isEmpty(res)) {
            return Collections.emptyList();
        } else {
            return res;
        }
    }

    @Override
    public List<Msoft> getMSoft(String imei) {
        List<Msoft> res = msoftService.list(new LambdaQueryWrapper<Msoft>().eq(Msoft::getImei, imei).orderByDesc(Msoft::getModidate).last("offset 0 rows fetch next 1000 rows only"));
        if (CollectionUtils.isEmpty(res)) {
            return Collections.emptyList();
        } else {
            return res;
        }
    }

    @Override
    public List<ShouhouUserHistoryRes> getUserHistory(Integer userId) {
        //授权隔离
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();

        Integer xTenant = oaUserBO.getXTenant();
        Boolean isAuthPart = false;
        Integer authorizeId =oaUserBO.getAuthorizeId();
        //授权隔离
        Boolean authPart = authConfigService.isAuthPart(oaUserBO);
        if (authPart) {
            AreaBelongsDcHqD1AreaId backInfo = areainfoService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
            //非HQ地区，数据需要隔离,排除九机
            if (backInfo != null && oaUserBO.getXTenant() >= THREAD_HOLD && !CollUtil.contains(backInfo.getHqAreaIds(), oaUserBO.getAreaId())) {
                isAuthPart = Boolean.TRUE;
            }
        }
        Boolean finalIsAuthPart = isAuthPart;
        List<ShouhouUserHistoryRes> res = Optional.ofNullable(CommenUtil.autoQueryMergeHist(()->baseMapper.getUserHistory(userId, finalIsAuthPart,xTenant,authorizeId)))
                .orElse(new ArrayList<>()).stream().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(res)) {
            return Collections.emptyList();
        } else {
            res = res.stream().map(e -> {
                e.setTuihuanKindText(EnumUtil.getMessageByCode(TuihuanKindEnum.class, e.getTuihuanKind()));
                e.setBaoxiuText(EnumUtil.getMessageByCode(BaoxiuStatusEnum.class,e.getBaoxiu()));
                e.setWxkindText(EnumUtil.getMessageByCode(WxKindEnum.class,e.getWxkind()));
                e.setServiceTypeText(ObjectUtil.defaultIfNull(EnumUtil.getMessageByCode(BaoXiuTypeEnum.class,e.getServiceType()),"无服务"));
                return e;
            }).collect(Collectors.toList());

            return res;
        }
    }

    @Override
    public R<Boolean> pjEvaluate(Integer dpid, Integer dianping) {
        Boolean flag = wxkcoutputService.update(new LambdaUpdateWrapper<Wxkcoutput>().set(Wxkcoutput::getDianping, dianping).eq(Wxkcoutput::getId, dpid));
        return flag ? R.success("配件点评成功") : R.error("配件点评失败");
    }

    @Override
    public Integer queryAlipayYouhuiSubId(Integer shouhouId, List<Integer> aliPayYouhuiPpids) {
        return baseMapper.queryAlipayYouhuiSubId(shouhouId, aliPayYouhuiPpids);
    }

    @Override
    public R<ShouhouToAreaRes> toArea(Integer id) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }
        ShouhouToAreaRes res = new ShouhouToAreaRes();
        LambdaBuild.create(res).set(ShouhouToAreaRes::setCheckCode,ResultCode.SUCCESS).build();
        //Shouhou shouhou = shouhouService.getById(id);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(id), MTableInfoEnum.SHOUHOU,id);
        Boolean isQuji = false;
        Integer mArea = oaUserBO.getAreaId();
        Integer toAreaId = 0;
        if (shouhou != null) {
            toAreaId = shouhou.getToareaid();
            mArea = shouhou.getAreaid();
            isQuji = shouhou.getIsquji() == null ? false : shouhou.getIsquji();
        }
        if (toAreaId == null || toAreaId == 0) {
            toAreaId = mArea;
        }

        String shiBieMa = shouhouToareaService.getShiBieMa(toAreaId);
        if (StringUtils.isEmpty(shiBieMa)) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(toAreaId);
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                shiBieMa = areaInfoR.getData().getArea() + DateUtil.localDateTime2LocalDate(LocalDateTime.now()).replaceAll("-", "");
            }

        }
        res.setShiBieMa(shiBieMa);
        List<ShouhouToarea> toareaList = shouhouToareaService.list(new LambdaQueryWrapper<ShouhouToarea>().eq(ShouhouToarea::getShouhouId, id).orderByDesc(ShouhouToarea::getId));
        if (CollectionUtils.isNotEmpty(toareaList)) {
            R<List<AreaInfo>> areaInfos = areaInfoClient.listAll();
            if (ResultCode.SUCCESS == areaInfos.getCode() && !CollUtil.isEmpty(areaInfos.getData())) {
                for (ShouhouToarea info : toareaList) {
                    String area = areaInfos.getData().parallelStream().filter(p -> p.getId().equals(info.getAreaid()))
                            .map(AreaInfo::getArea).findFirst().orElse(null);
                    info.setArea(area);
                    String toArea = areaInfos.getData().parallelStream().filter(p -> p.getId().equals(info.getToareaid()))
                            .map(AreaInfo::getArea).findFirst().orElse(null);
                    info.setToarea(toArea);
                }
            }
            res.setToAreaLog(toareaList);
        }
        //设置转地区的校验结果
        boolean isExistTuihuan = SpringUtil.getBean(ShouhouTuihuanService.class).lambdaQuery().select(ShouhouTuihuan::getId)
                .in(ShouhouTuihuan::getTuihuanKind, Stream.of(TuihuanKindEnum.TWXF, TuihuanKindEnum.TDJ_WXF, TuihuanKindEnum.HJT,
                        TuihuanKindEnum.HZB, TuihuanKindEnum.TK, TuihuanKindEnum.HQTXH).map(TuihuanKindEnum::getCode).collect(Collectors.toList()))
                .eq(ShouhouTuihuan::getShouhouId, id).and(cnd -> cnd.eq(ShouhouTuihuan::getIsdel, Boolean.FALSE).or().isNull(ShouhouTuihuan::getIsdel))
                .isNull(ShouhouTuihuan::getCheck3)
                .count()>0;
        if (isExistTuihuan){
            res.setCheckCode(ResultCode.SERVER_ERROR);
            res.setCheckMessage("存在未办理完成的退换记录,不可操作");
        }
        return R.success(res);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> delToArea(Integer id, Integer shouhouId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (!oaUserBO.getRank().contains("0c7")) {
            return R.error("你无权限操作，权值：0c7");
        }
        ShouhouToarea shouhouToarea = Optional.ofNullable(shouhouToareaService.lambdaQuery()
                .eq(ShouhouToarea::getId, id)
                .eq(ShouhouToarea::getShouhouId, shouhouId)
                .one()).orElse(new ShouhouToarea());
        Long wuliuid = shouhouToarea.getWuliuid();
        if(ObjectUtil.isNotNull(wuliuid)){
            if(XtenantEnum.isJiujiXtenant()){
                //九机修改物流单状态
                String comment = String.format("维修单：%s 取消转地区操作,所以取消物流单", shouhouId);
                WuliuInvalidReqV2 req = new WuliuInvalidReqV2();
                req.setLogMsg(comment);
                req.setWuliuId(wuliuid.intValue());
                R<Boolean> booleanResult = wuliuStockCloud.invalidV2(req);
                if(!booleanResult.isSuccess()){
                    throw new CustomizeException("物流单取消失败:"+Optional.ofNullable(booleanResult.getUserMsg()).orElse(booleanResult.getMsg()));
                }
            } else {
                //输出修改物流单状态
                String host = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST))
                        .filter(t -> t.getCode() == ResultCode.SUCCESS)
                        .map(R::getData)
                        .orElseThrow(()-> new CustomizeException("获取域名出错"));
                String url=host+"/oaApi.svc/rest/CancelWuliu";
                Map<String, String> headers = new HashMap<>(1);
                headers.put("Content-Type", "application/json");
                Map<String, String> map = new HashMap<>();
                map.put("wuliuId",wuliuid.toString());
                map.put("userName",oaUserBO.getUserName());
                log.warn("调用C#接口取消物流单url：{},传入参数：{}",url,map);
                String result = HttpClientUtil.post(url, JSONUtil.toJsonStr(map),headers);
                log.warn("调用C#接口取消物流单返回结果：{}",result);
                R r;
                try {
                    r = JSON.parseObject(JSON.parse(result).toString(), R.class);
                }catch (Exception e){
                    log.error("调用C#接口取消物流单返回结果异常",e);
                    throw new CustomizeException("调用C#接口取消物流单返回结果异常");
                }
                if (!r.isSuccess()) {
                    throw new CustomizeException(Optional.ofNullable(r.getMsg()).orElse(r.getUserMsg()));
                }

            }
        }
        //维修单日志
        String content = String.format("关联物流单：%s 的转地区操作取消", wuliuid);
        ShouhouLogNoticeBo notice = new ShouhouLogNoticeBo();
        notice.setNeedNotice(false);
        SpringUtil.getBean(ShouhouLogsService.class).addShouhouLog(oaUserBO.getUserName(),shouhouId, ShouHouLogTypeEnum.CLXX.getCode(),content,notice,false,0);
        Boolean flag = shouhouToareaService.remove(new LambdaQueryWrapper<ShouhouToarea>().eq(ShouhouToarea::getId, id).eq(ShouhouToarea::getShouhouId, shouhouId));
        return flag ? R.success("删除成功") : R.error("删除失败！");

    }

    @Override
    public R<Boolean> toAreaSubmit(ToAreaSetReq req) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();

        //Shouhou shouhou = shouhouService.getById(req.getId());
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(req.getId()), MTableInfoEnum.SHOUHOU,req.getId());
        if (shouhou == null) {
            return R.error("已取机，不可操作");
        }
        if (shouhou.getIsquji() != null && shouhou.getIsquji()) {
            return R.error("已取机，不可操作");
        }
        //检验是否存在锁定库存配件
//        List<Wxkcoutput> checkList = wxkcoutputService.list(new LambdaQueryWrapper<Wxkcoutput>().eq(Wxkcoutput::getIslockkc, true)
//                .isNotNull(Wxkcoutput::getPpriceid).eq(Wxkcoutput::getWxid, req.getId()));
        List<Wxkcoutput> checkList =CommenUtil.autoQueryHist(()->wxkcoutputService.list(new LambdaQueryWrapper<Wxkcoutput>().eq(Wxkcoutput::getIslockkc, true)
                .isNotNull(Wxkcoutput::getPpriceid).eq(Wxkcoutput::getWxid, req.getId())));
        if (CollectionUtils.isNotEmpty(checkList)) {
            return R.error("存在锁定维修配件，请先撤销再转地区");
        }
        Integer count = shouhouToareaService.getCount(req.getId());
        if (count != null) {
            return R.error("还有未完成转地区操作！请核实，若要操作，请先删除上次转出操作。");
        }
        ShouhouToarea toareaInfo = new ShouhouToarea();
        toareaInfo.setShouhouId(req.getId());
        toareaInfo.setAreaid(req.getArea());
        toareaInfo.setToareaid(req.getToArea());
        toareaInfo.setShibian(req.getShibian());
        toareaInfo.setInuser(oaUserBO.getUserName());

        shouhouToareaService.save(toareaInfo);
        Integer shToareaId = toareaInfo.getId();
        //todo  转地区操作暂时调用C#接口
        return null;
    }

    @Override
    public R<String> imeiApply(Integer id, String imei, String comment) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (StringUtils.isEmpty(imei)) {
            return R.error("串号不可为空！");
        }
        if (StringUtils.isEmpty(comment)) {
            return R.error("备注不可为空！");
        }

        String user = oaUserBO.getUserName();
        ShouhouImeiInfoBo imeiInfo = this.getImeiInfo(id);
        if (imeiInfo == null) {
            return R.error("没有退换记录，不允许更改IMEI");
        }
        if (imeiInfo.getTradedate() == null) {
            return R.error("非本公司销售手机不可更改串号！");
        } else if (imeiInfo.getUserid() != null && imeiInfo.getUserid() == 76783) {
            return R.error("现货不在这里改串号！");
        } else if (1 == imeiInfo.getTuihuanKind()) {
            return R.error("换机头，不用进行此项操作！");
        }
        String imei1 = imeiInfo.getImei();

        List<String> imeiList = baseMapper.getImei1(imei1);
        if (CollectionUtils.isNotEmpty(imeiList)) {
            imei1 = imeiList.get(0);
        }
        Integer count = baseMapper.getImeiChangeCount(id);
        Boolean flag = false;
        if (count > 0) {
            flag = shouhouImeichangeService.update(new LambdaUpdateWrapper<ShouhouImeichange>()
                    .set(ShouhouImeichange::getImei1, imei1).set(ShouhouImeichange::getImei2, imei)
                    .set(ShouhouImeichange::getMsg, comment).set(ShouhouImeichange::getInuser, user)
                    .set(ShouhouImeichange::getDtime, LocalDateTime.now()).eq(ShouhouImeichange::getShouhouId, id));
        } else {
            ShouhouImeichange shImeichange = new ShouhouImeichange();
            shImeichange.setShouhouId(id);
            shImeichange.setImei1(imei1);
            shImeichange.setImei2(imei);
            shImeichange.setMsg(comment);
            shImeichange.setInuser(user);
            shImeichange.setDtime(LocalDateTime.now());
            flag = shouhouImeichangeService.save(shImeichange);
        }
        if (flag) {
            return R.success("提交成功！");
        } else {
            return R.error("更新数据库失败！");
        }

    }

    @Override
    public R<String> imeiApplyCheck(Integer id) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        String user = oaUserBO.getUserName();
        Boolean flag = shouhouImeichangeService.update(new LambdaUpdateWrapper<ShouhouImeichange>()
                .set(ShouhouImeichange::getCheckuser, user).set(ShouhouImeichange::getCheckdtime, LocalDateTime.now())
                .isNull(ShouhouImeichange::getCheckdtime).eq(ShouhouImeichange::getShouhouId, id));
        return flag ? R.success("操作成功") : R.error("操作失败");
    }

    @Override
    public R<String> imeiApplyDel(Integer id) {

        Boolean flag = shouhouImeichangeService.remove(new LambdaQueryWrapper<ShouhouImeichange>()
                .isNull(ShouhouImeichange::getCheckdtime).eq(ShouhouImeichange::getShouhouId, id));
        return flag ? R.success("操作成功") : R.error("操作失败");
    }

    @Override
    public ShouhouImeiInfoBo getImeiInfo(Integer id) {
        return baseMapper.getImeiInfo(id);
    }

    @Override
    public R<String> saveMoney(Integer userId, Integer subId, BigDecimal money, String inuser,
                               String comment, Integer eKind, Integer areaId, Integer originAreaId, Boolean isSms,
                               Integer transferKind, Boolean limitSaveMoney) {
        String url = inwcfUrlSource.getMoneySave();
        List<String> activeProfile = SpringContextUtil.getActiveProfileList();
        // 加上风控
        if (!activeProfile.stream().anyMatch(e -> e.equalsIgnoreCase("jiuji")) && url.contains("ch999")) {
            log.error("退余额链接错误");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("退余额链接错误");
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        SaveMoneyInfoBO saveMoneyInfoBO = new SaveMoneyInfoBO();
        saveMoneyInfoBO.setUserid(userId)
                .setAmount(money.doubleValue())
                .setEkind(eKind)
                .setSubid(subId)
                .setAreaid(areaId)
                .setMemo(EnumUtil.getMessageByCode(SaveMoneyEkindEnum.class, eKind));
        //SaveMoneyInfoReqBO saveMoneyInfoReqBO = new SaveMoneyInfoReqBO().setItem(saveMoneyInfoBO);
        OaApiReq oaApiReq = getOaApiReq(saveMoneyInfoBO);
        oaApiReq.setData(null);
        Map<String, Object> stringObjectMap = CommenUtil.transBean2Map(oaApiReq);
        stringObjectMap.put("timestamp", oaApiReq.getTimeStamp());
        stringObjectMap.put("Data", saveMoneyInfoBO);
        HashMap<String, Object> map = new HashMap<>();
        map.put("item", stringObjectMap);
        String params = JSONObject.toJSONString(map);
        try {
            String kcResult = HttpClientUtil.post(url, params, headers);
            R r = JSON.parseObject(JSON.parse(kcResult).toString(), R.class);
            if (r.getCode() == 0) {
                return R.success("1");
            } else {
                log.error("退余额失败，返回结果为：" + kcResult);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.success("1");
            }
        } catch (Exception e) {
            log.error("退余额失败", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error(e.getMessage());
        }

    }

    private OaApiReq getOaApiReq(SaveMoneyInfoBO saveMoneyInfoReqBO) {
        OaApiReq oaApiReq = new OaApiReq();
        oaApiReq.setTimeStamp(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        oaApiReq.setData(saveMoneyInfoReqBO);
        String secret = batchReturnMkcDao.getSecretByCode(saveMoneyInfoReqBO.getEkind());
        oaApiReq.setSign(OaVerifyUtil.createSign(
                secret, oaApiReq));
        return oaApiReq;
    }

    @Override
    public List<BBSXPUsersMoneyBo> queryBBsXpUserMoneyInfo(Integer userId) {

        return baseMapper.queryBBsXpUserMoneyInfo(userId);
    }

    @Override
    public BigDecimal getFueduByUserId(Integer userId) {

        return baseMapper.getFueduByUserId(userId);
    }

    @Override
    public void updateCostPriceById(Integer shouhouId) {
        baseMapper.updateCostPriceById(shouhouId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> enterShouyin(Integer shouhouId) {
        Integer areaId = baseMapper.getShouyinAreaId(shouhouId);
        if (areaId == null) {
            return R.error("金额错误");
        }
        Integer count = baseMapper.updateShouyingLock(shouhouId);
        return R.success("操作成功");
    }


    /**
     * 判断是否可以使用售后优惠码
     * 综合以上分析，isUseShouHouYouhuiMa 方法中明确使用了以下 NumberCard 字段：
     *
     * ch999Id - 用于判断优惠码类别（例如是否为软件接件单专用或投诉代金券）。
     * limit1 - 判断是否仅限大件使用。
     * limintClint - 判断客户类别限制。
     * limit - 判断使用次数限制。
     * useCount - 检查优惠码的使用次数。
     * startTime - 检查优惠码的开始时间。
     * endTime - 检查优惠码的结束时间。
     * state - 检查优惠码是否已使用。
     * cardID - 检查是否在特定白名单中。
     * total - 获取优惠码金额，判断是否大于维修费用，以及累加投诉优惠码金额。
     * limitprice - 获取最低使用金额限制，判断维修费用是否满足条件。
     * limitType
     * limitids
     * cardID
     * excludePpIds
     *
     * @param shouhou
     * @param dr
     * @return
     */
    @Override
    public R<Boolean> isUseShouHouYouhuiMa(Shouhou shouhou,NumberCard dr){
        Integer maCh999Id = dr.getCh999Id() == null ? 0 : dr.getCh999Id();
        Integer shouhouId = shouhou.getId();
        if (shouhou == null || Optional.ofNullable(shouhou.getIsquji()).orElse(Boolean.FALSE)) {
            return R.error("请确认维修单是否已被取机！");
        }
        if (Objects.equals(dr.getLimit1(), 1)) {
            return R.error("该优惠码仅大件可使用");
        }
        if (!Objects.equals(dr.getLimintClint(), 1) && !CommenUtil.isNullOrZero(dr.getLimintClint())) {
            return R.error("该优惠码类型不可使用");
        }
        if (dr.getLimit() != null && dr.getLimit().equals(1) && CommenUtil.isNotNullZero(dr.getUseCount())) {
            return R.error("该优惠码已超过使用次数");
        }
        if (shouhou.getFeiyong() == null || shouhou.getFeiyong().compareTo(BigDecimal.ZERO) == 0) {
            return R.error("维修费用为0，不满足使用条件");
        }

        Boolean isSoft = shouhou.getIssoft();
        Integer count = wxkcoutputService.count(new LambdaQueryWrapper<Wxkcoutput>().gt(Wxkcoutput::getPpriceid, 0).eq(Wxkcoutput::getWxid, shouhou.getId()));
        if (isSoft != null && isSoft && maCh999Id.equals(-39) && CommenUtil.isNullOrZero(count)) {
            if (StringUtils.isNotEmpty(shouhou.getYouhuima()) || (shouhou.getYouhuifeiyong() != null && shouhou.getYouhuifeiyong().compareTo(BigDecimal.ZERO) > 0)) {
                return R.error("该优惠码不可以叠加使用");
            }
            //校验配件ppid
            if (CommenUtil.isNotNullZero(count)) {
                return R.error("维修配件不符合使用规则");
            }
        }
        //限制软件优惠码
        if (isSoft != null && isSoft && !Objects.equals(-39, maCh999Id)) {
            return R.error("该优惠码不能在软件接件单使用");
        }
        if (isSoft != null && !isSoft && Objects.equals(-39, maCh999Id)) {
            return R.error("该优惠码限软件接件单使用");
        }
        LocalDate StartTime = dr.getStartTime();
        LocalDate EndTime = dr.getEndTime();
        LocalDateTime startTime = LocalDateTime.of(StartTime.getYear(), StartTime.getMonth(), StartTime.getDayOfMonth(), 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(EndTime.getYear(), EndTime.getMonth(), EndTime.getDayOfMonth(), 23, 59, 59);
        if (startTime.isAfter(LocalDateTime.now()) || endTime.isBefore(LocalDateTime.now())) {
            return R.error("该优惠码未在有效期内！");
        }
        // limit 2 不限制使用次数
        if ((dr.getState() != null && dr.getState()) && ObjectUtil.notEqual(dr.getLimit(),2) &&
                !Arrays.asList("6AD39F3F4", "8550D5CA4", "9DEC64C94", "633E2D224", "DCC4F82C4").contains(dr.getCardID())) {
            return R.error("该优惠码已使用！");
        }
        //获取售后单
        ShouhouDtBo sh = shouhouMapper.getShouhouDtById(shouhouId);
        Integer huanCount = shouhouHuishouService.count(new LambdaQueryWrapper<ShouhouHuishou>().eq(ShouhouHuishou::getShouhouId, sh.getId()));
        huanCount = huanCount == null ? 0 : huanCount;
        Integer wxKcCount = wxkcoutputService.getWxKcCount(sh.getId());
        wxKcCount = wxKcCount == null ? 0 : wxKcCount;
        Integer serviceType = sh.getServiceType() == null ? 0 : sh.getServiceType();

        if (serviceType == 0 && huanCount == 0 && wxKcCount > 0) {
            return R.error("此单已改价，不允许再使用优惠码！");
        }
        //是否存在渠道
        Integer shQuDaoCount = shouhouQudaoService.count(new LambdaQueryWrapper<ShouhouQudao>().eq(ShouhouQudao::getShouhouid, sh.getId()));
        shouhou.setIsQuDao(shQuDaoCount);
        //优惠码使用配件校验
        R<Set<Integer>> pjListR = listYouhuimaPj(dr, shouhou);
        if (pjListR.getCode() != ResultCode.SUCCESS){
            //验证配件未通过
            return R.error(pjListR.getUserMsg());
        }
        //租户为九机的话 重新调整优惠码使用逻辑
        if (!XtenantEnum.isJiujiXtenant()) {
            if (shQuDaoCount > 0) {
                return R.error("优惠码仅限自修使用，请核实！");
            }
        }
        BigDecimal feiyong = sh.getFeiyong() == null ? BigDecimal.ZERO : sh.getFeiyong();
        BigDecimal total = dr.getTotal() == null ? BigDecimal.ZERO : dr.getTotal();
        BigDecimal limitPrice = dr.getLimitprice() == null ? BigDecimal.ZERO : dr.getLimitprice();
        if (feiyong.compareTo(total) < 0) {
            return R.error("优惠码金额大于维修费，无法使用！");
        }
        if (serviceType > 0) {
            return R.error("已使用九机服务，不能再使用优惠码！");
        }
        List<ShouhouQudao> qudaoList = shouhouQudaoService.list(new LambdaQueryWrapper<ShouhouQudao>().eq(ShouhouQudao::getShouhouid, sh.getId()));
        if (!XtenantEnum.isJiujiXtenant()) {
            if (CollectionUtils.isNotEmpty(qudaoList)) {
                return R.error("外送渠道维修单，不能使用优惠码！");
            }
        }
        //如果已经使用折扣,不允许使用普通优惠码
        boolean isDaijingJuanOrTousuFlag = isDaijingJuanOrTousu(dr);
        if(memberDiscountService.existDiscount(shouhouId) && !isDaijingJuanOrTousuFlag){
            return R.error("已经使用会员折扣，不能使用普通的优惠码！");
        }
        List<Integer> bargainPprices = Collections.emptyList();
        if(!isDaijingJuanOrTousuFlag && !(bargainPprices = baseMapper.listBargainPprice(shouhouId)).isEmpty()){
            return R.error(StrUtil.format("存在ppid为:{}的特价商品，不能使用普通的优惠码！",bargainPprices.stream().distinct().map(String::valueOf)
                    .collect(Collectors.joining(SignConstant.COMMA))));
        }
        //是否是投诉优惠码
        AtomicBoolean isComplaintDiscountCode = new AtomicBoolean(false);
        //投诉优惠码的金额
        AtomicReference<BigDecimal> isComplaintDiscountCodeTotal = new AtomicReference<>(BigDecimal.ZERO);
        //判断是否是代金券类型
        //1.如果当前优惠码不为空时，取出优惠码判断其类型
        if (StringUtils.isNotEmpty(sh.getYouhuima()) && 194 != sh.getAreaid() && 221 != sh.getAreaid()) {
            List<String> youHuiMaList = CommenUtil.toYouHuiMaList(sh.getYouhuima());
            AtomicBoolean flag = new AtomicBoolean(false);
            if (youHuiMaList.size() == 1) {
                List<NumberCard> list = listNumberCardByCardId(youHuiMaList);
                list.forEach(l -> {
                    //当优惠码类型是投诉代金券时
                    if (isDaijingJuanOrTousu(l)) {
                        isComplaintDiscountCode.set(true);
                        isComplaintDiscountCodeTotal.getAndUpdate(l.getTotal()::add);
                        //那么第二次使用优惠码时，只能使用普通优惠码
                        if (!Objects.equals(maCh999Id, JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_20.getCode())) {
                            flag.set(true);
                        }
                    } else {
                        if (Objects.equals(maCh999Id, JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_20.getCode())) {
                            flag.set(true);
                        }
                    }
                });
            }
            if (Boolean.FALSE.equals(flag.get())) {
                return R.error("该维修单已使用过该类型优惠码，不能重复使用！");
            }
        }
        //优惠码调整
        //如果是用过投诉优惠码的，金额不占用限制
        if (Boolean.TRUE.equals(isComplaintDiscountCode.get())) {
            //获得优惠码的金额
            if (feiyong.add(isComplaintDiscountCodeTotal.get()).compareTo(limitPrice) < 0 && 194 != sh.getAreaid() && 221 != sh.getAreaid()) {
                return R.error("该维修单维修费用未达到使用该优惠码的额度，使用额度：" + limitPrice);
            }
        } else {
            if (feiyong.compareTo(limitPrice) < 0 && 194 != sh.getAreaid() && 221 != sh.getAreaid()) {
                return R.error("该维修单维修费用未达到使用该优惠码的额度，使用额度：" + limitPrice);
            }
        }
        return R.success(true);
    }

    @Override
    public R<UseYouhuiMaInfo> checkYouhuiMa(Integer shouhouId, String youhuima) {
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> useYouhuiMa(Integer shouhouId, String youhuima, String operateUser) {
        //判断优惠码是否存在
        List<NumberCard> youhuiMaList = numberCardService.getNumberCardByCardId(youhuima);
        if (CollectionUtils.isEmpty(youhuiMaList)) {
            return R.error("优惠码不存在或已删除！");
        }
        NumberCard dr = youhuiMaList.get(0);
        Integer maCh999Id = dr.getCh999Id() == null ? 0 : dr.getCh999Id();
        //上门优惠码，只能上门维修单使用
        Shouhou shouhou = CommenUtil.autoQueryHist(() -> shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU,shouhouId);
        //优惠码校验提出来
        R<Boolean> useShouHouYouhuiMa = isUseShouHouYouhuiMa(shouhou, dr);
        if(!useShouHouYouhuiMa.isSuccess()){
            return useShouHouYouhuiMa;
        }
        //软件单使用优惠码
        Boolean isSoft = shouhou.getIssoft();
        Integer count = wxkcoutputService.count(new LambdaQueryWrapper<Wxkcoutput>().gt(Wxkcoutput::getPpriceid, 0).eq(Wxkcoutput::getWxid, shouhou.getId()));
        if (isSoft != null && isSoft && maCh999Id.equals(-39) && CommenUtil.isNullOrZero(count)) {
            return this.useYouHuiMaWithMsoft(shouhou, dr, operateUser);
        }

/*        if (shouhou == null || shouhou.getIsquji()) {
            return R.error("请确认维修单是否已被取机！");
        }
        if (Objects.equals(dr.getLimit1(), 1)) {
            return R.error("该优惠码仅大件可使用");
        }
        if (!Objects.equals(dr.getLimintClint(), 1) && !CommenUtil.isNullOrZero(dr.getLimintClint())) {
            return R.error("该优惠码类型不可使用");
        }
        if (dr.getLimit() != null && dr.getLimit().equals(1) && CommenUtil.isNotNullZero(dr.getUseCount())) {
            return R.error("该优惠码已超过使用次数");
        }
        if (shouhou.getFeiyong() == null || shouhou.getFeiyong().compareTo(BigDecimal.ZERO) == 0) {
            return R.error("维修费用为0，不满足使用条件");
        }

        Boolean isSoft = shouhou.getIssoft();
        if (isSoft != null && isSoft && maCh999Id.equals(-39)) {
            if (StringUtils.isNotEmpty(shouhou.getYouhuima()) || (shouhou.getYouhuifeiyong() != null && shouhou.getYouhuifeiyong().compareTo(BigDecimal.ZERO) > 0)) {
                return R.error("该优惠码不可以叠加使用");
            }
        }
        //限制软件优惠码
        if (isSoft != null && isSoft && !Objects.equals(-39, maCh999Id)) {
            return R.error("该优惠码不能在软件接件单使用");
        }
        if (isSoft != null && !isSoft && Objects.equals(-39, maCh999Id)) {
            return R.error("该优惠码限软件接件单使用");
        }
        LocalDate StartTime = dr.getStartTime();
        LocalDate EndTime = dr.getEndTime();
        LocalDateTime startTime = LocalDateTime.of(StartTime.getYear(), StartTime.getMonth(), StartTime.getDayOfMonth(), 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(EndTime.getYear(), EndTime.getMonth(), EndTime.getDayOfMonth(), 23, 59, 59);
        if (startTime.isAfter(LocalDateTime.now()) || endTime.isBefore(LocalDateTime.now())) {
            return R.error("该优惠码未在有效期内！");
        }
        // limit 2 不限制使用次数
        if ((dr.getState() != null && dr.getState()) && ObjectUtil.notEqual(dr.getLimit(),2) &&
                !Arrays.asList("6AD39F3F4", "8550D5CA4", "9DEC64C94", "633E2D224", "DCC4F82C4").contains(dr.getCardID())) {
            return R.error("该优惠码已使用！");
        }
        //获取售后单
        ShouhouDtBo sh = shouhouMapper.getShouhouDtById(shouhouId);
        useYouhuiMaInfo.setSh(sh);
        Integer huanCount = shouhouHuishouService.count(new LambdaQueryWrapper<ShouhouHuishou>().eq(ShouhouHuishou::getShouhouId, sh.getId()));
        huanCount = huanCount == null ? 0 : huanCount;
        Integer wxKcCount = wxkcoutputService.getWxKcCount(sh.getId());
        wxKcCount = wxKcCount == null ? 0 : wxKcCount;
        Integer serviceType = sh.getServiceType() == null ? 0 : sh.getServiceType();

        if (serviceType == 0 && huanCount == 0 && wxKcCount > 0) {
            return R.error("此单已改价，不允许再使用优惠码！");
        }
        //是否存在渠道
        Integer shQuDaoCount = shouhouQudaoService.count(new LambdaQueryWrapper<ShouhouQudao>().eq(ShouhouQudao::getShouhouid, sh.getId()));
        shouhou.setIsQuDao(shQuDaoCount);
        //优惠码使用配件校验
        R<Set<Integer>> pjListR = listYouhuimaPj(dr, shouhou);
        if (pjListR.getCode() != ResultCode.SUCCESS){
            //验证配件未通过
            return R.error(pjListR.getUserMsg());
        }
        //租户为九机的话 重新调整优惠码使用逻辑
        if (!XtenantEnum.isJiujiXtenant()) {
            if (shQuDaoCount > 0) {
                return R.error("优惠码仅限自修使用，请核实！");
            }
        }
        BigDecimal feiyong = sh.getFeiyong() == null ? BigDecimal.ZERO : sh.getFeiyong();
        BigDecimal total = dr.getTotal() == null ? BigDecimal.ZERO : dr.getTotal();
        BigDecimal limitPrice = dr.getLimitprice() == null ? BigDecimal.ZERO : dr.getLimitprice();
        if (feiyong.compareTo(total) < 0) {
            return R.error("优惠码金额大于维修费，无法使用！");
        }
        if (serviceType > 0) {
            return R.error("已使用九机服务，不能再使用优惠码！");
        }
        List<ShouhouQudao> qudaoList = shouhouQudaoService.list(new LambdaQueryWrapper<ShouhouQudao>().eq(ShouhouQudao::getShouhouid, sh.getId()));
        if (!XtenantEnum.isJiujiXtenant()) {
            if (CollectionUtils.isNotEmpty(qudaoList)) {
                return R.error("外送渠道维修单，不能使用优惠码！");
            }
        }
        //如果已经使用折扣,不允许使用普通优惠码
        boolean isDaijingJuanOrTousuFlag = isDaijingJuanOrTousu(dr);
        if(memberDiscountService.existDiscount(shouhouId) && !isDaijingJuanOrTousuFlag){
            return R.error("已经使用会员折扣，不能使用普通的优惠码！");
        }
        List<Integer> bargainPprices;
        if(!isDaijingJuanOrTousuFlag && !(bargainPprices = baseMapper.listBargainPprice(shouhouId)).isEmpty()){
            return R.error(StrUtil.format("存在ppid为:{}的特价商品，不能使用普通的优惠码！",bargainPprices.stream().distinct().map(String::valueOf)
                    .collect(Collectors.joining(SignConstant.COMMA))));
        }
        //是否是投诉优惠码
        AtomicBoolean isComplaintDiscountCode = new AtomicBoolean(false);
        //投诉优惠码的金额
        AtomicReference<BigDecimal> isComplaintDiscountCodeTotal = new AtomicReference<>(BigDecimal.ZERO);
        //判断是否是代金券类型
        //1.如果当前优惠码不为空时，取出优惠码判断其类型
        if (StringUtils.isNotEmpty(sh.getYouhuima()) && 194 != sh.getAreaid() && 221 != sh.getAreaid()) {
            List<String> youHuiMaList = CommenUtil.toYouHuiMaList(sh.getYouhuima());
            AtomicBoolean flag = new AtomicBoolean(false);
            if (youHuiMaList.size() == 1) {
                List<NumberCard> list = listNumberCardByCardId(youHuiMaList);
                list.forEach(l -> {
                    //当优惠码类型是投诉代金券时
                    if (isDaijingJuanOrTousu(l)) {
                        isComplaintDiscountCode.set(true);
                        isComplaintDiscountCodeTotal.getAndUpdate(l.getTotal()::add);
                        //那么第二次使用优惠码时，只能使用普通优惠码
                        if (!Objects.equals(maCh999Id, JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_20.getCode())) {
                            flag.set(true);
                        }
                    } else {
                        if (Objects.equals(maCh999Id, JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_20.getCode())) {
                            flag.set(true);
                        }
                    }
                });
            }
            if (Boolean.FALSE.equals(flag.get())) {
                return R.error("该维修单已使用过该类型优惠码，不能重复使用！");
            }
        }
        //优惠码调整
        //如果是用过投诉优惠码的，金额不占用限制
        if (Boolean.TRUE.equals(isComplaintDiscountCode.get())) {
            //获得优惠码的金额
            if (feiyong.add(isComplaintDiscountCodeTotal.get()).compareTo(limitPrice) < 0 && 194 != sh.getAreaid() && 221 != sh.getAreaid()) {
                return R.error("该维修单维修费用未达到使用该优惠码的额度，使用额度：" + limitPrice);
            }
        } else {
            if (feiyong.compareTo(limitPrice) < 0 && 194 != sh.getAreaid() && 221 != sh.getAreaid()) {
                return R.error("该维修单维修费用未达到使用该优惠码的额度，使用额度：" + limitPrice);
            }
        }*/
        ShouhouDtBo sh = shouhouMapper.getShouhouDtById(shouhouId);
        BigDecimal total = dr.getTotal() == null ? BigDecimal.ZERO : dr.getTotal();
        //以下代码片段需进行事务控制
        try {
            youhuima = dr.getCardID();
            if (StringUtils.isEmpty(youhuima)) {
                youhuima = "";
            }
            BigDecimal youhuimaTotal = total;
            shouhouService.updateYouhuiMaUseInfo(sh.getId(), youhuima, youhuimaTotal);

            //修改优惠码使用
            numberCardService.updateNumberCardUseCount(youhuima);

            //添加优惠码使用日志
            CardLogs cardLogs = new CardLogs();
            cardLogs.setCardid(dr.getId());
            cardLogs.setSubId(sh.getId());
            cardLogs.setPushTime(LocalDateTime.now());
            cardLogs.setAreaid(sh.getAreaid());
            cardLogs.setUseren(operateUser);
            cardLogs.setUserid(dr.getUserid());
            cardLogs.setUseType(1);
            cardLogs.setPrices(dr.getTotal());
            cardLogs.setLiangpin(3);

            cardLogsService.save(cardLogs);

            //添加售后进程
            String youhuimaCode = dr.getCardID();
            String comment ="使用优惠码抵扣费用" + dr.getTotal() + "元！优惠码： <a class='yhmTip' data="+youhuimaCode+" title=标题：" +dr.getGName()+"，限制金额："+dr.getTotal()+getDrLimitValue(Optional.ofNullable(dr.getLimit1()).orElse(NumberConstant.ZERO))+">" +youhuimaCode+ "</a>";
            shouhouService.saveShouhouLog(shouhouId, comment, operateUser, null, true);
        } catch (Exception e) {
            log.error("优惠码使用异常：{}", e);
            //回滚事务
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("优惠码使用失败！");
        }
        return R.success("优惠码使用成功！");
    }

    /**
     * 获取限制信息
     * @param limit
     * @return
     */
    public static String getDrLimitValue(Integer limit){
        String limitValue = "";
        // 1 仅大件  2 仅小件 3 不限制 4 大件+小件
        switch (limit){
            case 1:
                limitValue="仅大件";
                break;
            case 2:
                limitValue="仅小件";
                break;
            case 3:
                limitValue="不限制";
                break;
            case 4:
                limitValue="大件+小件";
                break;
            default:
                break;
        }
        return StringUtils.isEmpty(limitValue)?limitValue:"，限制1："+limitValue ;
    }

    private List<NumberCard> listNumberCardByCardId(List<String> youHuiMaList) {
        List<NumberCard> list = numberCardService.list(new LambdaQueryWrapper<NumberCard>().in(NumberCard::getCardID, youHuiMaList).last("and isnull(isdel,0) = 0"));
        return list;
    }

    private boolean isDaijingJuanOrTousu(NumberCard l) {
        return Objects.equals(l.getCh999Id(), JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_20.getCode()) || Boolean.TRUE.equals(l.getIsdjq());
    }

    @Override
    public R<Set<Integer>> listYouhuimaPj(NumberCard dr,Shouhou shouhou) {
        //当租户为九机的时候重新更改优惠码使用规则
        //是九机 且自修 且都是手工费
        boolean isZiXiuAndIsCheckManualAll = false;
        //是九机 且自修 且既有配件又有手工费
        boolean isZiXiuAndIsCheckManual = false;
        //是九机 且外修 且既有配件又有手工费
        boolean isWaiXiuAndIsCheckManual = false;
        List<Integer> manualByIds = new ArrayList<>();
        List<Integer> wxpjByIds = new ArrayList<>();
        if (XtenantEnum.isJiujiXtenant()) {
            //是否存在渠道
            Integer shQuDaoCount = shouhouQudaoService.count(new LambdaQueryWrapper<ShouhouQudao>().eq(ShouhouQudao::getShouhouid, shouhou.getId()));
            shouhou.setIsQuDao(shQuDaoCount);
            //是否外送 true是外送售后，false是自修
            boolean isWaiSong = Boolean.TRUE.equals(CommenUtil.isNotNullZero(shouhou.getIsQuDao()));
            //获取当前维修单所有配件信息
            List<HexiaoBo> hexiaoBoList = wxkcoutputService.getHexiao(shouhou.getId());
            boolean isManualByAll = false;
            boolean isCheckManual = false;
            if (CollUtil.isNotEmpty(hexiaoBoList)){
                //为true的话 都是手工费
                isManualByAll = hexiaoBoList.stream().allMatch(hexiaoBo -> CommenUtil.isNullOrZero(hexiaoBo.getPpid()));
                //为true的话 在维修中既有配件又有手工费
                isCheckManual = hexiaoBoList.stream().anyMatch(hexiaoBo -> CommenUtil.isNullOrZero(hexiaoBo.getPpid())) &&
                                hexiaoBoList.stream().anyMatch(hexiaoBo -> CommenUtil.isNotNullZero(hexiaoBo.getPpid()));
            }
            //判断  自修且都是手工费的话 手工费也要参与优惠码分摊计算 手工费也能使用优惠码
            if (Boolean.FALSE.equals(isWaiSong) && isManualByAll){
                //拿到手工费的id
                manualByIds = hexiaoBoList.stream().filter(hexiaoBo -> CommenUtil.isNullOrZero(hexiaoBo.getPpid())).map(HexiaoBo::getId).collect(Collectors.toList());
                isZiXiuAndIsCheckManualAll = true;
            }
            //判断 自修 且存在手工费的话 手工费需要和配件分摊优惠码
            if (Boolean.FALSE.equals(isWaiSong) && isCheckManual){
                //拿到手工费的id
                manualByIds = hexiaoBoList.stream().filter(hexiaoBo -> CommenUtil.isNullOrZero(hexiaoBo.getPpid())).map(HexiaoBo::getId).collect(Collectors.toList());
                isZiXiuAndIsCheckManual = true;
            }
            //外修 且全部都是手工费
            if (isWaiSong && isManualByAll){
                return R.error("外修渠道且均是手工费，不满足优惠码使用条件！");
            }
            //外修 且在维修中既有配件又有手工费
            if (isWaiSong && isCheckManual){
                //判断维修配件金额是否满足使用优惠码
                BigDecimal reduce = hexiaoBoList.stream().filter(he -> CommenUtil.isNotNullZero(he.getPpid()))
                        .map(HexiaoBo::getPrice1).reduce(BigDecimal.ZERO, BigDecimal::add);
                //维修配件费用比较优惠码限制费用
                if (reduce.compareTo(Optional.ofNullable(dr.getLimitprice()).orElse(BigDecimal.ZERO)) < 0) {
                    return R.error("该维修单维修费用未达到使用该优惠码的额度，使用额度：" + Optional.ofNullable(dr.getLimitprice()).orElse(BigDecimal.ZERO));
                }
                //拿到手工费的id
                manualByIds = hexiaoBoList.stream().filter(hexiaoBo -> CommenUtil.isNullOrZero(hexiaoBo.getPpid())).map(HexiaoBo::getId).collect(Collectors.toList());
                //拿到配件的ID
                wxpjByIds = hexiaoBoList.stream().filter(hexiaoBo -> CommenUtil.isNotNullZero(hexiaoBo.getPpid())).map(HexiaoBo::getId).collect(Collectors.toList());
                isWaiXiuAndIsCheckManual = true;
            }
        }
        Integer limitType = dr.getLimitType() == null ? 0 : dr.getLimitType();
        Integer maCh999Id = dr.getCh999Id() == null ? 0 : dr.getCh999Id();
        String limitids = StringUtils.isEmpty(dr.getLimitids()) ? "" : dr.getLimitids();
        List<Integer> cidList = new LinkedList<>();
        List<Integer> limitPpidList = new LinkedList<>();

        if (StringUtils.isNotEmpty(limitids)) {
            if (!limitType.equals(LimitProTypeEnum.LIMIT_PPID.getCode())) {
                cidList = SpringUtil.getBean(CategoryService.class)
                        .getProductChildCidList(Arrays.asList(limitids.split(",")).stream().map(e -> Integer.valueOf(e))
                                .collect(Collectors.toList()));
            } else {
                //限制ppid ppid对应
                limitPpidList = Arrays.asList(limitids.split(",")).stream().map(e -> Integer.valueOf(e)).collect(Collectors.toList());
            }
        }
        //xiexiongkun 可以进行分摊费用的维修配件,到最后为空就是没有限制配件,可以全部进行分摊
        Set<Integer> wxkcIds = new HashSet<>();
        Integer shouhouId = shouhou.getId();
        if (limitType.equals(LimitProTypeEnum.LIMIT_PPID.getCode())) {
            //限制ppid并且cid为23分类下的
            if (CollectionUtils.isNotEmpty(limitPpidList)) {
                //查询当前维修单配件是否满足使用条件
                List<Integer> satisfys = numberCardService.validateYouHuiMaSatisfyUseCondition(limitPpidList, shouhouId);
                if (satisfys.isEmpty()) {
                    return R.error(StrUtil.format("优惠码不满足使用条件,必须存在ppid为:{}的配件,编号:1222",StrUtil.join(SignConstant.COMMA,limitPpidList)));
                }else{
                    wxkcIds.addAll(satisfys);
                }
                cidList = productinfoService.getCidsByPpid(limitPpidList);
            }
        }

        Integer webtype2 = shouhou.getWebtype2() == null ? 0 : shouhou.getWebtype2();
        if (JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_25.getCode().equals(maCh999Id) && webtype2 != 4) {
            return R.error("优惠码限制上门维修单才可以使用！");
        }
        // 2021-12 祁连让放开投诉优惠码限制
        /*if (maCh999Id.equals(JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_20.getCode())) {
            return R.error("投诉优惠码不能使用于维修单！");
        }*/

        LocalDate codeAddTime = dr.getAddTime();
        LocalDate newCreateTime = LocalDate.of(shouhouConstants.getWeixiuCodeUpdateTime().getYear(), shouhouConstants.getWeixiuCodeUpdateTime().getMonth()
                , shouhouConstants.getWeixiuCodeUpdateTime().getDayOfMonth());

        if (equals(JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_23.getCode().equals(maCh999Id))) {
            List<Integer> wxpjIds = wxkcoutputService.cidCheck1(shouhouId, cidList);
            if (wxpjIds.isEmpty()) {
                return R.error(StrUtil.format("维修配件不符合优惠码使用规则！必须存在分类为:{}的配件,编号:1247",StrUtil.join(SignConstant.COMMA,cidList)));
            }else{
                wxkcIds.addAll(wxpjIds);
            }
        } else {
            if (codeAddTime.isBefore(newCreateTime)) {
                List<String> suportCids = Arrays.asList("23", "24", "25", "31", "393", "410");
                if (cidList.stream().allMatch(suportCids::contains)) {
                    List<Integer> wxpjIds = wxkcoutputService.cidCheck(shouhouId, limitPpidList);
                    //自修，且都是手工费 且是九机 添加手工费的维修id
                    if (isZiXiuAndIsCheckManualAll){
                        wxpjIds.addAll(manualByIds);
                    }
                    if (wxpjIds.isEmpty()) {
                        return R.error(StrUtil.format("维修配件不符合优惠码使用规则！必须存在分类为:{}的配件,编号:1257",StrUtil.join(SignConstant.COMMA,limitPpidList)));
                    }else{
                        //当自修 存在手工费 且配件满足优惠码使用规则 手工费也参与分摊优惠码
                        if (isZiXiuAndIsCheckManual){
                            wxkcIds.addAll(manualByIds);
                        }
                        //外修 且存在配件和手工费
                        if (isWaiXiuAndIsCheckManual){
                            //过滤掉手工费
                            wxkcIds.stream().filter(manualByIds::contains).collect(Collectors.toList()).forEach(wxkcIds::remove);
                        }
                        wxkcIds.addAll(wxpjIds);
                    }
                } else {
                    return R.error(StrUtil.format("优惠码限制类别错误！限制类别必须为:{}之一,编号:1262",StrUtil.join(SignConstant.COMMA,suportCids)));
                }
            } else {
                if (CollectionUtils.isNotEmpty(cidList)) {
                    if (cidList.stream().anyMatch(e -> shouhouConstants.getWeixiuCodeLimitIds().contains(e))) {
                        List<Integer> wxpjIds = wxkcoutputService.cidCheck(shouhouId, cidList);
                        //自修，且都是手工费 且是九机 添加手工费的维修id
                        if (isZiXiuAndIsCheckManualAll){
                            wxpjIds.addAll(manualByIds);
                        }
                        if (wxpjIds.isEmpty()) {
                            return R.error(StrUtil.format("维修配件不符合优惠码使用规则！必须存在分类为:{}的配件,编号:1269",StrUtil.join(SignConstant.COMMA,cidList)));
                        }else {
                            //当自修 存在手工费 且配件满足优惠码使用规则 手工费也参与分摊优惠码
                            if (isZiXiuAndIsCheckManual){
                                wxkcIds.addAll(manualByIds);
                            }
                            //外修 且存在配件和手工费
                            if (isWaiXiuAndIsCheckManual){
                                //过滤掉手工费
                                wxpjIds.stream().filter(manualByIds::contains).collect(Collectors.toList()).forEach(wxkcIds::remove);
                            }
                            wxkcIds.addAll(wxpjIds);
                        }
                    } else {
                        return R.error(StrUtil.format("优惠码限制类别错误！限制类别必须包含:{}之一,编号:1274",StrUtil.join(SignConstant.COMMA,shouhouConstants.getWeixiuCodeLimitIds())));
                    }
                }
            }
        }
        if (codeAddTime.isBefore(newCreateTime)) {
            List<Integer> suportCids = Arrays.asList(24, 25, 31, 393, 410);
            if (CollUtil.containsAny(limitPpidList,suportCids)) {
                List<Integer> cidChecks = wxkcoutputService.cidCheck(shouhouId, limitPpidList);
                //自修，且都是手工费 且是九机 添加手工费的维修id
                if (isZiXiuAndIsCheckManualAll){
                    cidChecks.addAll(manualByIds);
                }
                if (cidChecks.isEmpty()) {
                    return R.error(StrUtil.format("维修配件不符合优惠码使用规则！必须存在分类为:{}的配件,编号:1284",StrUtil.join(SignConstant.COMMA,suportCids)));
                }else{
                    //当自修 存在手工费 且配件满足优惠码使用规则 手工费也参与分摊优惠码
                    if (isZiXiuAndIsCheckManual){
                        wxkcIds.addAll(manualByIds);
                    }
                    //外修 且存在配件和手工费
                    if (isWaiXiuAndIsCheckManual){
                        //过滤掉手工费
                        wxkcIds.stream().filter(manualByIds::contains).collect(Collectors.toList()).forEach(wxkcIds::remove);
                    }
                    wxkcIds.addAll(cidChecks);
                }
            }
        } else {
            Boolean flag = false;
            for (Integer e : limitPpidList) {
                if (shouhouConstants.getWeixiuCodeLimitIds().contains(e)) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                List<Integer> cidChecks = wxkcoutputService.cidCheck(shouhouId, shouhouConstants.getWeixiuCodeLimitIds());
                //自修，且存在手工费 且是九机 添加手工费的维修id
                if (isZiXiuAndIsCheckManualAll){
                    cidChecks.addAll(manualByIds);
                }
                if (cidChecks.isEmpty()) {
                    return R.error(StrUtil.format("维修配件不符合优惠码使用规则！必须存在分类为:{}的配件,编号:1300",StrUtil.join(SignConstant.COMMA,shouhouConstants.getWeixiuCodeLimitIds())));
                }else{
                    //当自修 存在手工费 且配件满足优惠码使用规则 手工费也参与分摊优惠码
                    if (isZiXiuAndIsCheckManual){
                        wxkcIds.addAll(manualByIds);
                    }
                    //外修 且存在配件和手工费
                    if (isWaiXiuAndIsCheckManual){
                        //过滤掉手工费
                        wxkcIds.stream().filter(manualByIds::contains).collect(Collectors.toList()).forEach(wxkcIds::remove);
                    }
                    wxkcIds.addAll(cidChecks);
                }
            }
        }
        //当外修 且九机 且手工费和维修配件均有 且是投诉优惠码 （只有维修配件进行优惠码分摊）
        if (isWaiXiuAndIsCheckManual && maCh999Id.equals(JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_20.getCode())){
            wxkcIds.addAll(wxpjByIds);
        }
        //为空返回提示
        if(removeExcludePpidWxkcId(dr.getExcludePpIds(), wxkcIds,null) && CollUtil.isEmpty(wxkcIds)){
            return R.error(StrUtil.format("没有找到匹配优惠码的商品：{}", dr.getCardID()));
        }
        return R.success(wxkcIds);
    }

    @Override
    public boolean removeExcludePpidWxkcId(String excludePpIdsStr, Set<Integer> wxkcIds, List<Wxkcoutput> idPpidList) {
        //过滤掉排除ppid的配件
        List<Integer> excludePpIds = StrUtil.splitTrim(excludePpIdsStr, StringPool.COMMA).stream().map(Convert::toInt).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(wxkcIds) && CollUtil.isNotEmpty(excludePpIds)){
            List<Wxkcoutput> idPpidListFinal = DecideUtil.isNull(idPpidList, () ->CommenUtil.autoQueryHist(()-> wxkcoutputService.lambdaQuery().select(Wxkcoutput::getId, Wxkcoutput::getPpriceid)
                    .in(Wxkcoutput::getId, wxkcIds).isNotNull(Wxkcoutput::getPpriceid)
                    .list()));
            return wxkcIds.removeIf(wxkcId->idPpidListFinal.stream().anyMatch(w -> Objects.equals(w.getId(),wxkcId) && excludePpIds.contains(w.getPpriceid())));
        }
        return false;
    }

    /**
     * 优惠码费用分摊
     * @param youhuimaTotal
     * @param wxkcoutputs
     */
    public void youhuiMaAllocation(BigDecimal youhuimaTotal, List<Wxkcoutput> wxkcoutputs) {
        wxkcoutputs.sort(Comparator.comparing(Wxkcoutput::getPrice).reversed());
        int wxkSize = wxkcoutputs.size();
        BigDecimal wxkTotalPrice = wxkcoutputs.stream().map(Wxkcoutput::getPrice).filter(Objects::nonNull)
                .reduce((p1, p2) -> p1.add(p2)).filter(wxtp->wxtp.compareTo(BigDecimal.ZERO)>0)
                .orElseGet(()->new BigDecimal(wxkSize));
        BigDecimal wxkAllocationPrice = splitYouHuiMa(youhuimaTotal, wxkcoutputs, wxkTotalPrice,NumberConstant.ZERO);
        //整数不能分摊完,进行小数平均分摊
        if(youhuimaTotal.compareTo(wxkAllocationPrice)>0){
           wxkAllocationPrice.add(splitYouHuiMa(youhuimaTotal.subtract(wxkAllocationPrice), wxkcoutputs, wxkTotalPrice,NumberConstant.TWO));
        }
    }

    @Override
    public boolean existsOrdinaryCoupon(Integer shouhouId) {
        List<String> youHuiMaCodes = StrUtil.splitTrim(shouhouMapper.getYouHuiMaCode(shouhouId),SignConstant.SHU_XIAN);
        if(youHuiMaCodes.isEmpty()){
            return false;
        }
        return listNumberCardByCardId(youHuiMaCodes).stream().anyMatch(nc->!this.isDaijingJuanOrTousu(nc));
    }

    /**
     * 处理默认标签
     *
     * @param maxPpirceId
     * @return
     */
    @Override
    public Boolean handleDeFaultLabel(Integer maxPpirceId) {
        baseMapper.handleInsertDeFaultLabel(maxPpirceId);
        baseMapper.handleUpdateDeFaultLabel(maxPpirceId);
        return true;
    }

    /**
     * 价格必须从高到低排列
     * @param youhuimaTotal
     * @param wxkcoutputs
     * @param wxkTotalPrice
     * @param resultScale
     * @return
     */
    private BigDecimal splitYouHuiMa(BigDecimal youhuimaTotal, List<Wxkcoutput> wxkcoutputs, BigDecimal wxkTotalPrice,int resultScale) {
        BigDecimal wxkAllocationPrice = BigDecimal.ZERO;
        BigDecimal prevRemaining = BigDecimal.ZERO;
        for (int i = 0,size = wxkcoutputs.size(); i < size; i++) {
            Wxkcoutput wxkcoutput = wxkcoutputs.get(i);
            BigDecimal price = DecideUtil.isNull(wxkcoutput.getPrice(),BigDecimal.ZERO);
            //进行优惠码分摊
            BigDecimal calYouHuiMaPrice = price.divide(wxkTotalPrice, NumberConstant.FOUR, RoundingMode.UP).multiply(youhuimaTotal)
                    .setScale(resultScale, RoundingMode.UP).add(prevRemaining);
            //原有优惠费用
            BigDecimal originYouhuiFeiyong = DecideUtil.isNull(wxkcoutput.getYouhuifeiyong(), BigDecimal.ZERO);
            BigDecimal newYouhuiFeiyong = calYouHuiMaPrice.add(originYouhuiFeiyong);
            BigDecimal reYouHuiMaPrice = calYouHuiMaPrice;
            if(newYouhuiFeiyong.compareTo(price)>0) {
                //优惠费用不能大于商品价格
                newYouhuiFeiyong = price.setScale(resultScale,RoundingMode.DOWN);
                reYouHuiMaPrice = newYouhuiFeiyong.subtract(originYouhuiFeiyong);
            }
            if(wxkAllocationPrice.add(reYouHuiMaPrice).compareTo(youhuimaTotal)>0) {
                //优惠费用不能大于总优惠费用
                reYouHuiMaPrice = youhuimaTotal.subtract(wxkAllocationPrice);
                newYouhuiFeiyong = reYouHuiMaPrice.add(originYouhuiFeiyong);
            }
            wxkcoutput.setYouhuifeiyong(newYouhuiFeiyong);
            wxkAllocationPrice = wxkAllocationPrice.add(reYouHuiMaPrice);
            if(calYouHuiMaPrice.compareTo(reYouHuiMaPrice)>0){
                prevRemaining = calYouHuiMaPrice.subtract(reYouHuiMaPrice);
            }else{
                //清空结余费用
                prevRemaining = BigDecimal.ZERO;
            }
        }
        //补差额
        BigDecimal leftPrice = youhuimaTotal.subtract(wxkAllocationPrice);
        if(leftPrice.compareTo(BigDecimal.ZERO) != 0){
            for (Wxkcoutput wxkcoutput : wxkcoutputs) {
                BigDecimal price = DecideUtil.isNull(wxkcoutput.getPrice(),BigDecimal.ZERO);
                BigDecimal newYouhuiFeiyong = wxkcoutput.getYouhuifeiyong().add(leftPrice);
                if(price.compareTo(newYouhuiFeiyong)>=0){
                    wxkcoutput.setYouhuifeiyong(newYouhuiFeiyong);
                    wxkAllocationPrice = wxkAllocationPrice.add(leftPrice);
                    break;
                }
            }
        }
        return wxkAllocationPrice;
    }

    /**
     * 软件接件优惠码使用逻辑
     *
     * @return
     */
    private R<Boolean> useYouHuiMaWithMsoft(Shouhou sh, NumberCard cardInfo, String operator) {

//        if (StringUtils.isNotEmpty(sh.getYouhuima()) || (sh.getYouhuifeiyong() != null && sh.getYouhuifeiyong().compareTo(BigDecimal.ZERO) > 0)) {
//            return R.error("该优惠码不可以叠加使用");
//        }
//
//        //校验配件ppid
//        Integer count = wxkcoutputService.count(new LambdaQueryWrapper<Wxkcoutput>().gt(Wxkcoutput::getPpriceid, 0).eq(Wxkcoutput::getWxid, sh.getId()));
//        if (CommenUtil.isNotNullZero(count)) {
//            return R.error("维修配件不符合使用规则");
//        }

        //更新费用
        shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getYouhuima, cardInfo.getCardID())
                .set(Shouhou::getYouhuifeiyong, sh.getFeiyong()).set(Shouhou::getFeiyong, BigDecimal.ZERO)
                .eq(Shouhou::getId, sh.getId()));

        //修改优惠码使用
        numberCardService.updateNumberCardUseCount(cardInfo.getCardID());

        //添加优惠码使用日志
        CardLogs cardLogs = new CardLogs();
        cardLogs.setCardid(cardInfo.getId()).setSubId(sh.getId()).setPushTime(LocalDateTime.now())
                .setAreaid(sh.getAreaid()).setUseren(operator)
                .setUserid(cardInfo.getUserid()).setUseType(1).setPrices(sh.getFeiyong()).setLiangpin(3);

        cardLogsService.save(cardLogs);

        //添加售后进程
        String youhuimaCode = cardInfo.getCardID();
        String comment = "使用优惠码抵扣费用" + sh.getFeiyong() + "元！优惠码：<a target='_blank' href='/member/NumberCard?search_kinds=cardid&key=" + youhuimaCode + "'>" + youhuimaCode + " </a>";
        shouhouService.saveShouhouLog(sh.getId(), comment, operator, null, true);

        return R.success("操作成功");
    }

    @Override
    public R<Page<ChaoshiZengPinRes>> getZengpingPageList(ChaoshiZengpinListReq req) {
        if (req.getCurrent() == null) {
            req.setCurrent(1);
        }
        if (req.getSize() == null || req.getSize() == 0) {
            req.setSize(20);
        }

        Page<ChaoshiZengPinRes> page = new Page<>(req.getCurrent(), req.getSize());
        Page<ChaoshiZengPinRes> pageRes = baseMapper.getZengpingPageList(page, req);
        return R.success(pageRes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> zengSongPpid(Integer ppid, Integer shouhouId, BigDecimal csDays, Integer type, String remark) {

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        String pname = "";
        if (1 == type) {
            List<Productinfo> existList = productinfoService.list(new LambdaQueryWrapper<Productinfo>().eq(Productinfo::getPpriceid, ppid).like(Productinfo::getCidFamily, ",23,"));
            if (CollectionUtils.isNotEmpty(existList)) {
                return R.error("不可赠送维修配件");
            }
            BigDecimal inprice = BigDecimal.ZERO;
            // 九机
            if(XtenantEnum.isJiujiXtenant()){
                // 小件库存
                List<ProductKc> kcList = productKcService.list(new LambdaQueryWrapper<ProductKc>().select(ProductKc::getInprice)
                        .eq(ProductKc::getAreaid, oaUserBO.getAreaId()).eq(ppid != null, ProductKc::getPpriceid, ppid));
                if (CollectionUtils.isEmpty(kcList)) {
                    return R.error("赠送商品没有库存，请选择其他赠品");
                }
                inprice = kcList.get(0).getInprice() == null ? inprice : kcList.get(0).getInprice();

                if (inprice.compareTo(BigDecimal.valueOf(20)) >= 0) {
                    return R.error("赠配成本超出限制！只能赠送成本20(不包含)以内的商品");
                }
            }else {

            List<ProductMkc> mkcList = productMkcService.list(new LambdaQueryWrapper<ProductMkc>().select(ProductMkc::getInprice)
                    .eq(ProductMkc::getAreaid, oaUserBO.getAreaId()).eq(ppid != null, ProductMkc::getPpriceid, ppid));
            if (CollectionUtils.isNotEmpty(mkcList)) {
                inprice = mkcList.get(0).getInprice() == null ? inprice : mkcList.get(0).getInprice();
            }

            if (inprice.compareTo(BigDecimal.valueOf(30)) >= 0) {

                String rankMark = "赠配成本超出限制！员工赠配成本30元内,主管50元内,店长150元内";
                if (oaUserBO.getLevel() > 6 && inprice.compareTo(BigDecimal.valueOf(30)) > 0) {
                    return R.error(rankMark);
                } else if (oaUserBO.getLevel() == 6 && inprice.compareTo(BigDecimal.valueOf(50)) > 0) {
                    return R.error(rankMark);
                } else if (oaUserBO.getLevel() == 5 && inprice.compareTo(BigDecimal.valueOf(150)) > 0) {
                    return R.error(rankMark);
                }

            }
            }
        }

        R<AreaInfo> areainfoR = areaInfoClient.getAreaInfoById(oaUserBO.getAreaId());
        AreaInfo areaInfo = areainfoR.getData();
        Integer ztId = authConfigService.getZtIdByAuId(areaInfo.getAuthorizeId());

        //判断是否存在赠品，存在则不允许再赠送
        Integer count = shouhouChaoshizengpinService.listGift(shouhouId, type);

        if (CommenUtil.isNotNullZero(count)) {
            return R.error("维修单已存在赠品，不可重复赠送！");
        }
        String logMsg = "";
        if (0 == type) {
            //判断赠品价值是否满足赠送条件
            List<String> zengpinValueList = shouhouChaoshizengpinService.judgeZengpinValue(ppid, csDays);
            if (CollectionUtils.isEmpty(zengpinValueList)) {
                return R.error("赠品价值超过赠送金额配置，请选择其他赠品。");
            }
            pname = zengpinValueList.get(0);
            logMsg = "超时赔付，赠送配件：" + pname + ",PPID:" + ppid + "，原因：" + remark;

        }
        if (1 == type) {
            List<String> zengpinValueList = null;
            // 九机 售价200(包含)以内
            if (XtenantEnum.isJiujiXtenant()){
                zengpinValueList = shouhouChaoshizengpinService.judgeZengpinValue4Jiuji(ppid);
                if (CollectionUtils.isEmpty(zengpinValueList)) {
                    return R.error("只允许赠送售价200(包含)以内的商品，请选择其他赠品。");
                }
            }else {

            zengpinValueList = shouhouChaoshizengpinService.judgeZengpinValue(ppid, null);
            if (CollectionUtils.isEmpty(zengpinValueList)) {
                return R.error("赠品价值超过赠送金额配置，请选择其他赠品。");
            }
            }

            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(oaUserBO.getAreaId());
            String xTenantName = areaInfoR.getData().getPrintName();
            logMsg = String.format("【" + remark + "】，%s特送上贴心小礼品【" + zengpinValueList.get(0) + "】，希望您继续支持%s。", xTenantName, xTenantName);

            //配件出库
//            SmallproNormalCodeMessageRes codeMessageRes = smallproForwardExService.stockOperations(ppid, -1, BigDecimal.ZERO, oaUserBO.getAreaId(), oaUserBO.getUserName(), "", "维修单超时赔付送赠品", shouhouId, 1, 1, shouhouId, false, false);
            OperateProductKcPara para = new OperateProductKcPara();
            para.setPpid(ppid);
            para.setCount(-1);
            para.setInprice(BigDecimal.ZERO);
            para.setAreaId(oaUserBO.getAreaId());
            para.setInuser(oaUserBO.getUserName());
            para.setInsource("");
            para.setComment("维修单超时赔付送赠品");
            para.setBasketId(Long.valueOf(shouhouId));
            para.setCheck1(true);
            para.setCheck2(true);
            para.setShouhouId(Long.valueOf(shouhouId));
            para.setIsLp(false);
            para.setDiaoboFlag(false);

            R<OperateProductKcRes> productKcR = productKcService.operateProductKc(para);
            if (ResultCode.SUCCESS != productKcR.getCode()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("赠品出库失败【" + productKcR.getUserMsg() + "】");
            } else {
                //写入赠送记录
                ShouhouChaoshizengpin zengpinInfo = new ShouhouChaoshizengpin();
                BigDecimal inprice = productKcR.getData().getInprice();

                zengpinInfo.setPpriceid(ppid);
                zengpinInfo.setShouhouid(shouhouId);
                zengpinInfo.setInuser(oaUserBO.getUserName());
                zengpinInfo.setDtime(LocalDateTime.now());
                zengpinInfo.setZcount(1);
                zengpinInfo.setCostprice(inprice);
                zengpinInfo.setType(type);
                zengpinInfo.setAreaid(oaUserBO.getAreaId());
                shouhouChaoshizengpinService.save(zengpinInfo);

                //配件赠送进程 优化
                //只要员工选择配件赠送，客户可视进程推送修改为：“感谢您选择九机售后，九机为您送上贴心小礼品【乐物 iPhone 11/XR 全屏2.5D钢化膜 黑色 】，希望您继续支持九机网。”
                //只要员工选择配件赠送，内部进程推送修改为：“配件赠送【xxxx】,赠送原因【xxxx】”
                if (XtenantEnum.isJiujiXtenant()) {
                    logMsg = StrUtil.format("感谢您选择九机售后，九机为您送上贴心小礼品【{}】，希望您继续支持九机网。",zengpinValueList.get(0));
                    shouhouService.saveShouhouLog(shouhouId, logMsg, oaUserBO.getUserName(), null, true);
                    logMsg = StrUtil.format("配件赠送【{}】,赠送原因【{}】", zengpinValueList.get(0), remark);
                    shouhouService.saveShouhouLog(shouhouId, logMsg, oaUserBO.getUserName(), null, false);
                } else {
                //写维修单日志
                shouhouService.saveShouhouLog(shouhouId, logMsg, oaUserBO.getUserName(), null, true);

                //写维修单工程师备注
                shouhouService.saveShouhouLog(shouhouId, logMsg, oaUserBO.getUserName(), null, false);
                }

                //自营的才进我们自己的账套

                if (1 == oaUserBO.getAreaKind1() && oaUserBO.getXTenant().equals(0)) {
                    //写凭证
                    String summary = "维修单号" + shouhouId + "送赠品|维修单号" + shouhouId + "送赠品";
                    String keMu = "671109|140502";
                    String jief = inprice + "|0";
                    String daif = "0|" + inprice;
                    String fzhs = areaInfoR.getData().getArea() + "|无";
                    PingzhengResultBO voucherResult =
                            voucherService.addPingZheng(voucherService.buildPingzheng(summary, keMu,
                                    jief, daif, fzhs));
                } else {
                    if (CommenUtil.isNotNullZero(ztId)) {
                        KemuFzhsItem k671109 = keMuService.getKemuFzhsInfoByEKemu(EKemuEnum.k671109.getCode(), ztId, oaUserBO.getAreaId());
                        KemuFzhsItem k140502 = keMuService.getKemuFzhsInfoByEKemu(EKemuEnum.k140502.getCode(), ztId, oaUserBO.getAreaId());
                        if (k140502 != null && k671109 != null) {
                            //写凭证
                            String summary = "维修单号" + shouhouId + "送赠品|维修单号" + shouhouId + "送赠品";
                            String keMu = k671109.getCode() + "|" + k140502.getCode();
                            String jief = inprice + "|0";
                            String daif = "0|" + inprice;
                            String fzhs = k671109.getFzhs() + "|" + k140502.getFzhs();
                            PingzhengResultBO voucherResult =
                                    voucherService.addPingZheng(voucherService.buildPingzheng(summary, keMu,
                                            jief, daif, fzhs));

                            NewVoucherBo voucher = new NewVoucherBo();
                            voucher.setAct("repairSendGift");
                            voucher.setActName("维修送赠品");
                            voucher.setAccountSetId(ztId.toString());
                            NewVoucherBo.VoucherArgs argsO = new NewVoucherBo.VoucherArgs();
                            argsO.setShouhouid(shouhouId);
                            argsO.setType(type);
                            voucher.setArgsO(argsO);
                            voucher.setSubId(zengpinInfo.getId().toString());
                            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                            voucher.setVoucherTime(LocalDateTime.now().format(dtf));
                            Shouhou shouhou = super.getById(shouhouId);
                            voucher.setAreaId(Optional.ofNullable(shouhou).map(Shouhou::getAreaid).filter(CommenUtil::isNotNullZero).orElse(oaUserBO.getAreaId()));
                            voucherService.addNewVoucher(voucher);
                        }
                    }
                }
            }
        }

        return R.success("操作成功", pname);
    }

    @Override
    public R<Boolean> zengSong(Integer ppid, Integer shouhouId, String zpName, BigDecimal memberprice,
                               Integer type, String remark) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }

        //判断是否存在赠品，存在则不允许再赠送
        List<ShouhouChaoshizengpin> zengpinList = shouhouChaoshizengpinService.list(new LambdaQueryWrapper<ShouhouChaoshizengpin>().eq(ShouhouChaoshizengpin::getShouhouid, shouhouId)
                .and(bo -> bo.eq(ShouhouChaoshizengpin::getType, 0)).or().isNull(ShouhouChaoshizengpin::getType));
        if (CollectionUtils.isNotEmpty(zengpinList)) {
            return R.error("维修单已存在赠品，不可重复赠送！");
        }

        R<AreaInfo> areainfoR = areaInfoClient.getAreaInfoById(oaUserBO.getAreaId());
        AreaInfo areaInfo = areainfoR.getData();
        Integer ztId = authConfigService.getZtIdByAuId(areaInfo.getAuthorizeId());


        List<String> zpNamesList = shouhouChaoshizengpinService.getZengpinName(ppid);
        String obj = "";
        if (CollectionUtils.isNotEmpty(zpNamesList)) {
            obj = zpNamesList.get(0);
        }
        String logMsg = "超时赔付，赠送配件：" + obj + ",PPID:" + ppid + "，赔付原因：" + remark;


        //配件出库
        OperateProductKcPara para = new OperateProductKcPara();
        para.setPpid(ppid);
        para.setCount(-1);
        para.setInprice(BigDecimal.ZERO);
        para.setAreaId(oaUserBO.getAreaId());
        para.setInuser(oaUserBO.getUserName());
        para.setInsource("");
        para.setComment("维修单超时赔付送赠品");
        para.setBasketId(Long.valueOf(shouhouId));
        para.setCheck1(true);
        para.setCheck2(true);
        para.setShouhouId(Long.valueOf(shouhouId));
        para.setIsLp(false);
        para.setDiaoboFlag(false);

        R<OperateProductKcRes> productKcR = productKcService.operateProductKc(para);
        if (ResultCode.SUCCESS != productKcR.getCode()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("操作库存失败【" + productKcR.getUserMsg() + "】");
        } else {
            //写入赠送记录
            ShouhouChaoshizengpin zengpinInfo = new ShouhouChaoshizengpin();
            BigDecimal inprice = productKcR.getData().getInprice();

            zengpinInfo.setPpriceid(ppid);
            zengpinInfo.setShouhouid(shouhouId);
            zengpinInfo.setInuser(oaUserBO.getUserName());
            zengpinInfo.setDtime(LocalDateTime.now());
            zengpinInfo.setZcount(1);
            zengpinInfo.setCostprice(inprice);
            zengpinInfo.setType(type);
            shouhouChaoshizengpinService.save(zengpinInfo);

            //写维修单日志
            shouhouService.saveShouhouLog(shouhouId, logMsg, oaUserBO.getUserName(), null, true);

            //写维修单工程师备注
            shouhouService.saveShouhouLog(shouhouId, logMsg, oaUserBO.getUserName(), null, false);

            //自营的才进我们自己的账套

            if (1 == oaUserBO.getAreaKind1() && oaUserBO.getXTenant().equals(0)) {
                //写凭证
                String summary = "维修单号" + shouhouId + "送赠品|维修单号" + shouhouId + "送赠品";
                String keMu = "671109|140502";
                String jief = inprice + "|0";
                String daif = "0|" + inprice;
                String fzhs = oaUserBO.getArea() + "|无";
                PingzhengResultBO voucherResult =
                        voucherService.addPingZheng(voucherService.buildPingzheng(summary, keMu,
                                jief, daif, fzhs));
            } else {
                if (CommenUtil.isNotNullZero(ztId)) {
                    KemuFzhsItem k671109 = keMuService.getKemuFzhsInfoByEKemu(EKemuEnum.k671109.getCode(), ztId, oaUserBO.getAreaId());
                    KemuFzhsItem k140502 = keMuService.getKemuFzhsInfoByEKemu(EKemuEnum.k140502.getCode(), ztId, oaUserBO.getAreaId());
                    if (k140502 != null && k671109 != null) {
                        //写凭证
                        String summary = "维修单号" + shouhouId + "送赠品|维修单号" + shouhouId + "送赠品";
                        String keMu = k671109.getCode() + "|" + k140502.getCode();
                        String jief = inprice + "|0";
                        String daif = "0|" + inprice;
                        String fzhs = k671109.getFzhs() + "|" + k140502.getFzhs();
                        PingzhengResultBO voucherResult =
                                voucherService.addPingZheng(voucherService.buildPingzheng(summary, keMu,
                                        jief, daif, fzhs));

                        NewVoucherBo voucher = new NewVoucherBo();
                        voucher.setAct("repairSendGift");
                        voucher.setActName("维修送赠品");
                        voucher.setAccountSetId(ztId.toString());
                        NewVoucherBo.VoucherArgs argsO = new NewVoucherBo.VoucherArgs();
                        argsO.setShouhouid(shouhouId);
                        argsO.setType(type);
                        voucher.setArgsO(argsO);
                        voucher.setSubId(zengpinInfo.getId().toString());
                        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        voucher.setVoucherTime(LocalDateTime.now().format(dtf));
                        Shouhou shouhou = super.getById(shouhouId);
                        voucher.setAreaId(Optional.ofNullable(shouhou).map(Shouhou::getAreaid).filter(CommenUtil::isNotNullZero).orElse(oaUserBO.getAreaId()));
                        voucherService.addNewVoucher(voucher);
                    }
                }
            }
        }

        return R.success("赠送成功。");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> delTuiEx(Integer shouhouId, Integer tuihuanKind) {
        if (TuihuanKindEnum.TK.getCode().equals(tuihuanKind) || TuihuanKindEnum.HQTXH.getCode().equals(tuihuanKind)) {

            ShouhouSubDetailBo subInfo = loadSubInfoByShouhouid(shouhouId);
            if (subInfo != null) {

                //判断原始订单是否有赠品
                List<Basket> basketList = basketService.list(new LambdaQueryWrapper<Basket>().eq(Basket::getSubId, subInfo.getSubId())
                        .eq(Basket::getGiftid, subInfo.getBasketId()).eq(Basket::getType, 1)
                        .and(bo -> bo.eq(Basket::getIsdel, 0).or().isNull(Basket::getIsdel))
                        .eq(Basket::getPrice, BigDecimal.ZERO));
                //提交赠品退货申请
                if (CollectionUtils.isNotEmpty(basketList)) {
                    Basket tuiDt = basketList.get(0);
                    //判断原始订单是否已提交并且未处理的
                    String comment = "大件退款，相应赠品退款操作，售后单：" + shouhouId;
                    Integer smallProId = smallproService.getSmallProIdBySubId(subInfo.getSubId(), comment);
                    if (smallProId != null) {
                        //删除小件单
                        smallproService.removeById(smallProId);
                        //删除退货商品信息
                        Boolean flag = smallproBillService.remove(new LambdaQueryWrapper<SmallproBill>().eq(SmallproBill::getSmallproID, smallProId));
                        if (flag) {
                            //删除退赠品记录
                            shouhouTuihuanService.remove(new LambdaQueryWrapper<ShouhouTuihuan>().eq(ShouhouTuihuan::getSubId, subInfo.getSubId())
                                    .eq(ShouhouTuihuan::getTuihuanKind, TuihuanKindEnum.TPJ.getCode()).and(bo -> bo.eq(ShouhouTuihuan::getIsdel, 0).or().isNull(ShouhouTuihuan::getIsdel))
                                    .isNotNull(ShouhouTuihuan::getCheck3).eq(ShouhouTuihuan::getComment, comment).eq(ShouhouTuihuan::getIszengpin, true));
                        }

                    }

                }

            }

        }

        return R.success("操作成功");
    }

    @Override
    public R<List<Insource>> insourceList() {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        String cacheKey = RedisKeys.insource + "_" + oaUserBO.getAuthorizeId();
        if (redisTemplate.hasKey(cacheKey)) {
            String json = redisTemplate.opsForValue().get(cacheKey).toString();
            return R.success(JSON.parseArray(json, Insource.class));
        }
        List<Insource> insourceList = insourceService.list(new LambdaQueryWrapper<Insource>().eq(Insource::getAuthorizeid, oaUserBO.getAuthorizeId()).orderByAsc(Insource::getRank));
        if (CollectionUtils.isEmpty(insourceList)) {
            return R.success("查无相关记录", insourceList);
        }
        redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(insourceList), NumberConstant.ONE, TimeUnit.DAYS);
        return R.success(insourceList);
    }

    @Override
    public R<Boolean> quxiaoQudao(Integer shouhouId) {
        //取消 判断当前是否有优惠码
        Boolean flag = shouhouQudaoService.remove(new LambdaQueryWrapper<ShouhouQudao>().eq(ShouhouQudao::getShouhouid, shouhouId));
        return flag ? R.success("操作成功") : R.error("取消失败");
    }

    @Override
    public R<ShouhouTestInfoRes> getShouhouTestInfo(Integer shouhouId, Integer testId, Boolean showInfo) {
        ShouhouTestInfoRes res = new ShouhouTestInfoRes();

        showInfo = showInfo == null ? false : showInfo;
        if (showInfo) {
            ShouhoutestInfo testInfo = null;
            if (CommenUtil.isNotNullZero(testId)) {
                testInfo = shouhoutestInfoService.getById(testId);
            } else {
                testInfo = this.getLastTestInfoByShId(shouhouId);
            }
            if (testInfo != null) {
                res.setTestInfo(testInfo);
                if (testInfo.getTesttype() != null && testInfo.getTesttype() == 1 && testInfo.getTestParms().contains("name")) {
                    List<WeixiuTestOptionReq.WeixiuTestOptionValueItem> testOptions = JSON.parseArray(testInfo.getTestParms(), WeixiuTestOptionReq.WeixiuTestOptionValueItem.class);
                    List<WeixiuTestOptionBo> weixiuTestOption = shouhoutestInfoService.getWeixiuTestOptions();

                    weixiuTestOption = weixiuTestOption.stream().map(opt -> {
                        Optional<WeixiuTestOptionReq.WeixiuTestOptionValueItem> optional = testOptions.stream().filter(e -> e.getName().equals(opt.getName())).findFirst();
                        String selectedValue = optional.isPresent() ? optional.get().getName() : "";
                        opt.setSelectedValue(selectedValue);
                        return opt;
                    }).collect(Collectors.toList());

                    res.setWeixiuTestOption(weixiuTestOption);
                } else {
                    if (StringUtils.isNotEmpty(testInfo.getTestParms())) {

                        ShouhouTestParamsVo testParams = JSON.parseObject(testInfo.getTestParms(), ShouhouTestParamsVo.class);

                        res.setTestParams(testParams);
                    }
                }
            }
        }

        return R.success(res);
    }

    @Override
    public R<Boolean> pingJia(Integer id, String pingjia, Integer pjKind) {

        return baseMapper.pingJia(id, pingjia, pjKind) > 0 ? R.success("评价成功") : R.error("操作失败");
    }

    @Override
    public R<Boolean> quJi(Integer shouhouId, String remark) {

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();

        Shouhou shouhou = super.getById(shouhouId);

        if (shouhou == null || (shouhou.getIsquji() != null && shouhou.getIsquji())) {
            return R.error("取机编号错误或用户已取机！");
        }

        Integer serviceType = shouhou.getServiceType();
        shouhou.setServiceType(serviceType);
        Integer nowAreaId = shouhou.getToareaid() == null ? shouhou.getAreaid() : shouhou.getToareaid();
        Boolean isSoft = shouhou.getIssoft() == null ? false : shouhou.getIssoft();
        Integer ishuishou = shouhou.getIshuishou() == null ? 0 : shouhou.getIshuishou();
        Integer huiPrint = shouhou.getHuiprint() == null ? 0 : shouhou.getHuiprint();
        Boolean isQuji = shouhou.getIsquji() == null ? false : shouhou.getIsquji();
        Integer isBakData = shouhou.getIsBakData() == null ? 0 : shouhou.getIsBakData();
        BigDecimal feiyong = shouhou.getFeiyong() == null ? BigDecimal.ZERO : shouhou.getFeiyong();
        BigDecimal costprice = shouhou.getCostprice() == null ? BigDecimal.ZERO : shouhou.getCostprice();
        BigDecimal yifum = shouhou.getYifum() == null ? BigDecimal.ZERO : shouhou.getYifum();
        Integer shouyinglock = shouhou.getShouyinglock() == null ? 0 : shouhou.getShouyinglock();

        Boolean flag = false;
        String msgStr = "";

        if (isBakData > 0 && !Arrays.asList(3, 4).contains(isBakData)) {
            msgStr = "请确保为用户备份并恢复了数据，再取机！";
            flag = true;
        }
        //取机验证码
        if (CommenUtil.isNotNullZero(serviceType) && nowAreaId != 298) {
            if (StringUtils.isNotEmpty(shouhou.getCodeMsg())) {
                R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(oaUserBO.getAreaId());
                if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                    msgStr = "涉及" + (areaInfoR.getData().getXtenant().equals(JiujiTenantEnum.JIUJI_TENANT_JIUJI.getCode().intValue()) ? "九机" : areaInfoR.getData().getPrintName()) + "服务维修，需要先保存验证码，再取机！";
                    flag = true;
                }
            }
        }
        //判断是否存在锁定库存
        Integer count = wxkcoutputService.count(new LambdaQueryWrapper<Wxkcoutput>().eq(Wxkcoutput::getIslockkc, true).isNotNull(Wxkcoutput::getPpriceid).ne(Wxkcoutput::getPpriceid, 0).eq(Wxkcoutput::getWxid, shouhouId));
        if (count > 0) {
            msgStr = "存在锁定维修配件，请先撤销或出库，再取机！";
            flag = true;
        }
        if (CommenUtil.isNotNullZero(shouhou.getDyjid())) {
            msgStr = "请先收回代用机，再进行取机操作！";
            flag = true;
        } else if (feiyong.compareTo(BigDecimal.ZERO) > 0 && (shouyinglock.equals(0) || yifum.compareTo(feiyong) < 0)) {
            msgStr = "请先收银，才可进行取机操作，若无收费请改费用为0！";
            flag = true;
        } else if (shouhou.getStats() != null && (shouhou.getStats().equals(0) || shouhou.getStats().equals(4))) {
            msgStr = "必须是在已修好或修不好状态下，才可取机！";
            flag = true;
        } else if (shouhou.getBaoxiu() != null && shouhou.getBaoxiu().equals(BaoxiuStatusEnum.DJC.getCode())) {
            msgStr = "待检测状态不可取机！";
            flag = true;
        } else if (StringUtils.isNotEmpty(shouhou.getPandianinuser()) && shouhou.getPandianinuser().trim().contains("已核对")) {
            msgStr = "盘点人为“已核对”不可取机！";
            flag = true;
        } else if (shouhou.getUserid() != null && shouhou.getUserid().equals(76783L) && oaUserBO.getRank().contains("0c5") && !isSoft) {
            msgStr = "系统判断错误！";
            flag = true;
        } else if (shouhou.getUserid() != null && !shouhou.getUserid().equals(76783L) && oaUserBO.getRank().contains("1c7") && !isSoft) {
            msgStr = "系统判断错误！";
            flag = true;
        }

        if (!flag) {
            Boolean isOk = shouHouPjService.checkQuji(shouhouId);
            if (isOk) {
                msgStr = "退换机流程还未完成，不可取机操作！";
                flag = true;
            }
            isOk = shouHouPjService.checkQujiToArea(shouhouId);
            if (isOk) {
                msgStr = "转地区操作还未完成，不可取机操作！";
                flag = true;
            }
        }
        if (!flag && shouhou.getWxkind() != 6) {
            Boolean isOk = shouHouPjService.hasGcLog(shouhouId, isSoft ? 2 : 1);
            if (!isOk) {
                msgStr = "工程师未添加备注，不可取机！";
                if (isSoft) {
                    msgStr = "未添加软件安装处理备注，不可取机！";
                }
                flag = true;
            }
        }

        //如果是进水保【换机】，非苹果机，业务【办理完】才可以取机
        if (serviceType.equals(6) && shouhou.getWxkind() != null && shouhou.getWxkind().equals(2)) {
            R<JinshuibaoInfoRes> jinshuibaoInfoResR = this.getJinshuibaoInfo(shouhouId, null);
            if (jinshuibaoInfoResR.getCode() == ResultCode.SUCCESS && jinshuibaoInfoResR.getData() != null) {
                return R.error("进水保换机业务未办理完成，不可取机");
            }
        }

        //维修配件必须收回才可以取机
        //维修单所出维修配件属于“主板、屏幕总成、内置电池、摄像头、显示屏、手动添加成本”类别未进行“回购/返回/换货”操作的无法进行取机操作
        Integer wxpjCheckCount = shouhouQujishenheService.qujiWxpjCheck(shouhouId);
        if (!flag && wxpjCheckCount > 0) {
            msgStr = "维修单所出维修配件属于“主板、屏幕总成、内置电池、摄像头、显示屏、手动添加成本”类别未进行“回购/返回/换货”操作的无法进行取机操作";
            flag = true;
        }
        if (serviceType > 0 && (shouhou.getCostprice() == null || shouhou.getCostprice().compareTo(BigDecimal.ZERO) == 0)) {
            msgStr = "九机服务出险必须含有维修成本，请核实配件是否出库或有无维修成本添加。";
            flag = true;
        }

        Integer wxKind = shouhou.getWxkind() == null ? 0 : shouhou.getWxkind();
        //判断维修费用,置换机不做判断
        if (wxKind != 5 && !shouhouQujishenheService.checkPjQuji(shouhouId) && !shouldBypassCostCheck(shouhouId)) {
            msgStr = "非存在【九机服务，在保，返修，配件换货，特殊质保，赔付】等情况，维修配件费用不可低于成本";
            flag = true;
        }

        if (shouhou.getUserid() != null && shouhou.getUserid().equals(76783L)) {
            if (!(ishuishou.equals(1) || ishuishou.equals(2)) && shouhouQujishenheService.isCheckMkcQuji(ishuishou, shouhou.getMkcId() == null ? 0 : shouhou.getMkcId())) {
                msgStr = "请先转出售后在取机操作！";
                flag = true;
            }
        }

        if (flag) {
            return R.error(msgStr);
        } else {
            //成本去掉回购价
            List<ShouhouHuishou> huishouList = shouhouHuishouService.getHuishouListBy(shouhouId);
            if (CollectionUtils.isNotEmpty(huishouList)) {
                for (ShouhouHuishou hs : huishouList) {
                    if (hs.getPrice() != null && hs.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                        costprice = costprice.subtract(hs.getPrice());
                    }
                }
            }

            //去掉参与核销的价格
            List<HexiaoBo> hexiaoBoList = wxkcoutputService.getHexiao(shouhouId);
            if (CollectionUtils.isNotEmpty(hexiaoBoList)) {
                for (HexiaoBo h : hexiaoBoList) {
                    if (h.getIshexiao() != null && h.getInprice() != null) {
                        costprice = costprice.subtract(h.getInprice());
                    }
                }
            }

            Boolean checkFlag = true;
            BigDecimal tempPrice = feiyong.subtract(costprice);

            //判断是否需要审核
            if (serviceType > 1 && tempPrice.compareTo(BigDecimal.ZERO) < 0) {
                checkFlag = shouhouQujishenheService.checkResult(1, shouhouId, oaUserBO.getUserName());
            } else if (tempPrice.compareTo(BigDecimal.ZERO) < 0) {
                //非九机服务
                double kuisun = Math.abs(tempPrice.doubleValue());//亏损额度绝对值
                if (kuisun > 0 && kuisun <= 100) {
                    checkFlag = shouhouQujishenheService.checkResult(1, shouhouId, oaUserBO.getUserName());
                } else if (kuisun > 100 && kuisun <= 500) {
                    checkFlag = shouhouQujishenheService.checkResult(2, shouhouId, oaUserBO.getUserName());
                } else if (kuisun > 500 && kuisun <= 1000) {
                    checkFlag = shouhouQujishenheService.checkResult(3, shouhouId, oaUserBO.getUserName());
                } else if (kuisun > 1000) {
                    checkFlag = shouhouQujishenheService.checkResult(4, shouhouId, oaUserBO.getUserName());
                }

            }

            //取机审核后置，可以随时取机
            // todo DAL.Quji(r, mLogInfo.UserID, mLogInfo.UserName)


        }


        return null;
    }

    @Override
    @Transactional
    public R<String> shouhouTuihuan(ShouhouTuihuanReq tuihuan) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (CommenUtil.isCheckTrue(tuihuan.getIsValidt()) && StringUtils.isNotEmpty(tuihuan.getKeyCode()) &&
                !"微信秒退".equals(tuihuan.getTuiWay())) {
            Integer sub_id = tuihuan.getSubId();
            if (tuihuan.getTuihuanKind() != null && (tuihuan.getTuihuanKind() == 3 || tuihuan.getTuihuanKind() == 4)) {
                sub_id = tuihuan.getShouhouId();
            }
            R<Boolean> validtResult = shouhouTuihuanService.validtReturnCode(sub_id,
                    String.valueOf(tuihuan.getTuihuanKind()),
                    tuihuan.getCodeType(), tuihuan.getKeyCode());
            if (ResultCode.SUCCESS != validtResult.getCode()) {
                return R.error(validtResult.getUserMsg());
            }
        }
        if (tuihuan.getTuiWay() == null) {
            tuihuan.setTuiWay("");
        }
        if (tuihuan.getComment() == null) {
            tuihuan.setComment("");
        }
        tuihuan.setInprice(tuihuan.getTuikuanM());
        if (tuihuan.getBaitiaoPrice() != null && tuihuan.getBaitiaoPrice().compareTo(BigDecimal.ZERO) > 0 && tuihuan.getTuihuanKind() != 3 && tuihuan.getTuihuanKind() != 1) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(oaUserBO.getAreaId());
            AreaInfo areaInfo = null;
            if (ResultCode.SUCCESS != areaInfoR.getCode() || areaInfoR.getData() == null) {
                return R.error("获取区域信息出错");
            }
            areaInfo = areaInfoR.getData();
            String subjectName = "九机网".equals(areaInfo.getPrintName()) ? "九机" : areaInfo.getPrintName();
            return R.error("订单使用" + subjectName + "白条分期支付，只能退款、换机头，请核对！");
        }
        QueryWrapper<ShouhouTuihuan> qw = new QueryWrapper<>();
        qw.lambda().in(ShouhouTuihuan::getTuihuanKind, Arrays.asList(1, 2, 3, 4)).eq(ShouhouTuihuan::getIsdel, 0).eq(ShouhouTuihuan::getShouhouId, tuihuan.getShouhouId());
        ShouhouTuihuan shouhouTuihuan = shouhouTuihuanService.listSqlServer(qw).stream().findFirst().orElse(null);
        if (shouhouTuihuan != null) {
            return R.error("已经存在退换机记录!");
        }
        //获取售后信息
        Shouhou info = CommenUtil.autoQueryHist(() -> baseMapper.listSqlServer(new LambdaQueryWrapper<Shouhou>().eq(Shouhou::getId,
                tuihuan.getShouhouId()).ne(Shouhou::getUserid, 76783)).stream().findFirst().orElse(null), MTableInfoEnum.SHOUHOU, tuihuan.getShouhouId());
        Integer ck_area = 0;
        Integer area_ = 0;
        Integer userid = 0;
        Integer buyarea = 0;
        if (info != null) {
            ck_area = info.getToareaid() == null ? info.getAreaid() : info.getToareaid();
            area_ = info.getToareaid() != null || info.getToareaid() == 0 ? info.getAreaid() : info.getToareaid();
            userid = info.getUserid().intValue();
            buyarea = info.getBuyareaid();
        }
        Sub sub =
                subService.listSqlServer(new LambdaQueryWrapper<Sub>().eq(info.getSubId() != null, Sub::getSubId, info.getSubId()).select(Sub::getUserId)).stream().findFirst().orElse(null);
        Integer subUserId = null;
        if (sub != null) {
            subUserId = sub.getUserId().intValue();
        }
        Integer basket_id = null;
        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(buyarea);
        Boolean isjoin = ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null && !Arrays.asList(1
                , 3).contains(areaInfoR.getData().getKind1()) && areaInfoR.getData().getUserId() != null;
        R<AreaInfo> tuiAreaR = areaInfoClient.getAreaInfoById(area_);
        if (Arrays.asList(3, 4).contains(tuihuan.getTuihuanKind()) && isjoin && !buyarea.equals(area_) && (info.getIshuishou() == null || info.getIshuishou() == 0)) {
            return R.error("加盟店购机退款或换其它型号操作只可在购买地区进行!");
        }

        //二手良品限制只能在自营店进行退款或换其他
        if (info.getIshuishou() != null && info.getIshuishou() != 0 && Arrays.asList(3, 4).contains(tuihuan.getTuihuanKind())) {
            areaInfoR = areaInfoClient.getAreaInfoById(area_);
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                AreaInfo a = areaInfoR.getData();
                //J区不做这个限制
                if (a.getKind1() != 1 && a.getDepartId() != 487) {

                    return R.error("良品退款需发回总部检测并由总部进行退款操作，请做好客户解释工作，如有特殊情况及时联系良品组负责人为客户处理问题!");
                }
            }
        }
        if (!area_.equals(oaUserBO.getAreaId()) && Arrays.asList(1, 3, 4).contains(tuihuan.getTuihuanKind())) {
            areaInfoR = areaInfoClient.getAreaInfoById(area_);
            if (areaInfoR.getCode() != ResultCode.SUCCESS || areaInfoR.getData() != null) {
                return R.error("地区错误，请进行地区切换");
            }
            return R.error("地区错误，请切换至" + areaInfoR.getData().getArea());
        }
        Integer ishuishou = info.getIshuishou() == null ? 0 : info.getIshuishou();
        HuanMkcBo mkc = basketService.huanMKC(subUserId, info.getMkcId(), info.getImei(), ishuishou);
        if (mkc != null) {
            basket_id = mkc.getBasketId();
            tuihuan.setBuypriceM(mkc.getBuyPrice() == null ? BigDecimal.ZERO : mkc.getBuyPrice());
            tuihuan.setInprice(mkc.getInBeihuoPrice() == null ? BigDecimal.ZERO : mkc.getInBeihuoPrice());
        }

        if (3 == tuihuan.getTuihuanKind() || 4 == tuihuan.getTuihuanKind()) {
            //如果原始订单有赠品金额 需要把赠品成本加过来
            if (ishuishou == 0 && mkc != null) {
                BigDecimal otherPrice = basketService.getSubGiftPrice(mkc.getSubId(), mkc.getBasketId(), null);
                BigDecimal djqPrice = subService.getSubDJQPrice(mkc.getSubId());
                tuihuan.setBuypriceM(tuihuan.getBuypriceM().add(otherPrice).subtract(djqPrice));
            }
            if (tuihuan.getBuypriceM().compareTo(BigDecimal.ZERO) != 0 && (tuihuan.getTuikuanM().subtract(tuihuan.getPeizhiPrice()).subtract(tuihuan.getPiaoPrice())).compareTo(tuihuan.getBuypriceM()) > 0) {
                return R.error("退款金额大于全款!" + tuihuan.getBuypriceM());
            }
        }

        if (1 == tuihuan.getTuihuanKind() || 2 == tuihuan.getTuihuanKind()) {
            if (tuihuan.getBasketId() != 0) {
                HuanMkcSecondBo mkc2 = basketService.huanMKC2(oaUserBO.getAreaId(), tuihuan.getBasketId().intValue());
                if (mkc2 == null) {
                    return R.error("mkc_id错误,请检查是否为售后、售后2状态!" + oaUserBO.getArea());
                }
            }
        } else if (3 == tuihuan.getTuihuanKind() && StringUtils.isEmpty(tuihuan.getTuiWay())) {
            return R.error("退款方式不能为空!");
        } else if (3 == tuihuan.getTuihuanKind() && "微信秒退".equals(tuihuan.getTuiWay()) && oaUserBO.getAuthorizeId() != 1) {
            return R.error("只有自营门店支持微信秒退!");
        } else if ("余额".equals(tuihuan.getTuiWay()) && oaUserBO.getAuthorizeId() != 1) {
            return R.error("加盟不支持余额退款!");
        } else if (4 == tuihuan.getTuihuanKind()) {
            if (tuihuan.getSubId() != 0 && (tuihuan.getTuikuanM().compareTo(BigDecimal.ZERO) > 0 || 1 == tuihuan.getPuhui())) {

            } else {
                return R.error("所换订单号不能为空！");
            }
            if (tuihuan.getTuikuanM1() == null) {
                tuihuan.setTuikuanM1(BigDecimal.ZERO);
            }
            if (tuihuan.getTuikuanM().compareTo(tuihuan.getSubIdm()) < 0) {
                tuihuan.setSubIdm(tuihuan.getTuikuanM());
            }
            if (tuihuan.getTuikuanM1().compareTo(BigDecimal.ZERO) != 0 && tuihuan.getSubIdm().compareTo(tuihuan.getSubIdm().add(tuihuan.getTuikuanM1()).add(tuihuan.getPeizhiPrice())) != 0) {
                return R.error("金额错误！");
            }
        }

        R<String> beginTuihuanR = beginTuihuan(tuihuan, userid, basket_id);

        return beginTuihuanR;
    }

    @Override
    public R<String> submitTuiEx(Integer shouhouId, String inuser, Integer tuihuanKind, Integer areaId) {

        if (3 == tuihuanKind || 4 == tuihuanKind) {
            ShouhouSubDetailBo subInfo = loadSubInfoByShouhouid(shouhouId);
            if (subInfo != null) {

                //判断原始订单是否有赠品
                List<ShouhouTuiDtInfo> tuiDtList = basketService.getSubZenpingByGiftId(subInfo.getBasketId());

                Map<Integer, List<ShouhouTuiDtInfo>> groups = tuiDtList.stream().collect(Collectors.groupingBy(ShouhouTuiDtInfo::getSubId));

                for (Map.Entry<Integer, List<ShouhouTuiDtInfo>> entry : groups.entrySet()) {
                    List<Integer> basketIds = entry.getValue().stream().map(e -> e.getBasketId()).collect(Collectors.toList());
                    List<Smallpro> obj = smallproService.checkOrginSubCommitAndUnDeal(basketIds);
                    if (CollectionUtils.isEmpty(obj)) {
                        return R.error("订单[" + entry.getKey() + "]所关联赠品已提交退货申请，请先取消！");
                    }
                    String pNames = entry.getValue().stream().map(ShouhouTuiDtInfo::getProductName).distinct().collect(Collectors.joining(SignConstant.BLANK));
                    //生成小件接件单 smallpro
                    Smallpro smallpro = new Smallpro();
                    smallpro.setUserId(subInfo.getUserId());
                    smallpro.setSubId(entry.getKey());
                    //减去省略号所占用的字符数
                    smallpro.setName(StrUtil.maxLength(pNames,NumberConstant.FIVE_HUNDRED-NumberConstant.THREE));
                    smallpro.setAreaId(areaId);
                    smallpro.setGroupId(1);
                    smallpro.setIsBaoxiu(true);
                    smallpro.setUserName(subInfo.getSubTo());
                    smallpro.setKind(3);
                    smallpro.setProblem("大件退款，相应赠品自动退款操作，售后单:" + shouhouId);
                    smallpro.setBuyDate(entry.getValue().get(0).getTradeDate());
                    smallpro.setInUser("系统");
                    smallpro.setInDate(LocalDateTime.now());
                    smallpro.setStats(0);
                    smallpro.setCodeMsg("系统授权");

                    smallproService.save(smallpro);
                    Integer newId = smallpro.getId();

                    if (newId != null) {
                        Boolean flag = false;
                        for (ShouhouTuiDtInfo e : entry.getValue()) {
                            SmallproBill bill = new SmallproBill();
                            bill.setSmallproID(newId);
                            bill.setBasketId(e.getBasketId());
                            bill.setPpriceid(e.getPpid());
                            bill.setCount(e.getBasketCount());
                            smallproBillService.save(bill);
                        }

                        ShouhouTuihuan tuihuan = new ShouhouTuihuan();
                        tuihuan.setShouhouId(shouhouId);
                        tuihuan.setTuihuanKind(7);
                        tuihuan.setTuikuanM(BigDecimal.ZERO);
                        tuihuan.setTuikuanM1(BigDecimal.ZERO);
                        tuihuan.setSubId(subInfo.getSubId());
                        tuihuan.setTuiWay("余额");
                        tuihuan.setComment("大件退款，相应赠品退款操作，售后单：" + shouhouId);
                        tuihuan.setDtime(LocalDateTime.now());
                        tuihuan.setInuser(inuser);
                        tuihuan.setIsdel(false);
                        tuihuan.setZhejiaM(BigDecimal.ZERO);
                        tuihuan.setInprice(BigDecimal.ZERO);
                        tuihuan.setAreaid(subInfo.getAreaId());
                        tuihuan.setSmallproid(newId);
                        tuihuan.setCheck2(true);
                        tuihuan.setCheck2dtime(LocalDateTime.now());
                        tuihuan.setCheck2user("系统");
                        tuihuan.setIszengpin(true);

                        flag = shouhouTuihuanService.save(tuihuan);

                        if (flag) {
                            for (ShouhouTuiDtInfo e : entry.getValue()) {
                                ReturnsDetail returnsDetail = new ReturnsDetail();
                                returnsDetail.setShthid(Long.valueOf(tuihuan.getId()));
                                returnsDetail.setBasketID(Long.valueOf(e.getBasketId()));
                                returnsDetail.setBasketCount(e.getBasketCount());
                                returnsDetailService.save(returnsDetail);
                            }

                        }

                    } else {
                        return R.success("0");
                    }

                }
            }
        }
        return R.success("1");
    }

    @Override
    public ShouhouSubDetailBo loadSubInfoByShouhouid(Integer shouhouId) {
        return baseMapper.loadSubInfoByShouhouid(shouhouId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> beginTuihuan(ShouhouTuihuanReq tuihuan, Integer userid, Integer basketId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }
        String myUser = oaUserBO.getUserName();
        Integer myAreaid = oaUserBO.getAreaId();
        Boolean stats = false;
        //退款 支付宝优惠码效验处理
        if (3 == tuihuan.getTuihuanKind()) {
            //支付宝优惠码的必须原路径退款
            String ppids = redisTemplate.opsForValue().get("alipayyouhuippriceids").toString();
            List<Integer> aliPayYouHuiPpriceids = Arrays.asList(ppids.split(",")).stream().map(e -> Integer.valueOf(e)).collect(Collectors.toList());
            Integer alipaySubId = baseMapper.queryAlipayYouhuiSubId(tuihuan.getShouhouId(), aliPayYouHuiPpriceids);
            if (alipaySubId != null && !"支付宝(pay1)返回".equals(tuihuan.getTuiWay())) {
                return R.error("此订单只能以【支付宝(pay1)返回】退款！");
            }
        }
        Integer isHuishou = 0;
        Integer subId = 0;
        //Shouhou shDt = shouhouService.getById(tuihuan.getShouhouId());
        Shouhou shDt = CommenUtil.autoQueryHist(() ->shouhouService.getById(tuihuan.getShouhouId()), MTableInfoEnum.SHOUHOU,tuihuan.getShouhouId());
        if (shDt != null) {
            isHuishou = shDt.getIshuishou();
            subId = shDt.getSubId();
        }

        List<NetPayModelBo> netList = new LinkedList<>();
        if (3 == tuihuan.getTuihuanKind() || 4 == tuihuan.getTuihuanKind()) {
            String tuiWayStr = "支付宝返回,微信返回,微信(电子)返回,微信(电子)APP返回,兴业银行扫码返回,ApplePay返回,支付宝（兴业）返回,浦发扫码返回,支付宝(pay1)返回,支付宝(dzpay)返回,浦发扫码(92653)返回,浦发扫码(92427)返回,兴业扫码(叁玖)返回,中信扫码(06306)返回,中信扫码(06305)返回,浦发扫码(93155)返回,兴业扫码(24833)返回,微信(yy)返回,微信APP(yy)返回,支付宝(yy)返回,浦发扫码(93057)返回,中信扫码(74026)返回,中信扫码(25762)返回,建行分期返回,平安扫码(39878)返回,首信易扫码支付返回";

            Boolean isPayOnline = Arrays.asList(tuiWayStr.split(",")).contains(tuihuan.getTuiWay());
            if (isPayOnline && StringUtils.isNotEmpty(tuihuan.getNetPay())) {
                netList = JSONArray.parseArray(tuihuan.getNetPay(), NetPayModelBo.class);

                if (CollectionUtils.isNotEmpty(netList)) {
                    Integer i = 0;
                    for (NetPayModelBo m : netList) {
                        if (i == 0) {
                            if ((tuihuan.getPeizhiPrice().add(tuihuan.getPiaoPrice())).compareTo(BigDecimal.ZERO) > 0) {
                                m.setPrice(m.getPrice().subtract(tuihuan.getPeizhiPrice().add(tuihuan.getPiaoPrice())));
                            }
                        }

                        NetpayRecord netDt = netpayRecordService.getOne(new LambdaQueryWrapper<NetpayRecord>().eq(NetpayRecord::getType, isHuishou == 0 ? 1 : 3).eq(NetpayRecord::getId, m.getId()));
                        if (netDt != null) {
                            if (m.getPrice().compareTo(netDt.getMoney().subtract(netDt.getRefundPrice())) > 0) {
                                return R.error("交易号[" + netDt.getTradeNo() + "]退款金额不能大于" + (netDt.getMoney().subtract(netDt.getRefundPrice())) + "！");
                            }
                        } else {
                            return R.error("交易号查找失败！");
                        }
                        i++;
                    }
                    BigDecimal netPriceSum = netList.stream().map(e -> e.getPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    tuihuan.setTuikuanM(netPriceSum.add(tuihuan.getPeizhiPrice().add(tuihuan.getPiaoPrice())));
                }
            }
        }
        //库分期金额自动提交退订
        if (tuihuan.getKuBaiTiaoM() != null && tuihuan.getKuBaiTiaoM().compareTo(BigDecimal.ZERO) > 0) {
            List<NetPayModelBo> kuDt = netpayRecordService.getNetPayInfo(subId, isHuishou == 0 ? 1 : 3);
            if (CollectionUtils.isNotEmpty(kuDt)) {
                BigDecimal realKuBaitiaoPrice = BigDecimal.ZERO;
                BigDecimal tmpPrice = tuihuan.getKuBaiTiaoM();
                for (NetPayModelBo m : kuDt) {
                    if (tmpPrice.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal leftPrice = m.getLeftPrice();
                        if (tmpPrice.compareTo(leftPrice) > 0) {
                            realKuBaitiaoPrice = realKuBaitiaoPrice.add(leftPrice);
                            m.setPrice(m.getLeftPrice());
                            netList.add(m);
                            tmpPrice = tmpPrice.subtract(leftPrice);
                        } else {
                            realKuBaitiaoPrice = realKuBaitiaoPrice.add(tmpPrice);
                            m.setPrice(tmpPrice);
                            netList.add(m);
                            tmpPrice = BigDecimal.ZERO;
                        }
                    }
                }
                tuihuan.setKuBaiTiaoM(realKuBaitiaoPrice);
            } else {
                return R.error("未找到库分期支付记录！");
            }
        }

        if (1 == tuihuan.getTuihuanKind() || 2 == tuihuan.getTuihuanKind()) {
            ShouhouTuihuan shouhouTuihuan = new ShouhouTuihuan();
            shouhouTuihuan.setShouhouId(tuihuan.getShouhouId());
            shouhouTuihuan.setTuihuanKind(tuihuan.getTuihuanKind());
            shouhouTuihuan.setInuser(myUser);
            shouhouTuihuan.setAreaid(myAreaid);
            shouhouTuihuan.setComment(tuihuan.getComment());
            shouhouTuihuan.setPiaoInfo(tuihuan.getPiaoInfo());
            shouhouTuihuan.setCtype(tuihuan.getCtype());
            shouhouTuihuan.setIncludeChecklist(tuihuan.getIncludeChecklist());
            shouhouTuihuan.setFaultType(StringUtils.isEmpty(tuihuan.getFaultType()) ? "" : tuihuan.getFaultType());
            shouhouTuihuan.setCheckType(StringUtils.isEmpty(tuihuan.getCheckType()) ? "" : tuihuan.getCheckType());
            shouhouTuihuan.setTradeType(tuihuan.getTradeType() == null ? 0 : tuihuan.getTradeType());
            shouhouTuihuan.setTradeDate(tuihuan.getTradeDate());

            if (0 == tuihuan.getBasketId()) {
                shouhouTuihuan.setBasketId(null);
            } else {
                shouhouTuihuan.setBasketId(tuihuan.getBasketId());
            }
            shouhouTuihuanService.save(shouhouTuihuan);
        } else if (3 == tuihuan.getTuihuanKind()) {
            //普惠分区退款
            BigDecimal puHuiM = 1 == tuihuan.getPuhui() ? tuihuan.getPuhuim() : BigDecimal.ZERO;

            ShouhouTuihuan shouhouTuihuan = new ShouhouTuihuan();
            shouhouTuihuan.setShouhouId(tuihuan.getShouhouId());
            shouhouTuihuan.setTuihuanKind(tuihuan.getTuihuanKind());
            shouhouTuihuan.setInuser(myUser);
            shouhouTuihuan.setAreaid(myAreaid);
            shouhouTuihuan.setComment(tuihuan.getComment());
            shouhouTuihuan.setTuiWay(tuihuan.getTuiWay());

            shouhouTuihuan.setBankname(tuihuan.getBankname());
            shouhouTuihuan.setBankfuming(tuihuan.getBankfuming());
            shouhouTuihuan.setBanknumber(tuihuan.getBanknumber());

            shouhouTuihuan.setTuikuanM(tuihuan.getTuikuanM());
            shouhouTuihuan.setTuikuanM1(tuihuan.getTuikuanM());
            shouhouTuihuan.setZhejiaM(tuihuan.getZhejiaM());
            shouhouTuihuan.setBuypriceM(tuihuan.getBuypriceM());
            shouhouTuihuan.setInprice(tuihuan.getInprice());
            shouhouTuihuan.setSalenm(tuihuan.getSalenm());
            shouhouTuihuan.setPiaoInfo(tuihuan.getPiaoInfo());
            shouhouTuihuan.setCtype(tuihuan.getCtype());
            shouhouTuihuan.setCoinM(tuihuan.getCoinM());

            shouhouTuihuan.setIncludeChecklist(tuihuan.getIncludeChecklist());
            shouhouTuihuan.setBaitiaoM(tuihuan.getBaitiaoM());
            shouhouTuihuan.setKuBaiTiaoM(tuihuan.getKuBaiTiaoM());
            shouhouTuihuan.setIsValidt(tuihuan.getIsValidt());
            shouhouTuihuan.setPeizhi(tuihuan.getPeizhi());
            shouhouTuihuan.setPeizhiPrice(tuihuan.getPeizhiPrice());
            shouhouTuihuan.setPiaoPrice(tuihuan.getPiaoPrice());
            shouhouTuihuan.setPiaoType(tuihuan.getPiaoType());

            shouhouTuihuan.setFaultType(StringUtils.isEmpty(tuihuan.getFaultType()) ? "" : tuihuan.getFaultType());
            shouhouTuihuan.setCheckType(StringUtils.isEmpty(tuihuan.getCheckType()) ? "" : tuihuan.getCheckType());
            shouhouTuihuan.setTradeType(tuihuan.getTradeType() == null ? 0 : tuihuan.getTradeType());
            shouhouTuihuan.setTradeDate(tuihuan.getTradeDate());

            shouhouTuihuan.setPuhuim(puHuiM);

            shouhouTuihuanService.save(shouhouTuihuan);

            Integer thId = shouhouTuihuan.getId();
            if (CollectionUtils.isNotEmpty(netList)) {

                for (NetPayModelBo m : netList) {
                    NetPayRefundInfo npInfo = new NetPayRefundInfo();
                    npInfo.setNetRecordId(m.getId());
                    npInfo.setPrice(m.getPrice());
                    npInfo.setReturnid(thId);
                    npInfo.setInuser(myUser);
                    if (m.getPrice() != null && m.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                        netPayRefundInfoService.save(npInfo);
                    }
                    Integer count = netpayRecordService.updateRefundPrice(m.getPrice(), m.getId());
                    if (count <= 0) {
                        throw new RRException("支付金额退款错误");
                    }

                }

            }

            //如果是退款或 换其他 检测手机原订单是否有赠品 如果有赠品 原始订单赠品做提交退款操作
            R<String> tuiExRes = submitTuiEx(tuihuan.getShouhouId(), myUser, tuihuan.getTuihuanKind(), myAreaid);
            if (tuiExRes.getUserMsg().equals("1")) {
                throw new RRException(tuiExRes.getUserMsg());
            }
            //提交时候直接扣减ovg返现金额
            if (tuihuan.getReduceSaveMoney().compareTo(BigDecimal.ZERO) > 0 && userid != null && basketId != null) {
                R<String> saveR = this.saveMoney(userid, basketId, BigDecimal.ZERO.subtract(tuihuan.getReduceSaveMoney()), "系统", "OVG退款余额退回", SaveMoneyEkindEnum.SAVE_MONEY_EKIND_20.getCode(), myAreaid, null, null, null, null);
                if (saveR.getCode() != ResultCode.SUCCESS && !"1".equals(saveR.getUserMsg())) {
                    throw new RRException("OVG退款余额退回失败！");
                }
            }
        } else if (4 == tuihuan.getTuihuanKind()) {
            ShouhouTuihuan tuihuanEntity = new ShouhouTuihuan();
            BeanUtils.copyProperties(tuihuan, tuihuanEntity);

            if (tuihuan.getTuikuanM1() == null || tuihuan.getTuikuanM1().compareTo(BigDecimal.ZERO) == 0) {

                Boolean flag = shouhouTuihuanService.save(tuihuanEntity);

                if (flag) {
                    Integer thId = tuihuanEntity.getId();
                    netList.stream().forEach(m -> {
                        NetPayRefundInfo netPayInfo = new NetPayRefundInfo();
                        netPayInfo.setNetRecordId(m.getId());
                        netPayInfo.setPrice(m.getPrice());
                        netPayInfo.setReturnid(thId);
                        netPayInfo.setInuser(myUser);
                        netPayInfo.setDtime(LocalDateTime.now());
                        if (m.getPrice() != null && m.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                            netPayRefundInfoService.save(netPayInfo);
                        }
                        Integer counts = netpayRecordService.updateRefundPrice(m.getPrice(), m.getId());
                        if (counts <= 0) {
                            throw new RRException("支付金额退款错误！");
                        }
                    });

                }

                //如果是退款或 换其他 检测手机原订单是否有赠品 如果有赠品 原始订单赠品做提交退款操作
                R<String> submitR = this.submitTuiEx(tuihuan.getShouhouId(), myUser, tuihuan.getTuihuanKind(), myAreaid);
                if (submitR.getCode() != ResultCode.SUCCESS && !"1".equals(submitR.getUserMsg())) {
                    throw new RRException(submitR.getUserMsg());
                }

                //提交时候直接扣减ovg返现金额
                if (tuihuan.getReduceSaveMoney().compareTo(BigDecimal.ZERO) > 0 && userid != null && basketId != null) {
                    R<String> saveR = this.saveMoney(userid, basketId, BigDecimal.ZERO.subtract(tuihuan.getReduceSaveMoney()), "系统", "OVG退款余额退回", SaveMoneyEkindEnum.SAVE_MONEY_EKIND_20.getCode(), myAreaid, null, null, null, null);
                    if (saveR.getCode() != ResultCode.SUCCESS && !"1".equals(saveR.getUserMsg())) {
                        throw new RRException("OVG退款余额退回失败！");
                    }
                }
            } else {
                BigDecimal tuikuanM = tuihuan.getSubIdm().add(tuihuan.getTuikuanM1()).add(tuihuan.getPeizhiPrice());
                if (tuihuan.getTuikuanM().compareTo(tuikuanM) == 0) {
                    Boolean flag = shouhouTuihuanService.save(tuihuanEntity);

                }
            }
        }

        return R.success("操作成功");
    }

    @Override
    public BigDecimal getHeXiaoJE(Integer shouhouId) {
        List<HexiaoBo> hexiaoList = wxkcoutputService.getHexiao(shouhouId);
        BigDecimal p = BigDecimal.ZERO;
        for (HexiaoBo e : hexiaoList) {
            if (e.getIshexiao() != null) {
                p = p.add(e.getInprice());
            }
        }
        return p;
    }

    @Override
    public ShouhouWxzqRes calcDate(Integer wxzq, Shouhou shouhouInfoRes) {
        ShouhouWxzqRes wxzqRes = new ShouhouWxzqRes();

        wxzqRes.setWxzq(wxzq);

        Boolean isquji = shouhouInfoRes.getIsquji() == null ? false : shouhouInfoRes.getIsquji();
        if (wxzq != 0) {
            //维修倒计时
            String timeTextDjs = "";
            LocalDateTime yDate = LocalDateTime.now();
            if (!isquji && shouhouInfoRes.getQujitongzhitime() != null) {
                yDate = shouhouInfoRes.getQujitongzhitime();
            } else if (!isquji && shouhouInfoRes.getQujitongzhitime() == null) {
                yDate = LocalDateTime.now();
            } else if (isquji) {
                if (shouhouInfoRes.getQujitongzhitime() != null) {
                    yDate = shouhouInfoRes.getQujitongzhitime();
                } else {
                    yDate = shouhouInfoRes.getOfftime() == null ? LocalDateTime.now() : shouhouInfoRes.getOfftime();
                }
            }

            Duration timesp = Duration.between(yDate, shouhouInfoRes.getModidate().plusDays(wxzq));

            System.out.println(timesp.toHours());
            if (timesp.toHours() >= 24 || timesp.toHours() < -24) {
                timeTextDjs = String.format("%.1f", timesp.toHours() / 24.0) + "天";
            } else if (timesp.toHours() > 1 || timesp.toHours() < 0) {
                timeTextDjs = String.format("%.1f", timesp.toMinutes() / 60.0) + "小时";
            } else {
                timeTextDjs = timesp.toMinutes() + "分钟";
            }
            Long chaoshiDay = timesp.toDays();
            wxzqRes.setChaoshiDay(chaoshiDay);

            //维修总时长
            String timeTextSum = "";
            if (isquji || shouhouInfoRes.getQujitongzhitime() != null) {
                if (shouhouInfoRes.getQujitongzhitime() != null) {
                    Duration timesp1 = Duration.between(shouhouInfoRes.getModidate(), shouhouInfoRes.getQujitongzhitime());

                    if (timesp1.toHours() >= 24 || timesp1.toHours() < -24) {
                        timeTextSum = String.format("%.1f", Math.abs(timesp1.toHours() / 24.0)) + "天";
                    } else if (timesp1.toHours() > 1 || timesp1.toHours() < 0) {
                        timeTextSum = String.format("%.1f", Math.abs(timesp1.toMinutes() / 60.0)) + "小时";
                    } else {
                        timeTextSum = Math.abs(timesp1.toMinutes()) + "分钟";
                    }
                } else {
                    Duration timesp2 = Duration.between(shouhouInfoRes.getModidate() == null ? LocalDateTime.now() : shouhouInfoRes.getModidate(), shouhouInfoRes.getOfftime() == null ? LocalDateTime.now() : shouhouInfoRes.getOfftime());
                    if (timesp2.toHours() >= 24 || timesp2.toHours() < -24) {
                        timeTextSum = String.format("%.1f", Math.abs(timesp2.toHours() / 24.0)) + "天";
                    } else if (timesp2.toHours() > 1 || timesp2.toHours() < 0) {
                        timeTextSum = String.format("%.1f", Math.abs(timesp2.toMinutes() / 60.0)) + "小时";
                    } else {
                        timeTextSum = Math.abs(timesp2.toMinutes()) + "分钟";
                    }

                }
            }
            wxzqRes.setTimeTextDjs(timeTextDjs);
            wxzqRes.setTimeTextSum(timeTextSum);
        }
        return wxzqRes;
    }

    @Override
    public List<Integer> getShouhouIdByFromid(Integer fromId) {
        List<Integer> result = new ArrayList<>();
        //76783 指现货订单
        Shouhou shouhou = Optional.ofNullable(super.getOne(new LambdaQueryWrapper<Shouhou>()
                .eq(Shouhou::getUserid, 76783)
                .eq(Shouhou::getFromshouhouid, fromId)))
                .orElse(super.getOne(new LambdaQueryWrapper<Shouhou>()
                        .eq(Shouhou::getUserid, 76783)
                        .eq(Shouhou::getId, fromId)));
        if (shouhou != null) {
            result.add(shouhou.getId());
            result.add(shouhou.getFromshouhouid());

        } else {
            result.add(0);
            result.add(0);
        }
        return result;
    }

    @Override
    public R<Boolean> tuiCheck(Integer shouhouId) {
        //业务说明，对于良品订单，如果会员被拉黑名单，限制15天内不能无理由退款，则需要做退款校验限制

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        //先查询是否启用此开关
        String code = sysConfigService.getValueByCode(SysConfigConstant.TUI_CHECK);
        if (StringUtils.isEmpty(code)) {
            return R.error("获取配置项失败");
        }
        if (code.equals("0")) {
            return R.success("true", Boolean.TRUE);
        }

        //Shouhou shouhou = super.getById(shouhouId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->super.getById(shouhouId), MTableInfoEnum.SHOUHOU, shouhouId);
        if (shouhou == null) {
            return R.error("维修单号错误");
        }

        if (CommenUtil.isNullOrZero(shouhou.getSubId())) {
            return R.error("订单号不存在");
        }
        if(Boolean.TRUE.equals(shouhou.getIsquji())){
            //已经取机没必要校验了
            return R.success("true", Boolean.TRUE);
        }
        //根据良品订单号查询下单会员的黑名单标识
        Integer blackListType = baseMapper.getUserBlackListTypeByRecoverId(shouhou.getSubId());
        boolean isWuliYou = StrUtil.endWith(shouhou.getWuliyou(), ShouhouExService.WU_LI_YOU_TEXT);
        if(isWuliYou && XtenantEnum.isJiujiXtenant() && shouhou.getTradedate() != null
                && Duration.between(shouhou.getTradedate(),shouhou.getModidate()).toDays()>ShouhouExService.getWuLiYouDays()
        ){
            //校验无理由退货的时间
            return R.error(StrUtil.format("*已超过{}{}时间，无法办理",ShouhouExService.getWuLiYouDays(),ShouhouExService.WU_LI_YOU_TEXT));
        }

        if (CommenUtil.isNotNullZero(blackListType)
                && Objects.equals(shouhou.getIshuishou(), IshuishouEnum.GOOD_PRODUCT.getCode())
                && Arrays.asList( NumberConstant.SIXTEEN,NumberConstant.TWENTY,NumberConstant.THIRTY + NumberConstant.ONE).contains(blackListType)
                && isWuliYou) {
            return R.error(StrUtil.format("您的良品{}天无理由退款次数已超限，不能进行退款",ShouhouExService.getWuLiYouDays()));
        }
        CsharpCommonService csharpCommonService = SpringUtil.getBean(CsharpCommonService.class);
        R<List<BlackListVo>> blackListsR = Optional.ofNullable(Convert.toInt(shouhou.getUserid())).map(csharpCommonService::getBlackLists)
                .orElseGet(()->R.success(null));
        if(!blackListsR.isSuccess()){
            return R.error(blackListsR.getUserMsg());
        }
        List<BlackListVo> blackSubTypes = ObjectUtil.defaultIfNull(blackListsR.getData(),(List<BlackListVo>) Collections.EMPTY_LIST).stream()
                .filter(Objects::nonNull).collect(Collectors.toList());
        List<Integer> blackSubTypeCodes = blackSubTypes.stream().map(BlackListVo::getCode).collect(Collectors.toList());
        if(Objects.equals(NumberConstant.ONE,shouhou.getIshuishou())
                && isWuliYou
                && blackSubTypeCodes.contains(MemberBlacklist.LIANGBACK.getCode())){
            return R.error(StrUtil.format("系统检测到您近期多次退款良品订单，购买和退货频率异常，系统已限制无理由退款{}，但不影响您正常购买商品。若有疑问，请咨询客服。",
                    getLockDurationText(blackSubTypes,MemberBlacklist.LIANGBACK)));
        }
        if(!Objects.equals(NumberConstant.ONE,shouhou.getIshuishou())
                && basketService.lambdaQuery().select(Basket::getBasketId).eq(Basket::getBasketId,shouhou.getBasketId()).apply("isnull(isdel,0)=0").eq(Basket::getType,22).count()>0
                && blackSubTypeCodes.contains(MemberBlacklist.YOUPIN.getCode())){
            // basketType 22 优品
            return R.error(StrUtil.format("系统检测到您近期多次退款优品订单，退货频率异常，系统已限制无理由退款{}，但不影响您正常购买商品。若有疑问，请咨询客服。",
                    getLockDurationText(blackSubTypes,MemberBlacklist.YOUPIN)));
        }
        if(!Objects.equals(NumberConstant.ONE,shouhou.getIshuishou())
                && blackSubTypeCodes.contains(MemberBlacklist.NEWSUBBACK.getCode())){
            return R.error(StrUtil.format("系统检测到您近期多次退款新机订单，退货频率异常，系统已限制无理由退款{}，但不影响您正常购买商品。若有疑问，请咨询客服。",
                    getLockDurationText(blackSubTypes,MemberBlacklist.NEWSUBBACK)));
        }
        if(XtenantEnum.isJiujiXtenant() && Objects.equals(NumberConstant.ONE,shouhou.getIshuishou()) && StrUtil.isBlank(shouhou.getWuliyou())) {
            return R.error("请先选择“良品退货原因”保存修改后再进行操作“退换机管理”。");
        }

        return R.success("true", Boolean.TRUE);

    }

    public static String getLockDurationText(List<BlackListVo> blackSubTypes,MemberBlacklist memberBlacklist) {
        return blackSubTypes.stream().filter(bst -> Objects.equals(bst.getCode(), memberBlacklist.getCode())).findFirst()
                .map(BlackListVo::getLockDuration).map(ld -> StrUtil.format("（时长{}个月）",ld)).orElse("");
    }

    @Override
    public R<List<SmallProInfo>> getSmallProInfo(Integer shouhouId) {
        List<Integer> ids = baseMapper.getSmallProId(shouhouId);
        if (CollectionUtils.isEmpty(ids)) {
            return R.success("小件单信息不存在", new ArrayList<>());
        }

        List<SmallProInfo> resList = ids.stream().map(id -> {
                    SmallProInfo res = new SmallProInfo();
                    res.setSmallProId(id);
                    String url = jiujiSystemProperties.getPc() + "/staticpc/#/small-refund/" + id;
                    res.setUrl(url);
                    return res;
                }
        ).collect(Collectors.toList());
        return R.success("操作成功", resList);
    }

    @Override
    public R<Integer> getBigProId(Integer smallId) {
        List<SmallproBill> smallproBillList = SpringUtil.getBean(SmallproBillService.class).lambdaQuery().eq(SmallproBill::getSmallproID, smallId).list();
        if(CollectionUtils.isEmpty(smallproBillList)){
            return R.success(null);
        }
        Integer basketId = Optional.ofNullable(smallproBillList.get(NumberConstant.ZERO)).orElse(new SmallproBill()).getBasketId();
        Integer giftid = Optional.ofNullable(CommenUtil.autoQueryHist(() -> basketService.getById(basketId), MTableInfoEnum.BASKET, basketId)).orElse(new Basket()).getGiftid();
        Shouhou shouhou = CommenUtil.autoQueryHist(() -> shouhouService.lambdaQuery().eq(Shouhou::getBasketId, giftid)
                .select(Shouhou::getId).list()).stream().findFirst().orElse(new Shouhou());
        return R.success(shouhou.getId());
    }

    @Override
    public String getOfficialInsurance(Integer subId) {
        List<OfficialInsurance> result = baseMapper.getOfficialInsurance(subId);
        return result.stream().map(OfficialInsurance::getInsuranceName).collect(Collectors.joining(","));
    }

    @Override
    public R<List<ShouhouYuYueBasicInfo>> getYuYueListByMobile(String mobile) {
        if (!CommenUtil.isMobile(mobile)) {
            return R.error("手机号码有误");
        }
        //根据手机号码查询正在进行中的大件预约单信息
        return R.success(baseMapper.getYuYueListByMobile(mobile));
    }

    @Override
    public R<String> getSomeUsefulTipsWhenAfter(String imei) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        List<MemberSubVO> subInfos = CommenUtil.autoQueryHist(()->subService.searchSubInfoByImei(imei));
        if (StringUtils.isEmpty(imei) || CollectionUtils.isEmpty(subInfos)) {
            if (oaUserBO.getXTenant() >= 1000) {
                return R.noData();
            }
            String printName = sysConfigService.getWebNameByXtenant(oaUserBO.getXTenant());
            String tips = String.format(OrderTypeAndAfterTipsEnum.WX_TIPS.getTips(), printName);
            return R.success("操作成功", tips);
        }
        Integer type = subInfos.get(0).getType();
        Integer brandId = subInfos.get(0).getBrandId();
        if (Objects.equals(brandId, 1)) {
            OrderTypeAndAfterTipsEnum tipsEnum = EnumUtil.getEnumByCode(OrderTypeAndAfterTipsEnum.class, type);
            if (tipsEnum != null) {
                return R.success("操作成功", tipsEnum.getTips());
            }
        }
        return R.noData();
    }

    @Override
    public List<RepairRecords> getRepairRecordsByImeiOrMobile(String imei, String mobile) {
        if (StringUtils.isEmpty(imei) && StringUtils.isEmpty(mobile)) {
            return new ArrayList<>();
        }
        return CommenUtil.autoQueryHist(()->baseMapper.getRepairRecordsByImeiOrMobile(imei, mobile));
    }

    @Override
    public final R<Boolean> isSupportModifyProductInfoWhenReceive() {
        String value = sysConfigService.getValueByCode(SysConfigConstant.SUPPORT_MODIFY_RECEIVE_PRODUCT_INFO);
        if (StringUtils.isEmpty(value)) {
            return R.success(Boolean.FALSE);
        }
        return R.success(Integer.parseInt(value) > 0);
    }

    /**
     * imei的维修记录
     * @param imei
     * @return
     */
    @Override
    public R<Integer> countRepairRecord(String imei) {
        if(StringUtils.isEmpty(imei)){
            return R.error("imei不能为空!");
        }
        Integer count = shouhouService.getBaseMapper()
                .selectCount(new LambdaQueryWrapper<Shouhou>().eq(Shouhou::getImei, imei).eq(Shouhou::getStats, 1)
                        .eq(Shouhou::getIsquji, Boolean.TRUE));
        String userMsg = count>0 ? "该IMEI存在九机维修历史接件记录，请注意做好拆修情况检测！" : "";
        return R.success(userMsg, count);
    }

    /**
     * 亏损是否需要备注
     * （1）维修单为在保
     *
     * （2）维修单产生亏损（按照配件亏损来算，手工费也算）
     *
     * （3）维修配件的旧件已经被回收或者返还（换货的不算）
     * @param shouhouId
     * @return
     */
    @Override
    public R<Boolean> qujiLossNeedRemark(Integer shouhouId) {
        return R.success(Optional.ofNullable(shouhouMapper.qujiTotalDifferentPrice(shouhouId))
                .filter(price->price.compareTo(BigDecimal.ZERO)<0).isPresent());
    }

    /**
     * 取机操作
     *
     * @param sh
     * @return
     */
    private Boolean begainQuji(Shouhou sh, Boolean isauto) {
        LambdaUpdateWrapper<Shouhou> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(sh.getOfftime() == null, Shouhou::getOfftime, LocalDateTime.now());
        updateWrapper.set(sh.getModidate() == null, Shouhou::getModidate, LocalDateTime.now());
        updateWrapper.set(sh.getCostprice() == null, Shouhou::getCostprice, BigDecimal.ZERO);

        if (sh.getIsweixiu() == null) {
            Integer stats = sh.getStats() == null ? -1 : sh.getStats();
            if (stats.equals(1)) {
                updateWrapper.set(Shouhou::getIsweixiu, true);
                updateWrapper.set(sh.getWeixiudtime() == null, Shouhou::getWeixiudtime, LocalDateTime.now());
            } else if (stats.equals(3)) {
                updateWrapper.set(Shouhou::getIsweixiu, true);
                updateWrapper.set(sh.getWeixiudtime() == null, Shouhou::getWeixiudtime, LocalDateTime.now());
            }
        }
        updateWrapper.set(Shouhou::getIsquji, true);

        updateWrapper.eq(Shouhou::getId, sh.getId()).and(bo -> bo.eq(Shouhou::getIsquji, false).or().isNull(Shouhou::getIsquji));

        Boolean isok = super.update(updateWrapper);
        if (!isok) {
            return isok;
        }

        Integer serviceType = sh.getServiceType() == null ? 0 : sh.getServiceType();
        Integer areaId = sh.getToareaid() == null ? sh.getAreaid() : sh.getToareaid();

        //平摊优惠金额
        String youhuima = sh.getYouhuima();
        BigDecimal youhuimaTotal = BigDecimal.ZERO;
        if (StringUtils.isNotEmpty(youhuima)) {
            youhuima = youhuima.replaceAll("\\|", "");
            List<NumberCard> numberCardList = numberCardService.list(new LambdaQueryWrapper<NumberCard>().eq(NumberCard::getCardID, youhuima));
            if (CollectionUtils.isNotEmpty(numberCardList)) {
                youhuimaTotal = numberCardList.get(0).getTotal();
            }
        }

//        wxkcoutputService.


        return null;
    }

    /**
     * 判断是否应该跳过成本校验
     * 满足任意条件不进行校验：
     * （1）条件①：维修单内只有：服务工时费（硬件）[ppid：420051]，或 拆机调试与主板进液清洗 [ppid：414747] 商品
     * （2）条件②：维修单内只有：维修耗材（分类id：801）商品（但不能超过2个）
     * （3）条件③：维修单内既有条件①，也有条件②的商品，且没有其他商品
     * @param shouhouId 维修单ID
     * @return true-跳过成本校验，false-需要进行成本校验
     */
    @Override
    public Boolean shouldBypassCostCheck(Integer shouhouId) {
        //如果是输出那就不跳过成本校验
        if(XtenantEnum.isSaasXtenant()){
            return false;
        }

        List<Wxkcoutput> items =CommenUtil.autoQueryHist(()->wxkcoutputService
                .list(new QueryWrapper<Wxkcoutput>().lambda().eq(Wxkcoutput::getWxid, shouhouId)),MTableInfoEnum.WXKCOUTPUT_WXID,shouhouId);

        if (CollUtil.isEmpty(items)) {
            return false; // 没有配件，默认行为（进行校验）
        }

        // 特殊ppid：服务工时费（硬件）和拆机调试与主板进液清洗
        List<Integer> specialPpids = Arrays.asList(420051, 414747);
        // 维修耗材分类id（包含子分类）
        List<Integer> consumableCids = categoryService.selectCategoryChildrenByCid(Arrays.asList(801));

        // 获取所有有ppid的配件
        List<Integer> itemPpids = items.stream()
                                    .map(Wxkcoutput::getPpriceid)
                                    .filter(ppid -> ppid != null && ppid > 0)
                                    .collect(Collectors.toList());

        // 如果存在没有ppid的项目（如手工费），或者有ppid的项目数量与总项目数量不匹配，
        // 说明存在其他类型的项目，不跳过校验
        if (itemPpids.size() != items.size()) {
            return false; // 包含无法识别的项目，不跳过校验
        }

        // 一次性获取所有商品信息，避免循环查询
        Map<Integer, Productinfo> productMap = productinfoService.getProductMapByPpids(itemPpids);

        // 条件①：维修单内只有特殊ppid（420051或414747）的商品
        boolean condition1 = !itemPpids.isEmpty() && itemPpids.stream().allMatch(specialPpids::contains);

        // 条件②：维修单内只有维修耗材（分类id：801及其子分类）商品，且不超过2个
        boolean condition2 = false;
        if (!itemPpids.isEmpty() && itemPpids.size() <= 2 && CollUtil.isNotEmpty(consumableCids)) {
            // 检查是否所有ppid都属于维修耗材分类
            condition2 = itemPpids.stream().allMatch(ppid -> {
                Productinfo productinfo = productMap.getOrDefault(ppid,new Productinfo());
                return consumableCids.contains(productinfo.getCid());
            });
        }

        // 条件③：维修单内既有条件①的商品，也有条件②的商品，且没有其他商品
        boolean condition3 = false;
        if (!itemPpids.isEmpty() && CollUtil.isNotEmpty(consumableCids)) {
            // 移除特殊ppid和维修耗材后，如果没有剩余商品，说明只有这两类商品
            Collection<Productinfo> remainingItems = new ArrayList<>(productMap.values());

            // 统计因为不同条件被移除的数量
            int removedBySpecialPpids = 0;
            int removedByConsumableCids = 0;

            Iterator<Productinfo> iterator = remainingItems.iterator();
            while (iterator.hasNext()) {
                Productinfo item = iterator.next();
                boolean shouldRemove = false;

                if (specialPpids.contains(item.getPpriceid())) {
                    removedBySpecialPpids++;
                    shouldRemove = true;
                } else if (consumableCids.contains(item.getCid())) {
                    removedByConsumableCids++;
                    shouldRemove = true;
                }

                if (shouldRemove) {
                    iterator.remove();
                }
            }
            condition3 = CollUtil.isEmpty(remainingItems) && removedBySpecialPpids > 0 && removedByConsumableCids > 0 && removedBySpecialPpids <= 2;
        }

        // 满足任意条件则跳过成本校验
        return condition1 || condition2 || condition3;
    } 


}
