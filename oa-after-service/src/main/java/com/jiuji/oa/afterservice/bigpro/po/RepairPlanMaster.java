package com.jiuji.oa.afterservice.bigpro.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 维修方案主表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("repair_plan_master")
@ApiModel(value = "RepairPlanMaster对象", description = "维修方案主表")
public class RepairPlanMaster extends Model<RepairPlanMaster> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "售后单ID")
    @TableField("shouHouId")
    private Integer shouHouId;


    @ApiModelProperty(value = "创建人")
    @TableField("create_user")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 0 - 未确认
     * 1 - 用户确认
     * @see RepairPlanMasterStatusEnum
     */
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "确认人")
    @TableField("confirm_user")
    private String confirmUser;

    @ApiModelProperty(value = "确认时间")
    @TableField("confirm_time")
    private LocalDateTime confirmTime;

    @ApiModelProperty(value = "逻辑删除(0:未删除 1:已删除)")
    @TableLogic
    @TableField("is_del")
    private Integer isDel;


} 