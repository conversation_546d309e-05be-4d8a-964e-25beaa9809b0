package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.RepairPlanMasterMapper;
import com.jiuji.oa.afterservice.bigpro.dto.*;
import com.jiuji.oa.afterservice.bigpro.enums.ConfirmSourceEnum;
import com.jiuji.oa.afterservice.bigpro.enums.RepairPlanMasterStatusEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.bo.WxFeeBo;
import com.jiuji.oa.afterservice.bigpro.enums.AccessoryTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.PlanTypeEnum;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.service.strategy.RepairPlanConfirmStrategy;
import com.jiuji.oa.afterservice.bigpro.service.strategy.RepairPlanConfirmStrategyFactory;
import com.jiuji.oa.afterservice.bigpro.service.strategy.impl.DJIRepairStrategy;
import com.jiuji.oa.afterservice.bigpro.vo.RepairFaultVO;
import com.jiuji.oa.afterservice.bigpro.vo.RepairPlanDetailVO;
import com.jiuji.oa.afterservice.bigpro.vo.RepairPlanMasterTypeVO;
import com.jiuji.oa.afterservice.bigpro.vo.RepairPlanVO;
import com.jiuji.oa.afterservice.bigpro.vo.req.CorrelationInfo;
import com.jiuji.oa.afterservice.bigpro.vo.req.CostPriceNewReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.RepairPlanConfirmReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.RepairPlanDetailReq;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.jiuji.oa.afterservice.bigpro.dto.PlanTypeParam;

/**
 * <p>
 * 维修方案主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Slf4j
@Service
public class RepairPlanMasterServiceImpl extends ServiceImpl<RepairPlanMasterMapper, RepairPlanMaster> implements RepairPlanMasterService {

    @Resource
    private RepairFaultService repairFaultService;
    
    @Resource
    private RepairPlanService repairPlanService;
    @Resource
    private AbstractCurrentRequestComponent component;
    @Resource
    private ShouhouService shouhouService;

    @Resource
    private ProductinfoService productinfoService;
    
    @Resource
    private ShouhouExtendService shouhouExtendService;
    @Resource
    private RedeemService redeemService;

    @Resource
    private RepairAccessoriesService repairService;
    @Resource
    private ProductKcService kcService;

    @Resource
    private  ShouHouPjService shouHouPjService;

    @Resource
    private WeixinUserService weixinUserService;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private ShouhouMsgService shouhouMsgService;

    @Resource
    private AreainfoService areaInfoService;
    @Resource
    private SmsService smsService;

    @Resource
    private BbsxpUsersService bbsxpUsersService;

    @Resource
    private RepairPlanConfirmStrategyFactory repairPlanConfirmStrategyFactory;

    @Resource
    private DJIRepairStrategy djiRepairStrategy;
    @Resource
    private RepairPlanMasterTypeService repairPlanMasterTypeService;

    public static final Integer DaJiangXtenant = 8;


    @Override
    public RepairPlanDetailVO getRepairPlanDetailByShouHouId(RepairPlanDetailReq req) {
        RepairPlanDetailVO detailVO = new RepairPlanDetailVO();
        // 1. 查询维修方案主表信息
        Integer shouHouId = req.getShouHouId();
        detailVO.setShouHouId(shouHouId);
        Integer userId = req.getUserId();
        Shouhou shouhou = Optional.ofNullable(CommenUtil.autoQueryHist(() ->shouhouService.lambdaQuery().eq(Shouhou::getId, shouHouId)
                        .eq(ObjectUtil.isNotNull(userId),Shouhou::getUserid, userId).one(), MTableInfoEnum.SHOUHOU, shouHouId))
                .orElseThrow(() -> new CustomizeException("售后单不存在"));
        LambdaQueryWrapper<RepairPlanMaster> masterWrapper = new LambdaQueryWrapper<>();
        masterWrapper.eq(RepairPlanMaster::getShouHouId, shouHouId);
        RepairPlanMaster master = this.getOne(masterWrapper);
        //查询类型（因为前端需要默认值所以及时没有提交也还是要查询出去）
        List<RepairPlanMasterTypeVO> planMasterTypeVOList = repairPlanMasterTypeService.getByMasterId(Optional.ofNullable(master).orElse(new RepairPlanMaster()).getId());
        detailVO.setPlanTypeList(planMasterTypeVOList);
        if (ObjectUtil.isNull(master)) {
            return detailVO;
        }
        
        // 2. 查询维修故障表信息
        LambdaQueryWrapper<RepairFault> faultWrapper = new LambdaQueryWrapper<>();
        faultWrapper.eq(RepairFault::getMasterId, master.getId());
        List<RepairFault> faultList = repairFaultService.list(faultWrapper);
        // 创建返回对象并复制主表信息
        BeanUtil.copyProperties(master, detailVO);
        if (faultList.isEmpty()) {
            detailVO.setFaultList(new ArrayList<>());
            return detailVO;
        }
        // 3. 查询维修方案表信息
        LambdaQueryWrapper<RepairPlan> planWrapper = new LambdaQueryWrapper<>();
        planWrapper.in(RepairPlan::getFaultId, faultList.stream().map(RepairFault::getId).collect(Collectors.toList()));
        List<RepairPlan> planList = repairPlanService.list(planWrapper);
        
        // 将方案按故障ID分组
        Map<Integer, List<RepairPlan>> planMap = planList.stream()
                .collect(Collectors.groupingBy(RepairPlan::getFaultId));
        
        // 先提取所有配件ID，用于一次性批量查询产品信息，避免循环查询
        List<Integer> allAccessoryPpids = planList.stream()
            .map(RepairPlan::getAccessoryPpid)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        
        // 一次性批量查询所有产品信息
        Map<Integer, Productinfo>  allProductMap = productinfoService.getProductMapByPpids(allAccessoryPpids);
        Map<Integer, ProductKc> kcMap = kcService.listKcCountMap(allAccessoryPpids, Optional.ofNullable(shouhou.getToareaid()).orElse(shouhou.getAreaid()));
        // 4. 组装数据
        List<RepairFaultVO> faultVOList = new ArrayList<>();
        for (RepairFault fault : faultList) {
            RepairFaultVO faultVO = new RepairFaultVO();
            BeanUtil.copyProperties(fault, faultVO);
            // 获取当前故障对应的方案列表
            List<RepairPlan> currentFaultPlans = planMap.getOrDefault(fault.getId(), new ArrayList<>());
            List<RepairPlanVO> planVOList = currentFaultPlans.stream().map(plan -> {
                RepairPlanVO planVO = new RepairPlanVO();
                BeanUtil.copyProperties(plan, planVO);
                planVO.setAccessoryTypeName(AccessoryTypeEnum.getDescByCode(plan.getAccessoryType()));
                Optional.ofNullable(plan.getCorrelationId()).ifPresent(wxKcOutPutId -> planVO.setIsGenerateAccessory(Boolean.TRUE));
                // 设置产品名称和规格信息
                Integer ppid = plan.getAccessoryPpid();
                if (ObjectUtil.isNotNull(ppid)) {
                    Productinfo product = allProductMap.getOrDefault(ppid, new Productinfo());
                    planVO.setProductName(product.getProductName());
                    // 规格信息可能需要从其他表获取，这里暂时设为空
                    planVO.setProductColor(product.getProductColor());
                    ProductKc productKc = kcMap.getOrDefault(ppid, new ProductKc());
                    planVO.setLeftCount(productKc.getLeftCount());
                }
                return planVO;
            }).collect(Collectors.toList());
            
            faultVO.setPlanList(planVOList);
            faultVOList.add(faultVO);
        }
        
        detailVO.setFaultList(faultVOList);
        return detailVO;
    }

    /**
     * 数据校验
     * @param saveDTO
     */
    private void checkData(RepairPlanSaveDTO saveDTO){
        // 0. 基础字段校验（从注解迁移过来的校验逻辑）
        validateBasicFields(saveDTO);

        Integer shouHouId = saveDTO.getShouHouId();
        Integer id = saveDTO.getId();
        // 1. 校验是否为大疆维修单
        if (!shouhouExtendService.isDJIRepairOrder(null, shouHouId)) {
            throw new CustomizeException("该售后单不是大疆维修单，无法操作维修方案");
        }
        // 2. 校验数据是否存在
        LambdaQueryWrapper<RepairPlanMaster> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RepairPlanMaster::getShouHouId, shouHouId);
        RepairPlanMaster existPlan = this.getOne(wrapper);
        // 新增情况：检查是否已存在
        if (ObjectUtil.isNull(id) || NumberConstant.ZERO.equals(id)) {
            if (ObjectUtil.isNotNull(existPlan)) {
                throw new CustomizeException("该售后单已存在维修方案，不能重复添加");
            }
        } else {
            // 修改情况：检查数据是否存在
            if (ObjectUtil.isNull(existPlan) || !existPlan.getId().equals(id)) {
                throw new CustomizeException("维修方案不存在或已被删除");
            }
            Integer status = existPlan.getStatus();
            String updateReason = saveDTO.getUpdateReason();
            if(RepairPlanMasterStatusEnum.USER_CONFIRMATION.equals(status) && StrUtil.isEmpty(updateReason)){
                if(updateReason.length()>200){
                    throw new CustomizeException("修改原因长度不能超过200个字符");
                }
                throw new CustomizeException("用户确认之后要修改维修方案，请填写修改原因");
            }
        }

    }

    /**
     * 基础字段校验（从DTO注解迁移过来的校验逻辑）
     * @param saveDTO 维修方案保存DTO
     */
    private void validateBasicFields(RepairPlanSaveDTO saveDTO) {
        // 校验售后单ID
        if (ObjectUtil.isNull(saveDTO.getShouHouId())) {
            throw new CustomizeException("售后单ID不能为空");
        }

        // 校验方案类型
        List<RepairPlanMasterTypeVO> planTypeList = saveDTO.getPlanTypeList();
        if (CollUtil.isEmpty(planTypeList)) {
            throw new CustomizeException("方案类型不能为空");
        }

        //只有大疆维修的时候才进行该校验
        List<Integer> typeList = planTypeList.stream().map(RepairPlanMasterTypeVO::getPlanType).collect(Collectors.toList());
        if(typeList.contains(PlanTypeEnum.DJI_REPAIR.getCode())){
            // 校验维修故障列表
            if (CollUtil.isEmpty(saveDTO.getFaultList())) {
                throw new CustomizeException("维修故障列表不能为空");
            }
            // 校验每个故障的详细信息
            for (int i = 0; i < saveDTO.getFaultList().size(); i++) {
                RepairFaultDTO faultDTO = saveDTO.getFaultList().get(i);
                validateFaultFields(faultDTO, i);
            }
        } else {
            // 如果不是大疆维修，故障列表不能有值
            if (CollUtil.isNotEmpty(saveDTO.getFaultList())) {
                throw new CustomizeException("非大疆维修方案不能包含故障列表");
            }
        }

    }

    /**
     * 校验故障字段
     * @param faultDTO 故障DTO
     * @param index 故障索引
     */
    private void validateFaultFields(RepairFaultDTO faultDTO, int index) {
        String prefix = "第" + (index + 1) + "个故障：";

        // 校验故障名称
        if (StrUtil.isEmpty(faultDTO.getFaultName())) {
            throw new CustomizeException(prefix + "故障名称不能为空");
        }

        // 校验是否必填字段
        if (faultDTO.getIsRequired() == null) {
            throw new CustomizeException(prefix + "是否必填不能为空");
        }

        // 校验维修方案列表
        if (CollUtil.isEmpty(faultDTO.getPlanList())) {
            throw new CustomizeException(prefix + "维修方案列表不能为空");
        }

        // 校验每个维修方案的详细信息
        for (int j = 0; j < faultDTO.getPlanList().size(); j++) {
            RepairPlanDTO planDTO = faultDTO.getPlanList().get(j);
            validatePlanFields(planDTO, index, j);
        }
    }

    /**
     * 校验维修方案字段
     * @param planDTO 维修方案DTO
     * @param faultIndex 故障索引
     * @param planIndex 方案索引
     */
    private void validatePlanFields(RepairPlanDTO planDTO, int faultIndex, int planIndex) {
        String prefix = "第" + (faultIndex + 1) + "个故障的第" + (planIndex + 1) + "个方案：";

        // 校验方案名称
        if (StrUtil.isEmpty(planDTO.getPlanName())) {
            throw new CustomizeException(prefix + "方案名称不能为空");
        }
    }

    /**
     * 数据处理
     * @param saveDTO
     */
    private void preHandleData(RepairPlanSaveDTO saveDTO){
        RepairPlanMaster planMaster = Optional.ofNullable(this.getById(saveDTO.getId())).orElse(new RepairPlanMaster());
        Integer status = planMaster.getStatus();
        if(RepairPlanMasterStatusEnum.USER_CONFIRMATION.equals(status)){
            //如果用户没有确认的情况，则移除用户没有选择的数据
            List<RepairPlanMasterTypeVO> planTypeList = saveDTO.getPlanTypeList();
            if(CollUtil.isEmpty(planTypeList)){
                return;
            }

            // 判断planTypeList里面的isGenerateAccessory为1和isSelectToOa为1的是不是同一条数据
            RepairPlanMasterTypeVO generateAccessoryItem = null;
            RepairPlanMasterTypeVO selectToOaItem = null;

            for (RepairPlanMasterTypeVO item : planTypeList) {
                if (NumberConstant.ONE.equals(item.getIsGenerateAccessory())) {
                    generateAccessoryItem = item;
                }
                if (NumberConstant.ONE.equals(item.getIsSelectToOa())) {
                    selectToOaItem = item;
                }
            }

            // 如果两个都不为空且不是同一条数据
            if (ObjectUtil.isNotNull(generateAccessoryItem) && ObjectUtil.isNotNull(selectToOaItem)
                && !ObjectUtil.equals(generateAccessoryItem.getId(), selectToOaItem.getId())) {
                // 把isSelectToOa为1那条数据的isGenerateAccessory设置为1
                selectToOaItem.setIsGenerateAccessory(NumberConstant.ONE);
                // 把isGenerateAccessory为1的那条数据设置为0
                generateAccessoryItem.setIsGenerateAccessory(NumberConstant.ZERO);
            }
            // 如果是同一条数据，直接return
            else if (ObjectUtil.isNotNull(generateAccessoryItem) && ObjectUtil.isNotNull(selectToOaItem)
                && ObjectUtil.equals(generateAccessoryItem.getId(), selectToOaItem.getId())) {
                return;
            }

            long count = planTypeList.stream().filter(item -> NumberConstant.ONE.equals(item.getIsSelectToOa())).count();
            if(count>1L){
                throw new CustomizeException(RepairPlanMasterStatusEnum.USER_CONFIRMATION.getDesc() +"之后只能选择一个方案类型");
            }
        } else {
            //如果用户没有确认的情况，则移除用户没有选择的数据
            List<RepairPlanMasterTypeVO> planTypeList = saveDTO.getPlanTypeList();
            if(CollUtil.isEmpty(planTypeList)){
                return;
            }
            //移出前端没有选中的数据
            planTypeList.removeIf(item->!NumberConstant.ONE.equals(item.getIsSelectToOa()));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveOrUpdateRepairPlan(RepairPlanSaveDTO saveDTO) {
        // 获取当前用户信息
        OaUserBO oaUserBO = Optional.ofNullable(component.getCurrentStaffId()).orElseThrow(() -> new RuntimeException("当前用户信息获取失败"));
        String currentUser = oaUserBO.getUserName();
        LocalDateTime now = LocalDateTime.now();
        //数据处理
        preHandleData(saveDTO);
        //数据校验
        checkData(saveDTO);
        
        // 1. 创建参数对象
        boolean isNew = ObjectUtil.isNull(saveDTO.getId()) || NumberConstant.ZERO.equals(saveDTO.getId());
        MasterInfoParam masterInfoParam = new MasterInfoParam(currentUser, now, saveDTO.getShouHouId(), isNew);
        
        // 2. 处理主表信息
        RepairPlanMaster master = handleMasterInfo(saveDTO, masterInfoParam);


        // 处理方案类型列表 - 修改
        PlanTypeParam planTypeParam = new PlanTypeParam(saveDTO.getPlanTypeList(), master.getId(), currentUser, now, saveDTO.getShouHouId(), isNew);
        handlePlanTypeList(planTypeParam);
        
        // 3. 创建存储数据的对象
        Map<Integer, RepairFault> existingFaultMap = new HashMap<>();
        Map<Integer, Map<Integer, RepairPlan>> existingPlanMap = new HashMap<>();
        
        // 4. 获取现有数据
        ExistingDataParam existingDataParam = new ExistingDataParam(currentUser, now);
        fetchExistingData(master.getId(), existingDataParam);
        existingFaultMap = existingDataParam.getExistingFaultMap();
        existingPlanMap = existingDataParam.getExistingPlanMap();
        
        // 5. 创建处理故障和方案的参数
        FaultPlanParam faultPlanParam = new FaultPlanParam(currentUser, now, saveDTO.getShouHouId(), existingFaultMap, existingPlanMap);
        
        // 6. 处理故障和方案信息
        processFaultsAndPlans(saveDTO, master, faultPlanParam);
        Set<Integer> processedFaultIds = faultPlanParam.getProcessedFaultIds();
        Set<Integer> processedPlanIds = faultPlanParam.getProcessedPlanIds();
        
        // 7. 处理需要逻辑删除的故障和方案
        if (!isNew) {
            //existingFaultMap和existingPlanMap包含了数据库中已有的所有故障和方案记录
            //processedFaultIds和processedPlanIds记录了本次请求中处理过的故障和方案ID
            //通过比较这两组数据，能找出"数据库中存在，但本次请求中未包含"的记录
            //这些记录需要做逻辑删除，表示它们在最新的维修方案中已被移除
            DeletionParam deletionParam = new DeletionParam(currentUser, now, saveDTO.getShouHouId(),
                    existingFaultMap, existingPlanMap, processedFaultIds, processedPlanIds);
            handleDeletions(deletionParam);
        } else {
            // 维修方案添加成功给用户推送信息 (消息推送的时候只看接件地区)
            sendDjMsg(saveDTO.getShouHouId());
        }
        return master.getId();
    }

    @Override
    public void sendDjMsg(Integer shouHouId) {
        Shouhou shouhou = CommenUtil.autoQueryHist(() -> shouhouService.getById(shouHouId), MTableInfoEnum.SHOUHOU, shouHouId);
        Integer userid = Convert.toInt(shouhou.getUserid());
        Integer areaId = Optional.ofNullable(shouhou.getAreaid()).orElseThrow(()->new CustomizeException("接件地区为空"));
        Areainfo areaInfo = areaInfoService.getById(areaId);
        Long xTenant = Convert.toLong(areaInfo.getXtenant());
        WeixinUser wxUser = weixinUserService.getWxxinUserByUserId(userid);
        String host = Optional.ofNullable(sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, Convert.toInt(xTenant))).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String url = host + "/after-service/detail/" + shouHouId +"?repairPlan=1";
        //判断为大疆门店
        boolean isDaJiangXtenant = DaJiangXtenant.equals(areaInfo.getXtenant());
        // 九机用户，有微信推送微信，没有微信推送短信。大疆没有微信，都推送短信
        if(isDaJiangXtenant){
            sendMsg(shouhou, url, areaInfo);
        } else {
            if (ObjectUtil.isNotNull(wxUser)) {
                String openId = wxUser.getOpenid();
                if (StringUtils.isNotEmpty(openId)) {
                    shouhouMsgService.sendShouHouNotify(openId, url, "", "", "售后服务", "处理中", LocalDateTime.now(), "维修方案已生成，点击确认", 1, xTenant, xTenant.equals(0L) ? null : areaInfo.getCityid());
                    log.warn("维修方案生成成功，微信发送成功，维修单：{}，openId：{}，url：{}", shouHouId, openId, url);
                }
            } else {
                sendMsg(shouhou, url, areaInfo);
            }
        }
    }

    private void sendMsg(Shouhou shouhou, String url, Areainfo areaInfo) {
        String mobile = shouhou.getMobile();
        Integer userid = Convert.toInt(shouhou.getUserid());
        Integer shouHouId = shouhou.getId();
        Integer xtenant = areaInfo.getXtenant();
        Integer areaId = areaInfo.getId();
        //如果没有送修人那就获取用户的号码
        if(StrUtil.isEmpty(mobile)){
            BbsxpUsers bbsxpUsers = bbsxpUsersService.lambdaQuery().eq(BbsxpUsers::getId, userid)
                    .select(BbsxpUsers::getId, BbsxpUsers::getMobile).list().stream().findFirst()
                    .orElse(new BbsxpUsers());
            mobile = bbsxpUsers.getMobile();
        }
        if(StrUtil.isNotEmpty(mobile)){
            String shortUrl = smsService.getShortLinkWithToken(userid, url, xtenant,5);
            String msg = String.format("[%s]您送修的%s，维修单%s，已生成维修方案，请点击打开确定维修方案。维修方案地址： %s", areaInfo.getPrintName(), shouhou.getName(), shouHouId, shortUrl);
            smsService.sendSms(mobile, msg, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YXTD));
            log.warn("维修方案生成成功，发送短信成功，维修单：{}，手机号：{}，短信内容：{}", shouHouId, mobile, msg);
        } else {
            log.warn("手机号不存在，请检查手机号是否正确 维修单："+ shouHouId);
        }
    }

    /**
     * 处理主表信息
     * 
     * @param saveDTO 保存DTO
     * @param param 主表参数
     * @return 维修方案主表对象
     */
    private RepairPlanMaster handleMasterInfo(RepairPlanSaveDTO saveDTO, MasterInfoParam param) {
        RepairPlanMaster master;
        
        if (param.isNew()) {
            // 新增
            master = new RepairPlanMaster();
            master.setShouHouId(saveDTO.getShouHouId());
            master.setCreateUser(param.getCurrentUser());
            master.setCreateTime(param.getNow());
            this.save(master);
            
            // 添加日志记录 - 新增维修方案
            shouhouService.saveShouhouLog(saveDTO.getShouHouId(), "新增维修方案，方案ID：" + master.getId(), param.getCurrentUser());
        } else {
            // 修改 - 先获取原始数据
            master = this.getById(saveDTO.getId());
            if (master == null) {
                throw new RuntimeException("维修方案不存在，ID：" + saveDTO.getId());
            }
            
            // 检查主表字段是否有变化
            boolean masterChanged = false;
            StringBuilder masterChangeLog = new StringBuilder();
            // 使用LambdaUpdate只更新真正变动的字段
            LambdaUpdateWrapper<RepairPlanMaster> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(RepairPlanMaster::getId, saveDTO.getId());
            
            // 如果有变化，更新变动字段并记录日志
            if (masterChanged) {
                boolean updated = this.update(updateWrapper);
                if (!updated) {
                    throw new RuntimeException("更新维修方案主表失败，ID：" + saveDTO.getId());
                }
            }
            
            // 如果有变化，记录日志
            if (masterChanged) {
                // 添加日志记录 - 修改维修方案主表信息
                shouhouService.saveShouhouLog(saveDTO.getShouHouId(), "修改维修方案主表信息：" + masterChangeLog.toString(), param.getCurrentUser());
            }
        }
        //记录修改原因
        String updateReason = saveDTO.getUpdateReason();
        if(StrUtil.isNotEmpty(updateReason)){
            shouhouService.saveShouhouLog(saveDTO.getShouHouId(), "修改大疆维修方案原因：" + updateReason, param.getCurrentUser(), null, Boolean.TRUE);
        }
        return master;
    }
    
    /**
     * 处理维修方案类型列表
     *
     * @param param 方案类型处理参数
     */
    private void handlePlanTypeList(PlanTypeParam param) {
        if (CollUtil.isEmpty(param.getPlanTypeList())) {
            return;
        }
        // 构建变更日志
        StringBuilder masterChangeLog = new StringBuilder();
        if (param.isNew()){
            // 再添加新的类型
            masterChangeLog.append(repairPlanMasterTypeService.saveBatchByParam(param));
        } else {
            // 查询现有的方案类型
            LambdaQueryWrapper<RepairPlanMasterType> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RepairPlanMasterType::getMasterId, param.getMasterId());
            List<RepairPlanMasterType> existingTypes = repairPlanMasterTypeService.list(queryWrapper);

            // 智能对比现有数据和传入数据
            boolean isDataChanged = isRepairPlanTypeDataChanged(existingTypes, param.getPlanTypeList());

            if (isDataChanged) {
                // 数据有变化，执行删除和新增操作
                if (CollUtil.isNotEmpty(existingTypes)) {
                    List<String> deletedTypeNames = new ArrayList<>();
                    List<Integer> existingTypeIds = new ArrayList<>();
                    for (RepairPlanMasterType existingType : existingTypes) {
                        // 获取方案类型名称
                        String typeName = PlanTypeEnum.getDescByCode(existingType.getPlanType());
                        deletedTypeNames.add(typeName);
                        existingTypeIds.add(existingType.getId());
                    }
                    // 使用 lambdaUpdate 批量更新
                    repairPlanMasterTypeService.lambdaUpdate()
                            .in(RepairPlanMasterType::getId, existingTypeIds)
                            .set(RepairPlanMasterType::getIsDel, NumberConstant.ONE)
                            .update();
                    masterChangeLog.append("删除原有方案类型[").append(String.join("、", deletedTypeNames)).append("]；");
                }

                // 添加新的类型
                masterChangeLog.append(repairPlanMasterTypeService.saveBatchByParam(param));

                // 记录日志
                if (masterChangeLog.length() > 0) {
                    shouhouService.saveShouhouLog(param.getShouHouId(), "修改维修方案主表信息：" + masterChangeLog.toString(), param.getCurrentUser());
                }
            } else {
                // 数据没有变化，不执行任何操作
                log.info("维修方案类型数据未发生变化，跳过删除和新增操作，masterId：{}", param.getMasterId());
            }
        }
    }

    /**
     * 智能对比维修方案类型数据是否发生变化
     *
     * @param existingTypes 现有的方案类型数据
     * @param newTypeList 传入的新方案类型数据
     * @return true-数据有变化，false-数据无变化
     */
    private boolean isRepairPlanTypeDataChanged(List<RepairPlanMasterType> existingTypes, List<RepairPlanMasterTypeVO> newTypeList) {
        // 如果现有数据为空，新数据不为空，则有变化
        if (CollUtil.isEmpty(existingTypes) && CollUtil.isNotEmpty(newTypeList)) {
            return true;
        }

        // 如果现有数据不为空，新数据为空，则有变化
        if (CollUtil.isNotEmpty(existingTypes) && CollUtil.isEmpty(newTypeList)) {
            return true;
        }

        // 如果都为空，则无变化
        if (CollUtil.isEmpty(existingTypes) && CollUtil.isEmpty(newTypeList)) {
            return false;
        }

        // 过滤出有效的新数据（只考虑有ID或者planType不为空的数据）
        List<RepairPlanMasterTypeVO> validNewTypes = newTypeList.stream()
                .filter(vo -> ObjectUtil.isNotNull(vo.getPlanType()))
                .collect(Collectors.toList());

        // 如果有效数据数量不同，则有变化
        if (existingTypes.size() != validNewTypes.size()) {
            return true;
        }

        // 将现有数据转换为Map，以planType为key
        Map<Integer, RepairPlanMasterType> existingMap = existingTypes.stream()
                .collect(Collectors.toMap(RepairPlanMasterType::getPlanType, Function.identity(), (o1, o2) -> o1));

        // 逐一对比每个新数据项
        for (RepairPlanMasterTypeVO newType : validNewTypes) {
            RepairPlanMasterType existingType = existingMap.get(newType.getPlanType());

            // 如果现有数据中没有对应的planType，则有变化
            if (existingType == null) {
                return true;
            }

            // 对比isGenerateAccessory字段
            Integer existingIsGenerate = Optional.ofNullable(existingType.getIsGenerateAccessory()).orElse(NumberConstant.ZERO);
            Integer newIsGenerate = Optional.ofNullable(newType.getIsGenerateAccessory()).orElse(NumberConstant.ZERO);

            if (!ObjectUtil.equals(existingIsGenerate, newIsGenerate)) {
                return true;
            }
        }

        // 所有对比都通过，数据无变化
        return false;
    }

    /**
     * 将isRequired字段转换为中文显示
     *
     * @param isRequired 是否必填字段值
     * @return 中文描述
     */
    private String convertIsRequiredToText(Integer isRequired) {
        return NumberConstant.ONE.equals(isRequired) ? "是" : "否";
    }



    /**
     * 获取现有的故障和方案数据
     * 
     * @param masterId 主表ID
     * @param param 现有数据参数
     */
    private void fetchExistingData(Integer masterId, ExistingDataParam param) {
        // 获取现有故障
        LambdaQueryWrapper<RepairFault> faultQuery = new LambdaQueryWrapper<>();
        faultQuery.eq(RepairFault::getMasterId, masterId);
        List<RepairFault> existingFaults = repairFaultService.list(faultQuery);
        
        // 填充故障Map
        existingFaults.forEach(fault -> param.getExistingFaultMap().put(fault.getId(), fault));
        
        // 获取现有方案
        if (!existingFaults.isEmpty()) {
            List<Integer> faultIds = existingFaults.stream().map(RepairFault::getId).collect(Collectors.toList());
            LambdaQueryWrapper<RepairPlan> planQuery = new LambdaQueryWrapper<>();
            planQuery.in(RepairPlan::getFaultId, faultIds);
            List<RepairPlan> existingPlans = repairPlanService.list(planQuery);
            
            // 按故障ID和方案ID进行分组
            param.getExistingPlanMap().putAll(existingPlans.stream()
                    .collect(Collectors.groupingBy(
                            RepairPlan::getFaultId,
                            Collectors.toMap(RepairPlan::getId, plan -> plan, (o1, o2) -> o1)
                    )));
        }
    }
    
    /**
     * 处理故障和方案信息
     * 
     * @param saveDTO 保存DTO
     * @param master 主表对象
     * @param param 故障方案参数
     */
    private void processFaultsAndPlans(RepairPlanSaveDTO saveDTO, RepairPlanMaster master,
            FaultPlanParam param) {
        
        for (RepairFaultDTO faultDTO : saveDTO.getFaultList()) {
            RepairFault fault;
            boolean isFaultNew = ObjectUtil.isNull(faultDTO.getId()) || NumberConstant.ZERO.equals(faultDTO.getId());
            if (isFaultNew) {
                // 新增故障
                FaultParam faultParam = new FaultParam(param.getCurrentUser(), param.getNow(), 
                        param.getShouHouId(), param.getExistingFaultMap());
                fault = handleNewFault(master.getId(), faultDTO, faultParam);
            } else {
                // 修改故障
                FaultParam faultParam = new FaultParam(param.getCurrentUser(), param.getNow(), 
                        param.getShouHouId(), param.getExistingFaultMap());
                fault = handleExistingFault(faultDTO,faultParam);
                param.getProcessedFaultIds().add(fault.getId());
            }
            // 处理该故障下的所有方案
            handlePlansForFault(faultDTO, fault, param);
        }
    }
    
    /**
     * 处理新增故障
     * 
     * @param masterId 主表ID
     * @param faultDTO 故障DTO
     * @param index 索引
     * @param param 故障参数
     * @return 故障对象
     */
    private RepairFault handleNewFault(Integer masterId, RepairFaultDTO faultDTO,FaultParam param) {
        RepairFault fault = new RepairFault();
        fault.setMasterId(masterId);
        fault.setFaultName(faultDTO.getFaultName());
        fault.setIsRequired(faultDTO.getIsRequired());
        fault.setCreateUser(param.getCurrentUser());
        fault.setCreateTime(param.getNow());

        
        // 保存故障，获取ID
        repairFaultService.save(fault);
        
        // 记录新增故障的日志
        shouhouService.saveShouhouLog(param.getShouHouId(), "新增维修故障：" + fault.getFaultName(), param.getCurrentUser());
        
        return fault;
    }
    
    /**
     * 处理已存在的故障
     * 
     * @param faultDTO 故障DTO
     * @param index 索引
     * @param param 故障参数
     * @return 故障对象
     */
    private RepairFault handleExistingFault(RepairFaultDTO faultDTO, FaultParam param) {
        RepairFault fault = param.getExistingFaultMap().get(faultDTO.getId());
        
        boolean faultChanged = false;
        StringBuilder faultChangeLog = new StringBuilder();
        // 使用LambdaUpdate只更新真正变动的字段
        LambdaUpdateWrapper<RepairFault> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RepairFault::getId, fault.getId());
        // 检查故障名称是否变化
        if (!ObjectUtil.equals(fault.getFaultName(), faultDTO.getFaultName())) {
            faultChangeLog.append("故障名称由[").append(fault.getFaultName()).append("]变更为[").append(faultDTO.getFaultName()).append("]；");
            updateWrapper.set(RepairFault::getFaultName, fault.getFaultName());
            fault.setFaultName(faultDTO.getFaultName());
            faultChanged = true;
        }
        
        // 检查是否必填是否变化
        if (!ObjectUtil.equals(fault.getIsRequired(), faultDTO.getIsRequired())) {
            if (faultChangeLog.length() > 0) {
                faultChangeLog.append(" ");
            }
            String oldIsRequiredText = convertIsRequiredToText(fault.getIsRequired());
            String newIsRequiredText = convertIsRequiredToText(faultDTO.getIsRequired());
            faultChangeLog.append("是否必填由[").append(oldIsRequiredText).append("]变更为[").append(newIsRequiredText).append("]；");
            updateWrapper.set(RepairFault::getIsRequired, faultDTO.getIsRequired());
            fault.setIsRequired(faultDTO.getIsRequired());
            faultChanged = true;
        }
        // 如果故障有变化，只更新变动的字段并记录日志
        if (faultChanged) {
            // 更新时间和操作人
            updateWrapper.set(RepairFault::getUpdateUser, param.getCurrentUser());
            updateWrapper.set(RepairFault::getUpdateTime, param.getNow());
            boolean updated = repairFaultService.update(updateWrapper);
            if (!updated) {
                throw new RuntimeException("更新故障信息失败，ID：" + fault.getId());
            }
            // 记录修改故障的日志
            shouhouService.saveShouhouLog(param.getShouHouId(), "修改维修故障[" + fault.getFaultName() + "]：" + faultChangeLog.toString(), param.getCurrentUser());
        }
        return fault;
    }
    
    /**
     * 处理故障下的方案
     * 
     * @param faultDTO 故障DTO
     * @param fault 故障对象
     * @param param 故障方案参数
     */
    private void handlePlansForFault(RepairFaultDTO faultDTO, RepairFault fault, FaultPlanParam param) {
        
        for (RepairPlanDTO planDTO:  faultDTO.getPlanList()) {
            // 获取当前故障下的所有已有方案
            Map<Integer, RepairPlan> faultPlanMap = param.getExistingPlanMap().getOrDefault(fault.getId(), new HashMap<>());
            boolean isPlanNew = ObjectUtil.isNull(planDTO.getId()) || NumberConstant.ZERO.equals(planDTO.getId());
            if (isPlanNew) {
                // 新增方案
                PlanParam planParam = new PlanParam(param.getCurrentUser(), param.getNow(), 
                        param.getShouHouId(), faultPlanMap, param.getProcessedPlanIds());
                handleNewPlan(planDTO, fault, planParam);
            } else {
                // 修改方案
                PlanParam planParam = new PlanParam(param.getCurrentUser(), param.getNow(), 
                        param.getShouHouId(), faultPlanMap, param.getProcessedPlanIds());
                RepairPlan plan = handleExistingPlan(planDTO, planParam);
                param.getProcessedPlanIds().add(plan.getId());
            }
        }
    }
    
    /**
     * 处理新增方案
     * 
     * @param planDTO 方案DTO
     * @param fault 故障对象
     * @param index 索引
     * @param param 方案参数
     */
    private void handleNewPlan(RepairPlanDTO planDTO, RepairFault fault, PlanParam param) {
        RepairPlan plan = new RepairPlan();
        plan.setFaultId(fault.getId());
        plan.setPlanName(planDTO.getPlanName());
        plan.setAccessoryType(planDTO.getAccessoryType());
        plan.setAccessoryPpid(planDTO.getAccessoryPpid());
        plan.setAccessoryName(planDTO.getAccessoryName());
        plan.setPlanName(planDTO.getPlanName());
        plan.setPrice(planDTO.getPrice());
        plan.setCreateUser(param.getCurrentUser());
        plan.setCreateTime(param.getNow());

        
        // 单条记录新增方案的日志
        shouhouService.saveShouhouLog(param.getShouHouId(), "新增维修方案选项：" + plan.getPlanName() + 
                (plan.getPrice() != null ? "，价格：" + plan.getPrice() : ""), param.getCurrentUser());
        //如果在新增的时候添加配件，则需要添加配件
        if(Boolean.TRUE.equals(planDTO.getIsGenerateAccessory())){
            List<CorrelationInfo> correlationInfos = djiRepairStrategy.addWxFeeBos(Collections.singletonList(plan), param.getShouHouId());
            if(CollUtil.isEmpty(correlationInfos)){
                throw new CustomizeException(plan.getPlanName()+"添加维修配件数据异常");
            }
            CorrelationInfo correlationInfo = correlationInfos.get(NumberConstant.ZERO);
            plan.setCorrelationId(correlationInfo.getCorrelationId())
                    .setConfirmSource(ConfirmSourceEnum.STORE_EMPLOYEE.getCode())
                    .setCorrelationType(correlationInfo.getCorrelationType());
        }
        repairPlanService.save(plan);
    }
    
    /**
     * 处理已存在的方案
     * 
     * @param planDTO 方案DTO
     * @param index 索引
     * @param param 方案参数
     */
    private RepairPlan handleExistingPlan(RepairPlanDTO planDTO, PlanParam param) {
        RepairPlan plan = param.getFaultPlanMap().get(planDTO.getId());

        
        boolean planChanged = false;
        StringBuilder planChangeLog = new StringBuilder();
        
        // 使用LambdaUpdate只更新真正变动的字段
        LambdaUpdateWrapper<RepairPlan> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RepairPlan::getId, plan.getId());
        
        // 检查方案名称是否变化
        if (!ObjectUtil.equals(plan.getPlanName(), planDTO.getPlanName())) {
            planChangeLog.append("方案名称由[").append(plan.getPlanName()).append("]变更为[").append(planDTO.getPlanName()).append("]；");
            updateWrapper.set(RepairPlan::getPlanName, planDTO.getPlanName());
            plan.setPlanName(planDTO.getPlanName());
            planChanged = true;
        }
        
        // 标记是否已经清空了配件ID
        boolean accessoryPpidCleared = false;

        // 检查关联配件类型是否变化
        if (!ObjectUtil.equals(plan.getAccessoryType(), planDTO.getAccessoryType())) {
            planChangeLog.append("关联配件类型由[").append(AccessoryTypeEnum.getDescByCode(plan.getAccessoryType())).append("]变更为[").append(AccessoryTypeEnum.getDescByCode(planDTO.getAccessoryType())).append("]；");
            updateWrapper.set(RepairPlan::getAccessoryType, planDTO.getAccessoryType());
            // 只有从修改为 维修配件 修改为 维修成本的时候才需要清空配件ID
            if (AccessoryTypeEnum.REPAIR_ACCESSORY.getCode().equals(plan.getAccessoryType())
                && AccessoryTypeEnum.REPAIR_COST.getCode().equals(planDTO.getAccessoryType())) {
                updateWrapper.set(RepairPlan::getAccessoryPpid, null);
                accessoryPpidCleared = true;
            }
            repairPlanService.cancelCorrelationInfo(plan.getId());
            plan.setAccessoryType(planDTO.getAccessoryType());
            planChanged = true;
        }

        // 检查关联配件ID是否变化（只有在没有清空配件ID的情况下才执行）
        if (!accessoryPpidCleared && !ObjectUtil.equals(plan.getAccessoryPpid(), planDTO.getAccessoryPpid())) {
            planChangeLog.append("关联配件由[").append(plan.getAccessoryPpid()).append("]变更为[").append(planDTO.getAccessoryPpid()).append("]；");
            updateWrapper.set(RepairPlan::getAccessoryPpid, planDTO.getAccessoryPpid());
            plan.setAccessoryPpid(planDTO.getAccessoryPpid());
            planChanged = true;
        }

        // 检查关联配件ID是否变化
        if (!ObjectUtil.equals(plan.getAccessoryName(), planDTO.getAccessoryName())) {
            planChangeLog.append("关联配件由[").append(plan.getAccessoryName()).append("]变更为[").append(planDTO.getAccessoryName()).append("]；");
            updateWrapper.set(RepairPlan::getAccessoryName, planDTO.getAccessoryName());
            plan.setAccessoryName(planDTO.getAccessoryName());
            planChanged = true;
        }
        
        // 检查价格是否变化
        if (!ObjectUtil.equals(plan.getPrice(), planDTO.getPrice())) {
            planChangeLog.append("价格由[").append(plan.getPrice()).append("]变更为[").append(planDTO.getPrice()).append("]；");
            updateWrapper.set(RepairPlan::getPrice, planDTO.getPrice());
            plan.setPrice(planDTO.getPrice());
            planChanged = true;
        }
        
        // 检查isGenerateAccessory与wxKcOutPutId的对应关系变化
        // wxKcOutPutId有值对应isGenerateAccessory为true，wxKcOutPutId无值对应isGenerateAccessory为false或null
        boolean currentIsGenerate = ObjectUtil.isNotNull(plan.getCorrelationId());
        boolean newIsGenerate = Boolean.TRUE.equals(planDTO.getIsGenerateAccessory());
        if (currentIsGenerate != newIsGenerate) {
            if (!currentIsGenerate && newIsGenerate) {
                // isGenerateAccessory由false改为true，添加维修配件
                List<CorrelationInfo> correlationInfos = djiRepairStrategy.addWxFeeBos(Collections.singletonList(plan), param.getShouHouId());
                if(CollUtil.isEmpty(correlationInfos)){
                    throw new CustomizeException(plan.getPlanName()+"添加维修配件数据异常");
                }
                CorrelationInfo correlationInfo = Optional.ofNullable(correlationInfos.get(NumberConstant.ZERO)).orElseThrow(() -> new CustomizeException(plan.getPlanName() + "维修配件获取id异常"));
                Integer correlationId = correlationInfo.getCorrelationId();
                updateWrapper.set(RepairPlan::getCorrelationId,correlationId);
                updateWrapper.set(RepairPlan::getConfirmSource,ConfirmSourceEnum.STORE_EMPLOYEE.getCode());
                updateWrapper.set(RepairPlan::getCorrelationType, correlationInfo.getCorrelationType());
                plan.setCorrelationId(correlationId);
                planChangeLog.append(plan.getPlanName()).append("由不生成配件变更为生成配件；");
            } else if (currentIsGenerate && !newIsGenerate) {
                // isGenerateAccessory由true改为false，进行维修配件撤销
                repairPlanService.cancelCorrelationInfo(plan.getId());
                planChangeLog.append(plan.getPlanName()).append("由生成配件变更为不生成配件，已撤销配件；");
            }
            planChanged = true;
        }
        // 如果方案有变化，只更新变动的字段并记录日志
        if (planChanged) {
            // 更新时间和操作人
            updateWrapper.set(RepairPlan::getUpdateUser, param.getCurrentUser());
            updateWrapper.set(RepairPlan::getUpdateTime, param.getNow());
            boolean updated = repairPlanService.update(updateWrapper);
            if (!updated) {
                throw new RuntimeException("更新维修方案选项失败，ID：" + plan.getId());
            }
            // 记录修改方案的日志
            shouhouService.saveShouhouLog(param.getShouHouId(), "修改维修方案选项[" + plan.getPlanName() + "]：" + planChangeLog.toString(), param.getCurrentUser());
        }
        return plan;
    }
    
    /**
     * 处理需要删除的故障和方案
     * 
     * @param param 删除参数
     * @param masterId 主表ID
     */
    private void handleDeletions(DeletionParam param) {
        
        // 找出需要删除的故障（未被处理的故障）
        List<Integer> faultsToDelete = param.getExistingFaultMap().keySet().stream()
                .filter(id -> !param.getProcessedFaultIds().contains(id))
                .collect(Collectors.toList());
        
        // 删除故障及其方案
        for (Integer faultId : faultsToDelete) {
            deleteFaultAndItsPlans(faultId, param);
        }
        
        // 删除未处理的方案（故障未被删除）
        for (Integer faultId : param.getProcessedFaultIds()) {
            deleteUnprocessedPlans(faultId, param);
        }
    }
    
    /**
     * 删除故障及其方案
     * 
     * @param faultId 故障ID
     * @param param 删除参数
     */
    private void deleteFaultAndItsPlans(Integer faultId, DeletionParam param) {
        RepairFault fault = param.getExistingFaultMap().get(faultId);
        
        // 逻辑删除故障下的所有方案
        LambdaQueryWrapper<RepairPlan> planWrapper = new LambdaQueryWrapper<>();
        planWrapper.eq(RepairPlan::getFaultId, faultId);
        List<RepairPlan> plansToDelete = repairPlanService.list(planWrapper);
        if (!plansToDelete.isEmpty()) {
            // 记录删除方案的日志
            for (RepairPlan plan : plansToDelete) {
                shouhouService.saveShouhouLog(param.getShouHouId(), "删除维修方案选项：" + plan.getPlanName(), param.getCurrentUser());
                repairPlanService.cancelCorrelationInfo(plan.getId());
                boolean updated = repairPlanService.lambdaUpdate().eq(RepairPlan::getId, plan.getId())
                        .set(RepairPlan::getIsDel, NumberConstant.ONE)
                        .set(RepairPlan::getUpdateUser, param.getCurrentUser())
                        .set(RepairPlan::getUpdateTime, param.getNow()).update();
                // 逻辑删除方案 - 只更新必要字段
                if (!updated) {
                    throw new RuntimeException("逻辑删除方案失败，ID：" + plan.getId());
                }
            }
            // 批量逻辑删除方案
            RepairPlan updatePlan = new RepairPlan();
            updatePlan.setIsDel(NumberConstant.ONE);
            updatePlan.setUpdateUser(param.getCurrentUser());
            updatePlan.setUpdateTime(param.getNow());
            repairPlanService.update(updatePlan, planWrapper);

        }
        
        // 记录删除故障的日志
        shouhouService.saveShouhouLog(param.getShouHouId(), "删除维修故障：" + fault.getFaultName(), param.getCurrentUser());
        
        // 逻辑删除故障 - 只更新必要字段
        LambdaUpdateWrapper<RepairFault> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RepairFault::getId, fault.getId())
            .set(RepairFault::getIsDel, NumberConstant.ONE)
            .set(RepairFault::getUpdateUser, param.getCurrentUser())
            .set(RepairFault::getUpdateTime, param.getNow());
            
        boolean updated = repairFaultService.update(updateWrapper);
            
        if (!updated) {
            throw new RuntimeException("逻辑删除故障失败，ID：" + fault.getId());
        }
    }
    
    /**
     * 删除未处理的方案
     * 
     * @param faultId 故障ID
     * @param param 删除参数
     */
    private void deleteUnprocessedPlans(Integer faultId, DeletionParam param) {
        Map<Integer, RepairPlan> planMap = param.getExistingPlanMap().getOrDefault(faultId, new HashMap<>());
        List<Integer> plansToDelete = planMap.keySet().stream()
                .filter(id -> !param.getProcessedPlanIds().contains(id))
                .collect(Collectors.toList());
        
        // 逻辑删除方案
        for (Integer planId : plansToDelete) {
            RepairPlan plan = planMap.get(planId);
            
            // 记录删除方案的日志
            shouhouService.saveShouhouLog(param.getShouHouId(), "删除维修方案选项：" + plan.getPlanName(), param.getCurrentUser());
            
            // 逻辑删除方案 - 只更新必要字段
            LambdaUpdateWrapper<RepairPlan> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(RepairPlan::getId, plan.getId())
                .set(RepairPlan::getIsDel, NumberConstant.ONE)
                .set(RepairPlan::getUpdateUser, param.getCurrentUser())
                .set(RepairPlan::getUpdateTime, param.getNow());
                
            boolean updated = repairPlanService.update(updateWrapper);
                
            if (!updated) {
                throw new RuntimeException("逻辑删除方案失败，ID：" + plan.getId());
            }
            repairPlanService.cancelCorrelationInfo(plan.getId());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer confirmRepairPlan(RepairPlanConfirmReq req) {
        Integer shouHouId = req.getShouHouId();
        Integer repairPlanMasterTypeId = req.getRepairPlanMasterTypeId();
        String confirmUser = req.getConfirmUser();
        // 查询主表，直接根据shouHouId查询，无需通过故障ID间接查找
        List<RepairPlanMaster> list = this.lambdaQuery().eq(RepairPlanMaster::getShouHouId, shouHouId).list();
        if (CollUtil.isEmpty(list)) {
            throw new CustomizeException("未找到维修方案主表信息");
        }
        RepairPlanMaster master = list.get(NumberConstant.ZERO);
        if(RepairPlanMasterStatusEnum.USER_CONFIRMATION.getCode().equals(master.getStatus())){
            throw new CustomizeException("用户确认状态不允许确认");
        }
        // 使用策略模式处理不同类型的维修方案确认
        RepairPlanConfirmStrategy strategy = repairPlanConfirmStrategyFactory.getStrategy(repairPlanMasterTypeId);
        return strategy.confirm(master, req);
    }
}