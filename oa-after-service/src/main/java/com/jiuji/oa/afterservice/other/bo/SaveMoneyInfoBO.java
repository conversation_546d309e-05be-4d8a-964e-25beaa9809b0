package com.jiuji.oa.afterservice.other.bo;

import com.alibaba.fastjson.annotation.JSONType;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * description: <操作余额接口参数模型>
 * translation: <Operating balance interface parameter model>
 *
 * <AUTHOR>
 * @date 2020/3/24
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JSONType(orders = {"userid", "amount", "subid", "memo", "ekind", "areaid"})
public class SaveMoneyInfoBO {
    /**
     * 客户Id BBSXP_USER_ID
     */
    private Integer userid;
    /**
     * 金额
     */
    private Double amount;
    /**
     * 订单Id
     */
    private Integer subid;
    /**
     * 备注信息
     */
    private String memo;
    /**
     * 操作类别
     *
     * @see com.jiuji.oa.afterservice.other.enums.SaveMoneyEkindEnum
     */
    private Integer ekind;

    /**
     * 所属门店：华腾等包含财务系统的租户，需要传入。进行做账处理
     */
    private Integer areaid;

    /**
     * 退款订单类型：1批量退，2组合退
     * @see RefundOrderTypeEnum
     */
    private Integer refundOrderType;

    /**
     * 退款订单号：组合退传shouhou_tuihuan_detail的主键ID
     */
    private Integer refundOrderId;

    /**
     * 退款订单类型枚举
     * Enum for Refund Order Types
     *
     * <AUTHOR>
     * @date 2019/11/13
     * @since 1.0.0
     */
    @AllArgsConstructor
    @Getter
    public enum RefundOrderTypeEnum implements CodeMessageEnumInterface {

        BATCH_REFUND(1, "批量退"),
        COMBINED_REFUND(2, "组合退");

        private final Integer code;
        private final String message;
    }
}
