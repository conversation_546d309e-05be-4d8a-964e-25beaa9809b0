package com.jiuji.oa.afterservice.smallpro.service.smallpro.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.cloud.after.vo.req.SmallExchangeReq;
import com.jiuji.oa.afterservice.bigpro.entity.Productbarcode;
import com.jiuji.oa.afterservice.bigpro.po.SmallConfigProductInfoPo;
import com.jiuji.oa.afterservice.bigpro.service.ProductbarcodeService;
import com.jiuji.oa.afterservice.bigpro.statistics.bo.BrandCategoryBo;
import com.jiuji.oa.afterservice.bigpro.statistics.service.BrandCategoryService;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.res.ShouhouInventoryEnumRes;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.CategoryConstants;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RequestCacheKeys;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.shouhou.vo.req.SelectConfigToWebReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.SelectConfigToWebRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.SmallExchangeConfigRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.SmallExchangeProductConfigRes;
import com.jiuji.oa.afterservice.smallpro.bo.SmallExchangeConfigContrastBO;
import com.jiuji.oa.afterservice.smallpro.bo.SmallRefundConfigContrastBO;
import com.jiuji.oa.afterservice.smallpro.dao.SmallProConfigMapper;
import com.jiuji.oa.afterservice.smallpro.enums.ExchangeGoodsEnums;
import com.jiuji.oa.afterservice.smallpro.enums.RefundGoodsEnums;
import com.jiuji.oa.afterservice.smallpro.enums.SmallRefundServiceTypeEnum;
import com.jiuji.oa.afterservice.smallpro.enums.SmallExchangeProductConfigKindEnum;
import com.jiuji.oa.afterservice.smallpro.mapstruct.SmallExchangeConfigMapStruct;
import com.jiuji.oa.afterservice.smallpro.po.exchange.ProductExchangePo;
import com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeConfigPo;
import com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeProductConfigPo;
import com.jiuji.oa.afterservice.smallpro.po.refund.ProductRefundPo;
import com.jiuji.oa.afterservice.smallpro.po.refund.SmallRefundConfigPo;
import com.jiuji.oa.afterservice.smallpro.po.refund.SmallRefundProductConfigPo;
import com.jiuji.oa.afterservice.smallpro.repository.SmallExchangeConfigLogService;
import com.jiuji.oa.afterservice.smallpro.repository.SmallRefundConfigLogService;
import com.jiuji.oa.afterservice.smallpro.repository.document.SmallExchangeConfigLogDocument;
import com.jiuji.oa.afterservice.smallpro.repository.document.SmallRefundConfigLogDocument;
import com.jiuji.oa.afterservice.smallpro.service.smallpro.SmallProConfigService;
import com.jiuji.oa.afterservice.smallpro.vo.req.*;
import com.jiuji.oa.afterservice.smallpro.vo.res.ExchangeGoodsConfigProductRes;
import com.jiuji.oa.afterservice.smallpro.vo.res.ExchangeGoodsConfigRes;
import com.jiuji.oa.afterservice.smallpro.vo.res.ExchangeGoodsConfigResV2;
import com.jiuji.oa.afterservice.smallpro.vo.res.RefundGoodsConfigRes;
import com.jiuji.oa.afterservice.sub.po.Category;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.WorkLogUtil;
import com.jiuji.tc.utils.constants.IntConstant;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.NonNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 小件配置服务类
 * <AUTHOR>
 * @since 2022/4/28 10:14
 */
@Service
public class SmallProConfigServiceImpl  implements SmallProConfigService {
    @Resource
    private SmallExchangeConfigLogService smallExchangeConfigLogService;
    @Resource
    private SmallRefundConfigLogService smallRefundConfigLogService;
    @Resource
    private SmallProConfigMapper smallProConfigMapper;
    @Resource
    private BrandCategoryService brandCategoryService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private SmallExchangeConfigMapStruct smallExchangeConfigMapStruct;


    @Override
    public SelectConfigToWebRes selectConfigToWeb(SelectConfigToWebReq req) {
        SelectConfigToWebRes result =new SelectConfigToWebRes();
        //查询配置列表
        List<SmallExchangeConfigRes> smallExchangeConfigRes = smallProConfigMapper.selectConfigToWeb(req);
        if(CollectionUtils.isEmpty(smallExchangeConfigRes)){
            return result;
        }
        result.setSmallExchangeConfigResList(smallExchangeConfigRes);
        List<Integer> configId = smallExchangeConfigRes.stream().map(SmallExchangeConfigRes::getId).collect(Collectors.toList());
        List<SmallExchangeProductConfigRes> productConfigRes = CommonUtils.bigDataInQuery(configId, ids -> smallProConfigMapper.selectConfigProductByIds(ids));
        if(CollectionUtils.isEmpty(productConfigRes)){
            return result;
        }
        Map<Integer, List<SmallExchangeProductConfigRes>> map = productConfigRes.stream().collect(Collectors.groupingBy(SmallExchangeProductConfigRes::getFkConfigId));
        //smallExchangeConfigRes.forEach(config -> config.setProductConfigList(map.get(config.getId())));
        smallExchangeConfigRes.forEach(config -> {
            List<SmallExchangeProductConfigRes> smallExchangeProductConfigRes = map.get(config.getId());
            if (CollUtil.isNotEmpty(smallExchangeProductConfigRes)) {
                List<SmallExchangeProductConfigRes> productConfigList = smallExchangeProductConfigRes.stream().filter(v -> SmallExchangeProductConfigKindEnum.PRODUCT_CONFIG.getCode().equals(v.getKind())).collect(Collectors.toList());
                List<SmallExchangeProductConfigRes> additionProductConfigList = smallExchangeProductConfigRes.stream().filter(v -> SmallExchangeProductConfigKindEnum.ADDITION_PRODUCT_CONFIG.getCode().equals(v.getKind())).collect(Collectors.toList());
                config.setProductConfigList(productConfigList);
                config.setAdditionProductConfigList(additionProductConfigList);
            }
        });

        return result;
    }

    @Override
    public SmallExchangeConfigPo getConfigById(Integer configId) {
        return smallProConfigMapper.getConfigById(configId);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public SmallExchangeConfigPo getConfig(SmallExchangeConfigPo.ProductConfigTypeEnum configType, Integer serviceType,
                                           List<SmallExchangeConfigPo.ConfigTypeAndValue> configTypeAndValues, Long wordKey, String key) {
        if(CollUtil.isEmpty(configTypeAndValues) || configType == null){
            return null;
        }
        Set<Integer> ppids = listPpidByBarCode(wordKey, key);
        if (Convert.toInt(wordKey) != null) {
            ppids.add(Convert.toInt(wordKey));
        }
        return smallProConfigMapper.getConfig(configType.getCode(),ObjectUtil.defaultIfNull(serviceType,0),
                configTypeAndValues, wordKey, key, ppids);
    }

    @Override
    public @NonNull Set<Integer> listPpidByBarCode(Long wordKey, String key) {
        return SpringContextUtil.reqCache(() -> {
            List<Integer> ppids = new LinkedList<>();

            if (ObjectUtil.defaultIfNull(wordKey, 0L) > 0 && StrUtil.length(key) >= 8) {
                // 先批量查询出ppid
                SpringUtil.getBean(ProductbarcodeService.class).lambdaQuery().likeLeft(Productbarcode::getBarCode, key)
                        .select(Productbarcode::getId, Productbarcode::getPpriceid).list().stream()
                        .map(Productbarcode::getPpriceid).forEach(ppids::add);
            }
            return ppids.stream().collect(Collectors.toSet());
        }, RequestCacheKeys.CONFIG_SERVICE_LIST_PPID_BY_BAR_CODE, wordKey, key);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public SmallExchangeConfigPo getConfigForWeb(SmallExchangeConfigPo.ProductConfigTypeEnum configType, Integer serviceType,
                                                 List<SmallExchangeConfigPo.ConfigTypeAndValue> configTypeAndValues, String webKeyword) {
        if (CollUtil.isEmpty(configTypeAndValues) || configType == null) {
            return null;
        }
        return smallProConfigMapper.getConfig(configType.getCode(), ObjectUtil.defaultIfNull(serviceType, 0),
                configTypeAndValues, null, webKeyword, null);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public List<SmallExchangeProductConfigPo> listConfigProduct(Integer configId) {
        if(Objects.equals(configId, SmallProConfigService.ADVANCE_RANK_CONFIG_ID)){
            //必须为手机配件才可以进行高级权限选择
            return SpringUtil.getBean(CategoryService.class).getProductChildCidList(Collections.singletonList(CategoryConstants.MOBILE_PJ)).stream()
                    .map(cid -> new SmallExchangeProductConfigPo()
                            .setConfigType(SmallExchangeProductConfigPo.ConfigTypeEnum.CATEGORY.getCode()).setFkConfigId(SmallProConfigService.ADVANCE_RANK_CONFIG_ID)
                    .setConfigValue(cid).setIsDel(Boolean.FALSE))
                    .collect(Collectors.toList());
        }
        if(configId == null || configId<1){
            return Collections.emptyList();
        }
        return smallProConfigMapper.listConfigProduct(configId);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public List<ProductExchangePo> listSmallProductInfo(SmallConfigProductInfoPo smallConfigProductInfo, Long wordKey, String key) {
        Set<Integer> ppids = listPpidByBarCode(wordKey, key);
        if (Convert.toInt(wordKey) != null) {
            ppids.add(Convert.toInt(wordKey));
        }
        return smallProConfigMapper.listSmallProductInfo(smallConfigProductInfo, wordKey, key, ppids);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public List<ProductExchangePo> listSmallProductInfo(SmallConfigProductInfoPo smallConfigProductInfo, SmallExchangeReq req) {
        return smallProConfigMapper.listSmallProductInfoForWeb(smallConfigProductInfo, req);
    }

    @Override
    public int countSmallProductInfo(SmallConfigProductInfoPo smallConfigProductInfo, SmallExchangeReq req) {
        Integer count = smallProConfigMapper.countSmallProductInfoForWeb(smallConfigProductInfo, req);
        if (Objects.isNull(count)) {

            return IntConstant.ZERO;
        }
        return count;
    }

    /**
     * 查询小件换货配置列表
     * @param req req
     * @return 小件换货配置列表
     */
    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public R<Page<ExchangeGoodsConfigRes>> getExchangeGoodsConfigInfo(ExchangeGoodsConfigReq req) {
        //查询当前配置信息
        Page<ExchangeGoodsConfigReq> page = new Page<>(req.getCurrent(), req.getSize());
        page.setDesc("sec.update_time");
        Page<ExchangeGoodsConfigRes> exchangeGoodsConfigResPage = smallProConfigMapper.getConfigList(page, req);
        List<ExchangeGoodsConfigRes> records = exchangeGoodsConfigResPage.getRecords();
        //获取到配置的ID 查询商品配置表数据
        List<Integer> exchangeGoodsConfigIds = records.stream().map(ExchangeGoodsConfigRes::getId).filter(Objects::nonNull).collect(Collectors.toList());
        //查询商品表配置 最大一千条in 不需要分批查询
        List<SmallExchangeProductConfigPo> smallExchangeProductConfigPos =new ArrayList<>();
        if (CollUtil.isNotEmpty(exchangeGoodsConfigIds)){
            smallExchangeProductConfigPos = smallProConfigMapper.listConfigProductByIds(exchangeGoodsConfigIds);
        }
        //进行数据汇总
        Map<Integer, List<SmallExchangeProductConfigPo>> smallExchangeProductConfigMap = smallExchangeProductConfigPos.stream()
                .collect(Collectors.groupingBy(SmallExchangeProductConfigPo::getFkConfigId));
        for (Map.Entry<Integer, List<SmallExchangeProductConfigPo>> integerListEntry : smallExchangeProductConfigMap.entrySet()) {
            for (ExchangeGoodsConfigRes item : records) {
                if (Objects.equals(item.getId(),integerListEntry.getKey())){
                    List<ExchangeGoodsConfigRes.ExchangeGoodsConfigValueBO> exchangeGoodsConfigValueBOList = new ArrayList<>();
                    //根据配置类型来判断 并解析
                    List<Integer> brandList = integerListEntry.getValue().stream()
                            .filter(in -> Objects.equals(in.getConfigType(), SmallExchangeProductConfigPo.ConfigTypeEnum.BRAND.getCode()))
                            .map(SmallExchangeProductConfigPo::getConfigValue).filter(Objects::nonNull).collect(Collectors.toList());
                    List<Integer> categoryList = integerListEntry.getValue().stream()
                            .filter(in -> Objects.equals(in.getConfigType(), SmallExchangeProductConfigPo.ConfigTypeEnum.CATEGORY.getCode()))
                            .map(SmallExchangeProductConfigPo::getConfigValue).filter(Objects::nonNull).collect(Collectors.toList());
                    List<Integer> spuList = integerListEntry.getValue().stream()
                            .filter(in -> Objects.equals(in.getConfigType(), SmallExchangeProductConfigPo.ConfigTypeEnum.SPU.getCode()))
                            .map(SmallExchangeProductConfigPo::getConfigValue).filter(Objects::nonNull).collect(Collectors.toList());
                    List<Integer> skuList = integerListEntry.getValue().stream()
                            .filter(in -> Objects.equals(in.getConfigType(), SmallExchangeProductConfigPo.ConfigTypeEnum.SKU.getCode()))
                            .map(SmallExchangeProductConfigPo::getConfigValue).filter(Objects::nonNull).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(brandList)){
                        exchangeGoodsConfigValueBOList.add(new ExchangeGoodsConfigRes.ExchangeGoodsConfigValueBO()
                                .setConfigType(SmallExchangeProductConfigPo.ConfigTypeEnum.BRAND.getCode())
                                .setConfigValue(brandList.stream().distinct().collect(Collectors.toList())));
                    }
                    if (CollUtil.isNotEmpty(categoryList)){
                        exchangeGoodsConfigValueBOList.add(new ExchangeGoodsConfigRes.ExchangeGoodsConfigValueBO()
                                .setConfigType(SmallExchangeProductConfigPo.ConfigTypeEnum.CATEGORY.getCode())
                                .setConfigValue(categoryList.stream().distinct().collect(Collectors.toList())));
                    }
                    if (CollUtil.isNotEmpty(spuList)){
                        exchangeGoodsConfigValueBOList.add(new ExchangeGoodsConfigRes.ExchangeGoodsConfigValueBO()
                                .setConfigType(SmallExchangeProductConfigPo.ConfigTypeEnum.SPU.getCode())
                                .setConfigValue(spuList.stream().distinct().collect(Collectors.toList())));
                    }
                    if (CollUtil.isNotEmpty(skuList)){
                        exchangeGoodsConfigValueBOList.add(new ExchangeGoodsConfigRes.ExchangeGoodsConfigValueBO()
                                .setConfigType(SmallExchangeProductConfigPo.ConfigTypeEnum.SKU.getCode())
                                .setConfigValue(skuList.stream().distinct().collect(Collectors.toList())));
                    }
                    item.setExchangeGoodsConfigValueBO(exchangeGoodsConfigValueBOList);
                }
            }
        }
        //从缓存获取品牌数据
        List<BrandCategoryBo> brandCategoryList = brandCategoryService.getBrandCategoryList();
        //从缓存获取分类数据
        List<Category> categories = categoryService.listAll();
        //进行数据解析
        for (ExchangeGoodsConfigRes configRes : records) {
            //解析服务类型名称
            configRes.setServiceTypeName(EnumUtil.getMessageByCode(SmallExchangeConfigPo.ExchangeServiceTypeEnum.class,configRes.getServiceType()));
            if (StrUtil.isEmpty(configRes.getServiceTypeName())){
                configRes.setServiceTypeName("无服务");
            }
            //可换货商品配置方式名称
            configRes.setProductConfigTypeName(EnumUtil.getMessageByCode(SmallExchangeConfigPo.ProductConfigTypeEnum.class,configRes.getProductConfigType()));
            if (XtenantEnum.isJiujiXtenant()) {
                configRes.setProductConfigTypeName(Optional.ofNullable(configRes.getProductConfigTypeName()).orElse("").replaceAll("SPU", "商品id").replaceAll("SKU", "sku_id"));
            }
            //解析配置值名称
            getConfigValueName(brandCategoryList, categories, configRes, configRes.getExchangeGoodsConfigValueBO());
        }
        exchangeGoodsConfigResPage.setRecords(records);
        //查询商品配置信息
        return R.success(exchangeGoodsConfigResPage);
    }

    /**
     * 对品牌 分类 sku spu 进行解析
     * @param brandCategoryList brandCategoryList
     * @param categories categories
     * @param configRes configRes
     * @param exchangeGoodsConfigValueList exchangeGoodsConfigValueList
     */
    private void getConfigValueName(List<BrandCategoryBo> brandCategoryList, List<Category> categories, ExchangeGoodsConfigRes configRes, List<ExchangeGoodsConfigRes.ExchangeGoodsConfigValueBO> exchangeGoodsConfigValueList) {
        AtomicReference<List<String>> configValueNameList = new AtomicReference<>();
        AtomicReference<List<String>> brandNameList = new AtomicReference<>();
        AtomicReference<List<String>> categoryNameList = new AtomicReference<>();
        String configValueName = "";
        if (CollUtil.isEmpty(exchangeGoodsConfigValueList)){
            return;
        }
        exchangeGoodsConfigValueList.forEach(ex ->{
            if (Objects.equals(ex.getConfigType(),SmallExchangeProductConfigPo.ConfigTypeEnum.BRAND.getCode())){
                //解析品牌
                brandNameList.set(Optional.of(brandCategoryList.stream()
                        .filter(br -> ex.getConfigValue().contains(br.getId())).map(BrandCategoryBo::getName).filter(Objects::nonNull).collect(Collectors.toList())).orElse(null));
            }else if (Objects.equals(ex.getConfigType(),SmallExchangeProductConfigPo.ConfigTypeEnum.CATEGORY.getCode())){
                //解析分类
                categoryNameList.set(Optional.of(categories.stream()
                        .filter(ca -> ex.getConfigValue().contains(ca.getId())).map(Category::getName).filter(Objects::nonNull).collect(Collectors.toList())).orElse(null));
            }else {
                configValueNameList.set(ex.getConfigValue().stream().map(String::valueOf).collect(Collectors.toList()));
            }
        });
        if (CollUtil.isNotEmpty(brandNameList.get())){
            configValueName += StrUtil.format("品牌：{}",StrUtil.maxLength(String.join(SignConstant.COMMA, brandNameList.get()),10));
        }
        if (CollUtil.isNotEmpty(categoryNameList.get())){
            configValueName += StrUtil.format("分类：{}",StrUtil.maxLength(String.join(SignConstant.COMMA, categoryNameList.get()),10));
        }
        if (CollUtil.isNotEmpty(configValueNameList.get())){
            configValueName += StrUtil.format("{}",StrUtil.maxLength(String.join(SignConstant.COMMA, configValueNameList.get()),20));
        }
        configRes.setConfigValueName(configValueName);
    }

    /**
     * 小件枚举值
     * @return R<List<ExchangeGoodsEnums>>
     */
    @Override
    public R<List<ExchangeGoodsEnums>> getExchangeGoodsEnums() {
        List<ExchangeGoodsEnums> enumVOS = new ArrayList<>();
        //服务类型
        List<ShouhouInventoryEnumRes> serviceType = toEnumVOList(SmallExchangeConfigPo.ExchangeServiceTypeEnum.class);
        serviceType.forEach(ser -> {
            if (Objects.equals(ser.getValue(),SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_NULL.getCode())){
                ser.setLabel("无服务");
            }
        });
        enumVOS.add(new ExchangeGoodsEnums().setLabel("服务类型").setValue("serviceType").setChildren(serviceType));
        //补差价方式
        List<ShouhouInventoryEnumRes> differentPriceType = toEnumVOList(SmallExchangeConfigPo.DifferentPriceTypeEnum.class);
        enumVOS.add(new ExchangeGoodsEnums().setLabel("补差价方式").setValue("differentPriceType").setChildren(differentPriceType));
        //spu sku 枚举
        List<ShouhouInventoryEnumRes> searchType = toEnumVOList(ExchangeGoodsConfigReq.SearchTypeEnum.class);
        enumVOS.add(new ExchangeGoodsEnums().setLabel("搜索类型枚举").setValue("searchType").setChildren(searchType));
        //可换货商品配置方式(1 品牌 2分类 3 品牌+分类 4 SPU 5 SKU)
        List<ShouhouInventoryEnumRes> productConfigType = toEnumVOList(SmallExchangeConfigPo.ProductConfigTypeEnum.class);
        if (XtenantEnum.isJiujiXtenant()) {
            productConfigType.forEach(v -> {
                if (SmallExchangeConfigPo.ProductConfigTypeEnum.SPU.getCode().equals(v.getValue())) {
                    v.setLabel("商品id");
                }
                if (SmallExchangeConfigPo.ProductConfigTypeEnum.SKU.getCode().equals(v.getValue())) {
                    v.setLabel("sku_id");
                }
            });
        }
        enumVOS.add(new ExchangeGoodsEnums().setLabel("可换货商品配置").setValue("productConfigType").setChildren(productConfigType));
        return R.success(enumVOS);
    }

    public static <T extends CodeMessageEnumInterface> List<ShouhouInventoryEnumRes> toEnumVOList(Class<T> enumClass) {
        List<ShouhouInventoryEnumRes> enums = new ArrayList<>();
        CodeMessageEnumInterface[] var2 = enumClass.getEnumConstants();
        for (CodeMessageEnumInterface temp : var2) {
            ShouhouInventoryEnumRes vo = new ShouhouInventoryEnumRes();
            vo.setLabel(temp.getMessage());
            vo.setValue(temp.getCode());
            enums.add(vo);
        }
        return enums;
    }

    /**
     * 更新接口
     * @param req req
     * @return R<Boolean>
     */
    @Override
    public R<Boolean> updateExchangeGoodsConfig(ExchangeGoodsConfigSaveReq req) {
        //更新操作
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null){
            return R.error("登陆信息获取失败！");
        }
        if (Objects.isNull(req.getServiceType()) ||
                !Arrays.stream(SmallExchangeConfigPo.ExchangeServiceTypeEnum.values()).map(SmallExchangeConfigPo.ExchangeServiceTypeEnum::getCode).collect(Collectors.toList()).contains(req.getServiceType())){
            return R.error("服务类型错误!");
        }
        //校验当补差价为空或者false的时候
        if (Boolean.FALSE.equals(Optional.ofNullable(req.getIsDifferentPrice()).orElse(Boolean.FALSE))){
            //使用商品服务为false
            req.setIsDifferentPrice(Boolean.FALSE);
            if (!Objects.equals(SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode(),req.getServiceType())){
                req.setIsServiceDifferentPrice(Boolean.FALSE);
            }
            req.setDifferentPriceType(null);
            req.setDifferentPricePercent(null);
        }else {
            req.setIsDifferentPrice(Boolean.TRUE);
            req.setIsServiceDifferentPrice(Optional.ofNullable(req.getIsServiceDifferentPrice()).orElse(Boolean.FALSE));
            if (!Objects.equals(SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode(),req.getServiceType()) && Boolean.TRUE.equals(req.getIsServiceDifferentPrice())){
                return R.error("服务类型是年包才能启用!");
            }
            if (CommenUtil.isNullOrZero(req.getDifferentPriceType())){
                return R.error("补差价方式不能为空!");
            }
            if (!Optional.ofNullable(req.getDifferentPricePercent()).isPresent()){
                return R.error("补差价百分比不能为空!");
            }
        }
        //配置方式校验
        if (CommenUtil.isNullOrZero(req.getProductConfigType())){
            return R.error("可换货商品配置方式不能为空!");
        }
        //进行数据拼装
        SmallExchangeConfigPo.ProductConfigTypeEnum configType = Optional.ofNullable(req.getProductConfigType())
                .map(st -> EnumUtil.getEnumByCode(SmallExchangeConfigPo.ProductConfigTypeEnum.class, st))
                .orElseThrow(() -> new CustomizeException("配置类型错误，请联系管理员!"));
        //对原本的数据进行查询
        SmallExchangeConfigPo smallExchangeConfigPo =  smallProConfigMapper.getConfigById(req.getId());
        List<SmallExchangeProductConfigPo> smallExchangeProductConfigPos = smallProConfigMapper.listConfigProduct(req.getId());
        //数据对比工具
        SmallExchangeConfigContrastBO oldObj = new SmallExchangeConfigContrastBO();
        BeanUtils.copyProperties(smallExchangeConfigPo, oldObj);
        SmallExchangeConfigContrastBO newObj = new SmallExchangeConfigContrastBO();
        BeanUtils.copyProperties(req, newObj);
        List<String> fieldModifiedLog = WorkLogUtil.getFieldModifiedLog(SmallExchangeConfigContrastBO.class, oldObj, newObj);
        //更新操作内容
        List<String> fieldLog = new ArrayList<>();
        //判断基本配置是否有值更新
        AtomicBoolean flag = new AtomicBoolean(false);
        if (CollUtil.isNotEmpty(fieldModifiedLog)) {
            for (String s : fieldModifiedLog) {
                String handleLog = s;
                if (StrUtil.count(handleLog, "由【空】修改为【空】") > 0) {
                    continue;
                }
                handleLog = StrUtil.replaceIgnoreCase(handleLog, "false", "关闭");
                handleLog = StrUtil.replaceIgnoreCase(handleLog, "true", "开启");
                if (StrUtil.count(handleLog, "服务类型") > 0) {
                    for (SmallExchangeConfigPo.ExchangeServiceTypeEnum value : SmallExchangeConfigPo.ExchangeServiceTypeEnum.values()) {
                        if (Objects.equals(value.getCode() , SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_NULL.getCode())){
                            handleLog = StrUtil.replaceIgnoreCase(handleLog,String.valueOf(value.getCode()), "无服务");
                        }
                        handleLog = StrUtil.replaceIgnoreCase(handleLog,String.valueOf(value.getCode()), value.getMessage());
                    }
                }
                if (StrUtil.count(handleLog, "补差方式") > 0) {
                    for (SmallExchangeConfigPo.DifferentPriceTypeEnum value : SmallExchangeConfigPo.DifferentPriceTypeEnum.values()) {
                        handleLog = StrUtil.replaceIgnoreCase(handleLog,String.valueOf(value.getCode()), value.getMessage());
                    }
                }
                if (StrUtil.count(handleLog, "配置方式") > 0) {
                    for (SmallExchangeConfigPo.ProductConfigTypeEnum value : SmallExchangeConfigPo.ProductConfigTypeEnum.values()) {
                        handleLog = StrUtil.replaceIgnoreCase(handleLog,String.valueOf(value.getCode()), value.getMessage());
                    }
                }
                fieldLog.add(handleLog);
                flag.set(Boolean.TRUE);
            }
        }
        //根据当前的置换类型来获取用户当前选择的值
        List<SmallExchangeConfigPo.ConfigTypeAndValue> newConfigTypeAndValues = getConfigTypeAndValues(req,configType);
        //分组操作 根据配置类型来分组当前配置信息
        Map<SmallExchangeConfigPo.ProductConfigTypeEnum, List<SmallExchangeConfigPo.ConfigTypeAndValue>> configTypeMap =
                newConfigTypeAndValues.stream().collect(Collectors.groupingBy(SmallExchangeConfigPo.ConfigTypeAndValue::getConfigType));
        Map<Integer, List<SmallExchangeProductConfigPo>> productConfigMap = smallExchangeProductConfigPos.stream().collect(Collectors.groupingBy(SmallExchangeProductConfigPo::getConfigType));
        //给map补类型
        for (SmallExchangeConfigPo.ProductConfigTypeEnum value : SmallExchangeConfigPo.ProductConfigTypeEnum.values()) {
            if (!configTypeMap.containsKey(value)){
                configTypeMap.put(value, new ArrayList<>());
            }
            if (!productConfigMap.containsKey(value.getCode())){
                productConfigMap.put(value.getCode(),new ArrayList<>());
            }
        }
        List<SmallExchangeProductConfigPo> addProductConfigPoList = new ArrayList<>();
        List<SmallExchangeProductConfigPo> delProductConfigPoList = new ArrayList<>();
        for (Map.Entry<SmallExchangeConfigPo.ProductConfigTypeEnum, List<SmallExchangeConfigPo.ConfigTypeAndValue>> productConfigTypeEnumListEntry : configTypeMap.entrySet()) {
            for (Map.Entry<Integer, List<SmallExchangeProductConfigPo>> integerListEntry : productConfigMap.entrySet()) {
                //当配置相同时  来比较是否有数据更改
                if (Objects.equals(productConfigTypeEnumListEntry.getKey().getCode(),integerListEntry.getKey())){
                    //对结果进行排序
                    List<Integer> collect = new ArrayList<>();
                    if (CollUtil.isNotEmpty(productConfigTypeEnumListEntry.getValue())){
                        //新数据 用户更新传递过来的数据
                        collect = productConfigTypeEnumListEntry.getValue().stream().map(SmallExchangeConfigPo.ConfigTypeAndValue::getConfigValue).sorted().distinct().collect(Collectors.toList());
                    }
                    List<Integer> collect1 = new ArrayList<>();
                    if (CollUtil.isNotEmpty(integerListEntry.getValue())){
                        //旧数据 未更新之前的数据
                        collect1 = integerListEntry.getValue().stream().map(SmallExchangeProductConfigPo::getConfigValue).sorted().distinct().collect(Collectors.toList());
                    }
                    if (CollUtil.isEmpty(collect) && CollUtil.isEmpty(collect1)){
                        continue;
                    }
                    //取两次单差集  分开更新和删除操作
                    // 新数据中有但旧数据没有
                    Collection<Integer> addSubtract = CollUtil.subtract(collect, collect1);
                    //旧数据中有  新数据中没有
                    Collection<Integer> delSubtract = CollUtil.subtract(collect1, collect);
                    //如果差集不为空的话 数据进行了更新
                    if (CollUtil.isNotEmpty(addSubtract)){
                        //存储进行更新
                        addSubtract.forEach(add ->
                                addProductConfigPoList.add(new SmallExchangeProductConfigPo().setConfigType(productConfigTypeEnumListEntry.getKey().getCode())
                                        .setConfigValue(add).setFkConfigId(req.getId())));
                        fieldLog.add(StrUtil.format("新增【{}】值：{}",EnumUtil.getMessageByCode(SmallExchangeConfigPo.ProductConfigTypeEnum.class,integerListEntry.getKey()),addSubtract.toString()));
                    }
                    if (CollUtil.isNotEmpty(delSubtract)){
                        //存储进行删除
                        delSubtract.forEach(del ->
                                delProductConfigPoList.add(new SmallExchangeProductConfigPo().setConfigType(productConfigTypeEnumListEntry.getKey().getCode())
                                        .setConfigValue(del).setFkConfigId(req.getId())));
                        fieldLog.add(StrUtil.format("删除【{}】值：{}",EnumUtil.getMessageByCode(SmallExchangeConfigPo.ProductConfigTypeEnum.class,integerListEntry.getKey()),delSubtract.toString()));
                    }
                }
            }
        }
        //如果日志操作中没有值 说明没有进行更新操作
        if (CollUtil.isEmpty(fieldLog)) {
            return R.success("无内容更新！");
        }
        //调用日志接口进行日志记录
        SmallExchangeConfigLogDocument smallProConfigLogDocument = new SmallExchangeConfigLogDocument();
        List<SmallExchangeConfigLogDocument.Conts> contsList = new ArrayList<>();
        smallProConfigLogDocument.setConfigId(Long.valueOf(req.getId()));
        //构建日志
        contsList.add(new SmallExchangeConfigLogDocument.Conts().setDTime(LocalDateTime.now()).setComment(StrUtil.format("更新操作：{}",fieldLog.toString())).setInUser(oaUserBO.getUserName()).setShowType(Boolean.TRUE));
        smallProConfigLogDocument.setCons(contsList);
        //添加日志操作
        smallExchangeConfigLogService.saveSmallProConfigLog(smallProConfigLogDocument);
        MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, () -> {
            if (flag.get()){
                smallProConfigMapper.updateExchangeConfig(req,oaUserBO.getUserName());
            }
            if (CollUtil.isNotEmpty(addProductConfigPoList)){
                smallProConfigMapper.updateExchangeProductConfig(req.getId(), addProductConfigPoList, oaUserBO.getUserName());
            }
            if (CollUtil.isNotEmpty(delProductConfigPoList)){
                smallProConfigMapper.delExchangeProductConfig(req.getId(), delProductConfigPoList, oaUserBO.getUserName());
            }
        }).commit();
        return R.success("更新成功！");
    }

    /**
     * 添加配置
     * @param req req
     * @return R<Boolean>
     */
    @Override
    public R<Boolean> addExchangeGoodsConfig(ExchangeGoodsConfigSaveReq req) {
        //添加操作
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null){
            return R.error("登陆信息获取失败！");
        }
        if (Objects.isNull(req.getServiceType()) ||
                !Arrays.stream(SmallExchangeConfigPo.ExchangeServiceTypeEnum.values()).map(SmallExchangeConfigPo.ExchangeServiceTypeEnum::getCode).collect(Collectors.toList()).contains(req.getServiceType())){
            return R.error("服务类型错误!");
        }
        //校验当补差价为空或者false的时候
        if (Boolean.FALSE.equals(Optional.ofNullable(req.getIsDifferentPrice()).orElse(Boolean.FALSE))){
            //使用商品服务为false
            req.setIsDifferentPrice(Boolean.FALSE);
            if (!Objects.equals(SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode(),req.getServiceType())){
                req.setIsServiceDifferentPrice(Boolean.FALSE);
            }
            req.setDifferentPriceType(null);
            req.setDifferentPricePercent(null);
        }else {
            req.setIsDifferentPrice(Boolean.TRUE);
            req.setIsServiceDifferentPrice(Optional.ofNullable(req.getIsServiceDifferentPrice()).orElse(Boolean.FALSE));
            if (!Objects.equals(SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode(),req.getServiceType()) && Boolean.TRUE.equals(req.getIsServiceDifferentPrice())){
                return R.error("服务类型是年包才能启用!");
            }
            if (CommenUtil.isNullOrZero(req.getDifferentPriceType())){
                return R.error("补差价方式不能为空!");
            }
            if (!Optional.ofNullable(req.getDifferentPricePercent()).isPresent()){
                return R.error("补差价百分比不能为空!");
            }
        }
        //配置方式校验
        if (CommenUtil.isNullOrZero(req.getProductConfigType())){
            return R.error("可换货商品配置方式不能为空!");
        }
        //进行数据拼装
        SmallExchangeConfigPo.ProductConfigTypeEnum configType = Optional.ofNullable(req.getProductConfigType())
                .map(st -> EnumUtil.getEnumByCode(SmallExchangeConfigPo.ProductConfigTypeEnum.class, st))
                .orElseThrow(() -> new CustomizeException("配置类型错误，请联系管理员!"));
        switch (configType) {
            case BRAND:
                if (CollUtil.isEmpty(req.getBrandIdList())) {
                    return R.error("品牌不能为空!");
                }
                break;
            case CATEGORY:
                if (CollUtil.isEmpty(req.getCidList())) {
                    return R.error("分类不能为空!");
                }
                break;
            case BRAND_CATEGORY:
                if (CollUtil.isEmpty(req.getBrandIdList()) && CollUtil.isEmpty(req.getCidList())) {
                return R.error("品牌与分类不能为空!");
            }
            break;
            case SKU:
                if (StrUtil.isEmpty(req.getSku())) {
                    return R.error("sku不能为空!");
                }
                if (CommenUtil.toIntList(req.getSpu()).size()> NumberConstant.ONE_HUNDRED){
                    return R.error("sku不能超过100组!");
                }
                break;
            case SPU:
                if (StrUtil.isEmpty(req.getSpu())) {
                    return R.error("spu不能为空!");
                }
                if (CommenUtil.toIntList(req.getSpu()).size()> NumberConstant.ONE_HUNDRED){
                    return R.error("spu不能超过100组!");
                }
                break;
            default:
                return R.error("配置方式错误!");
        }
        List<SmallExchangeConfigPo.ConfigTypeAndValue> configTypeAndValues = getConfigTypeAndValues(req,configType);
        //开事务 进行添加操作
        MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, () -> {
            smallProConfigMapper.addExchangeConfig(req,oaUserBO.getUserName());
            int i = smallProConfigMapper.addExchangeProductConfig(req.getId(),configTypeAndValues, oaUserBO.getUserName());
            if (i<=0){
                throw new RuntimeException("添加失败！");
            }
        }).commit();
        //构建日志
        SmallExchangeConfigLogDocument smallProConfigLogDocument = new SmallExchangeConfigLogDocument();
        List<SmallExchangeConfigLogDocument.Conts> contsList = new ArrayList<>();
        smallProConfigLogDocument.setConfigId(Long.valueOf(req.getId()));
        String messageByCode = EnumUtil.getMessageByCode(SmallExchangeConfigPo.ExchangeServiceTypeEnum.class, req.getServiceType());
        if (Objects.equals(req.getServiceType(), 0)) {
            messageByCode = "无服务";
        }
        contsList.add(new SmallExchangeConfigLogDocument.Conts().setDTime(LocalDateTime.now())
                .setComment(StrUtil.format("新增配置：标题【{}】,服务类型【{}】", req.getTitle(), messageByCode))
                .setInUser(oaUserBO.getUserName()).setShowType(Boolean.TRUE));
        smallProConfigLogDocument.setCons(contsList);
        //添加日志操作
        smallExchangeConfigLogService.saveSmallProConfigLog(smallProConfigLogDocument);
        return R.success("添加成功！");
    }

    /**
     * 删除配置
     * @param id id
     * @return R<Boolean>
     */
    @Override
    public R<Boolean> delExchangeGoodsConfig(Integer id) {
        //查询当前配置信息
        if (CommenUtil.isNullOrZero(id)){
            return R.error("删除数据错误!");
        }
        SmallExchangeConfigPo configById = smallProConfigMapper.getConfigById(id);
        if (configById == null || CommenUtil.isNullOrZero(configById.getId())){
            return R.error("删除失败，请刷新页面重试!");
        }
        //添加操作
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null){
            return R.error("登陆状态失效!");
        }
        SmallExchangeConfigLogDocument smallProConfigLogDocument = new SmallExchangeConfigLogDocument();
        List<SmallExchangeConfigLogDocument.Conts> contsList = new ArrayList<>();
        smallProConfigLogDocument.setConfigId(Long.valueOf(id));
        //构建日志
        contsList.add(new SmallExchangeConfigLogDocument.Conts().setDTime(LocalDateTime.now())
                .setComment(StrUtil.format("删除操作：编号{}，标题{}",configById.getId(),configById.getTitle()))
                .setInUser(oaUserBO.getUserName()).setShowType(Boolean.TRUE));
        smallProConfigLogDocument.setCons(contsList);
        MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, () -> {
            int i = smallProConfigMapper.delExchangeGoodsConfig(id);
            if (i>0){
                //添加日志操作
                smallExchangeConfigLogService.saveSmallProConfigLog(smallProConfigLogDocument);
            }else {
                throw new RuntimeException("删除失败！");
            }
            smallProConfigMapper.delExchangeProductConfigByConfigId(id);
        }).commit();
        return R.success("删除成功!");
    }

    /**
     * 进行数据过滤
     * @param req req
     * @param configType configType
     * @return List<SmallExchangeConfigPo.ConfigTypeAndValue>
     */
    private List<SmallExchangeConfigPo.ConfigTypeAndValue> getConfigTypeAndValues(ExchangeGoodsConfigSaveReq req,SmallExchangeConfigPo.ProductConfigTypeEnum configType) {
        List<SmallExchangeConfigPo.ConfigTypeAndValue> configTypeAndValues = new ArrayList<>();
        switch (configType){
            case SKU:
                List<Integer> skuList = CommenUtil.toIntList(req.getSku()).stream().distinct().filter(Objects::nonNull).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(skuList)) {
                    skuList.forEach(sku -> configTypeAndValues.add(new SmallExchangeConfigPo.ConfigTypeAndValue()
                            .setConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.SKU).setConfigValue(sku)));
                }
                break;
            case SPU:
                List<Integer> spuList = CommenUtil.toIntList(req.getSpu()).stream().distinct().filter(Objects::nonNull).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(spuList)) {
                    spuList.forEach(spu -> configTypeAndValues.add(new SmallExchangeConfigPo.ConfigTypeAndValue()
                            .setConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.SPU).setConfigValue(spu)));
                }
                break;
            case BRAND_CATEGORY:
                //品牌查询直接返回查询结果
                if (CollUtil.isNotEmpty(req.getBrandIdList())){
                    req.getBrandIdList().stream().distinct().forEach(brandId -> configTypeAndValues.add(new SmallExchangeConfigPo.ConfigTypeAndValue()
                            .setConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.BRAND).setConfigValue(brandId)));
                }
                if (CollUtil.isNotEmpty(req.getCidList())){
                    req.getCidList().stream().distinct().forEach(cid -> configTypeAndValues.add(new SmallExchangeConfigPo.ConfigTypeAndValue()
                            .setConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.CATEGORY).setConfigValue(cid)));
                }
                break;
            case CATEGORY:
                if (CollUtil.isNotEmpty(req.getCidList())){
                    req.getCidList().stream().distinct().forEach(cid -> configTypeAndValues.add(new SmallExchangeConfigPo.ConfigTypeAndValue()
                            .setConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.CATEGORY).setConfigValue(cid)));
                }
                break;
            case BRAND:
                if (CollUtil.isNotEmpty(req.getBrandIdList())){
                    req.getBrandIdList().stream().distinct().forEach(brandId -> configTypeAndValues.add(new SmallExchangeConfigPo.ConfigTypeAndValue()
                            .setConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.BRAND).setConfigValue(brandId)));
                }
                break;
        }
        return configTypeAndValues;
    }


    @Override
    public SmallRefundConfigPo getRefundConfig(Integer ppid, LocalDateTime buyTime,Integer areaId,Integer smallRefundServiceType) {
        if (CommenUtil.isNullOrZero(ppid) && CommenUtil.isNullOrZero(areaId)){
            return null;
        }
        Integer xtenant = 0;
        //根据areaId获取Xtenant
        R<AreaInfo> areaInfoById = areaInfoClient.getAreaInfoById(areaId);
        if (areaInfoById.isSuccess()){
            xtenant = areaInfoById.getData().getXtenant();
        }
        //根据ppid查询分类和品牌
        ProductRefundPo productRefundPo = smallProConfigMapper.listRefundSmallProductInfo(ppid);
        List<SmallRefundConfigPo.ConfigTypeAndValue> configTypeAndValues = listRefundConfigTypeAndValues(productRefundPo);
        //根据值匹配规则 获取出一条规则
        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(buyTime, now);
        int days = (int) duration.toDays();
        Integer finalXtenant = xtenant;
        List<SmallRefundConfigPo> smallRefundConfigPoList = new LinkedList<>();
        configTypeAndValues.forEach(configTypeAndValue -> smallRefundConfigPoList.add(smallProConfigMapper.getRefundConfig(configTypeAndValue, days, finalXtenant,smallRefundServiceType)));
        //按照更新时间来排序取出第一个配置
        return smallRefundConfigPoList.stream().filter(Objects::nonNull).filter(CommenUtil.distinctByKey(SmallRefundConfigPo::getId)).max(Comparator.comparing(SmallRefundConfigPo::getUpdateTime)).orElse(null);
    }

    /**
     * 配置查询接口
     * @param req req
     * @return R<Page<RefundGoodsConfigRes>>
     */
    @Override
    public R<Page<RefundGoodsConfigRes>> getRefundGoodsConfigInfo(RefundGoodsConfigReq req) {
        //查询当前配置信息
        Page<RefundGoodsConfigReq> page = new Page<>(req.getCurrent(), req.getSize());
        page.setDesc("src.id");
        Page<RefundGoodsConfigRes> refundGoodsConfigResPage = smallProConfigMapper.getRefundConfigList(page, req);
        List<RefundGoodsConfigRes> records = refundGoodsConfigResPage.getRecords();
        //获取到配置的ID 查询商品配置表数据
        List<Integer> refundGoodsConfigIds = records.stream().map(RefundGoodsConfigRes::getId).filter(Objects::nonNull).collect(Collectors.toList());
        //查询商品表配置 最大一千条in 不需要分批查询
        List<SmallRefundProductConfigPo> smallRefundProductConfigPos =new ArrayList<>();
        if (CollUtil.isNotEmpty(refundGoodsConfigIds)){
            smallRefundProductConfigPos = smallProConfigMapper.listRefundConfigProductByIds(refundGoodsConfigIds);
        }
        //进行数据汇总
        Map<Integer, List<SmallRefundProductConfigPo>> smallRefundProductConfigMap = smallRefundProductConfigPos.stream()
                .collect(Collectors.groupingBy(SmallRefundProductConfigPo::getFkConfigId));
        for (Map.Entry<Integer, List<SmallRefundProductConfigPo>> integerListEntry : smallRefundProductConfigMap.entrySet()) {
            for (RefundGoodsConfigRes item : records) {
                if (Objects.equals(item.getId(),integerListEntry.getKey())){
                    List<RefundGoodsConfigRes.RefundGoodsConfigValueBO> refundGoodsConfigValueBOList = new ArrayList<>();
                    //根据配置类型来判断 并解析
                    List<Integer> brandList = integerListEntry.getValue().stream()
                            .filter(in -> Objects.equals(in.getConfigType(), SmallRefundProductConfigPo.ConfigTypeEnum.BRAND.getCode()))
                            .map(SmallRefundProductConfigPo::getConfigValue).filter(Objects::nonNull).collect(Collectors.toList());
                    List<Integer> categoryList = integerListEntry.getValue().stream()
                            .filter(in -> Objects.equals(in.getConfigType(), SmallRefundProductConfigPo.ConfigTypeEnum.CATEGORY.getCode()))
                            .map(SmallRefundProductConfigPo::getConfigValue).filter(Objects::nonNull).collect(Collectors.toList());
                    List<Integer> spuList = integerListEntry.getValue().stream()
                            .filter(in -> Objects.equals(in.getConfigType(), SmallRefundProductConfigPo.ConfigTypeEnum.SPU.getCode()))
                            .map(SmallRefundProductConfigPo::getConfigValue).filter(Objects::nonNull).collect(Collectors.toList());
                    List<Integer> skuList = integerListEntry.getValue().stream()
                            .filter(in -> Objects.equals(in.getConfigType(), SmallRefundProductConfigPo.ConfigTypeEnum.SKU.getCode()))
                            .map(SmallRefundProductConfigPo::getConfigValue).filter(Objects::nonNull).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(brandList)){
                        refundGoodsConfigValueBOList.add(new RefundGoodsConfigRes.RefundGoodsConfigValueBO()
                                .setConfigType(SmallRefundProductConfigPo.ConfigTypeEnum.BRAND.getCode())
                                .setConfigValue(brandList.stream().distinct().collect(Collectors.toList())));
                    }
                    if (CollUtil.isNotEmpty(categoryList)){
                        refundGoodsConfigValueBOList.add(new RefundGoodsConfigRes.RefundGoodsConfigValueBO()
                                .setConfigType(SmallRefundProductConfigPo.ConfigTypeEnum.CATEGORY.getCode())
                                .setConfigValue(categoryList.stream().distinct().collect(Collectors.toList())));
                    }
                    if (CollUtil.isNotEmpty(spuList)){
                        refundGoodsConfigValueBOList.add(new RefundGoodsConfigRes.RefundGoodsConfigValueBO()
                                .setConfigType(SmallRefundProductConfigPo.ConfigTypeEnum.SPU.getCode())
                                .setConfigValue(spuList.stream().distinct().collect(Collectors.toList())));
                    }
                    if (CollUtil.isNotEmpty(skuList)){
                        refundGoodsConfigValueBOList.add(new RefundGoodsConfigRes.RefundGoodsConfigValueBO()
                                .setConfigType(SmallRefundProductConfigPo.ConfigTypeEnum.SKU.getCode())
                                .setConfigValue(skuList.stream().distinct().collect(Collectors.toList())));
                    }
                    item.setRefundGoodsConfigValueBO(refundGoodsConfigValueBOList);
                }
            }
        }
        //从缓存获取品牌数据
        List<BrandCategoryBo> brandCategoryList = brandCategoryService.getBrandCategoryList();
        //从缓存获取分类数据
        List<Category> categories = categoryService.listAll();
        //进行数据解析
        for (RefundGoodsConfigRes configRes : records) {
            //解析配置值名称
            if (CollUtil.isNotEmpty(configRes.getRefundGoodsConfigValueBO())) {
                getRefundConfigValueName(brandCategoryList, categories, configRes, configRes.getRefundGoodsConfigValueBO());
            }
            configRes.setServiceTypeName(EnumUtil.getMessageByCode(SmallRefundServiceTypeEnum.class,configRes.getServiceType()));
            //进制转换
            List<Integer> hitchTypeList = CommenUtil.BHDConverterByEnums(configRes.getHitchType());
            List<Integer> refundTypeList = CommenUtil.BHDConverterByEnums(configRes.getRefundType());
            configRes.setHitchTypeList(hitchTypeList);
            configRes.setRefundTypeList(refundTypeList);
            List<String> hitchTypeName = new ArrayList<>();
            List<String> refundTypeName = new ArrayList<>();
            hitchTypeList.forEach(hi -> hitchTypeName.add(EnumUtil.getMessageByCode(SmallRefundConfigPo.HitchTypeEnum.class,hi)));
            refundTypeList.forEach(re -> refundTypeName.add(EnumUtil.getMessageByCode(SmallRefundConfigPo.RefundTypeEnum.class,re)));
            configRes.setRefundTypeName(refundTypeName);
            configRes.setHitchTypeName(hitchTypeName);
        }
        refundGoodsConfigResPage.setRecords(records);
        //查询商品配置信息
        return R.success(refundGoodsConfigResPage);
    }

    /**
     * 对品牌 分类 sku spu 进行解析
     * @param brandCategoryList brandCategoryList
     * @param categories categories
     * @param configRes configRes
     * @param RefundGoodsConfigValueList RefundGoodsConfigValueList
     */
    private void getRefundConfigValueName(List<BrandCategoryBo> brandCategoryList, List<Category> categories, RefundGoodsConfigRes configRes, List<RefundGoodsConfigRes.RefundGoodsConfigValueBO> RefundGoodsConfigValueList) {
        AtomicReference<List<String>> configValueNameList = new AtomicReference<>();
        AtomicReference<List<String>> brandNameList = new AtomicReference<>();
        AtomicReference<List<String>> categoryNameList = new AtomicReference<>();
        String configValueName = "";
        RefundGoodsConfigValueList.forEach(ex ->{
            if (Objects.equals(ex.getConfigType(),SmallRefundProductConfigPo.ConfigTypeEnum.BRAND.getCode())){
                //解析品牌
                brandNameList.set(Optional.of(brandCategoryList.stream()
                        .filter(br -> ex.getConfigValue().contains(br.getId())).map(BrandCategoryBo::getName).filter(Objects::nonNull).collect(Collectors.toList())).orElse(null));
            }else if (Objects.equals(ex.getConfigType(),SmallRefundProductConfigPo.ConfigTypeEnum.CATEGORY.getCode())){
                //解析分类
                categoryNameList.set(Optional.of(categories.stream()
                        .filter(ca -> ex.getConfigValue().contains(ca.getId())).map(Category::getName).filter(Objects::nonNull).collect(Collectors.toList())).orElse(null));
            }else {
                configValueNameList.set(ex.getConfigValue().stream().map(String::valueOf).collect(Collectors.toList()));
            }
        });
        if (CollUtil.isNotEmpty(brandNameList.get())){
            configValueName += StrUtil.format("品牌：{}",StrUtil.maxLength(String.join(SignConstant.COMMA, brandNameList.get()),10));
        }
        if (CollUtil.isNotEmpty(categoryNameList.get())){
            configValueName += StrUtil.format("分类：{}",StrUtil.maxLength(String.join(SignConstant.COMMA, categoryNameList.get()),10));
        }
        if (CollUtil.isNotEmpty(configValueNameList.get())){
            configValueName += StrUtil.format("{}",String.join(SignConstant.COMMA, configValueNameList.get()));
        }
        configRes.setConfigValueName(configValueName);
    }

    @Override
    public R<List<RefundGoodsEnums>> getRefundGoodsEnums() {
        List<RefundGoodsEnums> enumVOS = new ArrayList<>();
        //服务类型
        List<ShouhouInventoryEnumRes> serviceType = toEnumVOList(SmallRefundServiceTypeEnum.class);
        enumVOS.add(new RefundGoodsEnums().setLabel("服务类型").setValue("serviceType").setChildren(serviceType));
        //补差价方式
        List<ShouhouInventoryEnumRes> hitchType = toEnumVOList(SmallRefundConfigPo.HitchTypeEnum.class);
        hitchType.removeIf(hi -> Objects.equals(hi.getValue(), SmallRefundConfigPo.HitchTypeEnum.ALL_FAULTY.getCode()));
        enumVOS.add(new RefundGoodsEnums().setLabel("故障类型").setValue("hitchType").setChildren(hitchType));
        //spu sku 枚举
        List<ShouhouInventoryEnumRes> searchType = toEnumVOList(RefundGoodsConfigReq.SearchTypeEnum.class);
        enumVOS.add(new RefundGoodsEnums().setLabel("搜索类型枚举").setValue("searchType").setChildren(searchType));
        //可换货商品配置方式(1 品牌 2分类 3 SPU 4 SKU)
        List<ShouhouInventoryEnumRes> refundType = toEnumVOList(SmallRefundConfigPo.RefundTypeEnum.class);
        refundType.removeIf(se -> Objects.equals(se.getValue(),SmallRefundConfigPo.RefundTypeEnum.ALL_REFUND.getCode()));
        enumVOS.add(new RefundGoodsEnums().setLabel("可换货商品配置").setValue("refundType").setChildren(refundType));
        return R.success(enumVOS);
    }

    @Override
    public R<Boolean> updateRefundGoodsConfig(RefundGoodsConfigSaveReq req) {
        //更新操作
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null){
            return R.error("登陆信息获取失败！");
        }
        if (Objects.isNull(req.getServiceType()) ||
                !Arrays.stream(SmallRefundServiceTypeEnum.values()).map(SmallRefundServiceTypeEnum::getCode).collect(Collectors.toList()).contains(req.getServiceType())){
            return R.error("服务类型错误!");
        }
        if (req.getStartDays() == null || req.getStartDays() < 0 || req.getEndDays() == null || req.getEndDays() < 0) {
            return R.error("天数设置不能为空或负数，请检查!");
        }
        if (!Optional.ofNullable(req.getDifferentPricePercent()).isPresent()){
            return R.error("补差价百分比不能为空!");
        }
        //附件折价默认为true
        if (req.getIsPartsDifferent() == null) {
            req.setIsPartsDifferent(Boolean.TRUE);
        }
        //获取当前添加人的xtenant
        if (req.getXtenant() == null){
            req.setXtenant(oaUserBO.getXTenant());
        }
        //如果为空  插入默认值
        if (CollUtil.isEmpty(req.getRefundTypeList())){
            req.setRefundTypeList(ListUtil.toList(SmallRefundConfigPo.RefundTypeEnum.NORMAL_REFUND.getCode()));
        }
        if (CollUtil.isEmpty(req.getHitchTypeList())){
            req.setHitchTypeList(ListUtil.toList(SmallRefundConfigPo.HitchTypeEnum.NO_FAULT.getCode()));
        }
        req.setHitchType(getBinaryByHitchTypeEnum(req.getHitchTypeList()));
        req.setRefundType(getBinaryByRefundTypeEnum(req.getRefundTypeList()));
        //对原本的数据进行查询
        SmallRefundConfigPo smallRefundConfigPo = smallProConfigMapper.getRefundConfigById(req.getId());
        List<SmallRefundProductConfigPo> smallRefundProductConfigPos = smallProConfigMapper.listRefundConfigProduct(req.getId());
        //数据对比工具
        SmallRefundConfigContrastBO oldObj = new SmallRefundConfigContrastBO();
        BeanUtils.copyProperties(smallRefundConfigPo, oldObj);
        oldObj.setRefundTypeList(CommenUtil.BHDConverterByEnums(oldObj.getRefundType()));
        oldObj.setHitchTypeList(CommenUtil.BHDConverterByEnums(oldObj.getHitchType()));
        SmallRefundConfigContrastBO newObj = new SmallRefundConfigContrastBO();
        BeanUtils.copyProperties(req, newObj);
        newObj.setRefundTypeList(CommenUtil.BHDConverterByEnums(oldObj.getRefundType()));
        newObj.setHitchTypeList(CommenUtil.BHDConverterByEnums(oldObj.getHitchType()));
        List<String> fieldModifiedLog = WorkLogUtil.getFieldModifiedLog(SmallRefundConfigContrastBO.class, oldObj, newObj);
        //更新操作内容
        List<String> fieldLog = new ArrayList<>();
        //判断基本配置是否有值更新
        AtomicBoolean flag = new AtomicBoolean(false);
        if (CollUtil.isNotEmpty(fieldModifiedLog)) {
            for (String s : fieldModifiedLog) {
                String handleLog = s;
                if (StrUtil.count(handleLog, "由【空】修改为【空】") > 0) {
                    continue;
                }
                handleLog = StrUtil.replaceIgnoreCase(handleLog, "false", "关闭");
                handleLog = StrUtil.replaceIgnoreCase(handleLog, "true", "开启");
                if (StrUtil.count(handleLog, "服务类型") > 0) {
                    for (SmallRefundServiceTypeEnum value : SmallRefundServiceTypeEnum.values()) {
                        handleLog = StrUtil.replaceIgnoreCase(handleLog,String.valueOf(value.getCode()), value.getMessage());
                    }
                }
                if (StrUtil.count(handleLog, "故障类型") > 0) {
                    for (SmallRefundConfigPo.HitchTypeEnum value : SmallRefundConfigPo.HitchTypeEnum.values()) {
                        handleLog = StrUtil.replaceIgnoreCase(handleLog,String.valueOf(value.getCode()), value.getMessage());
                    }
                }
                if (StrUtil.count(handleLog, "退款方式") > 0) {
                    for (SmallRefundConfigPo.RefundTypeEnum value : SmallRefundConfigPo.RefundTypeEnum.values()) {
                        handleLog = StrUtil.replaceIgnoreCase(handleLog,String.valueOf(value.getCode()), value.getMessage());
                    }
                }
                fieldLog.add(handleLog);
                flag.set(Boolean.TRUE);
            }
        }
        //根据当前的置换类型来获取用户当前选择的值
        List<SmallRefundConfigPo.ConfigTypeAndValue> newConfigTypeAndValues = getRefundConfigTypeAndValues(req);
        //分组操作 根据配置类型来分组当前配置信息
        Map<SmallRefundConfigPo.RefundProductConfigTypeEnum, List<SmallRefundConfigPo.ConfigTypeAndValue>> configTypeMap =
                newConfigTypeAndValues.stream().collect(Collectors.groupingBy(SmallRefundConfigPo.ConfigTypeAndValue::getConfigType));
        Map<Integer, List<SmallRefundProductConfigPo>> productConfigMap = smallRefundProductConfigPos.stream().collect(Collectors.groupingBy(SmallRefundProductConfigPo::getConfigType));
        //给map补类型
        for (SmallRefundConfigPo.RefundProductConfigTypeEnum value : SmallRefundConfigPo.RefundProductConfigTypeEnum.values()) {
            if (!configTypeMap.containsKey(value)){
                configTypeMap.put(value, new ArrayList<>());
            }
            if (!productConfigMap.containsKey(value.getCode())){
                productConfigMap.put(value.getCode(),new ArrayList<>());
            }
        }
        List<SmallRefundProductConfigPo> addProductConfigPoList = new ArrayList<>();
        List<SmallRefundProductConfigPo> delProductConfigPoList = new ArrayList<>();
        for (Map.Entry<SmallRefundConfigPo.RefundProductConfigTypeEnum, List<SmallRefundConfigPo.ConfigTypeAndValue>> productConfigTypeEnumListEntry : configTypeMap.entrySet()) {
            for (Map.Entry<Integer, List<SmallRefundProductConfigPo>> integerListEntry : productConfigMap.entrySet()) {
                //当配置相同时  来比较是否有数据更改
                if (Objects.equals(productConfigTypeEnumListEntry.getKey().getCode(),integerListEntry.getKey())){
                    //对结果进行排序
                    List<Integer> collect = new ArrayList<>();
                    if (CollUtil.isNotEmpty(productConfigTypeEnumListEntry.getValue())){
                        //新数据 用户更新传递过来的数据
                        collect = productConfigTypeEnumListEntry.getValue().stream().map(SmallRefundConfigPo.ConfigTypeAndValue::getConfigValue).sorted().distinct().collect(Collectors.toList());
                    }
                    List<Integer> collect1 = new ArrayList<>();
                    if (CollUtil.isNotEmpty(integerListEntry.getValue())){
                        //旧数据 未更新之前的数据
                        collect1 = integerListEntry.getValue().stream().map(SmallRefundProductConfigPo::getConfigValue).sorted().distinct().collect(Collectors.toList());
                    }
                    if (CollUtil.isEmpty(collect) && CollUtil.isEmpty(collect1)){
                        continue;
                    }
                    //取两次单差集  分开更新和删除操作
                    // 新数据中有但旧数据没有
                    Collection<Integer> addSubtract = CollUtil.subtract(collect, collect1);
                    //旧数据中有  新数据中没有
                    Collection<Integer> delSubtract = CollUtil.subtract(collect1, collect);
                    //如果差集不为空的话 数据进行了更新
                    if (CollUtil.isNotEmpty(addSubtract)){
                        //存储进行更新
                        addSubtract.forEach(add ->
                                addProductConfigPoList.add(new SmallRefundProductConfigPo().setConfigType(productConfigTypeEnumListEntry.getKey().getCode())
                                        .setConfigValue(add).setFkConfigId(req.getId())));
                        fieldLog.add(StrUtil.format("新增【{}】值：{}",EnumUtil.getMessageByCode(SmallRefundConfigPo.RefundProductConfigTypeEnum.class,integerListEntry.getKey()),addSubtract.toString()));
                    }
                    if (CollUtil.isNotEmpty(delSubtract)){
                        //存储进行删除
                        delSubtract.forEach(del ->
                                delProductConfigPoList.add(new SmallRefundProductConfigPo().setConfigType(productConfigTypeEnumListEntry.getKey().getCode())
                                        .setConfigValue(del).setFkConfigId(req.getId())));
                        fieldLog.add(StrUtil.format("删除【{}】值：{}",EnumUtil.getMessageByCode(SmallRefundConfigPo.RefundProductConfigTypeEnum.class,integerListEntry.getKey()),delSubtract.toString()));
                    }
                }
            }
        }
        //如果日志操作中没有值 说明没有进行更新操作
        if (CollUtil.isEmpty(fieldLog)) {
            return R.success("无内容更新！");
        }
        //调用日志接口进行日志记录
        SmallRefundConfigLogDocument smallProConfigLogDocument = new SmallRefundConfigLogDocument();
        List<SmallRefundConfigLogDocument.Conts> contsList = new ArrayList<>();
        smallProConfigLogDocument.setConfigId(Long.valueOf(req.getId()));
        //构建日志
        contsList.add(new SmallRefundConfigLogDocument.Conts().setDTime(LocalDateTime.now()).setComment(StrUtil.format("更新操作：{}",fieldLog.toString())).setInUser(oaUserBO.getUserName()).setShowType(Boolean.TRUE));
        smallProConfigLogDocument.setCons(contsList);
        //添加日志操作
        smallRefundConfigLogService.saveSmallProConfigLog(smallProConfigLogDocument);
        MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, () -> {
            if (flag.get()){
                smallProConfigMapper.updateRefundConfig(req,oaUserBO.getUserName());
            }
            if (CollUtil.isNotEmpty(addProductConfigPoList)){
                smallProConfigMapper.updateRefundProductConfig(req.getId(), addProductConfigPoList, oaUserBO.getUserName());
            }
            if (CollUtil.isNotEmpty(delProductConfigPoList)){
                smallProConfigMapper.delRefundProductConfig(req.getId(), delProductConfigPoList, oaUserBO.getUserName());
            }
        }).commit();
        return R.success("更新成功！");
    }

    /**
     * 进行数据过滤
     * @param req req
     * @return List<SmallRefundConfigPo.ConfigTypeAndValue>
     */
    private List<SmallRefundConfigPo.ConfigTypeAndValue> getRefundConfigTypeAndValues(RefundGoodsConfigSaveReq req) {
        List<SmallRefundConfigPo.ConfigTypeAndValue> configTypeAndValues = new ArrayList<>();
        if (CollUtil.isNotEmpty(req.getBrandIdList())) {
            req.getBrandIdList().stream().distinct().forEach(brandId -> configTypeAndValues.add(new SmallRefundConfigPo.ConfigTypeAndValue()
                    .setConfigType(SmallRefundConfigPo.RefundProductConfigTypeEnum.BRAND).setConfigValue(brandId)));
        }
        if (CollUtil.isNotEmpty(req.getCidList())) {
            req.getCidList().stream().distinct().forEach(ci -> configTypeAndValues.add(new SmallRefundConfigPo.ConfigTypeAndValue()
                    .setConfigType(SmallRefundConfigPo.RefundProductConfigTypeEnum.CATEGORY).setConfigValue(ci)));
        }
        if (StrUtil.isNotEmpty(req.getSku())) {
            CommenUtil.toIntList(req.getSku()).stream().distinct().forEach(sku -> configTypeAndValues.add(new SmallRefundConfigPo.ConfigTypeAndValue()
                    .setConfigType(SmallRefundConfigPo.RefundProductConfigTypeEnum.SKU).setConfigValue(sku)));
        }
        if (StrUtil.isNotEmpty(req.getSpu())) {
            CommenUtil.toIntList(req.getSpu()).stream().distinct().forEach(spu -> configTypeAndValues.add(new SmallRefundConfigPo.ConfigTypeAndValue()
                    .setConfigType(SmallRefundConfigPo.RefundProductConfigTypeEnum.SPU).setConfigValue(spu)));
        }
        return configTypeAndValues;
    }

    /**
     * 退货配置添加
     * @param req req
     * @return R<Boolean>
     */
    @Override
    public R<Boolean> addRefundGoodsConfig(RefundGoodsConfigSaveReq req) {
        //添加操作
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null){
            return R.error("登陆信息获取失败！");
        }
        if (Objects.isNull(req.getServiceType()) ||
                !Arrays.stream(SmallRefundServiceTypeEnum.values()).map(SmallRefundServiceTypeEnum::getCode).collect(Collectors.toList()).contains(req.getServiceType())){
            return R.error("服务类型错误!");
        }
        if (req.getStartDays() == null || req.getStartDays() < 0 || req.getEndDays() == null || req.getEndDays() < 0) {
            return R.error("天数设置不能为空或负数，请检查!");
        }
        if (!Optional.ofNullable(req.getDifferentPricePercent()).isPresent()){
            return R.error("补差价百分比不能为空!");
        }
        //附件折价默认为true
        if (req.getIsPartsDifferent() == null) {
            req.setIsPartsDifferent(Boolean.TRUE);
        }
        //获取当前添加人的xtenant
        if (req.getXtenant() == null){
            req.setXtenant(oaUserBO.getXTenant());
        }
        //如果为空  插入默认值
        if (CollUtil.isEmpty(req.getRefundTypeList())){
            req.setRefundTypeList(ListUtil.toList(SmallRefundConfigPo.RefundTypeEnum.NORMAL_REFUND.getCode()));
        }
        if (CollUtil.isEmpty(req.getHitchTypeList())){
            req.setHitchTypeList(ListUtil.toList(SmallRefundConfigPo.HitchTypeEnum.NO_FAULT.getCode()));
        }
        req.setHitchType(getBinaryByHitchTypeEnum(req.getHitchTypeList()));
        req.setRefundType(getBinaryByRefundTypeEnum(req.getRefundTypeList()));
        //对可换货商品进行添加
        List<SmallRefundConfigPo.ConfigTypeAndValue> configTypeAndValues = new ArrayList<>();
        if (CollUtil.isNotEmpty(req.getBrandIdList())) {
            req.getBrandIdList().forEach(br -> configTypeAndValues.add(new SmallRefundConfigPo.ConfigTypeAndValue()
                    .setConfigType(SmallRefundConfigPo.RefundProductConfigTypeEnum.BRAND).setConfigValue(br)));
        }
        if (CollUtil.isNotEmpty(req.getCidList())) {
            req.getCidList().forEach(ci -> configTypeAndValues.add(new SmallRefundConfigPo.ConfigTypeAndValue()
                    .setConfigType(SmallRefundConfigPo.RefundProductConfigTypeEnum.CATEGORY).setConfigValue(ci)));
        }
        if (StrUtil.isNotEmpty(req.getSku())) {
            CommenUtil.toIntList(req.getSku()).forEach(sku -> configTypeAndValues.add(new SmallRefundConfigPo.ConfigTypeAndValue()
                    .setConfigType(SmallRefundConfigPo.RefundProductConfigTypeEnum.SKU).setConfigValue(sku)));
        }
        if (StrUtil.isNotEmpty(req.getSpu())) {
            CommenUtil.toIntList(req.getSpu()).forEach(spu -> configTypeAndValues.add(new SmallRefundConfigPo.ConfigTypeAndValue()
                    .setConfigType(SmallRefundConfigPo.RefundProductConfigTypeEnum.SPU).setConfigValue(spu)));
        }
        //开事务 进行添加操作
        MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, () -> {
            smallProConfigMapper.addRefundConfig(req,oaUserBO.getUserName());
            if (CollUtil.isNotEmpty(configTypeAndValues)){
                smallProConfigMapper.addRefundProductConfig(req.getId(),configTypeAndValues, oaUserBO.getUserName());
            }
        }).commit();
        //构建日志
        SmallRefundConfigLogDocument smallProConfigLogDocument = new SmallRefundConfigLogDocument();
        List<SmallRefundConfigLogDocument.Conts> contsList = new ArrayList<>();
        smallProConfigLogDocument.setConfigId(Long.valueOf(req.getId()));
        contsList.add(new SmallRefundConfigLogDocument.Conts().setDTime(LocalDateTime.now())
                .setComment(StrUtil.format("新增配置：标题{},服务类型{}",req.getTitle(),req.getServiceType()))
                .setInUser(oaUserBO.getUserName()).setShowType(Boolean.TRUE));
        smallProConfigLogDocument.setCons(contsList);
        //添加日志操作
        smallRefundConfigLogService.saveSmallProConfigLog(smallProConfigLogDocument);
        return R.success("添加成功！");
    }

    @Override
    public R<Boolean> delRefundGoodsConfig(Integer id) {
        //查询当前配置信息
        if (CommenUtil.isNullOrZero(id)){
            return R.error("删除数据错误!");
        }
        SmallRefundConfigPo configById = smallProConfigMapper.getRefundConfigById(id);
        if (configById == null || CommenUtil.isNullOrZero(configById.getId())){
            return R.error("删除失败，请刷新页面重试!");
        }
        //添加操作
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null){
            return R.error("登陆状态失效!");
        }
        SmallRefundConfigLogDocument smallProConfigLogDocument = new SmallRefundConfigLogDocument();
        List<SmallRefundConfigLogDocument.Conts> contsList = new ArrayList<>();
        smallProConfigLogDocument.setConfigId(Long.valueOf(id));
        //构建日志
        contsList.add(new SmallRefundConfigLogDocument.Conts().setDTime(LocalDateTime.now())
                .setComment(StrUtil.format("删除操作：编号{}，标题{}",configById.getId(),configById.getTitle()))
                .setInUser(oaUserBO.getUserName()).setShowType(Boolean.TRUE));
        smallProConfigLogDocument.setCons(contsList);
        MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, () -> {
            int i = smallProConfigMapper.delRefundGoodsConfig(id);
            if (i>0){
                //添加日志操作
                smallRefundConfigLogService.saveSmallProConfigLog(smallProConfigLogDocument);
            }else {
                throw new RuntimeException("删除失败！");
            }
            smallProConfigMapper.delRefundProductConfigByConfigId(id);
        }).commit();
        return R.success("删除成功!");
    }

    private Integer getBinaryByRefundTypeEnum(List<Integer> nums) {
        if (CollUtil.isEmpty(nums)) {
            return 0;
        }
        int sum = 0;
        for (Integer num : nums) {
            if (Objects.equals(num,SmallRefundConfigPo.RefundTypeEnum.NORMAL_REFUND.getCode())) {
                sum += 1;
            } else if (Objects.equals(num,SmallRefundConfigPo.RefundTypeEnum.SPECIAL_REFUND.getCode())) {
                sum += 2;
            }
        }
        return sum;
    }
    private Integer getBinaryByHitchTypeEnum(List<Integer> nums) {
        if (CollUtil.isEmpty(nums)) {
            return 0;
        }
        int sum = 0;
        for (Integer num : nums) {
            if (Objects.equals(num,SmallRefundConfigPo.HitchTypeEnum.NO_FAULT.getCode())) {
                sum += 1;
            } else if (Objects.equals(num,SmallRefundConfigPo.HitchTypeEnum.FAULTY.getCode())) {
                sum += 2;
            }
        }
        return sum;
    }

    @Override
    public SmallExchangeConfigPo buildAdvancedExchangeConfig(Integer type) {
        return new SmallExchangeConfigPo().setId(SmallProConfigService.ADVANCE_RANK_CONFIG_ID).setTitle("高级权限置换配置").setServiceType(type).setIsDifferentPrice(Boolean.TRUE)
                .setIsServiceDifferentPrice(Boolean.FALSE).setDifferentPriceType(SmallExchangeConfigPo.DifferentPriceTypeEnum.DIFFERENT_PRICE.getCode())
                .setDifferentPricePercent(new BigDecimal("100"))
                .setProductConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.CATEGORY.getCode())
                .setPolicyContent("高级权限置换,不受置换政策的限制")
                ;
    }


    /**
     * 获取配置列表
     * @param productRefundPo
     * @return
     */
    private List<SmallRefundConfigPo.ConfigTypeAndValue> listRefundConfigTypeAndValues(ProductRefundPo productRefundPo) {
        List<SmallRefundConfigPo.ConfigTypeAndValue> configTypeAndValues = new LinkedList<>();
        if (CommenUtil.isNotNullZero(productRefundPo.getPpriceid())){
            configTypeAndValues.add(new SmallRefundConfigPo.ConfigTypeAndValue()
                    .setConfigType(SmallRefundConfigPo.RefundProductConfigTypeEnum.SKU).setConfigValue(productRefundPo.getPpriceid()));
        }
        if (CommenUtil.isNotNullZero(productRefundPo.getProductId())){
            configTypeAndValues.add(new SmallRefundConfigPo.ConfigTypeAndValue()
                    .setConfigType(SmallRefundConfigPo.RefundProductConfigTypeEnum.SPU).setConfigValue(productRefundPo.getProductId()));
        }
        if (CommenUtil.isNotNullZero(productRefundPo.getCid())){
            configTypeAndValues.add(new SmallRefundConfigPo.ConfigTypeAndValue()
                    .setConfigType(SmallRefundConfigPo.RefundProductConfigTypeEnum.CATEGORY).setConfigValue(productRefundPo.getCid()));
        }
        if (CommenUtil.isNotNullZero(productRefundPo.getBrandID())){
            configTypeAndValues.add(new SmallRefundConfigPo.ConfigTypeAndValue()
                    .setConfigType(SmallRefundConfigPo.RefundProductConfigTypeEnum.BRAND).setConfigValue(productRefundPo.getBrandID()));
        }
        return configTypeAndValues;
    }
    /**
     * 更新小件换货配置v2
     *
     * @param req
     * @return
     */
    @Override
    public R<Boolean> updateExchangeGoodsConfigV2(ExchangeGoodsConfigSaveReqV2 req) {
        //更新操作
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null){
            return R.error("登陆信息获取失败！");
        }
        //配置方式校验
        if (Objects.isNull(req.getServiceType()) ||
                !Arrays.stream(SmallExchangeConfigPo.ExchangeServiceTypeEnum.values()).map(SmallExchangeConfigPo.ExchangeServiceTypeEnum::getCode).collect(Collectors.toList()).contains(req.getServiceType())){
            return R.error("服务类型错误!");
        }
        //校验当补差价为空或者false的时候
        if (Boolean.FALSE.equals(Optional.ofNullable(req.getIsDifferentPrice()).orElse(Boolean.FALSE))){
            //使用商品服务为false
            req.setIsDifferentPrice(Boolean.FALSE);
            if (!Objects.equals(SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode(),req.getServiceType())){
                req.setIsServiceDifferentPrice(Boolean.FALSE);
            }
            req.setDifferentPriceType(null);
            req.setDifferentPricePercent(null);
        } else {
            req.setIsDifferentPrice(Boolean.TRUE);
            req.setIsServiceDifferentPrice(Optional.ofNullable(req.getIsServiceDifferentPrice()).orElse(Boolean.FALSE));
            if (!Objects.equals(SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode(),req.getServiceType()) && Boolean.TRUE.equals(req.getIsServiceDifferentPrice())){
                return R.error("服务类型是年包才能启用!");
            }
            if (CommenUtil.isNullOrZero(req.getDifferentPriceType())){
                return R.error("补差价方式不能为空!");
            }
            if (!Optional.ofNullable(req.getDifferentPricePercent()).isPresent()){
                return R.error("补差价百分比不能为空!");
            }
        }
        //配置方式校验
        if (CommenUtil.isNullOrZero(req.getProductConfigType()) || Objects.isNull(req.getProductConfig())){
            return R.error("适用项可换货商品配置方式不能为空!");
        }
        //对原本的数据进行查询
        SmallExchangeConfigPo smallExchangeConfigPo =  smallProConfigMapper.getConfigById(req.getId());
        List<SmallExchangeProductConfigPo> smallExchangeProductConfigPos = smallProConfigMapper.listConfigProduct(req.getId());
        //数据对比工具
        SmallExchangeConfigContrastBO oldObj = smallExchangeConfigMapStruct.toSmallExchangeConfigContrastBO(smallExchangeConfigPo);
        SmallExchangeConfigContrastBO newObj = smallExchangeConfigMapStruct.toSmallExchangeConfigContrastBO(req);
        List<String> fieldModifiedLog = WorkLogUtil.getFieldModifiedLog(SmallExchangeConfigContrastBO.class, oldObj, newObj);
        //更新操作内容
        List<String> fieldLog = new ArrayList<>();
        //判断基本配置是否有值更新
        AtomicBoolean flag = new AtomicBoolean(false);
        if (CollUtil.isNotEmpty(fieldModifiedLog)) {
            for (String s : fieldModifiedLog) {
                String handleLog = s;
                if (StrUtil.count(handleLog, "由【空】修改为【空】") > 0) {
                    continue;
                }
                handleLog = StrUtil.replaceIgnoreCase(handleLog, "false", "关闭");
                handleLog = StrUtil.replaceIgnoreCase(handleLog, "true", "开启");
                if (StrUtil.count(handleLog, "服务类型") > 0) {
                    for (SmallExchangeConfigPo.ExchangeServiceTypeEnum value : SmallExchangeConfigPo.ExchangeServiceTypeEnum.values()) {
                        if (Objects.equals(value.getCode() , SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_NULL.getCode())){
                            handleLog = StrUtil.replaceIgnoreCase(handleLog,String.valueOf(value.getCode()), "无服务");
                        }
                        handleLog = StrUtil.replaceIgnoreCase(handleLog,String.valueOf(value.getCode()), value.getMessage());
                    }
                }
                if (StrUtil.count(handleLog, "补差方式") > 0) {
                    for (SmallExchangeConfigPo.DifferentPriceTypeEnum value : SmallExchangeConfigPo.DifferentPriceTypeEnum.values()) {
                        handleLog = StrUtil.replaceIgnoreCase(handleLog,String.valueOf(value.getCode()), value.getMessage());
                    }
                }
                handleLog = StrUtil.replaceIgnoreCase(handleLog, "【SPU】", "【商品id】");
                handleLog = StrUtil.replaceIgnoreCase(handleLog, "【SKU】", "【sku_id】");
                fieldLog.add(handleLog);
                flag.set(Boolean.TRUE);
            }
        }
        //适用项可换货商品配置
        List<SmallExchangeProductConfigPo> productConfigPoList = new ArrayList<>();
        String msg = getSmallExchangeProductConfigList(productConfigPoList, req.getProductConfig(), req.getId(), req.getProductConfigType());
        if (StrUtil.isNotBlank(msg)){
            return R.error(StrUtil.format("适用项{}",msg));
        }
        productConfigPoList.forEach(v -> v.setKind(SmallExchangeProductConfigKindEnum.PRODUCT_CONFIG.getCode()));

        Map<String, SmallExchangeProductConfigPo> productConfigMap = productConfigPoList.stream().collect(Collectors.toMap(k -> StrUtil.format("{}-{}",k.getConfigType(),k.getConfigValue()), Function.identity(), (v1, v2) -> v2));
        List<SmallExchangeProductConfigPo> oldProductConfigList = smallExchangeProductConfigPos.stream().filter(v -> SmallExchangeProductConfigKindEnum.PRODUCT_CONFIG.getCode().equals(v.getKind())).collect(Collectors.toList());
        Map<String, SmallExchangeProductConfigPo> oldProductConfigMap = oldProductConfigList.stream().collect(Collectors.toMap(k -> StrUtil.format("{}-{}",k.getConfigType(),k.getConfigValue()), Function.identity(), (v1, v2) -> v2));
        List<SmallExchangeProductConfigPo> delProductConfigPoList = oldProductConfigList.stream().filter(k -> !productConfigMap.containsKey(StrUtil.format("{}-{}",k.getConfigType(),k.getConfigValue()))).collect(Collectors.toList());
        List<SmallExchangeProductConfigPo> addProductConfigPoList = productConfigPoList.stream().filter(k -> !oldProductConfigMap.containsKey(StrUtil.format("{}-{}",k.getConfigType(),k.getConfigValue()))).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(delProductConfigPoList)) {
            Integer configType = delProductConfigPoList.stream().findFirst().map(SmallExchangeProductConfigPo::getConfigType).orElse(1);
            String configName = EnumUtil.getMessageByCode(SmallExchangeConfigPo.ProductConfigTypeEnum.class, configType, "").replaceAll("SPU", "商品id").replaceAll("SKU", "sku_id");
            String ids = delProductConfigPoList.stream().map(v -> Convert.toStr(v.getConfigValue())).collect(Collectors.joining(","));
            fieldLog.add(StrUtil.format("适用项删除【{}】值：{}",configName,ids));
        }
        if (CollUtil.isNotEmpty(addProductConfigPoList)) {
            Integer configType = addProductConfigPoList.stream().findFirst().map(SmallExchangeProductConfigPo::getConfigType).orElse(1);
            String configName = EnumUtil.getMessageByCode(SmallExchangeConfigPo.ProductConfigTypeEnum.class, configType, "").replaceAll("SPU", "商品id").replaceAll("SKU", "sku_id");
            String ids = addProductConfigPoList.stream().map(v -> Convert.toStr(v.getConfigValue())).collect(Collectors.joining(","));
            fieldLog.add(StrUtil.format("适用项新增【{}】值：{}",configName,ids));
        }

        //附加项可换货商品配置
        List<SmallExchangeProductConfigPo> additionProductConfigPoList = new ArrayList<>();
        if (CommenUtil.isNotNullZero(req.getAdditionProductConfigType())) {
            String additionMsg = getSmallExchangeProductConfigList(additionProductConfigPoList, req.getAdditionProductConfig(), req.getId(), req.getAdditionProductConfigType());
            if (StrUtil.isNotBlank(additionMsg)){
                return R.error(StrUtil.format("附加项{}",additionMsg));
            }
            additionProductConfigPoList.forEach(v -> v.setKind(SmallExchangeProductConfigKindEnum.ADDITION_PRODUCT_CONFIG.getCode()));
        }
        List<SmallExchangeProductConfigPo> oldAdditionProductConfigList = smallExchangeProductConfigPos.stream().filter(v -> SmallExchangeProductConfigKindEnum.ADDITION_PRODUCT_CONFIG.getCode().equals(v.getKind())).collect(Collectors.toList());
        Map<String, SmallExchangeProductConfigPo> additionProductConfigMap = additionProductConfigPoList.stream().collect(Collectors.toMap(k -> StrUtil.format("{}-{}",k.getConfigType(),k.getConfigValue()), Function.identity(), (v1, v2) -> v2));
        Map<String, SmallExchangeProductConfigPo> oldAdditionProductConfigMap = oldAdditionProductConfigList.stream().collect(Collectors.toMap(k -> StrUtil.format("{}-{}",k.getConfigType(),k.getConfigValue()), Function.identity(), (v1, v2) -> v2));
        List<SmallExchangeProductConfigPo> delAdditionProductConfigPoList = oldAdditionProductConfigList.stream().filter(k -> !additionProductConfigMap.containsKey(StrUtil.format("{}-{}",k.getConfigType(),k.getConfigValue()))).collect(Collectors.toList());
        List<SmallExchangeProductConfigPo> addAdditionProductConfigPoList = additionProductConfigPoList.stream().filter(k -> !oldAdditionProductConfigMap.containsKey(StrUtil.format("{}-{}",k.getConfigType(),k.getConfigValue()))).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(delAdditionProductConfigPoList)) {
            Integer configType = delAdditionProductConfigPoList.stream().findFirst().map(SmallExchangeProductConfigPo::getConfigType).orElse(1);
            String configName = EnumUtil.getMessageByCode(SmallExchangeConfigPo.ProductConfigTypeEnum.class, configType, "").replaceAll("SPU", "商品id").replaceAll("SKU", "sku_id");
            String ids = delAdditionProductConfigPoList.stream().map(v -> Convert.toStr(v.getConfigValue())).collect(Collectors.joining(","));
            fieldLog.add(StrUtil.format("附加项删除【{}】值：{}",configName,ids));
        }
        if (CollUtil.isNotEmpty(addAdditionProductConfigPoList)) {
            Integer configType = addAdditionProductConfigPoList.stream().findFirst().map(SmallExchangeProductConfigPo::getConfigType).orElse(1);
            String configName = EnumUtil.getMessageByCode(SmallExchangeConfigPo.ProductConfigTypeEnum.class, configType, "").replaceAll("SPU", "商品id").replaceAll("SKU", "sku_id");
            String ids = addAdditionProductConfigPoList.stream().map(v -> Convert.toStr(v.getConfigValue())).collect(Collectors.joining(","));
            fieldLog.add(StrUtil.format("附加项新增【{}】值：{}",configName,ids));
        }

        //如果日志操作中没有值 说明没有进行更新操作
        if (CollUtil.isEmpty(fieldLog)) {
            return R.success("无内容更新！");
        }
        SmallExchangeConfigPo exchangeProductConfigPo = smallExchangeConfigMapStruct.toSmallExchangeConfigPo(req);
        //调用日志接口进行日志记录
        SmallExchangeConfigLogDocument smallProConfigLogDocument = new SmallExchangeConfigLogDocument();
        List<SmallExchangeConfigLogDocument.Conts> contsList = new ArrayList<>();
        smallProConfigLogDocument.setConfigId(Long.valueOf(req.getId()));
        //构建日志
        contsList.add(new SmallExchangeConfigLogDocument.Conts().setDTime(LocalDateTime.now()).setComment(StrUtil.format("更新操作：{}",fieldLog.toString())).setInUser(oaUserBO.getUserName()).setShowType(Boolean.TRUE));
        smallProConfigLogDocument.setCons(contsList);
        //添加日志操作
        smallExchangeConfigLogService.saveSmallProConfigLog(smallProConfigLogDocument);
        MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, () -> {
            if (flag.get()){
                smallProConfigMapper.updateExchangeProductConfigById(exchangeProductConfigPo);
            }
            if (CollUtil.isNotEmpty(addProductConfigPoList)){
                addProductConfigPoList.forEach(v -> v.setCreateUser(oaUserBO.getUserName()));
                smallProConfigMapper.saveExchangeProductConfigBatch(addProductConfigPoList);
            }
            if (CollUtil.isNotEmpty(delProductConfigPoList)){
                List<Integer> ids = delProductConfigPoList.stream().map(SmallExchangeProductConfigPo::getId).collect(Collectors.toList());
                smallProConfigMapper.delExchangeProductConfigByIds(ids);
            }
            if (CollUtil.isNotEmpty(addAdditionProductConfigPoList)){
                addAdditionProductConfigPoList.forEach(v -> v.setCreateUser(oaUserBO.getUserName()));
                smallProConfigMapper.saveExchangeProductConfigBatch(addAdditionProductConfigPoList);
            }
            if (CollUtil.isNotEmpty(delAdditionProductConfigPoList)){
                List<Integer> ids = delAdditionProductConfigPoList.stream().map(SmallExchangeProductConfigPo::getId).collect(Collectors.toList());
                smallProConfigMapper.delExchangeProductConfigByIds(ids);
            }
        }).commit();
        return R.success("更新成功！");
    }

    /**
     * 新增小件换货配置v2
     *
     * @param req
     * @return
     */
    @Override
    public R<Boolean> addExchangeGoodsConfigV2(ExchangeGoodsConfigSaveReqV2 req) {
        //添加操作
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null){
            return R.error("登陆信息获取失败！");
        }
        if (Objects.isNull(req.getServiceType()) ||
                !Arrays.stream(SmallExchangeConfigPo.ExchangeServiceTypeEnum.values()).map(SmallExchangeConfigPo.ExchangeServiceTypeEnum::getCode).collect(Collectors.toList()).contains(req.getServiceType())){
            return R.error("服务类型错误!");
        }
        //校验当补差价为空或者false的时候
        if (Boolean.FALSE.equals(Optional.ofNullable(req.getIsDifferentPrice()).orElse(Boolean.FALSE))){
            //使用商品服务为false
            req.setIsDifferentPrice(Boolean.FALSE);
            if (!Objects.equals(SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode(),req.getServiceType())){
                req.setIsServiceDifferentPrice(Boolean.FALSE);
            }
            req.setDifferentPriceType(null);
            req.setDifferentPricePercent(null);
        }else {
            req.setIsDifferentPrice(Boolean.TRUE);
            req.setIsServiceDifferentPrice(Optional.ofNullable(req.getIsServiceDifferentPrice()).orElse(Boolean.FALSE));
            if (!Objects.equals(SmallExchangeConfigPo.ExchangeServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode(),req.getServiceType()) && Boolean.TRUE.equals(req.getIsServiceDifferentPrice())){
                return R.error("服务类型是年包才能启用!");
            }
            if (CommenUtil.isNullOrZero(req.getDifferentPriceType())){
                return R.error("补差价方式不能为空!");
            }
            if (!Optional.ofNullable(req.getDifferentPricePercent()).isPresent()){
                return R.error("补差价百分比不能为空!");
            }
        }
        //配置方式校验
        if (CommenUtil.isNullOrZero(req.getProductConfigType()) || Objects.isNull(req.getProductConfig())){
            return R.error("适用项可换货商品配置方式不能为空!");
        }
        //适用项配置
        List<SmallExchangeProductConfigPo> productConfigPoList = new ArrayList<>();
        String msg = getSmallExchangeProductConfigList(productConfigPoList, req.getProductConfig(), req.getId(), req.getProductConfigType());
        if (StrUtil.isNotBlank(msg)){
            return R.error(StrUtil.format("适用项{}",msg));
        }
        //附加项配置
        List<SmallExchangeProductConfigPo> additionProductConfigPoList = new ArrayList<>();
        if (CommenUtil.isNotNullZero(req.getAdditionProductConfigType())) {
            String additionMsg = getSmallExchangeProductConfigList(additionProductConfigPoList, req.getAdditionProductConfig(), req.getId(), req.getAdditionProductConfigType());
            if (StrUtil.isNotBlank(additionMsg)){
                return R.error(StrUtil.format("附加项{}",additionMsg));
            }
        }

        SmallExchangeConfigPo exchangeConfigPo = smallExchangeConfigMapStruct.toSmallExchangeConfigPo(req);
        exchangeConfigPo.setCreateUser(oaUserBO.getUserName());
        //开事务 进行添加操作
        MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, () -> {
            smallProConfigMapper.saveExchangeConfig(exchangeConfigPo);
            productConfigPoList.forEach(v -> {
                v.setFkConfigId(exchangeConfigPo.getId());
                v.setKind(SmallExchangeProductConfigKindEnum.PRODUCT_CONFIG.getCode());
                v.setCreateUser(oaUserBO.getUserName());
            });
            smallProConfigMapper.saveExchangeProductConfigBatch(productConfigPoList);
            if (CollUtil.isNotEmpty(additionProductConfigPoList)) {
                additionProductConfigPoList.forEach(v -> {
                    v.setFkConfigId(exchangeConfigPo.getId());
                    v.setKind(SmallExchangeProductConfigKindEnum.ADDITION_PRODUCT_CONFIG.getCode());
                    v.setCreateUser(oaUserBO.getUserName());
                });
                smallProConfigMapper.saveExchangeProductConfigBatch(additionProductConfigPoList);
            }
        }).commit();
        //构建日志
        SmallExchangeConfigLogDocument smallProConfigLogDocument = new SmallExchangeConfigLogDocument();
        List<SmallExchangeConfigLogDocument.Conts> contsList = new ArrayList<>();
        smallProConfigLogDocument.setConfigId(Long.valueOf(exchangeConfigPo.getId()));
        String messageByCode = EnumUtil.getMessageByCode(SmallExchangeConfigPo.ExchangeServiceTypeEnum.class, req.getServiceType());
        if (Objects.equals(req.getServiceType(), 0)) {
            messageByCode = "无服务";
        }
        contsList.add(new SmallExchangeConfigLogDocument.Conts().setDTime(LocalDateTime.now())
                .setComment(StrUtil.format("新增配置：标题【{}】,服务类型【{}】", req.getTitle(), messageByCode))
                .setInUser(oaUserBO.getUserName()).setShowType(Boolean.TRUE));
        smallProConfigLogDocument.setCons(contsList);
        //添加日志操作
        smallExchangeConfigLogService.saveSmallProConfigLog(smallProConfigLogDocument);
        return R.success("添加成功！");
    }

    /**
     * 根据id查询小件换货配置v2
     *
     * @param id
     * @return
     */
    @Override
    public ExchangeGoodsConfigResV2 getExchangeGoodsConfigById(Integer id) {
        SmallExchangeConfigPo configById = this.getConfigById(id);
        ExchangeGoodsConfigResV2 result = smallExchangeConfigMapStruct.toSmallExchangeConfigResV2(configById);
        List<SmallExchangeProductConfigPo> smallExchangeProductConfigPos = smallProConfigMapper.listConfigProduct(id);
        if (CollUtil.isEmpty(smallExchangeProductConfigPos)) {
            return result;
        }
        Map<Integer, List<SmallExchangeProductConfigPo>> configKindMap = smallExchangeProductConfigPos.stream().collect(Collectors.groupingBy(SmallExchangeProductConfigPo::getKind));
        for (Integer kind : configKindMap.keySet()) {
            List<SmallExchangeProductConfigPo> productConfigList = configKindMap.get(kind);
            if (CollUtil.isEmpty(productConfigList)) {
                continue;
            }
            if (SmallExchangeProductConfigKindEnum.PRODUCT_CONFIG.getCode().equals(kind)) {
                ExchangeGoodsConfigProductRes productConfig = new ExchangeGoodsConfigProductRes();
                if (SmallExchangeConfigPo.ProductConfigTypeEnum.BRAND_CATEGORY.getCode().equals(result.getProductConfigType())) {
                    List<Integer> brandIds = productConfigList.stream().filter(v -> SmallExchangeConfigPo.ProductConfigTypeEnum.BRAND.getCode().equals(v.getConfigType())).map(SmallExchangeProductConfigPo::getConfigValue).collect(Collectors.toList());
                    List<Integer> cids = productConfigList.stream().filter(v -> SmallExchangeConfigPo.ProductConfigTypeEnum.CATEGORY.getCode().equals(v.getConfigType())).map(SmallExchangeProductConfigPo::getConfigValue).collect(Collectors.toList());
                    productConfig.setMainIdList(brandIds);
                    productConfig.setCidList(cids);
                } else {
                    if (SmallExchangeConfigPo.ProductConfigTypeEnum.CATEGORY.getCode().equals(result.getProductConfigType())) {
                        productConfig.setCidList(productConfigList.stream().map(SmallExchangeProductConfigPo::getConfigValue).collect(Collectors.toList()));
                    } else {
                        productConfig.setMainIdList(productConfigList.stream().map(SmallExchangeProductConfigPo::getConfigValue).collect(Collectors.toList()));
                    }
                }
                result.setProductConfig(productConfig);
            } else if (SmallExchangeProductConfigKindEnum.ADDITION_PRODUCT_CONFIG.getCode().equals(kind)) {
                ExchangeGoodsConfigProductRes additionProductConfig = new ExchangeGoodsConfigProductRes();
                if (SmallExchangeConfigPo.ProductConfigTypeEnum.BRAND_CATEGORY.getCode().equals(result.getAdditionProductConfigType())) {
                    List<Integer> brandIds = productConfigList.stream().filter(v -> SmallExchangeConfigPo.ProductConfigTypeEnum.BRAND.getCode().equals(v.getConfigType())).map(SmallExchangeProductConfigPo::getConfigValue).collect(Collectors.toList());
                    List<Integer> cids = productConfigList.stream().filter(v -> SmallExchangeConfigPo.ProductConfigTypeEnum.CATEGORY.getCode().equals(v.getConfigType())).map(SmallExchangeProductConfigPo::getConfigValue).collect(Collectors.toList());
                    additionProductConfig.setMainIdList(brandIds);
                    additionProductConfig.setCidList(cids);
                } else {
                    if (SmallExchangeConfigPo.ProductConfigTypeEnum.CATEGORY.getCode().equals(result.getAdditionProductConfigType())) {
                        additionProductConfig.setCidList(productConfigList.stream().map(SmallExchangeProductConfigPo::getConfigValue).collect(Collectors.toList()));
                    } else {
                        additionProductConfig.setMainIdList(productConfigList.stream().map(SmallExchangeProductConfigPo::getConfigValue).collect(Collectors.toList()));
                    }
                }
                result.setAdditionProductConfig(additionProductConfig);
            }
        }
        return result;
    }

    /**
     * 获取换货商品配置信息
     * @param productConfigPoList
     * @param productConfig
     * @param configId
     * @param configType
     * @return
     */
    private String getSmallExchangeProductConfigList(List<SmallExchangeProductConfigPo> productConfigPoList,
                                                     ExchangeGoodsConfigProductSaveReq productConfig,
                                                     Integer configId,
                                                     Integer configType) {
        if (Objects.isNull(productConfig)) {
            return "商品配置为空!";
        }
        SmallExchangeConfigPo.ProductConfigTypeEnum additionConfigType = Optional.ofNullable(configType)
                .map(st -> EnumUtil.getEnumByCode(SmallExchangeConfigPo.ProductConfigTypeEnum.class, st))
                .orElseThrow(() -> new CustomizeException("配置类型错误，请联系管理员!"));
        if (SmallExchangeConfigPo.ProductConfigTypeEnum.BRAND_CATEGORY.getCode().equals(configType)) {
            if (CollUtil.isEmpty(productConfig.getMainIdList()) || CollUtil.isEmpty(productConfig.getCidList())) {
                return "配置品牌与分类不能为空!";
            }
            List<SmallExchangeProductConfigPo> brandProductConfigList = productConfig.getMainIdList().stream().distinct().map(v -> new SmallExchangeProductConfigPo().setFkConfigId(configId).setConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.BRAND.getCode()).setConfigValue(v)).collect(Collectors.toList());
            List<SmallExchangeProductConfigPo> categoryProductConfigList = productConfig.getCidList().stream().distinct().map(v -> new SmallExchangeProductConfigPo().setFkConfigId(configId).setConfigType(SmallExchangeConfigPo.ProductConfigTypeEnum.CATEGORY.getCode()).setConfigValue(v)).collect(Collectors.toList());
            productConfigPoList.addAll(brandProductConfigList);
            productConfigPoList.addAll(categoryProductConfigList);
        } else {
            //处理分类配置前端传CidList
            if (SmallExchangeConfigPo.ProductConfigTypeEnum.CATEGORY.getCode().equals(configType)) {
                productConfig.setMainIdList(productConfig.getCidList());
            }
            if (CollUtil.isEmpty(productConfig.getMainIdList())) {
                return StrUtil.format("配置{}不能为空!", additionConfigType.getMessage().replaceAll("SPU", "商品id").replaceAll("SKU", "sku_id"));
            }
            List<SmallExchangeProductConfigPo> productConfigList = productConfig.getMainIdList().stream().distinct().map(v -> new SmallExchangeProductConfigPo().setFkConfigId(configId).setConfigType(configType).setConfigValue(v)).collect(Collectors.toList());
            productConfigPoList.addAll(productConfigList);
        }
        return "";
    }

}
