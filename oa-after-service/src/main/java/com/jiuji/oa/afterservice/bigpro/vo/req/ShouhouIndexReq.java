package com.jiuji.oa.afterservice.bigpro.vo.req;

import cn.hutool.core.util.StrUtil;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.oa.afterservice.bigpro.enums.IndexPageEnums;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.BusinessUtil;
import com.jiuji.oa.afterservice.common.vo.CheckDataViewScopeReq;
import com.jiuji.oa.afterservice.common.vo.Pagination;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * @author: liuwenwu
 * @date: 2020/3/24
 */
@ApiModel("售后列表查询Req")
@Data
public class ShouhouIndexReq {

    /**
     * 订单来源
     */
    private Integer fromSource;
    private String act;
    private String shou_kind1;
    private String shou_kind3;
    private String key;
    private String key1;
    private String issoft;
    private String shou_kind2;
    private String isticheng;
    private String baoxiu;
    private String shou_kinds;
    private String ishanghuo;
    private String tuihuan_kind;
    private Boolean isyouhui;
    private Boolean printFlag;
    private Integer isdat;
    /**
     * 是否查询全区，1是，0不是
     */
    private Integer allarea;
    /**
     * 售后idList
     */
    private List<Integer> shouHouIdList;

    private String isfan;
    private String pandian;
    private String newj;
    private String weixiuzuid;
    private String isweixiu;
    private String isXianhuo;
    private String istoarea;
    private String area;
    private String isquji;
    private String webstats;
    private String isyuyue;
    private String serviceType;
    private Integer webType2;

    /**是否优品机 1 优品机 2 非优品机*/
    @ApiModelProperty("是否优品机 1 优品机 2 非优品机")
    private Integer youPinMachine;
    private String date_kind;
    private LocalDateTime date1;
    private LocalDateTime date2;
    private String weixiuren;

    private Integer currpage;
    private String orderby;

    private Integer ishuishou;

    /**
     * 订单来源
     */
    private Integer sourceType;
    /**
     * 是否新机
     */
    private Integer isNewMachine ;
    /**
     * 是否优品
     */
    private Integer isExcellentProduct ;
    /**
     * 是否良品
     */
    private Integer isGoodProduct ;
    /**
     * 是否回收增值机
     */
    private Integer isValueAddedRecycling ;
    /**
     * 是否外修机
     */
    private Integer isExternalRepairMachine;
    /**
     * 是否现货
     */
    private Integer isGoodsInStock ;

    private Integer jiujian;
    private Integer ishuan;

    private Boolean needFahuo;
    /**
     * 维修等级 1-更配维修 2-芯片维修
     */
    private Integer repairLevel;
    private Integer areaid;
    private Integer authid;

    private String areaCode_;

    /// <summary>
    /// 测试结果
    /// </summary>
    private Integer ceshijieguo;

    private Integer upgradetype;
    private Integer Export;

    private Integer smsday;
    private Integer telday;

    /// <summary>
    /// 维修处理方式：外送(waisong)，自修(zixiu)
    /// </summary>
    private String wxcltype;
    /// <summary>
    /// 外送渠道id
    /// </summary>
    private Integer waisongqidao;

    /// <summary>
    /// 非保方式查询，目前主要用在中邮的 非保交易量 和 非保自修量 的查询中
    /// </summary>
    private String feibaoKind;
    /// <summary>
    /// dianchi=电池，ping=屏幕
    /// </summary>
    private String wxpjKind;

    /// <summary>
    /// 是否外送 1 外送 2自修
    /// </summary>
    private Integer waisong;
    private String waisongname;
    /// <summary>
    /// 是否收银
    /// </summary>
    private String isshouyinlock;

    private String shouhouServicesSale;

    /// <summary>
    /// 售后统计兼容查询
    /// </summary>
    private String tongjitype;
    private Integer noCheck;
    private Boolean keyIsNum;
    private List<Integer> areaIds;
    private Boolean waisongNameIsNumber;
    private Integer area_kind1;

    /**
     * 维修人
     */
    private String inUser;

    private Pagination pagination;

    /**
     * 是否授权隔离
     */
    private Boolean isAuthPart;

    private Integer authorizeId;
    private Boolean gift;
    /**
     * 是否二维码加单
     */
    @ApiModelProperty("是否二维码加单")
    private Boolean isCodeSub;
    /**
     * 1 抖音团购
     */
    @ApiModelProperty("抖音团购")
    private Integer douyinCoupon;
    /**
     * 团购券类型
     */
    @ApiModelProperty("团购券类型")
    private List<Integer> couponKinds;
    /**
     * 保修状态
     */
    private List<Integer> baoxiuList;
    /**
     * 九机服务
     */
    private List<Integer> serviceTypeList;
    /**
     * 售后险售出
     */
    private List<Integer> shouhouServicesSaleList;
    /**
     * 退换类别
     */
    private List<Integer> tuihuanKindList;

    /**
     * 查询历史库
     * 1-查询
     * 0-不查询
     */
    private Integer selectHis;

    /**
     * 1- 新机
     * 2- 非新机
     * 3- 良品
     * 4- 非良品
     * @see HuiShouAndNewJEnum
     */
    private List<Integer> huiShouAndNewJ;

    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private Integer xtenant;

    /**
     * 查询条件为唯一标识去掉其他查询条件
     * @param req
     * @return
     */
    public ShouhouIndexReq uniqueClearOtherVariable(ShouhouIndexReq req) {
        //mkc_id
        boolean uniqueFlag = Arrays.asList(IndexPageEnums.ShouKind1.MKC_ID.getCode(),IndexPageEnums.ShouKind1.ID.getCode()).contains(req.getShou_kind1());
        if (uniqueFlag && StringUtils.isNotBlank(req.getKey())) {
            ShouhouIndexReq newReq = new ShouhouIndexReq();
            newReq.setKey(req.getKey());
            newReq.setShou_kind1(req.getShou_kind1());
            newReq.setPagination(req.getPagination());
            newReq.setSelectHis(req.getSelectHis());
            return newReq;
        }
        //查询包含取机状态
        if (StrUtil.isBlank(req.getIsquji()) || "1".equals(req.getIsquji())) {
            //设置默认按照送修时间查
            if (StrUtil.isBlank(req.getDate_kind())) {
                req.setDate1(null);
                req.setDate2(null);
                req.setDate_kind("modidate");
            }
            //角色数据查询
            R dataViewRes = BusinessUtil.checkDataViewScope(CheckDataViewScopeReq.builder().moduleEnum(RoleTermModuleEnum.AFTER_SALES)
                    .getStartTimeFun(req::getDate1).getEndTimeFun(req::getDate2)
                    .setStartTimeFun(req::setDate1)
                    .setEndTimeFun(req::setDate2)
                    .build(), null);
            if (!dataViewRes.isSuccess()) {
                throw new CustomizeException(dataViewRes.getUserMsg());
            }
        }
        return req;
    }
}
