package com.jiuji.oa.afterservice.other.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.other.dao.ShouyingMapper;
import com.jiuji.oa.afterservice.other.po.Shouying;
import com.jiuji.oa.afterservice.other.service.ShouyingService;
import com.jiuji.oa.afterservice.refund.utils.RefundMoneyUtil;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-25
 */
@Service
public class ShouyingServiceImpl extends ServiceImpl<ShouyingMapper, Shouying> implements ShouyingService {

    @Override
    public List<Shouying> listSqlServer(Wrapper wrapper) {
        return baseMapper.listSqlServer(wrapper);
    }

    @Override
    public Shouying getByIdSqlServer(Integer id) {
        return baseMapper.getByIdSqlServer(id);
    }

    @Override
    public List<Shouying> listActivityShouYing(String activityType, Integer subId, BusinessTypeEnum businessType){
        Set<String> shouyingTypes = RefundMoneyUtil.getShouyingTypes(businessType);
        return baseMapper.listActivityShouYing(activityType, subId, shouyingTypes);
    }
}
