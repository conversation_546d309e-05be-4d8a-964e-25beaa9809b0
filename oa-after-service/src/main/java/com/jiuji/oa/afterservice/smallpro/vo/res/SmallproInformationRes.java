package com.jiuji.oa.afterservice.smallpro.vo.res;

import com.jiuji.cloud.after.vo.res.ChangeInfoRes;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.smallpro.bo.*;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPayInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallProReturnTypeBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproReturnsBO;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * description: <小件详情返回结果>
 * translation: <Smallpro details return results>
 *
 * <AUTHOR>
 * @date 2019/11/14
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproInformationRes implements Serializable {

    private static final long serialVersionUID = 2671240885766950687L;
    /**
     * 小件接件Id
     */
    @ApiModelProperty(value = "小件接件Id")
    private Integer smallproId;

    /**
     * 商品绑定类型
     * 0-不做任何操作
     * 1-商品绑定串号，或者商品没有绑定串号但在接件时间24h内
     * 2-商品没有绑定串号且已超过接件时间24h
     */
    private Integer productBindType;

    /**
     * 是否使用小简单新流程
     */
    private Boolean isNewSmallPro;

    /**
     * 是否展示生成现货单
     */
    private Boolean isGenerateCashOrder;

    private Boolean hasGeneratedCash;

    private Integer oldIdType;

    /**
     * 预约单id
     */
    private Integer yuYueId;
    /**
     * 用户信息
     */
    @ApiModelProperty(value = "用户信息")
    private SmallproUserInfoBO userInfo;

    /**
     * 订单用户信息
     */
    @ApiModelProperty(value = "订单用户信息")
    private SmallproUserInfoBO subUserInfo;
    /**
     * 订单信息
     */
    @ApiModelProperty(value = "订单信息")
    private List<SmallproOrderInfoBO> orderInfoList;
    /**
     * 售后接件要求信息
     */
    @ApiModelProperty(value = "售后接件要求信息")
    private List<SmallproServiceConfigBO> serviceConfig;
    /**
     * 接件地区Id
     */
    @ApiModelProperty(value = "接件地区Id")
    private Integer areaId;
    /**
     * 转地区标志
     */
    @ApiModelProperty(value = "转地区标志[1转地区|0非转地区]")
    private Integer isToArea;
    /**
     * 转地区的Id
     */
    @ApiModelProperty(value = "转地区的Id")
    private Integer toAreaId;
    /**
     * 接件地区名称
     */
    @ApiModelProperty(value = "接件地区名称")
    private String areaName;
    /**
     * 转地区的名称
     */
    @ApiModelProperty(value = "转地区的名称")
    private String toAreaName;
    /**
     * 接件时间
     */
    @ApiModelProperty(value = "接件时间")
    private String inDate;
    /**
     * 接件到当前天数
     */
    @ApiModelProperty(value = "接件到当前天数")
    private String overInDate;
    /**
     * 购买时间
     */
    @ApiModelProperty(value = "购买时间")
    private String tradeDate;
    /**
     * 购买到当前天数
     */
    @ApiModelProperty(value = "购买到当前天数")
    private String overTradeDate;
    /**
     * 接件人
     */
    @ApiModelProperty(value = "接件人")
    private String inUser;
    /**
     * 接件人Id
     */
    @ApiModelProperty(value = "接件人Id")
    private Integer inUserId;
    /**
     * 在职状态
     */
    @ApiModelProperty(value = "在职状态[1在职|0不在职]")
    private Integer isInUserInService;
    /**
     * 保修状态,old=baoXiuStatus[1保修|0非保修]
     */
    @ApiModelProperty(value = "保修状态,old=baoXiuStatus[1保修|0非保修]")
    private Integer warrantyStatus;

    @ApiModelProperty(value = "保修状态 true在保")
    private Boolean isBaoxiu = Boolean.FALSE;
    /**
     * 配置
     */
    @ApiModelProperty(value = "配置")
    private String config;
    /**
     * 串码/SN码
     */
    @ApiModelProperty(value = "串码/SN码")
    private String imei;
    /**
     * 故障描述
     */
    @ApiModelProperty(value = "故障描述")
    private String problem;
    /**
     * 九机服务
     */
    @ApiModelProperty(value = "九机服务")
    private Byte serviceType;
    /**
     * 处理方式
     */
    @ApiModelProperty(value = "处理方式")
    private Integer kind;
    /**
     * 维修状态
     */
    @ApiModelProperty(value = "维修状态")
    private Integer stats;
    /**
     * 换货验证码
     */
    @ApiModelProperty(value = "换货验证码|退货验证码")
    private String codeMsg;
    /**
     * 换货验证码图片fid
     */
    @ApiModelProperty(value = "换货验证码图片fid")
    private String codeMsgFid;
    /**
     * 处理分组
     */
    @ApiModelProperty(value = "处理分组")
    private Integer group;
    /**
     * 接件备注
     */
    @ApiModelProperty(value = "接件备注")
    private String comment;
    /**
     * 维修费用,old=wxPrice
     */
    @ApiModelProperty(value = "维修费用,old=wxPrice")
    private BigDecimal maintainPrice;
    /**
     * 维修人,old=wxUser
     */
    @ApiModelProperty(value = "维修人,old=wxUser")
    private String maintainUser;
    /**
     * 维修状态,old=wxState
     */
    @ApiModelProperty(value = "维修状态,old=wxState")
    private Integer maintainState;
    /**
     * 维修渠道,old=wxChannel
     */
    @ApiModelProperty(value = "维修渠道,old=wxChannel")
    private String maintainChannel;
    /**
     * 外观选项[1磨损|0完好]
     */
    @ApiModelProperty(value = "外观选项[1磨损|0完好]")
    private Integer outwardFlag;
    /**
     * 最后的操作信息
     */
    @ApiModelProperty(value = "最后的操作信息")
    private String operatingLog;
    /**
     * 维修成本
     */
    @ApiModelProperty(value = "维修成本")
    private BigDecimal costPrice;
    /**
     * 数据解绑
     */
    @ApiModelProperty(value = "数据解绑")
    private Integer dataRelease;
    /**
     * 外观描述
     */
    @ApiModelProperty(value = "外观描述")
    private String outward;
    /**
     * 验证图片UrlLink
     */
    @ApiModelProperty(value = "验证图片urlLink")
    private String imageLink;
    /**
     * 返厂信息,old=fanchangInfo
     */
    @ApiModelProperty(value = "返厂信息,old=fanchangInfo")
    private List<SmallproReturnFactoryInfoBO> returnFactoryInfoBOList;
    /**
     * 退换信息列表
     */
    @ApiModelProperty(value = "退换信息列表")
    private List<SmallproSendBackInfoBO> sendBackInfoList;
    /**
     * 附件信息列表
     */
    @ApiModelProperty(value = "附件信息列表")
    private List<SmallproFileLinkBO> filesList;

    /**
     * 退款方式列表
     */
    @ApiModelProperty(value = "退款方式列表")
    private List<SmallProReturnTypeBO> refundWay;
    /**
     * 处理日志
     */
    @ApiModelProperty(value = "处理日志")
    private List<SmallproOperationInfoBO> logs;
    /**
     * 用户可见处理日志
     */
    @ApiModelProperty(value = "用户可见处理日志")
    private List<SmallproOperationInfoBO> showLogs;
    /**
     * 历史处理记录
     */
    @ApiModelProperty(value = "历史处理记录")
    private List<SmallproInfoBO> historySmallpro;
    /**
     * 历史处理次数
     */
    @ApiModelProperty(value = "历史处理次数")
    private Integer historySize;
    /**
     * isTui,是否退还，2021-7-27发现bug，该字段值需要进行业务扩展 1 ing    2已退款
     */
    @ApiModelProperty(value = "old:isTui,是否退还",notes = "2021-7-27发现bug，该字段值需要进行业务扩展 1 ing  2已退款")
    private Integer isReturn;


    /**
     * 退维修费
     * 1 ing  2已退款
     */
    private Integer isReturnMaintenance;

    private List<SmallProRefundHistoryList> refundHistoryList;
    /**
     * 是否已收银[1是|0否]，old:isShouyinLock
     */
    @ApiModelProperty(value = "是否已收银[1是|0否]，old:isShouyinLock")
    private Integer isCashRegister;
    /**
     * 收银用户
     */
    @ApiModelProperty(value = "收银用户")
    private String cashRegisterUser;
    /**
     * 收银时间
     */
    @ApiModelProperty(value = "收银时间")
    private String cashRegisterDate;
    /**
     * 取件时间
     */
    @ApiModelProperty(value = "取件时间")
    private String pickupDate;
    /**
     * 接件确认,old:yuyuecheck
     */
    @ApiModelProperty(value = "接件确认,old:yuyuecheck")
    private Integer pickupCheck;
    /**
     * 预约ID,old:yuyueid
     */
    @ApiModelProperty(value = "预约ID,old:yuyueid")
    private Integer reservationId;
    /**
     * 预约确认标志
     */
    @ApiModelProperty(value = "预约确认标志")
    private Boolean reservationCheck;
    /**
     * 情况选项
     */
    @ApiModelProperty(value = "情况选项")
    private Integer situationKind;
    /**
     * shouhoutuihuanList
     */
    @ApiModelProperty(value = "shouhoutuihuanList")
    private List<ShouhouTuihuan> shouhouTuihuanList;

    /**
     * 退款公用接口相关参数
     */
    @ApiModelProperty(value = "退款公用接口相关参数")
    private SmallproReturnsBO returns;
    /**
     * 置换商品Id
     */
    @ApiModelProperty(value = "置换商品Id")
    private Integer changePpriceId;

    /**
     * 置换商品名称
     */
    @ApiModelProperty(value = "置换商品名称")
    private String changeProductName;

    /**
     * 置换商品名称
     */
    @ApiModelProperty(value = "置换商品名称")
    private String changeProductNameStr;

    /**
     * 置换商品规格
     */
    @ApiModelProperty(value = "置换商品规格")
    private String changeProductColorStr;

    /**
     * 置换商品条形码
     */
    @ApiModelProperty(value = "置换商品名称")
    private String changeProductBarCode;

    /**
     * 置换商品条形码
     */
    @ApiModelProperty(value = "置换商品条形码")
    private List<String> changeProductBarCodeList;

    /**
     * 库存
     */
    @ApiModelProperty(value = "库存")
    private Integer kcCount;

    /**
     * 是否30天无理由换新
     */
    @ApiModelProperty(value = "是否30天无理由换新")
    private Boolean isFreeExchange;
    /**
     * 退款支付相关信息
     */
    @ApiModelProperty(value = "退款支付相关信息")
    private List<SmallproPayInfoBO> refundPayInfoList;
    /**
     * 折价金额(最大可退金额)
     */
    @ApiModelProperty(value = "折价金额")
    private Double discountM;
    /**
     * 扣除金额
     */
    @ApiModelProperty(value = "扣除金额")
    private Double deductionM;
    /**
     * 是否为服务类商品
     */
    @ApiModelProperty(value = "是否为服务类商品")
    private Boolean isServiceProduct;
    /**
     * 是否为年包卡商品
     */
    @ApiModelProperty(value = "是否为年包卡商品")
    private Boolean isYearCardProduct;

    /**
     * 和包分期金额
     */
    @ApiModelProperty(value = "和包分期金额")
    private BigDecimal installmentAmountPrice;

    @ApiModelProperty(value = "运营商返利金额")
    private BigDecimal offsetMoney;

    /**
     * 是否存在运营商抵扣
     */
    private Boolean isReturnOperatorBasket;

    /**
     * 现货中关联的订单id
     */
    @ApiModelProperty(value = "现货中关联的订单id")
    private String oldId;

    @ApiModelProperty(value = "销售单联系电话")
    private String phone;

    @ApiModelProperty(value = "是否显示切膜信息")
    private Boolean showCutScreen = Boolean.FALSE;

    @ApiModelProperty(value = "切膜链接")
    private String cutScreenUrl;

    @ApiModelProperty(value = "切膜错误消息")
    private String cutScreenError;


    @ApiModelProperty(value = "切膜审核显示")
    private Boolean showCutScreenAudit = Boolean.FALSE;

    @ApiModelProperty(value = "是否启用科目和辅助核算验证。0未启用 1已启用")
    private Integer isSubjectVerify;

    @ApiModelProperty(value = "处理方式是否进行了特殊处理")
    private Boolean isSpecialTreatment;

    @ApiModelProperty(value = "故障类型(无故障 有故障 )")
    private List<String> hitchTypeList;

    @ApiModelProperty(value = "退款方式(正常退款 特殊退款 )")
    private List<String> refundTypeList;

    @ApiModelProperty(value = "配件是否折价")
    private Boolean isPartsDifferent;

    @ApiModelProperty(value = "折价文案")
    private String discountMessage;

    @ApiModelProperty(value = "折价政策")
    private String policyContent;

    @ApiModelProperty(value = "命中的规则Id")
    private Integer smallRefundConfigId;


    private AreaInfo areaSubject;

    /**
     * 客户名称
     */
    private String userName;

    /**
     *置换商品列表
     */
    private List<ChangeInfoRes> changeInfoList;

    /**
     * 年包赠送
     */
    @ApiModelProperty(value = "年包赠送")
    private Boolean packageTransferFlag;
}
