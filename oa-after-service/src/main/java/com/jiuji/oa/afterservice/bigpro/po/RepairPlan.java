package com.jiuji.oa.afterservice.bigpro.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 维修方案表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("repair_plan")
@ApiModel(value = "RepairPlan对象", description = "维修方案表")
public class RepairPlan extends Model<RepairPlan> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "关联维修故障表ID")
    @TableField("fault_id")
    private Integer faultId;

    @ApiModelProperty(value = "方案名称")
    @TableField("plan_name")
    private String planName;

    /**
     * @see com.jiuji.oa.afterservice.bigpro.enums.AccessoryTypeEnum
     */
    @ApiModelProperty(value = "关联配件类型：1-维修配件（默认），2-维修成本")
    @TableField("accessory_type")
    private Integer accessoryType;

    @ApiModelProperty(value = "关联ppid")
    @TableField("accessory_ppid")
    private Integer accessoryPpid;



    @ApiModelProperty(value = "关联名称")
    @TableField("accessory_name")
    private String accessoryName;

    /**
     * 关联id
     */
    @ApiModelProperty(value = "关联id")
    @TableField(value = "correlation_id")
    private Integer correlationId;


    /**
     * @see com.jiuji.oa.afterservice.bigpro.enums.ConfirmSourceEnum
     * 确认来源
     */
    @TableField(value = "confirm_source")
    private Integer confirmSource;

    /**
     * @see com.jiuji.oa.afterservice.bigpro.enums.CorrelationTypeEnum
     * 1-wxkcoutput 表的id
     * 2-shouhou_apply 表的id
     */
    @ApiModelProperty(value = "关联id类型")
    @TableField("correlation_type")
    private Integer correlationType;

    @ApiModelProperty(value = "方案价格")
    @TableField("price")
    private BigDecimal price;

    @ApiModelProperty(value = "创建人")
    @TableField("create_user")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField("update_user")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除(0:未删除 1:已删除)")
    @TableLogic
    @TableField("is_del")
    private Integer isDel;


} 