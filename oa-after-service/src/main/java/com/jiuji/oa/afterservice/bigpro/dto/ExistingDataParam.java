package com.jiuji.oa.afterservice.bigpro.dto;

import com.jiuji.oa.afterservice.bigpro.po.RepairFault;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlan;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 维修方案现有数据的参数类
 */
@Data
public class ExistingDataParam {
    private String currentUser;
    private LocalDateTime now;
    private Map<Integer, RepairFault> existingFaultMap = new HashMap<>();
    private Map<Integer, Map<Integer, RepairPlan>> existingPlanMap = new HashMap<>();

    public ExistingDataParam(String currentUser, LocalDateTime now) {
        this.currentUser = currentUser;
        this.now = now;
    }

} 