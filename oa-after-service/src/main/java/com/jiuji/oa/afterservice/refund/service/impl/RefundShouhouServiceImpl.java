package com.jiuji.oa.afterservice.refund.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.afterservice.bigpro.bo.HexiaoBo;
import com.jiuji.oa.afterservice.bigpro.bo.wxpj.WxKcBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouRefundMapper;
import com.jiuji.oa.afterservice.bigpro.enums.BigShouhouOrderTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouHuishou;
import com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.service.tuihuan.TuiHuanService;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeV1Enum;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.common.vo.res.ListBean;
import com.jiuji.oa.afterservice.mapstruct.CommonStructMapper;
import com.jiuji.oa.afterservice.other.po.ReturnsDetail;
import com.jiuji.oa.afterservice.other.service.ReturnsDetailService;
import com.jiuji.oa.afterservice.refund.bo.DetailParamBo;
import com.jiuji.oa.afterservice.refund.dao.ShouhouRefundMoneyMapper;
import com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanDetailPo;
import com.jiuji.oa.afterservice.refund.service.RefundCommonService;
import com.jiuji.oa.afterservice.refund.service.RefundMachineService;
import com.jiuji.oa.afterservice.refund.service.RefundMoneyService;
import com.jiuji.oa.afterservice.refund.service.RefundShouhouService;
import com.jiuji.oa.afterservice.refund.service.kind.BaseTuiHuanKindService;
import com.jiuji.oa.afterservice.refund.utils.RefundMoneyUtil;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.req.RefundValidVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiHuanCheckVo;
import com.jiuji.oa.afterservice.refund.vo.res.shouhou.*;
import com.jiuji.oa.afterservice.sys.enums.ValidMemberType;
import com.jiuji.oa.afterservice.sys.service.BaseValidMemberService;
import com.jiuji.oa.afterservice.sys.vo.req.ValidReq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.LambdaCaseWhen;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023/10/9 10:48
 */
@Service
@Slf4j
public class RefundShouhouServiceImpl implements RefundShouhouService {

    @Resource
    @Lazy
    private RefundShouhouService refundShouhouService;
    @Resource
    private RefundMoneyService refundMoneyService;
    @Resource
    private SmsService smsService;
    @Resource
    private ShouhouRefundMapper shouhouRefundMapper;
    @Resource
    private RefundMachineService refundMachineService;
    @Resource
    private ShouHouPjService shouHouPjService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private WxkcoutputService wxkcoutputService;
    @Resource
    private ReturnsDetailService returnsDetailService;
    @Resource
    private ShouhouService shouhouService;
    @Resource
    private CommonStructMapper commonStructMapper;
    @Resource
    private WebCloud webCloud;


    @Override
    public R<RefundShouhouDetailVo> detail(Integer shouhouId, Integer tuihuanId) {
        //查询售后单信息
        //Shouhou shouhou = shouhouService.getById(shouhouId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU, shouhouId);
        //都查询不到,单号错误
        if(shouhou == null){
            return R.error(StrUtil.format("售后单号{}错误",shouhouId));
        }
        //查询退换申请信息shouhou_tuihua
        ShouhouTuiHuanPo tuiHuan = getTuiHuanInfo(shouhouId, tuihuanId);
        SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.SHOUHOU_TUIHUAN_INFO, tuiHuan));
        RefundShouhouDetailVo refundDetail = new RefundShouhouDetailVo().setShouhou(shouhou).setTuihuan(tuiHuan).setNotes(new LinkedList<>());
        AreaInfo areaInfo = refundMachineService.getShouhouAreaInfo(shouhou);
        refundDetail.setShouhouAreaInfo(areaInfo);
        //详情页面信息设置
        convertAndSetDetailInfo(refundDetail);
        return R.success(refundDetail).addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));

    }

    private ShouhouTuiHuanPo getTuiHuanInfo(Integer shouhouId, Integer tuihuanId) {
        return Optional.ofNullable(tuihuanId).filter(thId -> thId > 0)
                .flatMap(thId -> SpringUtil.getBean(TuiHuanService.class).lambdaQuery()
                        .in(ShouhouTuiHuanPo::getTuihuanKind, TuihuanKindEnum.TWXF.getCode())
                        .eq(ShouhouTuiHuanPo::getId, thId).list().stream().findFirst())
                .orElseGet(() -> shouhouRefundMapper.getLastNotCompleteRefund(shouhouId, TuihuanKindEnum.TWXF.getCode()));
    }

    @Override
    public R<ZheJiaShouhouVo> zheJia(ZheJiaShouhouFormVo zheJiaFormVo) {
        R<ZheJiaShouhouVo> cR = checkAndSetZheJia(zheJiaFormVo);
        if (!cR.isSuccess()){
            return cR;
        }
        //所有配件扣减优惠码的退款金额求和且必须要小于等于最大可退
        BigDecimal totalPrice = NumberUtil.min(zheJiaFormVo.getWxkcSelectList().stream().map(RefundWxkcVo::getRefundPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO), zheJiaFormVo.getMaxRefundPrice());
        BigDecimal refundedPrice = NumberUtil.min(zheJiaFormVo.getWxkcSelectList().stream().map(RefundWxkcVo::getRefundedPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO), totalPrice);
        //构建结果对象
        ZheJiaShouhouVo zheJiaShouhouVo = new ZheJiaShouhouVo().setTotalPrice(totalPrice)
                .setZhejiaM(NumberUtil.max(totalPrice.subtract(refundedPrice), BigDecimal.ZERO)).setRefundPrice(refundedPrice);
        R<ZheJiaShouhouVo> r = R.success(zheJiaShouhouVo);
        // 获取组合退信息
        //折价后的金额 给组合退
        SpringContextUtil.getRequest().ifPresent(req -> {
            req.setAttribute(RequestAttrKeys.SHOUHOU_TOTAL_PRICE,zheJiaShouhouVo.getTotalPrice());
            req.setAttribute(RequestAttrKeys.SHOUHOU_MAX_REFUND_PRICE,zheJiaShouhouVo.getRefundPrice());
        });
        R<RefundMoneyDetailVo> groupR = SpringUtil.getBean(RefundMoneyService.class).detail(new DetailParamBo()
                        .setOrderId(zheJiaFormVo.getShouhouId()).setSupportSeconds(NumberConstant.ONE)
                        .setTuihuanId(null).setTuihuanKindEnum(zheJiaFormVo.getTuihuanKindEnum()),
                detail -> BaseTuiHuanKindService.getBean(zheJiaFormVo.getTuihuanKindEnum())
                        .getSuInfoWithMaxRefundPrice(zheJiaFormVo.getShouhouId(), zheJiaFormVo.getTuihuanKind()));
        r.addAllBusinessLog(groupR.businessLogs());
        if(!groupR.isSuccess()){
            r.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
            r.addBusinessLog(StrUtil.format("组合退获取结果: {}", groupR.getUserMsg()));
        }
        RefundMoneyDetailVo refundMoneyDetailVo = groupR.getData();
        zheJiaShouhouVo.setRefundMoneyDetailVo(refundMoneyDetailVo);
        removeNotSuportRefundWay(refundMoneyDetailVo, zheJiaFormVo.getShouhou());
        return r.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    @Override
    @RepeatSubmitCheck(expression = "#{classFullName}:#{shouhouFormVo.subId}",message = "存在未完成的退款")
    public R<Integer> save(RefundShouhouFormVo shouhouFormVo) {
        try {
            R<Integer> checkR = assertCheckSaveAndSet(shouhouFormVo);
            if (!checkR.isSuccess()) {
                return checkR;
            }
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }
        //折价后的金额 给组合退
        SpringContextUtil.getRequest().ifPresent(req -> {
            req.setAttribute(RequestAttrKeys.SHOUHOU_TOTAL_PRICE,shouhouFormVo.getTotalPrice());
            req.setAttribute(RequestAttrKeys.SHOUHOU_MAX_REFUND_PRICE,shouhouFormVo.getRefundPrice());
        });
        //前面已经校验过用户信息, 后续不再需要校验
        // 跳过用户验证
        SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.PASS_GROUP_REFUND_IS_NEED_VALID,Boolean.TRUE));

        //组合退信息
        R<Integer> grouSaveR = refundMoneyService.save(shouhouFormVo.getTuihuanFormVo(), tuihuanForm -> BaseTuiHuanKindService.getBean(shouhouFormVo.getTuihuanKindEnum())
                .getSuInfoWithMaxRefundPrice(ObjectUtil.defaultIfNull(shouhouFormVo.getShouhouId(), shouhouFormVo.getSubId()), shouhouFormVo.getTuihuanKind()));
        if(!grouSaveR.isSuccess()){
            throw new CustomizeException(grouSaveR.getUserMsg());
        }
        //只做校验逻辑
        if(RefundShouhouService.CHECK_DATA.equals(grouSaveR.getData())){
            return grouSaveR;
        }
        //更新维修费退款才有的字段信息
        shouhouFormVo.setTuihuanId(grouSaveR.getData());
        TuiHuanService tuiHuanService = SpringUtil.getBean(TuiHuanService.class);
        LambdaUpdateChainWrapper<ShouhouTuiHuanPo> tuiHuanLambdaUpdate = tuiHuanService.lambdaUpdate().eq(ShouhouTuiHuanPo::getId,shouhouFormVo.getTuihuanId());
        tuiHuanLambdaUpdate.set(ShouhouTuiHuanPo::getBuypriceM,shouhouFormVo.getTotalPrice());
        if(!tuiHuanLambdaUpdate.update()){
            throw new CustomizeException("组合退款,换机信息更新失败");
        }

        //插入配件和退款关联表
        shouhouFormVo.getWxkcSelectList().stream()
                .map(wxkc -> {
                    ReturnsDetail returnsDetail = new ReturnsDetail();
                    return returnsDetail.setShthid(shouhouFormVo.getTuihuanId().longValue())
                            .setBasketCount(wxkc.getCount())
                            .setBasketID(wxkc.getId().longValue());
                })
                .forEach(SpringUtil.getBean(ReturnsDetailService.class)::save);
        //更新配件的退款金额
        for (RefundShouhouFormVo.RefundWxkcFormVo refundWxkcVo : shouhouFormVo.getWxkcSelectList()) {
            CommenUtil.autoWriteHist(()->wxkcoutputService.lambdaUpdate().eq(Wxkcoutput::getId, refundWxkcVo.getId())
                    .set(Wxkcoutput::getRefundedPrice, refundWxkcVo.getRefundedPrice())
                    .set(Wxkcoutput::getTuiStatus, refundWxkcVo.getTuiStatus())
                    .set(Wxkcoutput::getFkTuihuanId, shouhouFormVo.getTuihuanId())
                    .update());
        }
        return R.success(shouhouFormVo.getTuihuanId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> cancelRefund(Integer tuihuanId, String mark) {
        //查询关联表明细信息
        List<Integer> wxkcIds = returnsDetailService.lambdaQuery().eq(ReturnsDetail::getShthid, tuihuanId).select(ReturnsDetail::getBasketID).list()
                .stream().map(ReturnsDetail::getBasketID).map(Convert::toInt).collect(Collectors.toList());
        R<Boolean> result = refundMoneyService.cancelRefund(tuihuanId);
        if(!result.isSuccess()){
            return result.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
        }
        // 恢复配件已退金额
        for (Integer wxkcId : wxkcIds) {
            CommenUtil.autoWriteHist(()->wxkcoutputService.lambdaUpdate().eq(Wxkcoutput::getId, wxkcId)
                    .set(Wxkcoutput::getRefundedPrice, BigDecimal.ZERO)
                    .set(Wxkcoutput::getTuiStatus, null)
                    .update());
        }
        return result.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    @Override
    public R<Integer> submitCheck(TuiHuanCheckVo tuiHuanCheckVo) {
        AbstractCurrentRequestComponent currentRequestComponent = SpringUtil.getBean(AbstractCurrentRequestComponent.class);
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        try {
            R<Integer> checkR = checkSubmitCheckAndSet(tuiHuanCheckVo,oaUser);
            if (!checkR.isSuccess()){
                return checkR;
            }
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }

        ShouhouTuiHuanPo tuiHuanPo = tuiHuanCheckVo.getTuiHuanPo();
        //先调用组合退的审核办理
        R<Integer> result;
        try {
            result = refundMoneyService.submitCheck(tuiHuanCheckVo);
        } catch (CustomizeException e) {
            if(e.getCode() == ResultCode.WEBSERVER_ERROR){
                //维修退款网络异常的时候,需要推送消息
                RRExceptionHandler.logError(StrUtil.format("[{}]维修费退款[{}]网络异常", tuiHuanPo.getShouhouId(), tuiHuanCheckVo.getTuihuanId()),
                        tuiHuanCheckVo, e, smsService::sendOaMsgTo9JiMan);
            }
            throw e;
        } catch (Exception e) {
            if (tuiHuanCheckVo.getProcessStatusEnum() == ShouhouRefundDetailVo.ProcessStatus.CHECK3){
                //维修退款网络异常的时候,需要推送消息
                RRExceptionHandler.logError(StrUtil.format("[{}]维修费退款[{}]网络异常", tuiHuanPo.getShouhouId(), tuiHuanCheckVo.getTuihuanId()),
                        tuiHuanCheckVo, e, smsService::sendOaMsgTo9JiMan);
            }
            throw e;
        }
        if(!result.isSuccess()){
            return result.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
        }
        if (tuiHuanCheckVo.getProcessStatusEnum() == ShouhouRefundDetailVo.ProcessStatus.CHECK3){
            //退款办理成功之后,撤销配件, 撤销配件失败直接抛出客户异常就行
            try {
                MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, () -> {
                    OaUserBO tuiUser = oaUser.copy();
                    tuiUser.setAreaId(tuiHuanPo.getAreaid());
                    tuiUser.setUserName(tuiHuanPo.getInuser());
                    //更新对应的折价金额
                    if(tuiHuanPo.getZhejiaM().compareTo(BigDecimal.ZERO)>0){
                        //需要重新查询yifum, 已经变更
//                        BigDecimal yifum = shouhouService.lambdaQuery().eq(Shouhou::getId, tuiHuanPo.getShouhouId()).select(Shouhou::getYifum)
//                                .list().stream().findFirst().map(Shouhou::getYifum).orElse(BigDecimal.ZERO);
                        BigDecimal yifum =CommenUtil.autoQueryHist(()->shouhouService.lambdaQuery().eq(Shouhou::getId, tuiHuanPo.getShouhouId()).select(Shouhou::getYifum)
                                .list().stream().findFirst().map(Shouhou::getYifum).orElse(BigDecimal.ZERO),MTableInfoEnum.SHOUHOU,tuiHuanPo.getShouhouId());
                        boolean isUpdateYfmR = CommenUtil.autoWriteHist(()->shouhouService.lambdaUpdate().eq(Shouhou::getId, tuiHuanPo.getShouhouId())
                                .eq(Shouhou::getYifum, yifum)
                                .set(Shouhou::getYifum, NumberUtil.max(yifum.subtract(tuiHuanPo.getZhejiaM()), BigDecimal.ZERO))
                                .update());
                        SpringContextUtil.addRequestLambda(() -> shouhouService.saveShouhouLog(tuiHuanPo.getShouhouId(),
                                StrUtil.format("折价退配件, 总退款金额: {}, 折价金额: {}", tuiHuanPo.getTuikuanM(), tuiHuanPo.getZhejiaM()), tuiUser.getUserName()));
                        if(!isUpdateYfmR){
                            throw new CustomizeException("更新退款后折价金额失败");
                        }
                    }

                    String tuiErrorMsg = currentRequestComponent.invokeWithUser(Convert.toLong(XtenantEnum.getXtenant()), tuiUser
                            ,user -> shouHouPjService.tui(tuiHuanCheckVo.getWxKcBoList(), false, true))
                            .entrySet().stream().filter(entry -> !entry.getValue().isSuccess())
                            .map(entry -> StrUtil.format("配件[{}]撤销失败, 原因: {}", entry.getKey(), entry.getValue().getUserMsg()))
                            .collect(Collectors.joining(StringPool.COMMA));
                    if(StrUtil.isNotBlank(tuiErrorMsg)){
                        throw new CustomizeException(tuiErrorMsg);
                    }
                }).commit();
            } catch (Exception e) {
                //为了保证最终一致性, 发送oa消息, 人工处理相关配件的撤销
                RRExceptionHandler.logError(StrUtil.format("[{}]维修费退款[{}]撤销配件", tuiHuanPo.getShouhouId(), tuiHuanCheckVo.getTuihuanId()),
                        tuiHuanCheckVo, e, smsService::sendOaMsgTo9JiMan);
            }
            if(XtenantEnum.isJiujiXtenant()){
                try {
                    List<Wxkcoutput> wxkcoutputList = CommenUtil.autoQueryHist(()->wxkcoutputService.lambdaQuery().eq(Wxkcoutput::getFkTuihuanId, tuiHuanCheckVo.getTuihuanId()).list());
                    if(CollectionUtils.isNotEmpty(wxkcoutputList)){
                        Integer shouhouId = tuiHuanPo.getShouhouId();
                        //Shouhou shouhou = Optional.ofNullable(shouhouService.getById(shouhouId)).orElse(new Shouhou());
                    Shouhou shouhou = Optional.ofNullable(CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU,shouhouId)).orElse(new Shouhou());
                    Integer userId = Convert.toInt(shouhou.getUserid());
                    List<Integer> wxKcIds = wxkcoutputList.stream().map(Wxkcoutput::getId).collect(Collectors.toList());
                    Result tuiFeeDel = webCloud.repairTuiFeeDel(userId, shouhouId, wxKcIds);
                    log.warn("维修退款撤销消费券返回结果：{}，传入参数：{}", JSONUtil.toJsonStr(tuiFeeDel),userId+""+shouhouId+""+wxKcIds);
                    if(!tuiFeeDel.isSuccess()){
                        throw new CustomizeException(Optional.ofNullable(tuiFeeDel.getUserMsg()).orElse(tuiFeeDel.getMsg()));
                    }
                }
            } catch (Exception e) {
                RRExceptionHandler.logError(StrUtil.format("[{}]维修费退款[{}]撤销配件 撤销消费券异常：[{}]", tuiHuanPo.getShouhouId(), tuiHuanCheckVo.getTuihuanId(),e.getMessage()),
                        tuiHuanCheckVo, e, smsService::sendOaMsgTo9JiMan);}
            }
        }
        //执行并移除lambda表达式
        SpringContextUtil.runAndRemoveRequestLambda();
        return result.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    private R<Integer> checkSubmitCheckAndSet(TuiHuanCheckVo tuiHuanCheckVo, OaUserBO oaUser) {
        ShouhouRefundDetailVo.ProcessStatus status = EnumUtil.getEnumByCode(ShouhouRefundDetailVo.ProcessStatus.class, tuiHuanCheckVo.getProcessStatus());
        ShouhouTuiHuanPo tuiHuanPo = SpringUtil.getBean(ShouhouRefundMoneyMapper.class).getRefund(tuiHuanCheckVo.getTuihuanId());
        Assert.isFalse(tuiHuanPo == null,"退款审核id[{}]错误", tuiHuanCheckVo.getTuihuanId());
        Assert.isFalse(tuiHuanPo.getZhejiaM() == null,"折价金额不能为空");
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuiHuanPo.getTuihuanKind());
        Assert.isFalse(tuihuanKindEnum == null,"退款类型[{}]不支持",tuiHuanPo.getTuihuanKind());
        //Shouhou shouhou = shouhouService.getById(tuiHuanPo.getShouhouId());
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(tuiHuanPo.getShouhouId()), MTableInfoEnum.SHOUHOU,tuiHuanPo.getShouhouId());
        Assert.isFalse(shouhou == null,"售后id错误",tuiHuanPo.getShouhouId());

        //校验是否符合退配件的条件
        //查询关联表明细信息
        List<Integer> wxkcIds = returnsDetailService.lambdaQuery().eq(ReturnsDetail::getShthid, tuiHuanCheckVo.getTuihuanId())
                .select(ReturnsDetail::getBasketID).list().stream().map(ReturnsDetail::getBasketID).map(Convert::toInt)
                .collect(Collectors.toList());
        List<WxKcBo> wxKcBoList = shouHouPjService.listWxkcOutput(wxkcIds, tuiHuanPo.getShouhouId());
        tuiHuanCheckVo.setWxKcBoList(wxKcBoList);
        //获取九机服务,售后单服务是否在保查询通过接件时间进行校验
        SpringContextUtil.getRequest().ifPresent( request -> request.setAttribute(RequestAttrKeys.NOW_DATE,tuiHuanPo.getDtime()));
        Map<Integer, R<Integer>> wxkcResultMap = shouHouPjService.tui(wxKcBoList, true, true);
        String errorMsgJoin = wxkcResultMap.entrySet().stream().filter(entry -> !entry.getValue().isSuccess()).map(entry -> entry.getValue().getUserMsg()).collect(Collectors.joining(StringPool.COMMA));
        Assert.isTrue(StrUtil.isBlank(errorMsgJoin),"配件发生变化, 请撤销退款重新申请, {}", errorMsgJoin);
        //设置值
        tuiHuanCheckVo.setProcessStatusEnum(status);
        tuiHuanCheckVo.setTuiHuanPo(tuiHuanPo);
        List<ShouhouTuihuanDetailPo> allTuihuanDetailPos = SpringUtil.getBean(RefundCommonService.class).listTuihuanDetail(tuiHuanPo.getId(), null, true);
        tuiHuanCheckVo.setTuihuanDetailPos(allTuihuanDetailPos.stream().filter(td -> ObjectUtil.notEqual(Boolean.TRUE,td.getIsDel())).collect(Collectors.toList()));
        tuiHuanCheckVo.setOtherTuihuanDetailPos(allTuihuanDetailPos.stream().filter(td -> ObjectUtil.equal(Boolean.TRUE,td.getIsDel())).collect(Collectors.toList()));
        tuiHuanCheckVo.setCurrUser(oaUser);
        tuiHuanCheckVo.setSubLogs(new LinkedList<>());
        tuiHuanCheckVo.setTuihuanKindEnum(tuihuanKindEnum);
        return R.success(null);
    }

    private R<ZheJiaShouhouVo> checkAndSetZheJia(ZheJiaShouhouFormVo zheJiaFormVo) {
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, zheJiaFormVo.getTuihuanKind());
        try {
            Assert.notNull(tuihuanKindEnum, "退款类型不能为空");
            Assert.notNull(zheJiaFormVo.getXtenant(), "租户标识码不能为空");
            Assert.isTrue(NumberUtil.null2Zero(zheJiaFormVo.getMaxRefundPrice()).compareTo(BigDecimal.ZERO)>=0, "最大可退金额必须大于等于0");
            Assert.isFalse(CollUtil.isEmpty(zheJiaFormVo.getWxkcSelectList()), "选择的退的配件不能为空");
            Assert.isFalse(zheJiaFormVo.getWxkcSelectList().stream()
                    .anyMatch(wxkc -> wxkc.getRefundPrice() == null || wxkc.getRefundedPrice() == null || NumberUtil.min(wxkc.getRefundPrice(),wxkc.getRefundedPrice()).compareTo(BigDecimal.ZERO)<0),
                    "配件的退款金额或可退金额不能小于0");
            zheJiaFormVo.setTuihuanKindEnum(tuihuanKindEnum);
            //查询售后单信息
            //Shouhou shouhou = shouhouService.getById(zheJiaFormVo.getShouhouId());
            Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(zheJiaFormVo.getShouhouId()), MTableInfoEnum.SHOUHOU,zheJiaFormVo.getShouhouId());
            zheJiaFormVo.setShouhou(shouhou);
            AreaInfo areaInfo = refundMachineService.getShouhouAreaInfo(shouhou);
            zheJiaFormVo.setShouhouAreaInfo(areaInfo);
        } catch (IllegalArgumentException e) {
            return R.error(e.getMessage());
        }
        return R.success(null);
    }

    /**
     * 内部流转的字段设置到详情上
     * @param refundDetail
     */
    private void convertAndSetDetailInfo(RefundShouhouDetailVo refundDetail) {
        //tuihuan属性设置到页面
        Shouhou shouhou = refundDetail.getShouhou();
        ShouhouTuiHuanPo tuihuan = refundDetail.getTuihuan();
        //设置售后信息
        refundDetail.setShouhouId(shouhou.getId());
        AreaInfo shouhouAreaInfo = refundDetail.getShouhouAreaInfo();
        refundDetail.setArea(shouhouAreaInfo.getArea());
        refundDetail.setAreaId(shouhouAreaInfo.getId());
        refundDetail.setXtenant(shouhouAreaInfo.getXtenant());
        refundDetail.setUserId(Convert.toInt(shouhou.getUserid()));
        //退维修费交易单号就是本身
        refundDetail.setSubId(shouhou.getId());
        List<ListBean.OptionsBean> collect = Arrays.stream(Wxkcoutput.TuiStatusEnum.values())
                .map(tuiStatus -> new ListBean.OptionsBean().setValue(tuiStatus.getCode()).setLabel(tuiStatus.getMessage()))
                .collect(Collectors.toList());
        if(XtenantEnum.isJiujiXtenant()){
            //如果是九机那就过滤 仅退款
            collect = collect.stream().filter(tuiStatus -> !Wxkcoutput.TuiStatusEnum.REFUND_ONLY.getCode().equals(Convert.toInt(tuiStatus.getValue()))).collect(Collectors.toList());
        }
        refundDetail.setTuiStatusOptions(collect);
        if(XtenantEnum.isJiujiXtenant()){
            refundDetail.getNotes().add(SpringUtil.getBean(RefundMoneyService.class).getAllShouYingDescription(shouhou.getId(), TuihuanKindEnum.TWXF));
        }

        if(tuihuan != null){
            //设置退换详情和流程状态
            setTuihuanDetail(refundDetail);
            //获取组合退的详情
            TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuihuan.getTuihuanKind());
            //折价后的金额 给组合退
            BigDecimal refundPrice = ObjectUtil.defaultIfNull(tuihuan.getTuikuanM(), BigDecimal.ZERO)
                    .subtract(ObjectUtil.defaultIfNull(tuihuan.getPeizhiPrice(), BigDecimal.ZERO))
                    .subtract(ObjectUtil.defaultIfNull(tuihuan.getPiaoPrice(), BigDecimal.ZERO));
            SpringContextUtil.getRequest().ifPresent(req -> {
                req.setAttribute(RequestAttrKeys.SHOUHOU_TOTAL_PRICE, tuihuan.getBuypriceM());
                req.setAttribute(RequestAttrKeys.SHOUHOU_MAX_REFUND_PRICE,refundPrice);
            });
            refundDetail.setRefundMoneyDetailVo(SpringUtil.getBean(RefundMoneyService.class).detail(new DetailParamBo()
                            .setOrderId(tuihuan.getShouhouId()).setSupportSeconds(NumberConstant.ONE)
                            .setTuihuanId(tuihuan.getId()).setTuihuanKindEnum(tuihuanKindEnum),
                    detail-> BaseTuiHuanKindService.getBean(tuihuanKindEnum)
                            .getSuInfoWithMaxRefundPrice(tuihuan.getShouhouId(),tuihuan.getTuihuanKind()))
                    .getData());
        } else{
            //申请页面,可选配件列表
            setBuyPriceAndSubInfo(refundDetail);
            refundDetail.setIsCanCancel(Boolean.FALSE);
            refundDetail.setIsNeedValid(Boolean.TRUE);
            if (Boolean.TRUE.equals(refundDetail.getShouhou().getIsquji())) {
                refundDetail.setProcessName(ShouhouRefundDetailVo.ProcessStatus.SUBMIT.getMessage());
                refundDetail.setProcessStatus(ShouhouRefundDetailVo.ProcessStatus.SUBMIT.getCode());
                refundDetail.setCurrProcessStatus(ShouhouRefundDetailVo.ProcessStatus.SUBMIT.getCode());
            } else {
                refundDetail.setProcessName(ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK.getMessage());
                refundDetail.setProcessStatus(ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK.getCode());
                refundDetail.setCurrProcessStatus(ShouhouRefundDetailVo.ProcessStatus.SUBMIT.getCode());
            }
        }
    }

    private void setTuihuanDetail(RefundShouhouDetailVo refundDetail) {
        ShouhouTuiHuanPo shouhouTuihuan = refundDetail.getTuihuan();
        refundDetail.setTuihuanId(shouhouTuihuan.getId());
        refundDetail.setTuihuanKind(shouhouTuihuan.getTuihuanKind());
        refundDetail.setZhejiaM(shouhouTuihuan.getZhejiaM());
        refundDetail.setTotalPrice(shouhouTuihuan.getBuypriceM());
        refundDetail.setInprice(shouhouTuihuan.getInprice());
        refundDetail.setComment(shouhouTuihuan.getComment());
        //设置退的配件列表信息
        List<HexiaoBo> hexiaoBoList = wxkcoutputService.getHexiao(refundDetail.getShouhouId());
        Map<Integer, HexiaoBo> hexiaoBoMap = hexiaoBoList.stream().collect(Collectors.toMap(HexiaoBo::getId, Function.identity(), (v1,v2) -> v1));
        List<ReturnsDetail> returnsDetails = returnsDetailService.lambdaQuery().eq(ReturnsDetail::getShthid, refundDetail.getTuihuanId()).list();
        //批量获取门店信息
        List<Integer> areaIds = hexiaoBoList.stream()
                .filter(hexiaoBo -> ObjectUtil.defaultIfNull(hexiaoBo.getHsid(), 0) > 0)
                .flatMap(hexiaoBo -> Stream.of(hexiaoBo.getHsAreaId(), hexiaoBo.getHsToAreaId())).collect(Collectors.toList());
        Map<Integer, AreaInfo> areaInfoMap = CommonUtils.getResultData(areaIds.isEmpty() ? R.success(Collections.emptyList()) : areaInfoClient.listAreaInfo(areaIds),
                msg -> {
                    throw new CustomizeException("门店信息获取异常,请稍后再试!");
                }).stream().collect(Collectors.toMap(AreaInfo::getId, Function.identity(), (v1,v2) -> v1));
        Function<Integer, String> getAreaFun = getAreaFun(areaInfoMap);
        refundDetail.setWxkcList(returnsDetails.stream().map(rd -> {
            HexiaoBo hexiaoBo = hexiaoBoMap.get(Convert.toInt(rd.getBasketID()));
            RefundWxkcVo refundWxkcVo = buildRefundWxkc(getAreaFun, hexiaoBo);
            return refundWxkcVo;
        }).collect(Collectors.toList()));


        //
        //按退款类型显示实际退款金额
        refundDetail.setRefundPrice(ObjectUtil.defaultIfNull(shouhouTuihuan.getTuikuanM(),BigDecimal.ZERO)
                .subtract(ObjectUtil.defaultIfNull(shouhouTuihuan.getPeizhiPrice(),BigDecimal.ZERO))
                .subtract(ObjectUtil.defaultIfNull(shouhouTuihuan.getPiaoPrice(),BigDecimal.ZERO)));

        refundDetail.setCheckHistorys(new LinkedList<>());
        ShouhouRefundDetailVo.ProcessStatus processStatus = ShouhouRefundService.addProcessInfo(shouhouTuihuan, ShouhouRefundDetailVo.ProcessStatus.SUBMIT, refundDetail.getCheckHistorys());
        ShouhouRefundDetailVo.ProcessStatus nextStatus = ShouhouRefundDetailVo.ProcessStatus.nextProcess(processStatus);
        refundDetail.setCurrProcessStatus(processStatus.getCode());
        refundDetail.setProcessName(nextStatus.getMessage());
        refundDetail.setProcessStatus(nextStatus.getCode());
        refundDetail.setIsCanCancel(Stream.of(ShouhouRefundDetailVo.ProcessStatus.SUBMIT,ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK)
                .noneMatch(ps -> ObjectUtil.equal(ps,nextStatus)));
    }



    /**
     * 获取和设置wxkc的配件信息
     * @param shouhouId
     * @param refundDetail
     */
    private void getAndSetWxkcList(Integer shouhouId, RefundShouhouDetailVo refundDetail) {
        //设置可退配件信息
        List<WxKcBo> wxKcBoList = shouHouPjService.listWxkcOutput(null, shouhouId).stream()
                .filter(wxkc -> NumberUtil.null2Zero(wxkc.getRefundedPrice()).compareTo(BigDecimal.ZERO) ==0)
                .filter(wxkc -> ObjectUtil.notEqual(wxkc.getStats(), Wxkcoutput.StatusEnum.CANCELED.getCode()))
                .collect(Collectors.toList());
        Map<Integer, WxKcBo> wxKcBoMap = wxKcBoList.stream().collect(Collectors.toMap(WxKcBo::getId, Function.identity(), (v1,v2) -> v1));
        if(wxKcBoList.isEmpty()){
            //没有配件了直接返回
            return;
        }
        Map<Integer, R<Integer>> wxkcCheckResult = shouHouPjService.tui(wxKcBoList, true, true);
        List<HexiaoBo> hexiaoBoList = wxkcoutputService.getHexiao(shouhouId);
        Map<Integer, HexiaoBo> hexiaoBoMap = hexiaoBoList.stream().collect(Collectors.toMap(HexiaoBo::getId, Function.identity(), (v1,v2) -> v1));
        //批量获取门店信息
        List<Integer> areaIds = hexiaoBoList.stream()
                .filter(hexiaoBo -> {
                    R<Integer> wxkcR = wxkcCheckResult.get(hexiaoBo.getId());
                    return wxkcR != null && wxkcR.isSuccess();
                })
                .filter(hexiaoBo -> ObjectUtil.defaultIfNull(hexiaoBo.getHsid(), 0) > 0)
                .flatMap(hexiaoBo -> Stream.of(hexiaoBo.getHsAreaId(), hexiaoBo.getHsToAreaId())).collect(Collectors.toList());
        Map<Integer, AreaInfo> areaInfoMap = CommonUtils.getResultData(areaIds.isEmpty() ? R.success(Collections.emptyList()) : areaInfoClient.listAreaInfo(areaIds),
                msg -> {
                    throw new CustomizeException("门店信息获取异常,请稍后再试!");
                }).stream().collect(Collectors.toMap(AreaInfo::getId, Function.identity(), (v1,v2) -> v1));
        Function<Integer, String> getAreaFun = getAreaFun(areaInfoMap);

        refundDetail.setWxkcList(new LinkedList<>());
        wxkcCheckResult.entrySet().stream()
                .filter(entry -> {
                    boolean isSuccess = entry.getValue().isSuccess();
                    HexiaoBo hexiaoBo = hexiaoBoMap.get(entry.getKey());
                    if(hexiaoBo == null){
                        return false;
                    }
                    if(!isSuccess){
                        refundDetail.getNotes().add(StrUtil.format("配件[{}]不可退, 原因: {}", hexiaoBo.getProductName(), entry.getValue().getUserMsg()));
                    }else if(NumberUtil.null2Zero(hexiaoBo.getRefundedPrice()).compareTo(BigDecimal.ZERO)>0 || ObjectUtil.defaultIfNull(hexiaoBo.getTuiStatus(), 0)>0){
                        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"配件[{}]不可退, 原因: 配件只能退一次款", hexiaoBo.getProductName());
                        isSuccess = false;
                    }
                    return isSuccess;
                })
                .map(entry -> {
                    HexiaoBo hexiaoBo = hexiaoBoMap.get(entry.getKey());
                    WxKcBo wxKcBo = wxKcBoMap.get(entry.getKey());
                    RefundWxkcVo refundWxkcVo = buildRefundWxkc(getAreaFun, hexiaoBo);
                    // 默认值设置为与可退金额相等
                    refundWxkcVo.setRefundedPrice(NumberUtil.min(ObjectUtil.defaultIfNull(wxKcBo.getReducePrice(),
                            refundWxkcVo.getRefundPrice()), refundWxkcVo.getRefundPrice()));
                    return refundWxkcVo;
                })
                .forEach(refundDetail.getWxkcList()::add);
    }

    private Function<Integer, String> getAreaFun(Map<Integer, AreaInfo> areaInfoMap) {
        Function<Integer, String> getAreaFun = areaId -> Optional.ofNullable(areaId).map(aId -> areaInfoMap.get(aId)).map(aInfo -> aInfo.getArea()).orElse(null);
        return getAreaFun;
    }

    private RefundWxkcVo buildRefundWxkc(Function<Integer, String> getAreaFun, HexiaoBo hexiaoBo) {
        Wxkcoutput.TuiStatusEnum tuiStatusEnum = EnumUtil.getEnumByCode(Wxkcoutput.TuiStatusEnum.class,hexiaoBo.getTuiStatus());
        if(tuiStatusEnum == null){
            tuiStatusEnum = Wxkcoutput.TuiStatusEnum.RETURN_AND_REFUND;
        }
        RefundWxkcVo refundWxkcVo = new RefundWxkcVo().setCount(1).setId(hexiaoBo.getId()).setPLabel(hexiaoBo.getPLabel())
                .setPlabelText(hexiaoBo.getPlabelText()).setPpid(hexiaoBo.getPpid()).setProductName(hexiaoBo.getOriginProductName())
                .setProductColor(hexiaoBo.getOriginProductColor()).setRefundedPrice(hexiaoBo.getRefundedPrice())
                .setTuiStatus(tuiStatusEnum.getCode()).setTuiStatusText(tuiStatusEnum.getMessage())
                .setRefundPrice(hexiaoBo.getRefundPrice()).setRefundedPrice(hexiaoBo.getRefundedPrice());
        if(ObjectUtil.defaultIfNull(hexiaoBo.getHsid(),0)>0){
            Boolean isfan = hexiaoBo.getIsfan();
            Boolean issale = hexiaoBo.getIssale();
            Boolean isSaleCheck = hexiaoBo.getHsIsSaleCheck();
            Integer jjSaletype = hexiaoBo.getHsjjSaletype();
            Boolean isComplete = hexiaoBo.getHsComplete();
            Boolean isConfirm = hexiaoBo.getHsConfirm();
            RefundWxkcVo.RefundHuiShou refundHuiShou = new RefundWxkcVo.RefundHuiShou()
                    .setArea(getAreaFun.apply(hexiaoBo.getHsAreaId())).setAreaid(hexiaoBo.getHsAreaId())
                    .setToarea(getAreaFun.apply((hexiaoBo.getHsToAreaId()))).setToareaid(hexiaoBo.getHsToAreaId())
                    .setTypeName(LambdaCaseWhen.lambdaCaseWhen(()-> Boolean.TRUE.equals(isfan), () -> "返还")
                            .when(()-> Boolean.TRUE.equals(hexiaoBo.getIshuanhuo()), ()-> "换货").endElse(() -> "回收"));
            ShouhouHuishou.HuiShouStatusEnum huiShouStatus = RefundShouhouService.getHuiShouStatusEnum(isfan, issale, isSaleCheck,
                    jjSaletype, isComplete, isConfirm, hexiaoBo.getHsFromSource(), hexiaoBo.getHsFancheck());
            refundHuiShou.setStatus(huiShouStatus.getCode()).setStatusText(huiShouStatus.getMessage());
            refundWxkcVo.setRefundHuishou(refundHuiShou);
        }
        return refundWxkcVo;
    }

    private R<Integer> assertCheckSaveAndSet(RefundShouhouFormVo shouhouFormVo) {
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, shouhouFormVo.getTuihuanKind());
        //setSaveDefault(shouhouFormVo);
        //设置退款类型
        shouhouFormVo.setTuihuanKindEnum(tuihuanKindEnum);
        Assert.isTrue(Objects.nonNull(tuihuanKindEnum),"不支持{}退款类型", shouhouFormVo.getTuihuanKind());

        Integer shouhouId = shouhouFormVo.getShouhouId();
        //Shouhou shouhou = shouhouService.getById(shouhouId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU,shouhouId);
        Assert.notNull(shouhou,"售后单号[{}]错误", shouhouId);
        shouhouFormVo.setShouhou(shouhou);
        AreaInfo shouhouAreaInfo = refundMachineService.getShouhouAreaInfo(shouhou);
        shouhouFormVo.setShouhouAreaInfo(shouhouAreaInfo);
        shouhouFormVo.setCurrUser(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId());
        //校验退款地区
        if (ObjectUtil.notEqual(shouhouFormVo.getCurrUser().getAreaId(),shouhouFormVo.getShouhouAreaInfo().getId())){
            return R.error(StrUtil.format("地区不符，请切换至{}再操作！",shouhouAreaInfo.getArea()));
        }
        //未取机,不允许退款
        Assert.isFalse(!Boolean.TRUE.equals(shouhou.getIsquji()) && TuihuanKindEnum.TWXF == tuihuanKindEnum, "未取机不允许退款");
        //校验配件
        List<RefundShouhouFormVo.RefundWxkcFormVo> wxkcSelectList = shouhouFormVo.getWxkcSelectList();
        List<Integer> wxkcIds = wxkcSelectList.stream().map(RefundShouhouFormVo.RefundWxkcFormVo::getId).collect(Collectors.toList());
        List<WxKcBo> wxKcBoList = shouHouPjService.listWxkcOutput(wxkcIds, shouhouId);
        Map<Integer, WxKcBo> wxKcBoMap = wxKcBoList.stream().collect(Collectors.toMap(WxKcBo::getId, Function.identity(), (v1,v2) -> v1));

        //设置到退款金额中
        List<WxKcBo> tuiCheckList = new LinkedList<>();
        for (RefundShouhouFormVo.RefundWxkcFormVo refundWxkc : wxkcSelectList) {
            WxKcBo wxKcBo = commonStructMapper.copyWxKcBo(wxKcBoMap.get(refundWxkc.getId()));
            wxKcBo.setRefundedPrice(refundWxkc.getRefundedPrice());
            wxKcBo.setTuiStatus(refundWxkc.getTuiStatus());
            tuiCheckList.add(wxKcBo);
        }
        shouhouFormVo.setWxKcBoList(wxKcBoList);
        Map<Integer, R<Integer>> wxkcResultMap = shouHouPjService.tui(tuiCheckList, true, true);
        Assert.isTrue(tuiCheckList.size() == wxkcIds.size(),"配件主键存在错误");
        String errorMsgJoin = wxkcResultMap.entrySet().stream().filter(entry -> !entry.getValue().isSuccess()).map(entry -> entry.getValue().getUserMsg()).collect(Collectors.joining(StringPool.COMMA));
        Assert.isTrue(StrUtil.isBlank(errorMsgJoin),"{}", errorMsgJoin);
        Assert.isFalse(wxKcBoList.stream().anyMatch(wxKcBo -> NumberUtil.null2Zero(wxKcBo.getRefundedPrice()).compareTo(BigDecimal.ZERO)>0),"存在多次退款的配件");
        Assert.isFalse(wxkcSelectList.stream().anyMatch(wxKcBo -> wxKcBo.getRefundedPrice() == null
                || wxKcBo.getRefundedPrice().compareTo(BigDecimal.ZERO)<0),"存在配件金额不合法");
        Function<WxKcBo, BigDecimal> canRefundPriceFun = originWxKcBo -> originWxKcBo.fetchCanRefundPrice();
        Assert.isFalse(wxkcSelectList.stream().anyMatch(wxKcBo -> {
            WxKcBo originWxKcBo = wxKcBoMap.get(wxKcBo.getId());
            return canRefundPriceFun.apply(originWxKcBo).compareTo(wxKcBo.getRefundedPrice())<0;
        }),"存在配件金额大于配件最大可退金额");

        //总金额校验
        Assert.isFalse(shouhouFormVo.getRefundPrice().compareTo(BigDecimal.ZERO)<0,"退款金额必须大于等于0");
        BigDecimal tuikuanM1 = wxKcBoList.stream().map(canRefundPriceFun).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal maxRefundPrice = getMaxRefundPrice(shouhou);
        Assert.isTrue(shouhouFormVo.getRefundPrice().compareTo(maxRefundPrice)<=0,"退款金额不能大于{}", maxRefundPrice.setScale(2, RoundingMode.HALF_DOWN));
        Assert.isTrue(shouhouFormVo.getTotalPrice().compareTo(shouhouFormVo.getRefundPrice().add(shouhouFormVo.getZhejiaM()))==0,
                "退款金额[{}]+折价金额[{}]!=商品总额[{}]",shouhouFormVo.getRefundPrice(), shouhouFormVo.getZhejiaM(), shouhouFormVo.getTotalPrice());
        //存在其他退款记录
        ShouhouTuiHuanPo shouhouTuiHuanPo = shouhouRefundMapper.getLastNotCompleteRefund(shouhouId, tuihuanKindEnum.getCode());
        Assert.isNull(shouhouTuiHuanPo,"已经存在退款记录!");
        OaUserBO oaUserBO = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        shouhouFormVo.setCurrUser(oaUserBO);
        AreaInfoClient areaInfoClient = SpringUtil.getBean(AreaInfoClient.class);
        shouhouFormVo.setCurrAreaInfo(Optional.ofNullable(areaInfoClient.getAreaInfoById(oaUserBO.getAreaId()))
                .map(R::getData).orElseThrow(()->new CustomizeException("获取当前门店信息异常")));
        //校验验证
        shouhouFormVo.setValidtWayEnum(EnumUtil.getEnumByCode(ValidMemberType.class,shouhouFormVo.getValidtWay()));
        TuihuanKindEnum validTuiKindEnum = tuihuanKindEnum;
        boolean isNeedValid = SpringUtil.getBean(RefundCommonService.class).isNeedValid(shouhouFormVo.getSubId(), shouhouId,shouhouFormVo.getValidtWay(), validTuiKindEnum);
        boolean isNotNeedValid = !isNeedValid;
        Assert.isTrue(isNotNeedValid || Objects.nonNull(shouhouFormVo.getValidtWay()),"验证类型不能为空");
        Assert.isTrue(isNotNeedValid || Objects.nonNull(shouhouFormVo.getValidtWayEnum()),"不支持验证类型{}",shouhouFormVo.getValidtWay());
        if(isNeedValid && !RefundShouhouService.CHECK_DATA_VALUE.equals(shouhouFormVo.getCheckData())){
            //如果是传1那就是单纯校验, 不需要校验验证码
            R<Boolean>  validtR = SpringUtil.getBean(BaseValidMemberService.class).valid(LambdaBuild.create(new ValidReq<RefundValidVo>())
                    .set(ValidReq::setValidCode,shouhouFormVo.getValidtCode()).set(ValidReq::setValidType,shouhouFormVo.getValidtWay())
                    .set(ValidReq::setBusinessType, BusinessTypeV1Enum.REFUND_MONEY.getCode()).set(ValidReq::setOrderId, Convert.toLong(shouhouFormVo.getSubId()))
                    .set(ValidReq::setT,new RefundValidVo().setShouhouId(shouhouId).setTuihuanKind(validTuiKindEnum.getCode())).build());
            if(!validtR.isSuccess()){
                return R.error(ResultCode.NEED_VERIFY,validtR.getUserMsg());
            }
        }
        Assert.isFalse(shouhouFormVo.getTuihuanFormVo() == null, "组合退信息不能为空");
        //设置组合退必须要的值
        GroupTuihuanFormVo tuihuanFormVo = shouhouFormVo.getTuihuanFormVo();
        tuihuanFormVo.setRefundPrice(shouhouFormVo.getRefundPrice());
        tuihuanFormVo.setComment(shouhouFormVo.getComment());
        tuihuanFormVo.setTuikuanM1(tuikuanM1);
        tuihuanFormVo.setTuihuanKind(shouhouFormVo.getTuihuanKind());
        tuihuanFormVo.setValidtCode(shouhouFormVo.getValidtCode());
        tuihuanFormVo.setValidtWay(shouhouFormVo.getValidtWay());
        tuihuanFormVo.setSubId(shouhouFormVo.getSubId());
        tuihuanFormVo.setZhejiaM(shouhouFormVo.getZhejiaM());
        tuihuanFormVo.setShouhouId(shouhouId);

        return R.success(null);
    }

    private void setBuyPriceAndSubInfo(RefundShouhouDetailVo refundDetail) {
        Shouhou shouhou = refundDetail.getShouhou();
        refundDetail.setYifuM(shouhou.getYifum());
        BigDecimal maxRefundPrice = getMaxRefundPrice(shouhou);
        refundDetail.setMaxRefundPrice(maxRefundPrice);
        getAndSetWxkcList(shouhou.getId(), refundDetail);
        removeNotSuportRefundWay(refundDetail.getRefundMoneyDetailVo(), shouhou);
    }

    private void removeNotSuportRefundWay(RefundMoneyDetailVo refundMoneyDetailVo, Shouhou shouhou) {
        // 大疆维修移除 现金和余额
        if(BigShouhouOrderTypeEnum.DJI.equals(shouhouService.getOrderType(shouhou.getId()))){
            // 移除现金和余额退款方式
            List<RefundMoneyDetailVo.RefundWayVo> refundWays = Optional.ofNullable(refundMoneyDetailVo)
                    .map(RefundMoneyDetailVo::getRefundWays).orElse(Collections.emptyList());
            refundWays.removeIf(way -> Stream.of(ShouhouRefundService.XIAN_JIN, ShouhouRefundService.YU_E,
                    ShouhouRefundService.WECHAT_REFUND_WAY, ShouhouRefundService.ALIPAY_REFUND_WAY).anyMatch(wayConst -> wayConst.equals(way.getName())));
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "大疆维修单移除现金和余额及秒退等退款方式");
        }
    }

    private BigDecimal getMaxRefundPrice(Shouhou shouhou) {
        //还没有退款类型,通过退款来进行获取不可退金额
        BigDecimal notRefundMoney = ObjectUtil.defaultIfNull(RefundMoneyUtil.getNotRefundMoney(
                DecideUtil.iif(Boolean.TRUE.equals(shouhou.getIsquji()), TuihuanKindEnum.TWXF, TuihuanKindEnum.TDJ_WXF),
                BusinessTypeEnum.REPAIR_ORDER.getCode(), shouhou.getId()).getNotRefundMoney(),BigDecimal.ZERO);
        BigDecimal maxRefundPrice = NumberUtil.max(ObjectUtil.defaultIfNull(shouhou.getYifum(), BigDecimal.ZERO).subtract(notRefundMoney), BigDecimal.ZERO);
        return maxRefundPrice;
    }
}
