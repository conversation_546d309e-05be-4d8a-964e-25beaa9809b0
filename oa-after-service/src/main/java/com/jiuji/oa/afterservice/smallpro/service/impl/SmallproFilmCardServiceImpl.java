package com.jiuji.oa.afterservice.smallpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ch999.common.util.vo.Result;
import com.jiuji.infra.lmstfy.anotation.LmstfyConsume;
import com.jiuji.oa.afterservice.apollo.ApolloEntity;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.AfterServiceTimeCfg;
import com.jiuji.oa.afterservice.cloud.vo.ProductServiceOpeningBO;
import com.jiuji.oa.afterservice.cloud.vo.ProductServiceOpeningVO;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.RequestCacheKeys;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.smallpro.bo.UsedTiemoCardBO;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardInfomationBO;
import com.jiuji.oa.afterservice.smallpro.constant.SmallProConstant;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.dao.TiemoCardMapper;
import com.jiuji.oa.afterservice.smallpro.po.TiemoCardUserLog;
import com.jiuji.oa.afterservice.smallpro.service.SmallProAdapterService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproDetailsExService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproFilmCardService;
import com.jiuji.oa.afterservice.smallpro.service.smallpro.SmallproSafeShellService;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.dto.YearPackageTransferDetailDto;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.service.IYearPackageTransferService;
import com.jiuji.oa.afterservice.sub.po.Category;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.service.RetryService;
import com.jiuji.oa.oacore.yearcardtransfer.enums.YearPackageTransferStatusEnum;
import com.jiuji.tc.utils.business.small.SmallproUtil;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.meitu.platform.lmstfy.Job;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * description: <小件接件-年包小件相关方法>
 * translation: <Small piece connection-small bag related method>
 *
 * <AUTHOR>
 * @date 2020/2/19
 * @since 1.0.0
 */
@Service
@Slf4j
public class SmallproFilmCardServiceImpl implements SmallproFilmCardService {

    private static final String REPURCHASE_BUY_EXPIRE_QUEUE = "${lmstfy.mult.first-lmstfy-client.repurchaseBuyExpireQueue}";

    @Autowired
    private SmallproMapper smallproMapper;
    @Autowired
    private SmallProAdapterService smallProAdapterService;
    @Autowired
    private SmallproSafeShellService smallproSafeShellService;
    @Resource
    private BasketService basketService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private TiemoCardMapper tiemoCardMapper;
    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;
    @Value("${lmstfy.mult.first-lmstfy-client.repurchaseBuyExpireQueue}")
    private String repurchaseBuyExpireQueue;
    @Resource
    private ApolloEntity apolloEntity;

    @Override
    public FilmCardInfomationBO getFilmCardInfomation(Integer basketId, boolean isNotDel){
        FilmCardInfomationBO filmCardInfomationBO = SpringContextUtil.reqCache(()-> CommenUtil.autoQueryHist(()->smallproMapper.getFilmCardInfomation(basketId, isNotDel)),
                RequestCacheKeys.GET_FILM_CARD_INFOMATION, basketId, isNotDel);
        if(filmCardInfomationBO == null){
            return null;
        }
        //如果basket信息为空且为九机查询历史库
        if(XtenantEnum.isJiujiXtenant() && filmCardInfomationBO.getCardBasketTbId() == null){
            Basket basket = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS,
                    () -> basketService.getByIdSqlServer(filmCardInfomationBO.getBasketId()));
            setHistBasketInfo(Collections.singletonList(basketId), filmCardInfomationBO, basket);
        }
        return filmCardInfomationBO;
    }

    @Override
    public FilmCardInfomationBO getFilmCardInfoByBasketId(Integer basketId) {
        if(CommenUtil.isNullOrZero(basketId)){
            return null;
        }
        FilmCardInfomationBO filmCardInfomationBO = getFilmCardInfomation(basketId, true);
        // 如果没有对应年包信息直接返回null
        if (filmCardInfomationBO == null) {
            return null;
        }
        handleCardPrice(filmCardInfomationBO);
        //判断是否绑定多个串号(basketBindRecord 这个表不进历史库)
        List<String> imeiList = smallproMapper.selectBasketBindRecordImei(basketId);
        filmCardInfomationBO.setImeiList(imeiList);
        if(CollectionUtils.isNotEmpty(imeiList) && imeiList.size()>NumberConstant.ONE){
            AtomicReference<String> imeiAtomic = new AtomicReference<>("");
            //获取前端传过来的串号
            SpringContextUtil.getRequest().ifPresent(request-> imeiAtomic.set(Optional.ofNullable(request.getAttribute(RequestAttrKeys.ANNUAL_PACKAGE_IMEI)).orElse("").toString()));
            Optional.ofNullable(imeiAtomic.get()).ifPresent(imei->{
                List<TiemoCardUserLog> tiemoCardUserLogs = smallproMapper.selectTiemoCardUserLog(basketId, imei);
                if(CollectionUtils.isNotEmpty(tiemoCardUserLogs)){
                    TiemoCardUserLog tiemoCardUserLog = tiemoCardUserLogs.get(0);
                    filmCardInfomationBO.setLastUseTime(tiemoCardUserLog.getDtime());
                } else {
                    filmCardInfomationBO.setLastUseTime(null);
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"家庭版年包设置LastUseTime为null,查询imei为：{}，basketId为：{},要使用质保, 通过使用过的串号接件,pc不支持使用，APP支持该功能",imei,basketId);
                }
            });
        }
        return getFilmCardInfo(filmCardInfomationBO);
    }

    private void handleCardPrice(FilmCardInfomationBO filmCardInfomationBO) {
        //更改年包价格
        Basket basket = Optional.ofNullable(basketService.getByIdSqlServer(filmCardInfomationBO.getBasketId())).orElseGet(() -> {
            AtomicReference<Basket> basketRef = new AtomicReference<>();
            basketRef.set(MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> basketService.getByIdSqlServer(filmCardInfomationBO.getBasketId())));
            return basketRef.get();
        });
        //判断壳年包
        if (smallproSafeShellService.tryHandleFilmCardInfo(filmCardInfomationBO)){
            filmCardInfomationBO.setPrice(Convert.toDouble(basket.getPrice()));
        }
    }

    @Override
    public FilmCardInfomationBO getFilmCardInfoByBasketIdByWrite(Integer basketId) {
        if(CommenUtil.isNullOrZero(basketId)){
            return null;
        }
        FilmCardInfomationBO filmCardInfomationBO = getFilmCardInfomation(basketId, true);
        // 如果没有对应年包信息直接返回null
        if (filmCardInfomationBO == null) {
            return null;
        }
        handleCardPrice(filmCardInfomationBO);
        return getFilmCardInfo(filmCardInfomationBO);
    }

    @Override
    public List<FilmCardInfomationBO> getFilmCardInfoByBasketIdByWrite(List<Integer> basketIds) {
        if(CollUtil.isEmpty(basketIds)){
            return Collections.emptyList();
        }
        List<FilmCardInfomationBO> filmCardInfomations = CommonUtils.bigDataInQuery(NumberConstant.FIVE_HUNDRED,basketIds, ids -> smallproMapper.listFilmCardInfomation(ids, true));

        //如果basket信息为空且为九机查询历史库
        if(XtenantEnum.isJiujiXtenant() && filmCardInfomations.stream().anyMatch(fci -> fci.getCardBasketTbId() == null)){
            List<FilmCardInfomationBO> fciHisList = filmCardInfomations.stream().filter(fci -> fci.getCardBasketTbId() == null).collect(Collectors.toList());
            Map<Integer,Basket> histBasketMap = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> CommonUtils.bigDataInQuery(fciHisList.stream()
                    .map(FilmCardInfomationBO::getBasketId).collect(Collectors.toSet()), ids -> basketService.lambdaQuery()
                    .in(Basket::getBasketId, ids).list())).stream().collect(Collectors.toMap(Basket::getBasketId, Function.identity(),(k1,k2) -> k1));
            //
            fciHisList.forEach(filmCardInfomationBO -> {
                Basket basket = histBasketMap.get(filmCardInfomationBO.getBasketId());
                setHistBasketInfo(basketIds, filmCardInfomationBO, basket);
            });
        }

        return filmCardInfomations.stream()
                .map(fci -> {
                    handleCardPrice(fci);
                    return getFilmCardInfo(fci);
                }).collect(Collectors.toList());
    }

    /**
     * 设置历史商品的信息
     * @param basketIds
     * @param filmCardInfomationBO
     * @param basket
     */
    private void setHistBasketInfo(List<Integer> basketIds, FilmCardInfomationBO filmCardInfomationBO, Basket basket) {
        if(basket == null){
            return;
        }
        filmCardInfomationBO.setCardBasketTbId(basket.getBasketId());
        // 是否为输入的商品id
        if(basketIds.stream().anyMatch(basketId -> Objects.equals(filmCardInfomationBO.getBasketId(), basketId))){
            filmCardInfomationBO.setPrice(Convert.toDouble(basket.getPrice()));
        }else if(basketIds.stream().anyMatch(basketId -> Objects.equals(filmCardInfomationBO.getBasketBindId(), basketId))){
            filmCardInfomationBO.setPrice(filmCardInfomationBO.getTcPrice());
        }
        filmCardInfomationBO.setCanRefundPrice(Convert.toDouble(ObjectUtil.defaultIfNull(basket.getPriceShouhou(), basket.getPrice2())));
        filmCardInfomationBO.setPpriceid(Convert.toInt(basket.getPpriceid()));
    }

    private FilmCardInfomationBO getFilmCardInfo(FilmCardInfomationBO filmCardInfomationBO) {
        // 如果没有对应年包信息直接返回null
        if (filmCardInfomationBO == null) {
            return null;
        }
        //时间统一为当天晚上12点之前有效
        if (filmCardInfomationBO.getEndTime() != null) {
            String endTimeStr = filmCardInfomationBO.getEndTime().format(DateUtil.format_day) + " 23:59:59";
            filmCardInfomationBO.setEndTime(DateUtil.stringToLocalDateTime(endTimeStr));
        }
        long buyTimeStamp = filmCardInfomationBO.getBuyTime().toEpochSecond(ZoneOffset.of("+8"));
        boolean newFlag = false;
        LocalDateTime frameTime = smallProAdapterService.getFrameTime();
        if (buyTimeStamp > frameTime.toEpochSecond(ZoneOffset.of("+8"))) {
            newFlag = true;
        }
        // 判断年包服务是否可用||截止时间大于购买时间
        filmCardInfomationBO.setIsNotExpired(filmCardInfomationBO.getEndTime().compareTo(LocalDateTime.now()) > 0);
        // 判断年包服务是否可用||是否还有使用余量
        filmCardInfomationBO.setIsUseCount(filmCardInfomationBO.getAllCount() > filmCardInfomationBO.getUseCount());
        // 如果年包服务可用，计算退款金额
        long nowTimeStamp = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        filmCardInfomationBO.setIsLastExchange(Boolean.FALSE);
        if(XtenantEnum.isSaasXtenant()){
            filmCardInfomationBO.setIsCanRefund(false);
        } else {
            filmCardInfomationBO.setIsCanRefund(true);
        }
        filmCardInfomationBO.setIsFreeExchange(false);
        //这里是交易时间
        setIsFreeChange(filmCardInfomationBO, newFlag, nowTimeStamp);
        //保护壳处理年包信息
        boolean isSafeShell = smallproSafeShellService.tryHandleFilmCardInfo(filmCardInfomationBO);
        //设置年包的类型
        if(isSafeShell){
            filmCardInfomationBO.setType(FilmCardInfomationBO.CardTypeEnum.SAFE_SHELL.getCode());
        }else{
            filmCardInfomationBO.setType(FilmCardInfomationBO.CardTypeEnum.TIE_MO.getCode());
        }

        //2022.03.03新增判断年包在过期的10年内还能使用一次
        if (!Boolean.TRUE.equals(filmCardInfomationBO.getIsNotExpired()) && XtenantEnum.isJiujiXtenant()
                && filmCardInfomationBO.getIsUseCount()
                && CommenUtil.isNullOrZero(filmCardInfomationBO.getLastCount())
                && LocalDateTime.now().isAfter(filmCardInfomationBO.getEndTime())
                && LocalDateTime.now().isBefore(filmCardInfomationBO.getEndTime().plusDays(FILM_CARD_OVER_DAY_LINE))) {
            filmCardInfomationBO.setIsLastExchange(Boolean.TRUE);
            filmCardInfomationBO.setIsNotExpired(Boolean.TRUE);
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"年包在过期的{}天内还能使用一次", FILM_CARD_OVER_DAY_LINE);
        }

        if (filmCardInfomationBO.getIsNotExpired() && filmCardInfomationBO.getIsUseCount()) {
            // 老退款规则
            if (!newFlag) {
                filmCardInfomationBO.setVersion(1);
                setOldRefundAmount(filmCardInfomationBO);
            }
            // 新退款规则
            if (newFlag) {
                filmCardInfomationBO.setVersion(2);
                setNewRefundAmount(filmCardInfomationBO);
            }
            //filmCardInfomationBO.setIsCanRefund(false);
            // 增加新字段记录生效时间
            // 新版本生效时间为购买后30天
            // 老版本生效时间为购买时间
            if(filmCardInfomationBO.getStartTime() != null){
                filmCardInfomationBO.setTradeDate(filmCardInfomationBO.getStartTime());
            }else if (newFlag) {
                filmCardInfomationBO.setTradeDate(filmCardInfomationBO.getTradeDate().plusDays(30));
            }
            filmCardInfomationBO.setBecomeEffectiveFlag(null == filmCardInfomationBO.getTradeDate() || !filmCardInfomationBO.getTradeDate().isAfter(LocalDateTime.now()));
        }

        if(isSafeShell && XtenantEnum.isJiujiXtenant() && Boolean.TRUE.equals(filmCardInfomationBO.getBecomeEffectiveFlag())
                && (Boolean.TRUE.equals(filmCardInfomationBO.getIsLastExchange()) || Boolean.TRUE.equals(filmCardInfomationBO.getIsNotExpired()))){
            filmCardInfomationBO.setIsLastExchange(Boolean.FALSE);
            filmCardInfomationBO.setIsNotExpired(Boolean.FALSE);
            filmCardInfomationBO.setStatusText("已生效(仅售前)");
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"保护壳年包改为售前接件");
        }

        if (isSafeShell){
            filmCardInfomationBO.setVersion(1);
//            filmCardInfomationBO.setIsFreeExchange(Boolean.FALSE);
//            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"保护壳年包没有质保换新");
            return filmCardInfomationBO;
        }
        // 赠送的膜年包
        IYearPackageTransferService yearPackageTransferService = SpringUtil.getBean(IYearPackageTransferService.class);
        String imei = SpringContextUtil.getRequest()
                .map(request -> request.getAttribute(RequestAttrKeys.ANNUAL_PACKAGE_IMEI)).map(Convert::toStr)
                .orElse(null);
        YearPackageTransferDetailDto transferDetail = yearPackageTransferService.getTransferDetail(imei);
        if(transferDetail != null){
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"增送膜年包信息整改");
            filmCardInfomationBO.setAllCount(1);
            filmCardInfomationBO.setLastCount(0);
            boolean isUsed = ObjectUtil.equals(transferDetail.getStatus(), YearPackageTransferStatusEnum.USED.getCode());
            filmCardInfomationBO.setUseCount(isUsed ? 1 : 0);
            boolean isPendingUse = ObjectUtil.equals(transferDetail.getStatus(), YearPackageTransferStatusEnum.PENDING_USE.getCode());
            filmCardInfomationBO.setBecomeEffectiveFlag(Boolean.TRUE);
            filmCardInfomationBO.setIsNotExpired(isPendingUse && !(LocalDateTime.now().isAfter(transferDetail.getEndTime())
                    && LocalDateTime.now().isBefore(transferDetail.getStartTime())));
            filmCardInfomationBO.setIsUseCount(!isUsed);

            filmCardInfomationBO.setIsCanRefund(Boolean.FALSE);
            filmCardInfomationBO.setIsLastExchange(Boolean.FALSE);
            filmCardInfomationBO.setIsFreeExchange(isUsed);

            filmCardInfomationBO.setBuyTime(transferDetail.getCreateTime());
            filmCardInfomationBO.setStartTime(transferDetail.getStartTime());
            filmCardInfomationBO.setEndTime(transferDetail.getEndTime());
            filmCardInfomationBO.setTradeDate(transferDetail.getStartTime());
            filmCardInfomationBO.setImeiList(CollUtil.newLinkedList(imei));
        }
        return filmCardInfomationBO;
    }

    private void setIsFreeChange(FilmCardInfomationBO filmCardInfomationBO, boolean newFlag, long nowTimeStamp) {
        // 判断是否为30天免费换新期
        // 新版本用最后一次使用时间&购买时间做判断
        // 老版本用购买时间判断
        long pointTimeStamp = 0L;
        int replaceDay = 45;
        if (XtenantEnum.isJiujiXtenant() && Objects.nonNull(filmCardInfomationBO.getBasketBindId())) {
            Optional<Long> ppidOptional = Optional.ofNullable(CommenUtil
                    .autoQueryHist(()-> basketService.getByIdSqlServer(filmCardInfomationBO.getBasketBindId())))
                    .map(Basket::getPpriceid);
            replaceDay = ppidOptional.map(v -> SpringUtil.getBean(SmallproDetailsExService.class).getAfterTimeCfgMap(Collections.singletonList(v.intValue())))
                    .map(afterTimeCfgMap -> afterTimeCfgMap.get(Convert.toStr(ppidOptional.get())))
                    .filter(v -> Objects.nonNull(v.getReplaceDay()))
                    .map(AfterServiceTimeCfg::getReplaceDay).orElse(replaceDay);
        }
        //绑定钢化膜的交易时间
        LocalDateTime tradeDate = CommenUtil.autoQueryHist(() -> basketService.lambdaQuery().eq(Basket::getBasketId, filmCardInfomationBO.getBasketBindId())
                    .select(Basket::getSubId).list().stream().findFirst()
                    .map(Basket::getSubId)
                    .flatMap(subId -> SpringUtil.getBean(SubService.class).lambdaQuery()
                            .eq(Sub::getSubId, subId).select(Sub::getTradeDate1).list().stream().filter(Objects::nonNull)
                            .findFirst().map(Sub::getTradeDate1)),
                MTableInfoEnum.BASKET, filmCardInfomationBO.getBasketBindId()).orElse(filmCardInfomationBO.getTradeDate());

        pointTimeStamp = SmallproUtil.getEndTime(tradeDate, replaceDay).toEpochSecond(ZoneOffset.of("+8"));
        // 获取到绑定的第一个串号
        List<String> imeiList = smallproMapper.selectBasketBindRecordImei(Optional.ofNullable(filmCardInfomationBO.getBasketBindId()).orElse(Integer.MIN_VALUE));
        String firstImei= Integer.MIN_VALUE+"";
        if(CollectionUtils.isNotEmpty(imeiList)){
            firstImei=imeiList.get(0);
        }
        AtomicReference<String> imei = new AtomicReference<>("");
        SpringContextUtil.getRequest().ifPresent(request-> imei.set(Optional.ofNullable(request.getAttribute(RequestAttrKeys.ANNUAL_PACKAGE_IMEI)).orElse("").toString()));
        if (nowTimeStamp <= pointTimeStamp && (StringUtils.isEmpty(imei.get()) || firstImei.equals(imei.get()))) {
            filmCardInfomationBO.setIsFreeExchange(true);
        }
        if (filmCardInfomationBO.getLastUseTime() != null && newFlag) {
            pointTimeStamp = SmallproUtil.getEndTime(filmCardInfomationBO.getLastUseTime(), replaceDay).toEpochSecond(ZoneOffset.of("+8"));
            if (nowTimeStamp <= pointTimeStamp) {
                filmCardInfomationBO.setIsFreeExchange(true);
            }
        }
    }

    /**
     * description: <老退款规则>
     * translation: <Old refund rules>
     *
     * @param filmCardInfomationBO 年包卡信息
     * @return void
     * <AUTHOR>
     * @date 11:12 2020/2/19
     * @since 1.0.0
     **/
    private void setOldRefundAmount(FilmCardInfomationBO filmCardInfomationBO) {
        // 使用次数大于两次不退
        // 使用次数小于0数据错误
        if (filmCardInfomationBO.getUseCount() > 2 || filmCardInfomationBO.getUseCount() < 0) {
            filmCardInfomationBO.setIsCanRefund(false);
            filmCardInfomationBO.setRefundAmount(0.0);
        } else {
            filmCardInfomationBO.setIsCanRefund(true);
            BigDecimal price = BigDecimal.valueOf(filmCardInfomationBO.getCanRefundPrice());
            switch (filmCardInfomationBO.getUseCount()) {
                case 0:
                    filmCardInfomationBO.setRefundAmount(filmCardInfomationBO.getCanRefundPrice());
                    break;
                case 1:
                    price = price.multiply(BigDecimal.valueOf(0.6));
                    filmCardInfomationBO.setRefundAmount(price.setScale(2, BigDecimal.ROUND_FLOOR).doubleValue());
                    break;
                case 2:
                    if (filmCardInfomationBO.getAllCount() == 2) {
                        filmCardInfomationBO.setIsCanRefund(false);
                        filmCardInfomationBO.setRefundAmount(0.0);
                    } else if (filmCardInfomationBO.getAllCount() > 2) {
                        price = price.multiply(BigDecimal.valueOf(0.3));
                        filmCardInfomationBO.setRefundAmount(price.setScale(2, BigDecimal.ROUND_FLOOR).doubleValue());
                    }
                    break;
                default:
                    // 错误
                    filmCardInfomationBO.setRefundAmount(-1.0);
                    break;
            }
        }
    }

    /**
     * description: <新退款规则>
     * translation: <New refund rules>
     *
     * @param filmCardInfomationBO 年包卡信息
     * @return void
     * <AUTHOR>
     * @date 11:13 2020/2/19
     * @since 1.0.0
     **/
    private void setNewRefundAmount(FilmCardInfomationBO filmCardInfomationBO) {
        // 判断是否已使用年包卡
        if (filmCardInfomationBO.getUseCount() > 0) {
            if(XtenantEnum.isJiujiXtenant()){
                filmCardInfomationBO.setRefundAmount(0.0);
                filmCardInfomationBO.setIsCanRefund(true);
            } else {
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"年包basketId:{} 已使用{}次,不可以退",filmCardInfomationBO.getBasketId(),filmCardInfomationBO.getUseCount());
                filmCardInfomationBO.setRefundAmount(0.0);
                filmCardInfomationBO.setIsCanRefund(false);
            }

            return;
        }

        Integer ppid = filmCardInfomationBO.getPpriceid();
        boolean isTiemoCard = ppid != null && (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_HALF_YEAR_SERVICE_PPID).contains(ppid)
                || smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_ONE_YEAR_SERVICE_PPID).contains(ppid)
                || smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_TWO_YEAR_SERVICE_PPID).contains(ppid)
                || ObjectUtil.equal(filmCardInfomationBO.getType(), FilmCardInfomationBO.CardTypeEnum.TIE_MO.getCode()));
        if (isTiemoCard) {
            //贴膜折价率从配置获取
            BigDecimal discountAmount = SpringUtil.getBean(SmallProAdapterService.class)
                    .calculateDiscountAmount(ppid, filmCardInfomationBO.getBuyTime(), BigDecimal.valueOf(filmCardInfomationBO.getCanRefundPrice())
                            , filmCardInfomationBO.getBasketId());
            filmCardInfomationBO.setRefundAmount(discountAmount.setScale(2, RoundingMode.HALF_UP).doubleValue());
            filmCardInfomationBO.setIsCanRefund(true);
            return;
        }
        LocalDateTime discountStartTime = Optional.ofNullable(filmCardInfomationBO.getStartTime()).orElse(filmCardInfomationBO.getBuyTime());
        LocalDateTime now = LocalDateTime.now();
        BigDecimal price = BigDecimal.valueOf(ObjectUtil.defaultIfNull(filmCardInfomationBO.getCanRefundPrice(), 0D));
        Duration duration = Duration.between(discountStartTime, now);
        Long day = duration.toDays();
        int month = 0;
        Period period = Period.between(discountStartTime.toLocalDate(), filmCardInfomationBO.getEndTime().toLocalDate());
        // 全年卡
        BigDecimal lessRule = BigDecimal.valueOf(0.08);
        // 半年卡
        if (period.getYears() <= 0 && period.getMonths() >= 6) {
            lessRule = BigDecimal.valueOf(0.16);
        }
        // 计算价格
        double rule = 0.8;
        BigDecimal ruleDecimal = BigDecimal.valueOf(rule);
        // 1-15天全额免费退
        if (day <= 15 || stringRedisTemplate.hasKey(StrUtil.format(RedisKeys.SMALLPRO_NOT_DISCOUNT_BASKET_KEY,filmCardInfomationBO.getBasketId()))) {
            filmCardInfomationBO.setIsCanRefund(true);
            filmCardInfomationBO.setRefundAmount(filmCardInfomationBO.getCanRefundPrice());
        }
        // 15-30天||半个月后退0.8
        else if (day <= 30) {
            price = price.multiply(ruleDecimal);
            filmCardInfomationBO.setIsCanRefund(true);
            filmCardInfomationBO.setRefundAmount(price.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        }
        // 超过一个月，每个月按照折损率退
        else {
            month = Convert.toInt(ChronoUnit.MONTHS.between(discountStartTime, now)) -1;
            BigDecimal monthDecimal = BigDecimal.valueOf(month);
            lessRule = lessRule.multiply(monthDecimal);
            rule = rule - lessRule.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            // 退款率衰减过后可能为负数
            if (rule < 0) {
                rule = 0.0;
            }
            ruleDecimal = BigDecimal.valueOf(rule);
            if (rule > 0) {
                price = price.multiply(ruleDecimal);
                double refundAmount = price.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                if (refundAmount <= 0) {
                    refundAmount = 0.0;
                }
                filmCardInfomationBO.setIsCanRefund(true);
                filmCardInfomationBO.setRefundAmount(price.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            } else {
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"年包basketId:{} {}个月,折损率衰减{},没有可退金额",filmCardInfomationBO.getBasketId(),month,lessRule);
                filmCardInfomationBO.setIsCanRefund(false);
                filmCardInfomationBO.setRefundAmount(0.0);
            }
        }
    }


    @Override
    public void pushRepurchaseBuyMsg() {
        List<UsedTiemoCardBO> usedTiemoCardList = tiemoCardMapper.listUsedTiemoCard(apolloEntity.getRepurchaseBuyDay());
        Map<Integer, List<UsedTiemoCardBO>> usedTiemoCardListMap = usedTiemoCardList.stream()
                .filter(v -> StringUtils.isNotBlank(v.getMobile()))
                .collect(Collectors.groupingBy(UsedTiemoCardBO::getXtenant));
        usedTiemoCardListMap.forEach(this::handleRepurchaseBuyMsg);
    }

    private void handleRepurchaseBuyMsg (Integer xtenant, List<UsedTiemoCardBO> usedTiemoCardList) {
        List<Integer> servicePpidLst = usedTiemoCardList.stream()
                .map(UsedTiemoCardBO::getServicePpid)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<Integer> servicePpidHis = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> CommonUtils.bigDataInQuery(usedTiemoCardList.stream()
                .filter(v -> Objects.isNull(v.getServicePpid()))
                .map(UsedTiemoCardBO::getBasketId).collect(Collectors.toSet()), ids -> basketService.lambdaQuery()
                .in(Basket::getBasketId, ids).list().stream().map(v -> Convert.toInt(v.getPpriceid(), 0)).distinct().collect(Collectors.toList())));
        servicePpidLst.addAll(servicePpidHis);
        List<Category> categorys = SpringUtil.getBean(CategoryService.class).listAll();
        RetryService retryService = SpringUtil.getBean(RetryService.class);
        WebCloud webCloud = SpringUtil.getBean(WebCloud.class);
        List<ProductServiceOpeningVO> productServiceList = Optional.ofNullable(retryService.retryByFeignRetryableException(() ->
                webCloud.openingGetServices(CollUtil.join(servicePpidLst, ","), xtenant)))
                //只获取成功的结果
                .filter(r -> {
                    boolean isSuccess = r.getCode() == Result.SUCCESS;
                    if (!isSuccess) {
                        throw new CustomizeException(StrUtil.format("获取服务配置, 网站接口返回: {}", JSON.toJSONString(r)));
                    }
                    return true;
                })
                .map(Result::getData).orElse(Collections.emptyList());
        Map<Integer, List<ProductServiceOpeningVO>> productServiceMap = productServiceList.stream().collect(Collectors.groupingBy(ProductServiceOpeningBO::getPpid));
        SmsService smsService = SpringUtil.getBean(SmsService.class);
        for (UsedTiemoCardBO v : usedTiemoCardList) {
            List<Integer> cidList = StrUtil.splitTrim(v.getCidFamily(), StringPool.COMMA)
                    .stream().filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
            int repurchaseBuyTime = Optional.ofNullable(productServiceMap.get(v.getServicePpid())).orElse(Collections.emptyList()).stream()
                    .sorted(Comparator.comparing(pso -> categorys.stream().filter(c -> Objects.equals(c.getId(), pso.getCategoryId()))
                            .findFirst().map(Category::getLevel).orElse(-1), Comparator.reverseOrder()))
                    .filter(pso -> cidList.contains(pso.getCategoryId()))
                    .findFirst().map(ProductServiceOpeningVO::getRepurchaseBuyTime).orElse(0);
            if (Objects.nonNull(v.getEndTime()) && repurchaseBuyTime > 0) {
                //复购结束时间
                LocalDate repurchaseBuyEndDate = v.getLastUserTime().plusHours(repurchaseBuyTime).toLocalDate();
                if (LocalDate.now().isAfter(repurchaseBuyEndDate)) {
                    log.warn("复购结束时间小于当前时间repurchaseBuyTime={},v={}", repurchaseBuyTime, v);
                    continue;
                }
                //使用完毕失效第七天,消息
                String repurchaseBuyUsedMsg = String.format(SmallProConstant.REPURCHASE_BUY_USED_MSG,
                        cn.hutool.core.date.DateUtil.format(v.getLastUserTime(), DatePattern.CHINESE_DATE_PATTERN),
                        cn.hutool.core.date.DateUtil.format(repurchaseBuyEndDate.atStartOfDay(), DatePattern.CHINESE_DATE_PATTERN));
                //复购到期前7天,消息
                String repurchaseBuyExpireMsg = String.format(SmallProConstant.REPURCHASE_BUY_EXPIRE_MSG,
                        cn.hutool.core.date.DateUtil.format(repurchaseBuyEndDate.atStartOfDay(), DatePattern.CHINESE_DATE_PATTERN));
                String mobile = v.getMobile();
                if (Boolean.TRUE.equals(v.getRepurchaseBuyExpireFlag())) {
                    if (LocalDateTime.now().isBefore(repurchaseBuyEndDate.atStartOfDay())){
                        smsService.sendSms(mobile,repurchaseBuyExpireMsg,DateUtil.localDateTimeToString(LocalDateTime.now()),"系统",smsService.getSmsChannelByTenant(v.getAreaId(), ESmsChannelTypeEnum.YZMTD));
                    }
                } else {
                    smsService.sendSms(mobile,repurchaseBuyUsedMsg,DateUtil.localDateTimeToString(LocalDateTime.now()),"系统",smsService.getSmsChannelByTenant(v.getAreaId(), ESmsChannelTypeEnum.YZMTD));

                    LocalDateTime localDateTime = repurchaseBuyEndDate.minusDays(apolloEntity.getRepurchaseBuyDelayDay()).atTime(LocalDateTime.now().toLocalTime());;
                    int delaySeconds = Convert.toInt(LocalDateTimeUtil.between(LocalDateTime.now(), localDateTime).getSeconds());
                    if (delaySeconds < 0) {
                        log.warn("延时推送时间小于当前时间localDateTime={},v={}", localDateTime, JSONUtil.toJsonStr(v));
                        continue;
                    }
                    try {
                        v.setRepurchaseBuyExpireFlag(true);
                        String publish = firstLmstfyClient.publish(repurchaseBuyExpireQueue, JSONUtil.toJsonStr(v).getBytes(), 0, (short) 1, delaySeconds);
                        log.info("年包复购到期前7天消息推送延迟队列推送成功，队列名称{}，推送参数{}，返回结果{}",repurchaseBuyExpireQueue,JSONUtil.toJsonStr(v),publish);
                    } catch (Exception e) {
                        log.error("年包复购到期前7天消息推送异常，队列名称{}，推送参数{}",repurchaseBuyExpireQueue,JSONUtil.toJsonStr(v),e);
                    }
                }
            }
        }
    }

    /**
     * 延迟队列监听消费
     * 处理方式为上门取件的预约单
     * @param job
     */
    @LmstfyConsume(queues = REPURCHASE_BUY_EXPIRE_QUEUE, clientBeanName = "firstLmstfyClient")
    public void repurchaseBuyExpireQueue(Job job) {
        log.warn("REPURCHASE_BUY_EXPIRE_QUEUE消费到数据：{}}", JSONUtil.toJsonStr(job));
        String data = Optional.ofNullable(job).orElse(new Job()).getData();
        if(StringUtils.isEmpty(data)){
            return ;
        }
        try {
            UsedTiemoCardBO vo = JSONUtil.toBean(data, UsedTiemoCardBO.class);
            if (Objects.isNull(vo)) {
                return;
            }
            Integer count = Optional.ofNullable(tiemoCardMapper.checkRepurchaseBuy(vo)).orElse(0);
            if (Objects.equals(0, count)) {
                handleRepurchaseBuyMsg(vo.getXtenant(), Collections.singletonList(vo));
            }
        } catch (Exception e) {
            log.error("年包复购提醒队列反序列化失败，反序列数据为{}", data, e);
        }
    }
}
