package com.jiuji.oa.afterservice.common.vo.res;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class DisplayImportCheckVoRes<T> {

    /**
     * 成功数量
     */
    private Integer successCount;
    /**
     * 失败数量
     */
    private Integer failCount;

    private List<T> successCheck;

    private List<T> failCheck;

    private Integer count;

}
