package com.jiuji.oa.afterservice.smallpro.dao;

import cn.hutool.core.lang.Dict;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.cloud.after.vo.req.XiaojianSubReqVO;
import com.jiuji.cloud.after.vo.res.XiaojianProductVO;
import com.jiuji.cloud.after.vo.res.XiaojianSubResVO;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.small.SmallYuyueOrderOnGoingVO;
import com.jiuji.oa.afterservice.smallpro.bo.*;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.*;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.*;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.*;
import com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproProductServiceTimeBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproReceivableProductBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.DIYTimoCardBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproMsgCodeSaveInfo;
import com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproSendMsgCodeAreaInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproSendMsgCodeSubInfoBO;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.po.TiemoCardUserLog;
import com.jiuji.oa.afterservice.smallpro.vo.req.*;
import com.jiuji.oa.afterservice.smallpro.vo.res.*;
import com.jiuji.oa.afterservice.statistics.bo.smallpro.AddSmallproLogBatchBO;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
@Mapper
public interface SmallproMapper extends BaseMapper<Smallpro> {


    /**
     * 返销数据相关查询
     * @param basketId
     * @return
     */
    List<OperatorBasketReq> selectOperatorBasketByBasketId(@Param("basketIds") List<Integer> basketIds);
    /**
     * 查询正在进行中的小件单
     * @param subId
     * @return
     */
    Integer selectProcessingIdBySubId(@Param("subId") Integer subId);
    /**
     * 查询退换记录
     *
     * @param basketIds
     * @return
     */
    @SqlParser(filter = true)
    List<FilmUseLog> getFilmHuanList(@Param("basketIds") List<Integer> basketIds);

    @SqlParser(filter = true)
    List<FilmCardInfomationBO> getTieMoCardUse(@Param("basketIds") List<Integer> basketIds);
    /**
     * 保护膜列表分页查询
     * @param page
     * @param req
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
     Page<SelectResistFilmRes> selectResistFilmPage(Page<SelectResistFilmRes> page,@Param("req")SelectResistFilmReq req);

     List<SelectResistFilmRes> selectLossCountSmallProId(@Param("ids") List<Integer> ids);

    List<LossDetailsInfo> selectLossDetails(@Param("req")LossDetailsReq req);
    /**
     * 获取最小basketId
     * @param smallProId
     * @return
     */
    Integer getMinBasketIdBySmallProId(@Param("smallProId")Integer smallProId);

    /**
     * 根据旧件id查询旧件数量
     * @param oldIdType
     * @param oldId
     * @return
     */
    Integer findQuantityOfLoss(@Param("oldIdType") Integer oldIdType, @Param("oldId") Integer oldId);

    /**
     * 查询九机服务商品
     * @param basketId
     * @return
     */
    List<ServiceProductBO> selectServiceProductByBasketId(@Param("basketId") Integer basketId);


    /**
     * 根据订单basketid查询log
     * @param basketId
     * @param startTime
     * @param endTime
     * @return
     */
    List<Smallpro> selectLogByBasketId(@Param("basketId") Integer basketId);

    /**
     * 特殊质保换货异常
     * @param basketId
     * @return
     */
    List<Smallpro> selectSpecialExchanges(@Param("basketIdList") List<Integer> basketIdList,@Param("isSpecialTreatment")Integer isSpecialTreatment);

    /**
     * 递归查询basketId
     * @param basketId
     * @return
     */
    List<RecursiveQueriesReq> selectRecursiveQueriesReq(@Param("basketId") Integer basketId);


    /**
     * 历史数据查询
     * @param req
     * @return
     */
    List<HistoricalProcessingRes> selectHistoricalProcessing(@Param("req") HistoricalProcessingReq req);


    /**
     * 小件退货管理总条数查询
     *
     * @param query
     * @return
     */
    Integer getSmallproReturnGoodsNum(@Param("query") SmallproReturnGoodsReq query);

    /**
     * 小件退货管理查询
     *
     * @param query
     * @param pageSize
     * @param startRow
     * @return
     */
    List<SmallproReturnGoodsRes> getSmallproReturnGoodsList(@Param("query") SmallproReturnGoodsReq query, @Param(
            "pageSize") Integer pageSize, @Param("startRow") Integer startRow);

    /**
     * 获取小件接件列表的成本总和
     * @param query
     * @return
     */
    SmallproInfoCostCalculateRes.Cost getSmallproCurrentPageCost(@Param("query") SmallproReturnGoodsReq query);

    /**
     * description: <获取小件接单的订单信息>
     * translation: <Get order information for smallpro>
     *
     * @param basketIdList 订单条目Id列表
     * @param xtenant
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoOrderBO>
     * <AUTHOR>
     * @date 15:30 2019/11/28
     * @since 1.0.0
     **/
    List<SmallproInfoOrderBO> getSmallproOrderInfo(@Param("basketIdList") List<Integer> basketIdList, int xtenant);

    /**
     * description: <获取小件接单的商品信息>
     * translation: <Get good information for smallpro>
     *
     * @param ppriceIdList SKU id列表
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoProductBO>
     * <AUTHOR>
     * @date 15:33 2019/11/28
     * @since 1.0.0
     **/
    List<SmallproInfoProductBO> getSmallproShopInfo(@Param("ppriceIdList") List<Long> ppriceIdList);

    /**
     * description: <获取小件接单的用户信息>
     * translation: <Get user information for smallpro>
     *
     * @param userId 用户Id
     * @return com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoUserBO
     * <AUTHOR>
     * @date 18:29 2019/11/14
     * @since 1.0.0
     **/
    SmallproInfoUserBO getSmallproUserInfo(@Param("userId") Integer userId);


    /**
     * 小件退换货旧件处理总数
     *
     * @param query
     * @return
     */
    Integer getSmallproOldPartTotalNum(@Param("query") SmallproOldPartReq query);

    /**
     * 小件退换货旧件处理列表
     *
     * @param query
     * @param pageSize
     * @param startRow
     * @return
     */
    List<SmallproOldPartBO> getSmallproOldPartList(@Param("query") SmallproOldPartReq query, @Param(
            "pageSize") Integer pageSize, @Param("startRow") Integer startRow);

    List<SmallproOldPartBO> getSmallproOldPartListAll(@Param("query") SmallproOldPartReq query);

    BigDecimal getSmallproOldPartAllCost(@Param("query") SmallproOldPartReq query);

    /**
     * 获取合计成本和合计金额
     *
     * @param query
     * @return
     */
    SmallproOldPartRes getAssessCostAndAmount(@Param("query") SmallproOldPartReq query);


    /**
     * description: <获取小件接件相关地区的地区名称>
     * translation: <Get the region name of the region associated with the widget>
     *
     * @param areaIdList 地区Id列表
     * @return java.util.List<java.util.Map>
     * <AUTHOR>
     * @date 18:23 2019/11/15
     * @since 1.0.0
     **/
    List<SmallproInfoAreaBO> getSmallproAreaName(@Param("areaIdList") List<Integer> areaIdList);

    /**
     * description: <获取小件接件单附件信息列表>
     * translation: <Get a list of smallpro attachments>
     *
     * @param smallproId 小件接件单Id
     * @return java.util.List<java.util.Map>
     * <AUTHOR>
     * @date 9:55 2019/11/18
     * @since 1.0.0
     **/
    List<SmallproInfoFileBO> getSmallproFileInfo(@Param("smallproId") Integer smallproId);

    /**
     * description: <获取接件人信息>
     * translation: <Get the sender information>
     *
     * @param inUserNameList 接件人姓名列表
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoInuserInfoBO>
     * <AUTHOR>
     * @date 17:21 2019/11/19
     * @since 1.0.0
     **/
    List<SmallproInfoInuserInfoBO> getSmallproInUserInfo(@Param("inUserNameList") List<String> inUserNameList);

    /**
     * description: <获取接件人信息>
     * translation: <Get the sender information>
     *
     * @param inUserNameList 接件人姓名列表
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoInuserInfoBO>
     * <AUTHOR>
     * @date 17:21 2019/11/19
     * @since 1.0.0
     **/
    List<SmallproInfoInuserInfoBO> getSmallproInUserInfoByWrite(@Param("inUserNameList") List<String> inUserNameList);

    /**
     * description: <获取小件订单九机服务>
     * translation: <Get smallpro Jiuji service>
     *
     * @param basketId 订单条目Id
     * @return java.util.Map
     * <AUTHOR>
     * @date 17:43 2019/11/27
     * @since 1.0.0
     **/
    SmallproInfoServiceRecordBO getSubServiceRecord(@Param("basketId") Integer basketId);

    SmallproInfoServiceRecordBO getSubServiceRecordByServiceProduct(@Param("basketId") Integer basketId);

    /**
     * description: <SqlServer-getById>
     * translation: <SqlServer-getById>
     *
     * @param id 主键
     * @return com.jiuji.oa.afterservice.smallpro.po.Smallpro
     * <AUTHOR>
     * @date 14:27 2019/11/29
     * @since 1.0.0
     **/
    @Select("select * from smallpro with(nolock) where id=${id}")
    Smallpro getByIdSqlServer(@Param("id") Integer id);

    /**
     * description: <SqlServer-getById>
     * translation: <SqlServer-getById>
     *
     * @param id 主键
     * @return com.jiuji.oa.afterservice.smallpro.po.Smallpro
     * <AUTHOR>
     * @date 14:27 2019/11/29
     * @since 1.0.0
     **/
    @Select("select * from smallpro with(nolock) where id=${id}")
    Smallpro getByIdSqlServerByWriter(@Param("id") Integer id);

    /**
     * description: <SqlServer-list>
     * translation: <SqlServer-list>
     *
     * @param wrapper 筛选条件
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.po.Smallpro>
     * <AUTHOR>
     * @date 14:27 2019/11/29
     * @since 1.0.0
     **/
    @Select("select * from smallpro with(nolock) ${ew.customSqlSegment}")
    List<Smallpro> listSqlServer(@Param(Constants.WRAPPER) Wrapper wrapper);

    /**
     * description: <SqlServer-list>
     * translation: <SqlServer-list>
     *
     * @param wrapper 筛选条件
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.po.Smallpro>
     * <AUTHOR>
     * @date 14:27 2019/11/29
     * @since 1.0.0
     **/
    @Select("select * from smallpro with(nolock) ${ew.customSqlSegment}")
    List<Smallpro> listSqlServerByWrite(@Param(Constants.WRAPPER) Wrapper wrapper);

    /**
     * description: <获取商品服务商时间>
     * translation: <Get merchandiser time>
     *
     * @param productIdList 商品Id列表
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproProductServiceTimeBO>
     * <AUTHOR>
     * @date 11:15 2019/12/2
     * @since 1.0.0
     **/
    @DS("web999")
    List<SmallproProductServiceTimeBO> getProductServiceTime(@Param("productIdList") List<Integer> productIdList);

    /**
     * description: <查询需要小件接件的订单信息>
     * translation: <Query order information for small parts>
     *
     * @param type    关键字类别
     * @param keyList 关键字列表
     * @param areaId  地区Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproReceivableProductBO>
     * <AUTHOR>
     * @date 14:27 2019/12/2
     * @since 1.0.0
     **/
    List<SmallproReceivableProductBO> getSmallproReceivableProduct(
            @Param("type") Integer type, @Param("keyList") List<String> keyList, @Param("areaId") Integer areaId);

    /**
     * description: <根据订单Id查询需要小件接件的订单信息>
     * translation: <Query order information that requires small parts according to order Id>
     *
     * @param subId 订单Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproReceivableProductBO>
     * <AUTHOR>
     * @date 14:37 2019/12/2
     * @since 1.0.0
     **/
    List<SmallproReceivableProductBO> getSmallproReceivableProductBySubId(
            @Param("subId") Integer subId,@Param("frameTime") String frameTime
            ,@Param("authPart") Boolean authPart,@Param("authorizeId") Integer authorizeId,@Param("basketId") Integer basketId,@Param("isJiujiXtenant") Boolean isJiujiXtenant);


    List<SmallproReceivableProductBO> getSmallproReceivableProductBySubIdV3(
            @Param("subId") Integer subId,@Param("frameTime") String frameTime
            ,@Param("authPart") Boolean authPart,@Param("authorizeId") Integer authorizeId,@Param("isJiujiXtenant") Boolean isJiujiXtenant);

    List<SmallproReceivableProductBO> getMobileReceivableProductBySubIdV2(
            @Param("subId") Integer subId,@Param("frameTime") String frameTime
            ,@Param("authPart") Boolean authPart,@Param("authorizeId") Integer authorizeId,@Param("isJiujiXtenant") Boolean isJiujiXtenant);

    /**
     * description: <获取小件接件取件相关数据>
     * translation: <Get smallpro and pickup data>
     *
     * @param smallproId 小件接件单Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPickUpInfoBO>
     * <AUTHOR>
     * @date 14:19 2019/12/27
     * @since 1.0.0
     **/
    List<SmallproPickUpInfoBO> getSmallproPickUpInfo(@Param("smallproId") Integer smallproId);

    /**
     * description: <获取小件换货库存操作相关数据>
     * translation: <Obtaining data related to stock exchange operations>
     *
     * @param smallproId 小件接件单Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproExchangeStockInfoBO>
     * <AUTHOR>
     * @date 15:47 2019/12/27
     * @since 1.0.0
     **/
    List<SmallproExchangeStockInfoBO> getSmallproExchangeStockInfo(@Param("smallproId") Integer smallproId);

    /**
     * description: <获取小件接件相关订单信息>
     * translation: <Get order information about smallpro pick up>
     *
     * @param smallproId 小件接件Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPickupOrderInfoBO>
     * <AUTHOR>
     * @date 14:47 2020/1/7
     * @since 1.0.0
     **/
    List<SmallproPickupOrderInfoBO> getSmallproPickupOrderInfo(@Param("smallproId") Integer smallproId);

    /**
     * description: <获取发送验证码需要的订单信息>
     * translation: <Get the order information needed to send a verification code>
     *
     * @param subId 订单Id
     * @return com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproSendMsgCodeSubInfoBO
     * <AUTHOR>
     * @date 9:47 2020/1/9
     * @since 1.0.0
     **/
    SmallproSendMsgCodeSubInfoBO getMsgCodeSendSubInfo(@Param("subId") Integer subId);

    /**
     * description: <获取发送验证码需要的门店信息>
     * translation: <Get store information needed to send a verification code>
     *
     * @param areaId 门店Id
     * @return com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproSendMsgCodeAreaInfoBO
     * <AUTHOR>
     * @date 9:59 2020/1/9
     * @since 1.0.0
     **/
    SmallproSendMsgCodeAreaInfoBO getMsgCodeSendAreaInfo(@Param("areaId") Integer areaId);

    /**
     * description: <获取小件接件保存验证码需要的信息>
     * translation: <Get the information needed for small parts to save the verification code>
     *
     * @param smallproId 小件接件Id
     * @return com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproMsgCodeSaveInfo
     * <AUTHOR>
     * @date 11:10 2020/1/9
     * @since 1.0.0
     **/
    SmallproMsgCodeSaveInfo getMsgCodeSaveInfo(@Param("smallproId") Integer smallproId);

    /**
     * description: <获取小件接件取件时出库相关信息>
     * translation: <Get out-of-warehouse information when picking up small pieces>
     *
     * @param smallproId 小件接件Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPickUpOutboundInfoBO>
     * <AUTHOR>
     * @date 16:04 2020/1/15
     * @since 1.0.0
     **/
    List<SmallproPickUpOutboundInfoBO> getOutboundInfo(@Param("smallproId") Integer smallproId);

    /**
     * description: <获取小件接件使用九机服务时的进价相关信息>
     * translation: <Get information about the purchase price of small pieces when using the nine-machine service>
     *
     * @param smallproId 小件接件Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproJiujiServiceInpriceInfoBO>
     * <AUTHOR>
     * @date 15:03 2020/1/16
     * @since 1.0.0
     **/
    List<SmallproJiujiServiceInpriceInfoBO> getJiujiServiceInPriceInfo(@Param("smallproId") Integer smallproId);

    /**
     * description: <更新小件九机服务使用情况>
     * translation: <Update the usage of small pieces of nine machine service>
     *
     * @param smallproUpdateJiujiServiceBO 小件接件九机服务更新信息BO
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 15:23 2020/1/16
     * @since 1.0.0
     **/
    Integer updateJiujiService(SmallproUpdateJiujiServiceBO smallproUpdateJiujiServiceBO);

    /**
     * description: <获取小件取件时推送评价所需信息>
     * translation: <Get the information you need to push reviews when picking up small pieces>
     *
     * @param smallproId 小件接件Id
     * @return com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproSendReviewInfoBO
     * <AUTHOR>
     * @date 11:12 2020/1/17
     * @since 1.0.0
     **/
    List<SmallproSendReviewInfoBO> getSendReviewInfo(@Param("smallproId") Integer smallproId);

    /**
     * description: <根据接件人电话获取接见人信息>
     * translation: <Get Recipient Information from Recipient's Phone>
     *
     * @param mobile 接件人电话
     * @return com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoInuserInfoBO
     * <AUTHOR>
     * @date 11:25 2020/1/17
     * @since 1.0.0
     **/
    SmallproInfoInuserInfoBO getInUserInfoByMobile(@Param("mobile") String mobile);

    /**
     * description: <获取用户微信相关信息>
     * translation: <Get user WeChat related information>
     *
     * @param userId 用户Id
     * @return com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproSendReviewUserWechatInfoBO
     * <AUTHOR>
     * @date 11:38 2020/1/17
     * @since 1.0.0
     **/
    SmallproSendReviewUserWechatInfoBO getUserWechatInfo(@Param("userId") Integer userId);

    /**
     * description: <根据门店Id获取该门店可用收货人信息>
     * translation: <Get the available consignee information of the store according to the store Id>
     *
     * @param areaId 门店Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.SmallproLogisticsReceiverUserInfoBO>
     * <AUTHOR>
     * @date 14:17 2020/1/17
     * @since 1.0.0
     **/
    List<SmallproLogisticsReceiverUserInfoBO> getLogisticsReceiverUserInfo(@Param("areaId") Integer areaId);

    /**
     * description: <获取门店相关联的门店信息>
     * translation: <Get store-related store information>
     *
     * @param areaId 门店Id
     * @return com.jiuji.oa.afterservice.smallpro.bo.SmallproRelatedAreaInfoBO
     * <AUTHOR>
     * @date 15:53 2020/1/17
     * @since 1.0.0
     **/
    SmallproRelatedAreaInfoBO getRelatedAreaInfoByArea(@Param("areaId") Integer areaId);

    /**
     * description: <是否有支付宝原路返回>
     * translation: <Is there any Alipay return>
     *
     * @param returnFactoryId shouhoutuihuan-Id
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 13:42 2020/1/19
     * @since 1.0.0
     **/
    Integer isReturnAlipay(@Param("returnFactoryId") Integer returnFactoryId);

    /**
     * description: <获取手机贴膜对应的年包卡>
     * translation: <Get the annual card corresponding to the mobile phone film>
     *
     * @param basketId 手机贴膜订单basketId
     * @param isNotDel 只查询未删除的
     * @return com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardInfomationBO
     * <AUTHOR>
     * @date 17:33 2020/2/18
     * @since 1.0.0
     **/
    FilmCardInfomationBO getFilmCardInfomation(@Param("basketId") Integer basketId, @Param("isNotDel") boolean isNotDel);

    /**
     * description: <获取小件接件备货信息>
     * translation: <Obtaining stock picking information for small parts>
     *
     * @param hhSubId 备货Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproStockingInfoBO>
     * <AUTHOR>
     * @date 23:40 2020/3/1
     * @since 1.0.0
     **/
    List<SmallproStockingInfoBO> getSmallproStockingInfoBo(@Param("hhSubId") Integer hhSubId);

    /**
     * description: <获取小件接件报废相关信息>
     * translation: <Obtain scrap-related information>
     *
     * @param returnFactoryId shouhoufanchangId
     * @return com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproScrapInfoBO
     * <AUTHOR>
     * @date 23:19 2020/3/3
     * @since 1.0.0
     **/
    SmallproScrapInfoBO getScrapSmallproInfo(@Param("returnFactoryId") Integer returnFactoryId);

    /**
     * description: <获取小件接件退款相关信息>
     * translation: <Get information on small item refunds>
     *
     * @param subId 订单Id
     * @return com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundMoneyInfoBO
     * <AUTHOR>
     * @date 15:15 2020/3/11
     * @since 1.0.0
     **/
    SmallproRefundMoneyInfoBO getRefundMoneyInfo(@Param("subId") Integer subId);

    /**
     * description: <获取小件接件退款订单信息>
     * translation: <Get small order refund information>
     *
     * @param subId 订单Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundSubInfoBO>
     * <AUTHOR>
     * @date 16:02 2020/3/11
     * @since 1.0.0
     **/
    List<SmallproRefundSubInfoBO> getRefundSubInfo(@Param("subId") Integer subId);

    /**
     * description: <获取现有的小件接件退款订单信息>
     * translation: <Get existing small parts refund order information>
     *
     * @param subId     订单Id
     * @param shouhouId 售后Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundSubInfoBO>
     * <AUTHOR>
     * @date 16:07 2020/3/11
     * @since 1.0.0
     **/

    List<SmallproRefundSubInfoBO> getRefundSubInfoExisting(@Param("subId") Integer subId,
                                                           @Param("shouhouId") Integer shouhouId);

    /**
     * description: <检查用户的二级密码是否正确>
     * translation: <Check if the user's secondary password is correct>
     *
     * @param userId 用户Id
     * @param pwd2   二级密码MD5格式
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 14:38 2020/3/13
     * @since 1.0.0
     **/
    Integer checkUserPwd2(Integer userId, String pwd2);

    /**
     * description: <获取退款所需门店信息>
     * translation: <Store information required for refund>
     *
     * @param areaId 门店Id
     * @return com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundAreaInfoBO
     * <AUTHOR>
     * @date 14:42 2020/3/16
     * @since 1.0.0
     **/
    SmallproRefundAreaInfoBO getRefundAreaInfo(@Param("areaId") Integer areaId);

    /**
     * description: <校验自提点支付>
     * translation: <Verify self-withdrawal point payment>
     *
     * @param subId 订单Id
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 14:53 2020/3/16
     * @since 1.0.0
     **/
    Integer checkZitidianType(@Param("subId") Integer subId);

    Integer refundServiceCheck1(@Param("basketIdList") List<Integer> basketIdList);

    /**
     * description: <获取小件退款金额>
     * translation: <Get small refund amount>
     *
     * @param subId        订单Id
     * @param basketIdList 商品条目Id列表
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundPriceInfoBO>
     * <AUTHOR>
     * @date 15:20 2020/3/16
     * @since 1.0.0
     **/
    List<SmallproRefundPriceInfoBO> getRefundPrice(@Param("subId") Integer subId,
                                                   @Param("basketIdList") List<Integer> basketIdList);

    /**
     * description: <获取订单的网络支付金额>
     * translation: <Get the online payment amount of the order>
     *
     * @param subId 订单Id
     * @return java.lang.Double
     * <AUTHOR>
     * @date 16:19 2020/3/16
     * @since 1.0.0
     **/
    Double getNetworkMoneyPayout(@Param("subId") Integer subId);

    /**
     * description: <获取微信秒退已退金额>
     * translation: <Get WeChat Refunded Seconds>
     *
     * @param subId 原订单Id
     * @return java.lang.Double
     * <AUTHOR>
     * @date 16:23 2020/3/16
     * @since 1.0.0
     **/
    Double getWechatMoneyPayout(@Param("subId") Integer subId);

    /**
     * description: <获取退款库白条信息>
     * translation: <Get refund bar white bar information>
     *
     * @param subId 原订单Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundKuBaiTiaoMBO>
     * <AUTHOR>
     * @date 17:00 2020/3/16
     * @since 1.0.0
     **/
    List<SmallproRefundKuBaiTiaoMBO> getRefundKuBaiTiaoM(@Param("subId") Integer subId);

    /**
     * description: <获取退款网单相关信息>
     * translation: <Get information about refund web forms>
     *
     * @param type   类别
     * @param idList id列表
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundNetPayInfoBO>
     * <AUTHOR>
     * @date 9:44 2020/3/17
     * @since 1.0.0
     **/
    List<SmallproRefundNetPayInfoBO> getRefundNetPayInfo(@Param("idList") List<Integer> idList,
                                                         @Param("type") Integer type);

    /**
     * description: <获取小件转现所需信息>
     * translation: <Get the information you need to transfer small pieces into inventory>
     *
     * @param returnFactoryId shouhou_fanchangId
     * @return com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproIncomingInfoBO
     * <AUTHOR>
     * @date 10:13 2020/3/18
     * @since 1.0.0
     **/
    SmallproIncomingInfoBO getIncomingSmallproInfo(@Param("returnFactoryId") Integer returnFactoryId);

    Integer updateNetPayRecordBySmallproSubmitRefund(@Param("shouhouTuihuanId") Integer shouhouTuihuanId, @Param(
            "price") BigDecimal price);


    /**
     *
     */


    /***
     * description: <撤销退款审核时删除支付批次>
     * translation: <Delete payment batch when reversing refund review>
     * @param shouhouTuihuanId shouhouTuihuanId
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 14:51 2020/3/19
     * @since 1.0.0
     **/
    Integer deleteNetPayRecordByShouhouTuihuanId(@Param("shouhouTuihuanId") Integer shouhouTuihuanId);

    Integer updateNetPayRefundInfoWhenCancelRefundCheck(@Param("shouhouTuihuanId") Integer shouhouTuihuanId);


    /**
     * description: <获取父订单Id>
     * translation: <Get the parent order Id>
     *
     * @param subId 订单Id
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 10:14 2020/3/23
     * @since 1.0.0
     **/
    Integer getSubPid(@Param("subId") Integer subId);

    /**
     * description: <获取退款方式时需要的门店信息>
     * translation: <Store information needed to get a refund>
     *
     * @param areaId 门店Id
     * @return com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoReturnWayAreaInfoBO
     * <AUTHOR>
     * @date 11:37 2020/3/30
     * @since 1.0.0
     **/
    SmallproInfoReturnWayAreaInfoBO getAreaInfoWithReturnWay(@Param("areaId") Integer areaId);

    /**
     * description: <获取小件接件退款办理所需信息>
     * translation: <Obtain the information required for refund handling>
     *
     * @param subId            原订单Id
     * @param shouhouTuihuanId 售后退换Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundProcessingInfoBO>
     * <AUTHOR>
     * @date 14:41 2020/4/7
     * @since 1.0.0
     **/
    List<SmallproRefundProcessingInfoBO> getSmallproRefundProcessingInfo(@Param("subId") Integer subId, @Param(
            "shouhouTuihuanId") Integer shouhouTuihuanId);

    /**
     * description: <获取小件接件退款办理所需信息>
     * translation: <Obtain the information required for refund handling>
     *
     * @param subId 原订单Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundProcessingInfoBO>
     * <AUTHOR>
     * @date 14:53 2020/4/7
     * @since 1.0.0
     **/
    List<SmallproRefundProcessingInfoBO> getSmallproRefundProcessingInfoWithNoOne(@Param("subId") Integer subId);

    /**
     * description: <获取小件退款时的退款数量信息>
     * translation: <Get the refund quantity information when refunding small items>
     *
     * @param subId              原订单Id
     * @param shouhouTuihuanId   售后退换Id
     * @param tuiMobile_basketID
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 15:57 2020/4/7
     * @since 1.0.0
     **/
    Integer getRefundSubCountInfo(@Param("subId") Integer subId
            , @Param("shouhouTuihuanId") Integer shouhouTuihuanId
            , @Param("tuiMobile_basketID") Integer tuiMobile_basketID);

    /**
     * description: <获取员工积分退还时的员工ID>
     * translation: <Get the employee ID when the employee points are returned>
     *
     * @param subId 原订单Id
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 15:46 2020/4/8
     * @since 1.0.0
     **/
    Integer getCh999UserIdForJifen(@Param("subId") Integer subId);

    /**
     * description: <退款办理时更新联通卡套餐信息>
     * translation: <Update Unicom card package information during refund processing>
     *
     * @param basketId 条目Id
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 16:07 2020/4/8
     * @since 1.0.0
     **/
    Integer updateTaocanByRefund1(@Param("basketId") Integer basketId);

    Integer updateTaocanByRefund2(@Param("basketId") Integer basketId);

    Integer updateTaocanByRefund3(@Param("basketId") Integer basketId);

    Integer updateHaomaByRefund(@Param("basketId") Integer basketId);

    Integer updateBasketByRefund1(@Param("subId") Integer subId, @Param("basketId") Integer basketId);

    Integer updateShouhouTuihuanByRefund1(@Param("subId") Integer subId,
                                          @Param("shouhouTuihuanId") Integer shouhouTuihuanId);

    Integer updateBasketByRefund2(@Param("basketCount") Integer basketCount,
                                  @Param("basketId") Integer basketId);

    Integer updateReturnsDetailByRefund1(@Param("newBasketId") Integer newBasketId,
                                         @Param("oldBasketId") Integer oldBasketId);

    Integer deleteBskHalfBuyRecordByRefund1(@Param("basketId") Integer basketId);

    Integer updateBskHalfBuyRecordByRefund2(@Param("basketCount") Integer basketCount,
                                            @Param("basketId") Integer basketId);

    Integer deleteServiceRecordByRefund1(@Param("basketIdList") List<Integer> basketIdList);

    Integer deleteInstallServicesRecordByRefund1(@Param("userName") String userName,
                                                 @Param("basketIdList") List<Integer> basketIdList);

    Integer deleteTiemoCardByRefund1(@Param("basketIdList") List<Integer> basketIdList);

    Integer deleteProductSnByRefund1(@Param("basketIdList") List<Integer> basketIdList);

    List<Integer> getTiemoCardUserLogToDeleteByRefund(@Param("basketIdList") List<Integer> basketIdList);

    Integer deleteTiemoCardUserLogByRefund(@Param("basketIdList") List<Integer> basketIdList);

    Integer updateTiemoCardByRefund2(@Param("cardIdList") List<Integer> cardIdList);

    Integer getBBSXPUserIdByRefund(@Param("subId") Integer subId);

    Long getPointsBySubId(@Param("subId") Integer subId, @Param("price") Double price);

    BigDecimal getAllPriceBySubId(@Param("subId") Integer subId);

    Integer updateNumberCardByRefund1(@Param("subId") Integer subId);

    Integer deleteNumberCardByRefund1(@Param("cardTypeList") List<Integer> cardTypeList, @Param("subId") Integer subId);

    Integer deleteCardLogsByRefund1(@Param("subId") Integer subId);

    BigDecimal getYingfuMFromRecoverMarketInfoByRefund(@Param("subId") Integer subId);

    Long getPointByRefund1(@Param("subId") Integer subId);

    List<SmallproBankInstallmentInfoBO> getBankInstallmentInfoByRefund(@Param("afterREId") Integer afterREId);


    FilmCardInfoForPickupBO selectTiemoCardInfoByPickup(@Param("basketId") Integer basketId);

    /**
     *
     * @param cardId
     * @param inDate 小件接件时间
     * @param type
     * @return
     */
    Integer updateTiemoCardByPickUp(@Param("cardId") Integer cardId, @Param("inDate") LocalDateTime inDate, @Param("type") boolean type);

    SmallproProductInfoBO getProductInfoByPpriceId(@Param("ppriceId") Integer ppriceId);

    SmallproProductInfoBO getProductInfoForCheck(@Param("ppriceId") Integer ppriceId);

    Integer getSubUserIdByTuihuanKind(@Param("kind") Integer tuihuanKind, @Param("subId") Integer subId);

    String getUserOpenId(@Param("userId") Integer userId);

    FilmCardLastChangePPidBO getLastChangePPID(@Param("basketId") Integer basketId, @Param("subId") Integer subId);

    List<SmallproPayInfoBO> getPayInfoBySubId(@Param("type") Integer type,
                                              @Param("subIdList") List<String> subIdList,
                                              @Param("payWayList") List<String> payWayList);

    List<SmallproPayInfoBO> getPayInfoByAfterSaleREId(@Param("afterSaleREId") Integer afterSaleREId);

    /**
     * 查询退款类型
     * @param subIdList
     * @param type
     * @return
     */
    List<String> getEffectiveRefundType(@Param("subIdList") List<Integer> subIdList, @Param("type") Integer type);

    List<SmallproKcRelatedInfoBO> getKcRelatedIdType(@Param("basketId") Integer basketId);

    SmallproOneClickStockUserInfoBO getCh999UserInfoByOneClickStocking(@Param("userId") Integer userId);

    List<SmallproOneClickStockingProductInfoBO> getProductInfoByOneClickStocking(@Param("ppidList") List<Long> ppidList);

    /**
     * description: <根据条形码获取对应的PpriceId>
     * translation: <Obtain the corresponding PpriceId according to the barcode>
     *
     * @param barCode 条形码
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 14:21 2020/5/11
     * @since 1.0.0
     **/
    Integer getPpriceIdByBarcodeWithSmallpro(@Param("barCode") String barCode);

    Integer checkTemperedFilm(@Param("basketId") Integer basketId);

    String getImeiFromBasketBindRecord(@Param("basketId") Integer basketId);


    Integer checkTemperedFilmWithImei(@Param("basketId") Integer basketId,
                                      @Param("imei") String imei);

    Integer getIsChangePre(@Param("id") Integer id, @Param("subId") Integer subId, @Param("basketId") Integer basketId);

    /**
     * @see SmallproMapper#listLastOutPpriceId(Set)
     * @param smallProId
     * @return
     */
    @Deprecated
    Integer getLastOutPPID(@Param("smallProId") Integer smallProId);

    @DS("ch999oanew")
    List<Integer> checkBasketBindRecord(@Param("imei") String imei, @Param("basketId") Long basketId);

    List<String> selectBasketBindRecordImei(@Param("basketId") Integer basketId);


    List<TiemoCardUserLog> selectTiemoCardUserLog(@Param("basketId") Integer basketId, @Param("imei") String imei);

    Integer updateCodeMsgAndFid(@Param("codeMsg") String codeMsg, @Param("fid") String fid,
                                @Param("smallproId") Integer smallproId);

    List<BasketBO> getBasketMessage(@Param("basketId") Long basketId);

    List<WxBO> getWeixinUser(@Param("userId") Long userId);

    int getUseCount(@Param("basketId") Long basketId);

    /**
     * 判断原始订单是否已提交并且未处理的
     *
     * @param basketId
     * @return
     */
    List<Smallpro> checkOrginSubCommitAndUnDeal(@Param("basketId") List<Integer> basketId);

    /**
     * 从历史库查
     *
     * @param subId
     * @return
     */
    @DS("oanew_his")
    List<SmallproReceivableProductBO> getSmallproReceivableProductBySubIdHis(
            @Param("subId") Integer subId,@Param("frameTime") String frameTime,@Param("basketId") Integer basketId,@Param("isJiujiXtenant") Boolean isJiujiXtenant);

    /**
     * 根据订单id查询小件商品id
     *
     * @param subId   订单id
     * @param comment 备注信息
     * @return
     */
    Integer getSmallProIdBySubId(@Param("subId") Integer subId, @Param("comment") String comment);

    Integer correctStatus();

    @DS("oanew_his")
    List<SmallproInfoOrderBO> getSmallproOrderInfoHis(@Param("basketIdList") List<Integer> collect);

    @DS("oanew_his")
    SmallproSendMsgCodeSubInfoBO getMsgCodeSendSubInfoHis(@Param("subId") Integer subId);

    List<Integer> checkPpid(@Param("ppId") List<String> ppId);

    List<Integer> checkBarcode(@Param("ppId") String ppId);

    List<DIYTimoCardBO> getDIYYearCard(@Param("basketId") Integer basketId);

    List<DIYTimoCardBO> getDIYYearCardByBasketIds(@Param("basketIds") List<Integer> basketIds);

    List<AddSmallproLogBatchBO> getAddSmallproLogBatch(@Param("idList") List<Integer> idList);

    /**
     * 判断当前ppids是否包含diy保护壳
     * @param ppids
     * @return
     */
    Integer getproductIdByPPid(@Param("ppids") List<Long> ppids);

    LocalDateTime getTradeDateTime(@Param("subId") Integer subId);

    BigDecimal getBaitiaoPrice(@Param("subId") String subId);

    BigDecimal getKuBaitiaoPrice(@Param("subId") String subId);

    Integer getSpotSubData(@Param("subId") Integer subId, @Param("ppIds") List<Long> ppIds);

    List<Integer> checkSmallProId(@Param("oldId") Integer oldId, @Param("ppId") Long ppId);

    List<SmallReceiptPrintingBO> getReceiptPrinting(@Param("smallProId") Integer smallProId);

    CheckSmallProWuLiuBO getCheckSmallProWuLiu(@Param("smallProId") Integer smallProId);

    List<SmallCategoryBO> getSmallCategoryData(@Param("req") SmallCategoryReq req, @Param("areaIds") List<Integer> areaIds, @Param("xTenant")Integer xTenant);

    List<SmallCategoryInventoryBO> getSmallCategoryDataInventory(@Param("req") SmallCategoryReq req, @Param("areaIds") List<Integer> areaIds);

    List<SmallCategoryInventoryBO> getSmallCategoryDataInventoryOn(@Param("req") SmallCategoryReq req, @Param("areaIds") List<Integer> areaIds);

    List<BatchPushBO> getBatchPushCh999Ids(@Param("smallProIds") List<Integer> smallProIds);

    Integer getYuyueIdByMobile(@Param("areaId") Integer areaId,@Param("mobile") String mobile);

    SmallProVimpelComBo getProVimpelComInfo(@Param("basketId") Integer basketId);

    Integer getFilmCountByPpids(@Param("ppids") List<Long> ppids);

    /**
     *
     * @param smallId
     * @return imei smallProBillId
     */
    List<Dict> getBindImeiBySmallId(@Param("smallId") Integer smallId);

    List<SmallProOldGoodsWaitingForSelectRes> getSmallProOldGoodsWaitingForSelect(@Param("areaId")Integer areaId);

    /**
     * 查询小件信息 因为使用mybatis-plus自带的方法会锁表
     * @param subId
     * @param areaId
     * @return
     */
    List<Smallpro> getSmallProListBySubId(@Param("subId") Integer subId, @Param("areaId") Integer areaId);

    /**
     * 查询分类信息
     * @return
     */
    List<CategoryBO> getAllCategory();

    /**
     * 查询陈列和优品
     * @param req
     * @param areaIds
     * @return
     */
    List<DisplayProductInfoBO> getDisplayProductInfoData(@Param("req") SmallCategoryReq req, @Param("areaIds") List<Integer> areaIds);

    /**
     * 根据ppid和areaId查询门店可用库存量
     * @param ppid
     * @param areaId
     * @return
     */
    Integer getProductKcLeftCountByPpidAndAreaId(@Param("ppid") Integer ppid, @Param("areaId") Integer areaId);


    /**
     * 根据id集合查询小件接件转现相关信息集合
     * @param collects id集合
     * @return 小件接件转现相关信息集合
     */
    List<SmallproIncomingInfoBO> getIncomingSmallproInfoList(@Param("collects") List<Integer> collects);

    /**
     * 根据返厂id查询小件报废相关信息
     * @param fanChangIdList 返厂id集合
     * @return 小件报废相关信息集合
     */
    List<SmallproScrapInfoBO> getScrapSmallproInfoList(@Param("fanChangIdList") List<Integer> fanChangIdList);

    /**
     * 查询小件售后退换信息
     * @param smallProId
     * @return
     */
    List<ShouhouTuihuan> getSmallProRefundList(@Param("smallProId") Integer smallProId);


    List<ShouhouTuihuan> getReturnMaintenanceCosts (@Param("smallProId") Integer smallProId);

    /**
     * 查询未审核
     * @param smallProId
     * @return
     */
    List<ShouhouTuihuan> getSmallProRefundNotCheckList(@Param("smallProId") Integer smallProId);


    /**
     * 根据basket_id获取运营商业务的状态
     * @param basketIdList basketIdList
     * @return
     */

    List<Integer> getOperatorBasketByStatus(@Param("basketIdList") List<Integer> basketIdList);

    List<Integer> getOperatorBasketByStatusJiuJi(@Param("basketIdList") List<Integer> basketIdList);

    /**
     * 获取上一次换货商品成本价
     * @param basketId
     * @return
     */
    BigDecimal getLastExchangePpidInpriceByBasketId(@Param("basketId") Integer basketId);

    /**
     * 根据basket_id获取商品是否类型（优品）
     * @return
     */
    List<Integer> getBasketTypeById(@Param("basketIdList") List<Integer> basketIdList);

    /**
     * 查询订单小件退换ID
     * @param basketId 订单ID
     * @return Integer
     */
    Integer getRefundCount(@Param("basketId") Integer basketId);


    List<NotSoldBackBO> getProductIsResale(@Param("basketIdList") List<Integer> basketIdList);

    List<LastExchangeSmallproBo> listLastChangeSmallpro(@Param("smallproId") Integer smallproId, @Param("subId") Integer subId,
                                                        @Param("basketIds") Set<Integer> basketIds);

    /**
     * 小件最后一次换货出库ppid
     * @param smallproIds
     * @return
     */
    List<LastExchangeSmallproBo> listLastOutPpriceId(@Param("smallproIds") Set<Integer> smallproIds);

    List<String> getBasketIdBySmallproID(@Param("smallproID") String smallproID);

    /**
     * 批量获取手机贴膜对应的年包卡
     * @param basketIds
     * @param isNotDel
     * @return
     */
    List<FilmCardInfomationBO> listFilmCardInfomation(@Param("basketIds") Collection<Integer> basketIds, @Param("isNotDel") boolean isNotDel);

    /**
     * 进行中的小件单数量
     *
     * @param basketIds
     * @param userId
     * @return
     */
    List<SmallYuyueOrderOnGoingVO> listSmallOrderOnGoing(@Param("basketIds") List<Integer> basketIds, @Param("userId") Integer userId);

    /**
     * 进行中的预约单数量
     *
     * @param basketIds
     * @param userId
     * @return
     */
    List<SmallYuyueOrderOnGoingVO> listYuyueOrderOnGoing(@Param("basketIds") List<Integer> basketIds, @Param("userId") Integer userId);

    /**
     * 门店实时库存数量，数量为现货库存（不含订单锁定，陈列）
     *
     * @param changePpriceid
     * @return
     */
    Integer getKcCount(@Param("ppriceid") Integer changePpriceid,@Param("areaId") Integer areaId);

    /**
     *  获取订单所有已付金额(包括拆单)
     * @param subId
     * @return
     */
    BigDecimal getSubWithPidSubTotalMoney(@Param("subId") Integer subId);

    BigDecimal remainingCashMoney(@Param("subId") Integer subId);

    /**
     * 获取九机年卡使用的总金额
     * @param subId
     * @return
     */
    BigDecimal getTotalYearCardUseNcPrices(@Param("subId") Integer subId);

    /**
     * 获取basketId处理次数
     *
     * @param basketIdList
     * @param ppids 商品ppid
     * @param areaId 门店id
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    List<HandleCountReq> getHandleCountList(@Param("basketIdList") List<Integer> basketIdList, @Param("ppids") List<Integer> ppids, @Param("areaId") Integer areaId);

    @DS(DataSourceConstants.CH999_OA_NEW)
    List<ShouHouBasketReq> getShouHouBasketIdList(@Param("basketIdList") List<Integer> basketIdList);

    List<String> checkSub(@Param("subIdList")List<String> subIdList);

    /**
     * 查询小件售后单
     * @param req
     * @return
     */
    XiaojianSubResVO getXiaojianSub(@Param("req") XiaojianSubReqVO req);

    List<XiaojianProductVO> getXiaojianProductList(@Param("req") XiaojianSubReqVO req);

    Integer getTuihuanHistCount(@Param("subId") Integer buySubId);

    /**
     * 获取售后小件收银金额与退款金额的差值
     * @param smallproId 小件ID
     * @return 差值
     */
    BigDecimal getMaintenanceCostDifference(@Param("smallproId") Integer smallproId);
}
