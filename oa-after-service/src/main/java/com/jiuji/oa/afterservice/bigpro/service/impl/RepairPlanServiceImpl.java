package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.RepairPlanMapper;
import com.jiuji.oa.afterservice.bigpro.dao.RepairPlanMasterMapper;
import com.jiuji.oa.afterservice.bigpro.enums.CorrelationTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlan;
import com.jiuji.oa.afterservice.bigpro.service.RepairAccessoriesService;
import com.jiuji.oa.afterservice.bigpro.service.RepairPlanService;
import com.jiuji.oa.afterservice.bigpro.service.ShouHouPjService;
import com.jiuji.oa.afterservice.bigpro.vo.req.ApplyDelReq;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.tc.common.vo.R;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 维修方案表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Service
public class RepairPlanServiceImpl extends ServiceImpl<RepairPlanMapper, RepairPlan> implements RepairPlanService {

    @Resource
    private ShouHouPjService shouHouPjService;
    @Resource
    private RepairAccessoriesService repairAccessoriesService;
    @Resource
    private RepairPlanMasterMapper repairPlanMasterMapper;

    /**
     * 取消绑定
     * @param correlationId
     * @param correlationType
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelBind(Integer correlationId, Integer correlationType,String userName) {
        List<RepairPlan> list = this.lambdaQuery().eq(RepairPlan::getCorrelationId, correlationId)
                .eq(RepairPlan::getCorrelationType,correlationType)
                .list();
        if(CollectionUtils.isNotEmpty(list)){
            List<Integer> planIds = list.stream().map(RepairPlan::getId).collect(Collectors.toList());
            boolean update = this.lambdaUpdate().in(RepairPlan::getId, planIds)
                    .set(RepairPlan::getCorrelationId, null)
                    .set(RepairPlan::getCorrelationType, null)
                    .set(RepairPlan::getUpdateUser, userName)
                    .set(RepairPlan::getUpdateTime, LocalDateTime.now()).update();
            if(!update){
                throw new CustomizeException("取消大疆维修绑定失败");
            }
        }
    }

    /**
     * 取消关联信息
     * @param plan
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelCorrelationInfo(Integer planId) {
        if(ObjectUtil.isNull(planId)){
            return;
        }
        RepairPlan plan = this.getById(planId);
        if(ObjectUtil.isNull(plan)){
            return;
        }

        // 根据故障ID查询售后单ID
        Integer shouHouId = repairPlanMasterMapper.getShouHouIdByFaultId(plan.getFaultId());
        if(ObjectUtil.isNull(shouHouId)){
            throw new CustomizeException("未找到对应的售后单信息");
        }
        Integer correlationId = plan.getCorrelationId();
        Integer correlationType = plan.getCorrelationType();
        if(ObjectUtil.isNotNull(correlationId) && ObjectUtil.isNotNull(correlationType)){
            if(CorrelationTypeEnum.WXKCOUTPUT.getCode().equals(correlationType)){
                // isGenerateAccessory由true改为false，进行维修配件撤销
                R<Boolean> tui = shouHouPjService.editWxPj(shouHouId, "tui", "", correlationId, null, null, null, 0);
                if(!tui.isSuccess()){
                    throw new CustomizeException("进行维修配件撤销失败:"+ Optional.ofNullable(tui.getUserMsg()).orElse(tui.getMsg()));
                }
            }

            if(CorrelationTypeEnum.SHOUHOU_APPLY.getCode().equals(correlationType)){
                ApplyDelReq applyDelReq = new ApplyDelReq();
                applyDelReq.setId(correlationId);
                applyDelReq.setComment("删除用户确认维修方案");
                repairAccessoriesService.applyDel(applyDelReq);
            }
        }
    }
}