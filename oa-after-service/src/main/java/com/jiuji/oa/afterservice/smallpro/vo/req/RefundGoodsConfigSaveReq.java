package com.jiuji.oa.afterservice.smallpro.vo.req;

import com.jiuji.oa.afterservice.smallpro.enums.SmallRefundServiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/23 11:50
 * @Description 小件换货配置查询参数
 */
@Data
@Accessors(chain = true)
@ApiModel("小件换货配置保存")
public class RefundGoodsConfigSaveReq implements Serializable {
    @ApiModelProperty(value = "id")
    private Integer id;
    /**
     * 配置名称 标题
     */
    @ApiModelProperty(value = "配置名称/标题")
    @Length(max = 50,message = "标题长度不能超过50个字")
    @NotNull(message = "标题不能为空")
    private String title;
    /**
     * 服务类型
     *
     * @see SmallRefundServiceTypeEnum
     */
    @ApiModelProperty(value = "服务类型")
    private Integer serviceType;
    /**
     * 开始天数
     */
    @ApiModelProperty(value = "开始天数")
    private Integer startDays;
    /**
     * 结束天数
     */
    @ApiModelProperty(value = "结束天数")
    private Integer endDays;
    /**
     * 故障类型(1无故障 2有故障)
     */
    @ApiModelProperty(value = "故障类型")
    private Integer hitchType;
    /**
     * 退款方式(1正常退款 2特殊退款 )
     */
    @ApiModelProperty(value = "退款方式")
    private Integer refundType;

    /**
     * 故障类型(1无故障 2有故障)
     */
    @ApiModelProperty(value = "故障类型")
    private List<Integer> hitchTypeList;
    /**
     * 退款方式(1正常退款 2特殊退款 )
     */
    @ApiModelProperty(value = "退款方式")
    private List<Integer> refundTypeList;
    /**
     * 补差价百分比
     */
    @ApiModelProperty(value = "补差价百分比")
    private BigDecimal differentPricePercent;
    /**
     * 是否附件折价
     */
    @ApiModelProperty(value = "是否附件折价")
    private Boolean isPartsDifferent;
    /**
     * 政策内容
     */
    @ApiModelProperty(value = "政策内容")
    private String policyContent;
    /**
     * 租户
     */
    @ApiModelProperty(value = "租户")
    private Integer xtenant;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌ID")
    private List<Integer> brandIdList;
    /**
     * 分类ID
     */
    @ApiModelProperty(value = "分类ID")
    private List<Integer> cidList;

    /**
     * sku以逗号分隔
     */
    @ApiModelProperty(value = "sku以逗号分隔")
    private String sku;

    /**
     * spu以逗号分隔
     */
    @ApiModelProperty(value = "spu以逗号分隔")
    private String spu;
}
