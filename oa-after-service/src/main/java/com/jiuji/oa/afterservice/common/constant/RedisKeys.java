package com.jiuji.oa.afterservice.common.constant;

/**
 * @author: gengjiaping
 * @date: 2020/4/3
 */
public class RedisKeys {
    /**
     * 售后维修优惠码
     */
    public static final String SHOUHOU_NUMBER_CARD_PERFIX = "shouhou_SendNumberCard_";


    public static final String REDIS_SWITCH = "java:hashRedisSwitch";


    /**
     * 区域商品配置
     */
    public static final String PRICE_CONFIG_PPID = "PriceConfigPpriceid";

    /**
     * 阿里优化码
     */
    public static final String ALI_YOUHUIMA = "alipayyouhuippriceids";

    /**
     * 渠道
     */
    public static final String insource = "insource";

    /**
     * 渠道用户缓存key
     */
    public static final String QUDAO_USER = "qudaoUser";

    public static final String SHOUHOU_TUIHUAN_MIAOTUI_CODE_PREFIX = "shtuikCode_";

    /**
     * 售后倒计时产品
     */
    public static final String SHOUHOU_DAOJISHIPRODUCT = "shouhou_DaojishiProduct";

    /**
     * 售后倒计时产品
     */
    public static final String IMEI_QUERY_API = "imeiqueryapi_";

    /**
     * 库位管理 库位商品拿货数量清单，库位:[{商品:数量}] 缓存key
     */
    public static final String STOCK_LOCATION_PPID_PREFIX = "location_ppid_";

    /**
     * 库位管理 商品收货门店数量清单，商品:[{门店:数量}] 缓存key
     */
    public static final String STOCK_PPID_AREA_PREFIX = "ppid_area_";

    /**
     * 库位管理 用户锁定的调拨单 缓存key
     */
    public static final String STOCK_DIAOBOS_PREFIX = "stock_diaobos_";

    /**
     * 锁定的调拨单
     */
    public static final String STOCK_LOCK_DIAOBOID_PREFIX = "stock_lock_diaoboid_";

    /**
     * C# oa999DAL.RedisServices.nahuoRedisServices.nahuoKey
     */
    public static final String NA_HUO_KEY = "sub_Take_Order";
    /**
     * 库位管理 订单配件拿货数量清单，库位:[{商品:数量}] 缓存key
     */
    public static final String NAHUO_STOCK_LOCATION_PPID_PREFIX = "nahuo_location_ppid_";

    /**
     * 库位管理 门店锁定的拿货队列ID 缓存key
     */
    public static final String STOCK_NAHUOIDS_PREFIX = "stock_nahuoids_";
    /**
     * 库位管理 门店锁定的拿货队列 缓存key
     */
    public static final String STOCK_NAHUO_USER_PREFIX = "stock_nahuo_user_";

    public static final String ALIPAY_COUPON_PPID = "alipayyouhuippriceids";


    /**
     * 支付宝配置信息 缓存key
     */
    public static final String ALIPAY_CONFIG_CACHE = "alipayConfigCacheKeys";
    /**
     * 微信支付配置信息 缓存key
     */
    public static final String WEIXINPAY_CONFIG_CACHE = "weixinConfigCacheKeys";
    /**
     * 威富通支付模型 缓存key
     */
    public static final String SWIFT_PASS_PAY_CONFIG_CACHE = "swiftpassConfigCacheKeys";
    /**
     * 首信易配置 缓存key
     */
    public static final String UPAY_CONFIG_CACHE = "5uPayConfigCacheKeys";

    /**
     * 通联支付配置 缓存key
     */
    public static final String TONGLIANPAY_CONFIG_CACHE = "tongLianConfigCacheKeys";

    /**
     * 通联支付配置 缓存key
     */
    public static final String POSTBANKPAY_CONFIG_CACHE = "posBankConfigCacheKeys";

    /**
     * 建行支付配置 缓存key
     */
    public static final String CCBPAY_CONFIG_CACHE = "ccbConfigCacheKeys";

    /**
     * 建行支付配置 缓存key
     */
    public static final String ICBCPAY_CONFIG_CACHE = "icbcConfigCacheKeys";

    /**
     * 建行支付配置 缓存key
     */
    public static final String ZEROPAY_CONFIG_CACHE = "zeroConfigCacheKeys";

    /**
     * 库分期支付配置 缓存key
     */
    public static final String KUBAITIAOPAY_CONFIG_CACHE = "KubaitiaoConfigCacheKeys";

    /**
     * 三方支付配置返回 缓存key
     */
    public static final String THREE_PARTY_NAME = "threePartyNameConfigCacheKeys";

    /**
     * 分期支付配置返回 缓存key
     */
    public static final String INSTALLMENT_NAME = "installmentNameConfigCacheKeys";

    /**
     * pos机支付配置返回 缓存key
     */
    public static final String POS_NAME = "posNameConfigCacheKeys";

    /**
     * 商品分类表的缓存key
     */
    public static final String ALL_CATEGORY_KEY = "afterservice.sub.service.CategoryService.listAll";
    /**
     * 所有可维修机型并显示的商品id
     */
    public static final String REPAIR_MODEL_BY_PRODUCT_ID ="repair_model_by_product_id_key";

    /**
     * 售后退款验证码redis key  shouhouId tuihuanKind
     */
    public static final String SHOUHOU_TUIHUAN_VALID_CODE = "{}_{}_returnMoney_{}";
    /**
     * 退款退订验证码redis key tuihuanKind code
     */
    public static final String TUIHUAN_VALID_CODE = "{}_returnMoney_{}";

    public static final String USER_CLASS_CONFIG_KEY = "user_class_config_key_{}";
    /**售后短信验证码key  业务类型 订单id*/
    public static final String AFTER_SMS_CODE_KEY = "OA_AFTER_SMS_CODE_{}_{}";
    /**阿里支付优惠id**/
    public static final String ALIPAY_YOUHUI_PPRICEID = "alipayyouhuippriceids";
    /**小件无折扣退款 变量 basket_id*/
    public static final String SMALLPRO_NOT_DISCOUNT_BASKET_KEY = "smallpro_not_discount_basket_{}";
    /**串号冲突服务查询处理 变量 imei串号*/
    public static final String IMEI_SERVICE_RECORD_USER_ID_KEY = "imei_service_record_user_id:{}";
    /**模块上线版本控制*/
    public static final String AFTERSERVICE_MODULE_KEY = "afterservice:module:{}";
    /**二级密码验证key*/
    public static final String PASSWORD2_VALID_KEY = "oaRedisCheck2_{}";
    /** 解除出险记录 变量 出险售后id value 服务记录id(可以为0,通过售后id来查询) */
    public static final String UNOUT_SERVICE_RECORD_SHOUHOU_ID_KEY = "unout_service_record_shouhou_id:{}";
    /** 撤销配件跳过财务凭证*/
    public static final String NOTPZ_CANCEL_SHOUHOU_PJ_ID_KEY = "notpz_cancel_shouhou_pj_id_key:{}";

    /** 手动设置为同授权门店 门店1id 门店2id */
    public static final String SAME_AUTH_AREA = "same_auth_area:{}_{}";

    /** 确认已经支付成功 手动设置结果 跳过支付 参数: shouhouId businessId payUserId*/
    public static final String SHOUHOU_WAISONG_HEXIAO_PAY_RESULT = "shouhou_waisong_hexiao_pay_result:{}_{}_{}";

    /** 指定url 打印请求参数 方便用户问题复现*/
    public static final String PRINT_URL_REQ_PARAM = "print_url_req_param:{}";

    /** 指定url 打印请求参数 方便用户问题复现*/
    public static final String ELECTRONIC_CHECK_HAS_CONFIG = "afterservice:ElectronicCheckService:getAppRoute:hasCheck";

    /** 服务器文件缓存*/
    public static final String EXCEL_CACHE_META = "afterservice:excelCacheMeta:{}";

    /** 只退维修配件,不退绑定的售后服务 变量 维修配件的id value 布尔值 */
    public static final String NOT_CANCEL_BIND_SERVICE_WXKC_ID_KEY = "not_cancel_bind_service_wxkc_id:{}";

    /** 换货拆单的时候不进行拆单操作  变量 小件单号 value 布尔值*/
    public static final String NOT_EXCHANGE_GOODS_DISASSEMBLE_KEY = "not_exchange_goods_disassemble_key_%s";

    /**询价单编号*/
    public static final String WEB_SHOUHOU_YUYUE_INQUIRY_FORMAT = "web_shouhou_yuyue_inquiry:{}";

    /** 启用组合退 {单号}{退换kinds}{门店id}*/
    public static final String GROUP_TUIHUAN_ENABLE = "afterservice:groupTuihuan:enable:{}_{}_{}";

    /**{退款单号}_{退款方式} */
    public static final String GROUP_REFUND_NOT_MUST_REFUND = "afterservice:groupRefund:notMustRefund:{}_{}";

    /**
     * 解决主从延迟的问题  shouhouId
     */
    public static final String SHOUHOU_ATTACHMENTS_ADD_CACHE = "shouhouService:attachmentsAddCache:{}";

    /**
     * 售后个人业绩统计 维修毛利
     */
    public static final String SGOUHOU_PERSON_MAOLI_KEY = "shouhou_personstatistics:maoli_key_{}_{}";

    /**
     * 售后个人业绩统计 维修毛利 排名
     */
    public static final String SGOUHOU_PERSON_MAOLI_RANK_KEY = "shouhou_personstatistics:maoli_rank_key_{}_{}_";
    /**
     * 服务不折价退款申请
     */
    public static final String SERVICE_NOT_DISCOUNT_REFUND_KEY = "afterservice:service_not_discount_refund_{}";
    /**
     * 盲盒商品
     */
    public static final String BLIND_BOX_BASKET_ID = "afterService:blindBox:basketId:{}";

    /**无折扣退款 变量 wkcoutput.id*/
    public static final String SHOUHOU_NOT_DISCOUNT_WKCOUTPUT_KEY = "shouhou_not_discount_wkcoutput_{}";

    /**库表最大键值 变量 dbname table id列名 类型列名 类型值*/
    public static final String SQLSERVER_DB_TABLE_MAX_ID_KEY = "sqlserver_db_{}_table_{}_max_{}_{}_{}";

    /**是否在历史库中 变量 dbname table id列名 类型列名 类型值*/
    public static final String CH999OANEW_TABLE_ID_IS_HISTORY_KEY = "ch999oanew_table_{}_{}_{}_{}_{}_is_history";

    /**解除不允许退款的限制 与 smallproNotRefundPpids 相反 变量 basket_id */
    public static final String SMALLPRO_REFUND_ALLOWED_BASKET_ID_KEY = "smallpro_refund_allowed_basket_id_{}";

    /**解除只能原路径退的限制 变量 交易单号 */
    public static final String NETPAY_OTHER_REFUND_ALLOWED_ORDER_ID_KEY = "netpay_other_refund_allowed_order_id_{}";

    /**授权查询已完成订单 缓存*/
    public static final String AUTHORIZE_QUERY_COMPLETED_ORDER_KEY = "authorize_query_completed_order_{ch999Id}_{userId}";

    /** 加盟店可以退余额 */
    public static final String JOIN_AREA_CAN_REFUND_YU_E = "join_area_can_refund_yu_e_{subId}";
}
