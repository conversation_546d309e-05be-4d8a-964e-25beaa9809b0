package com.jiuji.oa.afterservice.refund.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.ProductinfoService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.tuihuan.TuiHuanService;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.RequestCacheKeys;
import com.jiuji.oa.afterservice.common.enums.RankEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.mapstruct.CommonStructMapper;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.refund.bo.DetailParamBo;
import com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo;
import com.jiuji.oa.afterservice.refund.bo.smallpro.SmallproSubBuyInfoBo;
import com.jiuji.oa.afterservice.refund.dao.ShouhouRefundMoneyMapper;
import com.jiuji.oa.afterservice.refund.enums.TuiGroupEnum;
import com.jiuji.oa.afterservice.refund.service.RefundMoneyService;
import com.jiuji.oa.afterservice.refund.service.RefundShouhouService;
import com.jiuji.oa.afterservice.refund.service.RefundSmallproService;
import com.jiuji.oa.afterservice.refund.service.kind.BaseTuiHuanKindService;
import com.jiuji.oa.afterservice.refund.service.kind.SmallproTuiHuanKindService;
import com.jiuji.oa.afterservice.refund.service.way.ThirdOriginWayService;
import com.jiuji.oa.afterservice.refund.utils.RefundMoneyUtil;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.req.smallpro.RefundSmallproFormVo;
import com.jiuji.oa.afterservice.refund.vo.req.smallpro.ZheJiaSmallproFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiHuanCheckVo;
import com.jiuji.oa.afterservice.refund.vo.res.machine.SmallproBasketVo;
import com.jiuji.oa.afterservice.refund.vo.res.smallpro.RefundSmallproDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.smallpro.ZheJiaSmallproVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.OtherRefundVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.oa.afterservice.smallpro.controller.SmallproController;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProKindEnum;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.service.SmallproDetailsExService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproService;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SubCheckStatusEnum;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import jdk.nashorn.internal.objects.NativeNumber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022/11/14 14:19
 */
@Service
@Slf4j
public class RefundSmallproServiceImpl implements RefundSmallproService {
    @Resource
    @Lazy
    private RefundSmallproService refundSmallproService;
    @Resource
    private RefundMoneyService refundMoneyService;
    @Resource
    private SmallproDetailsExService smallproDetailsExService;
    @Resource
    private SmallproService smallproService;
    @Override
    public R<RefundSmallproDetailVo> detail(Integer smallproId) {
        //查询小件单信息
        Smallpro smallpro = refundSmallproService.getSmallproById(smallproId);
        //校验数据
        if(smallpro == null){
            return R.error(StrUtil.format("小件单号{}错误",smallproId));
        }
        SmallProKindEnum smallProKindEnum = EnumUtil.getEnumByCode(SmallProKindEnum.class, smallpro.getKind());
        if(smallProKindEnum == null){
            return R.error("小件接件类型为空无法查看退换详情");
        }
        if(Stream.of(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE, SmallProKindEnum.SMALL_PRO_KIND_RETURN)
                .noneMatch(spEnum -> Objects.equals(spEnum.getCode(), smallpro.getKind()))){
            return R.error(StrUtil.format("小件接件类型[{}]不支持查看退换详情", smallProKindEnum.getMessage()));
        }
        TuihuanKindEnum tuihuanKindEnum;
        if(smallProKindEnum == SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE){
            tuihuanKindEnum = TuihuanKindEnum.SMALL_PRO_HQTXH;
        }else{
            tuihuanKindEnum = TuihuanKindEnum.TPJ;
        }
        //获取最大可退金额
        Optional<RefundSubInfoBo> rsiOpt = BaseTuiHuanKindService.getBean(tuihuanKindEnum)
                .getSuInfoWithMaxRefundPrice(smallproId, tuihuanKindEnum.getCode());
        SmallproSubBuyInfoBo smallproSubBuyInfo = smallproDetailsExService.getSmallproSubBuyInfo(smallproId, tuihuanKindEnum);

        //查询详情的明细信息
        DetailParamBo detailParamBo = new DetailParamBo()
                .setOrderId(smallproId).setSupportSeconds(NumberConstant.ONE).setTuihuanKindEnum(tuihuanKindEnum);
        R<RefundMoneyDetailVo> refundMoneyDetailR = SpringUtil.getBean(RefundMoneyService.class).detail(detailParamBo,
                detail -> rsiOpt);
        if(!refundMoneyDetailR.isSuccess()){
            return R.error(refundMoneyDetailR.getUserMsg());
        }
        RefundMoneyDetailVo refundMoneyDetailVo = refundMoneyDetailR.getData();
        RefundSmallproDetailVo refundDetail = SpringUtil.getBean(CommonStructMapper.class).toRefundSmallproDetail(refundMoneyDetailVo);
        refundDetail
                .setSmallpro(smallpro).setSmallproSubBuyInfo(smallproSubBuyInfo).setTuihuanKindEnum(tuihuanKindEnum)
                .setSmallproAreaInfo(detailParamBo.getOrderAreaInfo()).setTuihuan(detailParamBo.getTuiHuanPo())
                .setInprice(rsiOpt.map(RefundSubInfoBo::getInPrice).orElse(BigDecimal.ZERO)).setRefundMoneyDetailVo(refundMoneyDetailVo)
                .setHuanBasketList(Collections.emptyList()).setBillBasketList(Collections.emptyList());
        //详情页面信息设置
        convertAndSetDetailInfo(refundDetail);
        return R.success(refundDetail).addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    @Override
    @RepeatSubmitCheck(expression = "#{classFullName}:#{smallproFormVo.subId}",message = "存在未完成的退款")
    @Transactional(rollbackFor = Exception.class)
    public R<Integer> save(RefundSmallproFormVo smallproFormVo) {
        try {
            R<Integer> checkR = assertCheckSaveAndSet(smallproFormVo);
            if (!checkR.isSuccess()) {
                return checkR;
            }
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }
        //换其他型号和退款都要保存组合退,记录退款金额
        GroupTuihuanFormVo tuihuanFormVo = smallproFormVo.getTuihuanFormVo();
        boolean isHqtxh = smallproFormVo.getTuihuanKindEnum() == TuihuanKindEnum.SMALL_PRO_HQTXH;
        if(isHqtxh){
            //增加换其他型号的退款记录, 不入库
            tuihuanFormVo.setTuikuanM1(tuihuanFormVo.getRefundPrice());
            tuihuanFormVo.setRefundPrice(tuihuanFormVo.getRefundPrice().add(smallproFormVo.getSubIdM()));
            tuihuanFormVo.getRefundWayDetails().add(JSON.parseObject(JSON.toJSONString(new OtherRefundVo().setGroupCode(TuiGroupEnum.DEDUCTION_NOT_SAVE.getCode())
            .setRefundPrice(smallproFormVo.getSubIdM()).setReturnWayName(TuihuanKindEnum.SMALL_PRO_HQTXH.getMessage()))));
        }
        //组合退信息
        R<Integer> grouSaveR = refundMoneyService.save(tuihuanFormVo, tuihuanForm -> BaseTuiHuanKindService.getBean(smallproFormVo.getTuihuanKindEnum())
                .getSuInfoWithMaxRefundPrice(ObjectUtil.defaultIfNull(smallproFormVo.getShouhouId(), smallproFormVo.getSubId()), smallproFormVo.getTuihuanKind()));
        if(!grouSaveR.isSuccess()){
            throw new CustomizeException(grouSaveR.getUserMsg());
        }
        //只做校验逻辑
        if(RefundShouhouService.CHECK_DATA.equals(grouSaveR.getData())){
            return grouSaveR;
        }
        smallproFormVo.setTuihuanId(grouSaveR.getData());

        TuiHuanService tuiHuanService = SpringUtil.getBean(TuiHuanService.class);
        //更新大件退款才有的字段信息
        LambdaUpdateChainWrapper<ShouhouTuiHuanPo> tuiHuanLambdaUpdate = tuiHuanService.lambdaUpdate().eq(ShouhouTuiHuanPo::getId,grouSaveR.getData());
        tuiHuanLambdaUpdate
                .set(ShouhouTuiHuanPo::getZhejiaM,smallproFormVo.getZhejiaM())
                .set(ShouhouTuiHuanPo::getTradeDate,smallproFormVo.getTradeDate())
                .set(ShouhouTuiHuanPo::getSubIdM,smallproFormVo.getSubIdM())
                .set(ShouhouTuiHuanPo::getBuypriceM,smallproFormVo.getTotalPrice())
                .set(isHqtxh, ShouhouTuiHuanPo::getTuikuanM1,tuihuanFormVo.getTuikuanM1());
        if (isHqtxh){
            //生成销售单
            Integer huanSubId = SpringUtil.getBean(SubService.class).createSubBySmallProId(smallproFormVo.getShouhouId());
            tuiHuanLambdaUpdate.set(ShouhouTuiHuanPo::getBasketId, SpringUtil.getBean(BasketService.class).lambdaQuery()
                    .eq(Basket::getSubId, huanSubId).select(Basket::getBasketId).list().stream().map(Basket::getBasketId)
                    .findFirst().orElseThrow(() -> new CustomizeException("生产销售单换货商品信息不能为空")));
        }
        if(!tuiHuanLambdaUpdate.update()){
            throw new CustomizeException("组合退款,换机信息更新失败");
        }
        return R.success(smallproFormVo.getTuihuanId()).addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> cancelRefund(Integer tuihuanId, String mark) {
        ShouhouTuiHuanPo tuiHuanPo = SpringContextUtil.reqCache(() -> SpringUtil.getBean(ShouhouRefundMoneyMapper.class).getRefund(tuihuanId),
                RequestCacheKeys.SHOUHOU_REFUND_MONEY_MAPPER_GET_REFUND, tuihuanId);
        if (TuihuanKindEnum.SMALL_PRO_HQTXH.getCode().equals(tuiHuanPo.getTuihuanKind()) && Boolean.TRUE.equals(tuiHuanPo.getCheck1())){
            R<Boolean> r = SpringUtil.getBean(SmallproController.class).checkExchange(tuiHuanPo.getSmallproid(), 1);
            if (!r.isSuccess()){
                return r;
            }
        }
        R<Boolean> result = refundMoneyService.cancelRefund(tuihuanId);
        if (!result.isSuccess()){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmitCheck(expression = "#{classFullName}:#{tuiHuanCheckVo.tuihuanId}")
    public R<Integer> submitCheck(TuiHuanCheckVo tuiHuanCheckVo) {
        //审核1 走原来的方法
        ShouhouRefundDetailVo.ProcessStatus status = EnumUtil.getEnumByCode(ShouhouRefundDetailVo.ProcessStatus.class, tuiHuanCheckVo.getProcessStatus());
        Assert.isFalse(status == null,"流程状态错误");
        OaUserBO oaUserBO = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        ShouhouTuiHuanPo tuiHuanPo = SpringContextUtil.reqCache(() -> SpringUtil.getBean(ShouhouRefundMoneyMapper.class).getRefund(tuiHuanCheckVo.getTuihuanId()),
                RequestCacheKeys.SHOUHOU_REFUND_MONEY_MAPPER_GET_REFUND, tuiHuanCheckVo.getTuihuanId());
        //重新生产换货单
        Integer reCreateHuanSubCode = 10001;
        Supplier<R<Integer>> checkNewSubFun = () -> {
            //每次审核都校验生成的订单状态是否正确
            Basket basket = SpringUtil.getBean(BasketService.class).lambdaQuery().eq(Basket::getBasketId, tuiHuanPo.getBasketId()).one();
            String reCreateMsgFormat = "办理失败，原因：销售单{}，结算失败。点击“重新生成销售单办理”系统将重新自动生成销售单并按照新生成的销售单进行换货金额结算。";
            if (basket == null || Boolean.TRUE.equals(basket.getIsdel())){
                return R.error(reCreateHuanSubCode, StrUtil.format(reCreateMsgFormat, "换货商品已删除"));
            }
            Sub sub = SpringUtil.getBean(SubService.class).getByIdSqlServer(Convert.toInt(basket.getSubId()));
            if (!SubCheckStatusEnum.GOING.getCode().equals(sub.getSubCheck())){
                return R.error(reCreateHuanSubCode, StrUtil.format(reCreateMsgFormat, "不允许支付定金"));
            }
            if (sub.getYingfuM().subtract(sub.getYifuM()).compareTo(tuiHuanPo.getSubIdM())<0){
                return R.error("充值金额大于订单应收金额,结算失败");
            }
            return R.success(null);
        };
        TuiHuanService tuiHuanService = SpringUtil.getBean(TuiHuanService.class);
        if(TuihuanKindEnum.SMALL_PRO_HQTXH.getCode().equals(tuiHuanPo.getTuihuanKind())){
            if (status == ShouhouRefundDetailVo.ProcessStatus.CHECK1){
                // 九机：权限校验 换其他 -> 正常换 xth2     特殊换 xth3
                if(XtenantEnum.isJiujiXtenant()){
                    Smallpro smallpro = smallproService.getByIdSqlServer(tuiHuanPo.getShouhouId());
                    if(smallpro == null){
                        throw new CustomizeException(StrUtil.format("小件单[{}]信息获取异常", tuiHuanPo.getShouhouId()));
                    }
                    List<String> ranks = Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId())
                            .map(OaUserBO::getRank).orElseGet(Collections::emptyList);
                    if(Boolean.TRUE.equals(smallpro.getIsSpecialTreatment()) && RankEnum.XJT_SPECIAL_9JI.noHasAuthority(ranks)){
                        return R.error("您没有权限：" + RankEnum.XJT_SPECIAL_9JI.getCode());
                    }else if(!Boolean.TRUE.equals(smallpro.getIsSpecialTreatment()) && RankEnum.XJT_HUAN_NORMAL_9JI.noHasAuthority(ranks)){
                        return R.error("您没有权限：" + RankEnum.XJT_HUAN_NORMAL_9JI.getCode());
                    }
                }
                // 换其他型号没有二审, 只有一审
                R<Boolean> r = SpringUtil.getBean(SmallproController.class).checkExchange(tuiHuanPo.getSmallproid(), 0);
                tuiHuanService.lambdaUpdate().eq(ShouhouTuiHuanPo::getId, tuiHuanPo.getId())
                        .setSql("check2=check1,check2user=check1user,check2dtime=check1dtime")
                        .update();
                return LambdaBuild.create(new R<Integer>()).set(R::setCode, r.getCode()).set(R::setMsg, r.getMsg()).set(R::setUserMsg, r.getUserMsg())
                        .set(R::setExData, r.getExData()).build();
            }else if(status == ShouhouRefundDetailVo.ProcessStatus.CHECK3 && reCreateHuanSubCode.equals(tuiHuanCheckVo.getWorkCode())){
                //重新生成换货单
                R<Integer> checkNewSubR = checkNewSubFun.get();
                //单独事务提交, 避免死锁
                MultipleTransaction.build().execute(DataSourceConstants.DEFAULT,() -> {
                    //校验订单成功,就不需要重新生成
                    if(checkNewSubR.isSuccess()){
                        return;
                    }

                    //生成销售单
                    Integer huanSubId = SpringUtil.getBean(SubService.class)
                            .createSubBySmallProId(tuiHuanPo.getSmallproid());
                    //更新大件退款才有的字段信息
                    LambdaUpdateChainWrapper<ShouhouTuiHuanPo> tuiHuanLambdaUpdate = tuiHuanService.lambdaUpdate().eq(ShouhouTuiHuanPo::getId, tuiHuanPo.getId());
                    tuiHuanLambdaUpdate.set(ShouhouTuiHuanPo::getBasketId, SpringUtil.getBean(BasketService.class).lambdaQuery()
                            .eq(Basket::getSubId, huanSubId).select(Basket::getBasketId).list().stream().map(Basket::getBasketId)
                            .findFirst().orElseThrow(() -> new CustomizeException("生产销售单换货商品信息不能为空")));
                    if (!tuiHuanLambdaUpdate.update()) {
                        throw new CustomizeException("重新生成销售单更新退换表信息失败");
                    }
                }).commit();
            }else if(status == ShouhouRefundDetailVo.ProcessStatus.CHECK3){
                R<Integer> checkNewSubR = checkNewSubFun.get();
                if(!checkNewSubR.isSuccess()){
                    return checkNewSubR;
                }
            }
        }

        R<Integer> result = refundMoneyService.submitCheck(tuiHuanCheckVo);
        //自动调用取件接口
        if(result.isSuccess()
                && TuihuanKindEnum.SMALL_PRO_HQTXH.getCode().equals(tuiHuanPo.getTuihuanKind())
                && status == ShouhouRefundDetailVo.ProcessStatus.CHECK3){
            try {
                R<Boolean> pickupR = SpringUtil.getBean(SmallproController.class).pickup(tuiHuanPo.getSmallproid(), 2, NumberConstant.ONE);
                if(!pickupR.isSuccess()){
                    //即使取件失败也不影响当前办理流程
                    result.setCode(pickupR.getCode());
                    result.setUserMsg(pickupR.getUserMsg());
                    result.addAllBusinessLog(pickupR.businessLogs());
                    throw new CustomizeException(pickupR);
                }
            } catch (Exception e) {
                RRExceptionHandler.logError("小件换其他型号自动取件", tuiHuanCheckVo, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            }
        }
        return result;
    }

    @Override
    public Smallpro getSmallproById(Integer smallproId) {
        return SpringUtil.getBean(SmallproService.class).getByIdSqlServer(smallproId);
    }

    @Override
    public R<ZheJiaSmallproVo> zheJia(ZheJiaSmallproFormVo zheJiaFormVo) {
        R<ZheJiaSmallproVo> cR = checkAndSetZheJia(zheJiaFormVo);
        if (!cR.isSuccess()){
            return cR;
        }
        ZheJiaSmallproVo zheJiaSmallVo = new ZheJiaSmallproVo().setSubIdM(BigDecimal.ZERO)
                .setMaxRefundPrice(zheJiaFormVo.getTotalPrice().subtract(zheJiaFormVo.getZhejiaM()));
        if (TuihuanKindEnum.SMALL_PRO_HQTXH == zheJiaFormVo.getTuihuanKindEnum()){
            BigDecimal subIdM = getSubIdM(zheJiaFormVo, zheJiaSmallVo.getMaxRefundPrice());
            zheJiaSmallVo.setSubIdM(subIdM);
            BigDecimal maxRefundPrice = NumberUtil.max(zheJiaSmallVo.getMaxRefundPrice().subtract(zheJiaSmallVo.getSubIdM()), BigDecimal.ZERO);
            //折价后的金额 给组合退
            SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.SMALLPRO_MAX_REFUND_PRICE,maxRefundPrice));
            R<RefundMoneyDetailVo> groupR = SpringUtil.getBean(RefundMoneyService.class).detail(new DetailParamBo()
                            .setOrderId(zheJiaFormVo.getShouhouId()).setSupportSeconds(NumberConstant.ONE)
                            .setTuihuanId(null).setTuihuanKindEnum(zheJiaFormVo.getTuihuanKindEnum()),
                    detail -> BaseTuiHuanKindService.getBean(zheJiaFormVo.getTuihuanKindEnum())
                            .getSuInfoWithMaxRefundPrice(zheJiaFormVo.getShouhouId(), zheJiaFormVo.getTuihuanKind()));
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, StrUtil.format("组合退获取结果: {}", groupR.getUserMsg()));

            RefundMoneyDetailVo refundMoneyDetailVo = groupR.getData();
            zheJiaSmallVo.setRefundMoneyDetailVo(refundMoneyDetailVo);

            zheJiaSmallVo.setMaxRefundPrice(maxRefundPrice);
        }
        zheJiaSmallVo.setIsShowGroupDetail(isGroupRefund(zheJiaFormVo.getTuihuanKindEnum(), zheJiaSmallVo.getMaxRefundPrice()));
        return R.success(zheJiaSmallVo);
    }

    /**
     * 获取折退金额
     * @param zheJiaFormVo
     * @return
     */
    private BigDecimal getSubIdM(ZheJiaSmallproFormVo zheJiaFormVo, BigDecimal refundPrice) {
        //需要冲抵商品总金额
        BigDecimal huanMoney = zheJiaFormVo.getHuanBasketList().stream().map(spb -> spb.getPrice().multiply(BigDecimal.valueOf(spb.getBasketCount())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //计算可冲抵实际金额
        TuihuanKindEnum tuihuanKindEnum = zheJiaFormVo.getTuihuanKindEnum();
        Optional<Boolean> isJiuJiOpt = Optional.ofNullable(XtenantEnum.isJiujiXtenant()).filter(Boolean::booleanValue);
        BigDecimal canPayPrice = isJiuJiOpt
                .map(isJiuji -> {
                    Optional<RefundSubInfoBo> refundSubInfoOpt = SpringUtil.getBean(SmallproTuiHuanKindService.class)
                            .getSuInfoWithMaxRefundPrice(zheJiaFormVo.getShouhouId(), tuihuanKindEnum.getCode());
                    return refundSubInfoOpt.map(refundSubInfo -> getCanChangePrice(zheJiaFormVo.getZhejiaM(), refundPrice,
                            refundSubInfo, tuihuanKindEnum))
                            .orElse(BigDecimal.ZERO);
                })
                .orElse(refundPrice);
        BigDecimal subIdM;
        if (canPayPrice.compareTo(huanMoney)>=0) {
            subIdM = huanMoney;
        }else{
            subIdM = canPayPrice;
        }
        return subIdM;
    }

    private static BigDecimal getCanChangePrice(BigDecimal zhejiaM, BigDecimal refundPrice, RefundSubInfoBo refundSubInfo, TuihuanKindEnum tuihuanKindEnum) {
        BigDecimal customerMaxRefundPrice = RefundMoneyUtil.getCustomerMaxRefundPrice(refundSubInfo, tuihuanKindEnum, refundPrice, zhejiaM);
        //当前商品的绑定的只支持原路径的收银
        List<ThirdOriginRefundVo> thirdOriginRefundVos = SpringUtil.getBean(ThirdOriginWayService.class)
                .listAll(refundSubInfo.getOrderId(), tuihuanKindEnum);
        // （1）三方收银方式为：代金券 （2）代金券活动类型为：非商场活动（3）代金券退款方式配置了：原路径退款 排除非当前商品的收银
        BigDecimal onlyOriginRefundPrice = RefundMoneyUtil.filterChangeThirdOrigin(thirdOriginRefundVos, tuihuanKindEnum, refundSubInfo.getBasketIds())
                .map(tor -> tor.getRefundPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return NumberUtil.min(NumberUtil.add(customerMaxRefundPrice, onlyOriginRefundPrice), refundPrice);
    }

    /**
     * 是否为组合退款
     * @param tuihuanKindEnum
     * @param refundPriceParam
     * @return
     */
    private boolean isGroupRefund(TuihuanKindEnum tuihuanKindEnum,BigDecimal refundPriceParam){
        BigDecimal refundPrice = ObjectUtil.defaultIfNull(refundPriceParam, BigDecimal.ZERO);
        return TuihuanKindEnum.TPJ == tuihuanKindEnum || TuihuanKindEnum.SMALL_PRO_HQTXH == tuihuanKindEnum
                && refundPrice.compareTo(BigDecimal.ZERO)>0;
    }


    private Optional<AreaInfo> getCurrAreaInfo(Integer currAreaId) {
        return Optional.ofNullable(SpringUtil.getBean(AreaInfoClient.class).getAreaInfoById(currAreaId))
                .filter(R::isSuccess).map(R::getData);
    }

    private void convertAndSetDetailInfo(RefundSmallproDetailVo refundDetail) {
        //tuihuan属性设置到页面
        Smallpro smallpro = refundDetail.getSmallpro();
        ShouhouTuiHuanPo tuihuan = refundDetail.getTuihuan();
        //默认为售后单的用户id, 查询订单信息后, 赋值为订单的用户id
        refundDetail.setUserId(Convert.toInt(smallpro.getUserId()));
        Integer subUserId = refundDetail.getSmallproSubBuyInfo().getRefundSubInfoBo().getUserId();
        if (subUserId != null){
            refundDetail.setUserId(subUserId);
        }
        TuihuanKindEnum tuihuanKindEnum = refundDetail.getTuihuanKindEnum();
        refundDetail.setTuihuanKind(tuihuanKindEnum.getCode());
        ProductKcService productKcService = SpringUtil.getBean(ProductKcService.class);
        ProductinfoService productinfoService = SpringUtil.getBean(ProductinfoService.class);
        AtomicInteger basketCountRef = new AtomicInteger(0);
        Set<Integer> kcPpids = refundDetail.getSmallproSubBuyInfo().getSmallproBillList().stream()
                .map(spb -> Convert.toInt(spb.getPpriceid())).collect(Collectors.toSet());
        Optional.ofNullable(smallpro.getChangePpriceid()).ifPresent(kcPpids::add);
        //批量获取区域价格
        Map<Integer, BigDecimal> pricePpidMap = SpringUtil.getBean(SubService.class)
                .getRegionalPrice(kcPpids, refundDetail.getSmallproAreaInfo().getCityId(), refundDetail.getSmallproAreaInfo().getXtenant());

        refundDetail.setBillBasketList(refundDetail.getSmallproSubBuyInfo().getSmallproBillList().stream()
                .map(spb -> {
                    Integer ppid = Convert.toInt(spb.getPpriceid());
                    Productinfo pInfo = productinfoService.getProductinfoByPpid(ppid);
                    basketCountRef.getAndAdd(ObjectUtil.defaultIfNull(spb.getCount(), 0));
                    return new SmallproBasketVo().setBarCode(pInfo.getBarCode()).setBasketCount(spb.getCount())
                            .setBasketId(spb.getBasketId()).setPpriceId(pInfo.getPpriceid()).setPrice(pInfo.getMemberprice())
                            .setProductColor(pInfo.getProductColor()).setProductName(pInfo.getProductName());
                }).collect(Collectors.toList()));
        RefundMoneyDetailVo refundMoneyDetailVo = refundDetail.getRefundMoneyDetailVo();
        switch (tuihuanKindEnum){
            case SMALL_PRO_HQTXH:
                refundDetail.setTuihuanKindMsg("换其他型号");
                //设置换货商品信息
                refundDetail.setHuanBasketList(Optional.ofNullable(smallpro.getChangePpriceid())
                        .map(changePpid -> {
                            Productinfo pInfo = productinfoService.getProductinfoByPpid(changePpid);
                            return new SmallproBasketVo().setBarCode(pInfo.getBarCode()).setBasketCount(basketCountRef.get())
                                    .setBasketId(Optional.ofNullable(tuihuan).map(ShouhouTuiHuanPo::getBasketId).orElse(null))
                                    .setPpriceId(pInfo.getPpriceid()).setPrice(ObjectUtil.defaultIfNull(pricePpidMap.get(pInfo.getPpriceid()), pInfo.getMemberprice()))
                                    .setProductColor(pInfo.getProductColor()).setProductName(pInfo.getProductName());
                        }).map(Collections::singletonList).orElse(Collections.emptyList()));
                // 根据小件换货商品计算冲抵金额
                BigDecimal maxRefundPrice = refundMoneyDetailVo.getMaxRefundPrice();
                BigDecimal subIdM = getSubIdM(new ZheJiaSmallproFormVo().setZhejiaM(refundDetail.getZhejiaM())
                        .setHuanBasketList(refundDetail.getHuanBasketList()).setTuihuanKindEnum(tuihuanKindEnum)
                        .setTotalPrice(refundDetail.getTotalPrice()).setShouhouId(smallpro.getId()), maxRefundPrice);
                refundDetail.setSubIdM(subIdM);
                refundDetail.setMaxRefundPrice(maxRefundPrice.subtract(refundDetail.getSubIdM()));
                refundMoneyDetailVo.setMaxRefundPrice(refundDetail.getMaxRefundPrice());
                if(tuihuan == null && refundDetail.getMaxRefundPrice().compareTo(BigDecimal.ZERO)<=0){
                    //清空默认展示的原路径和退款金额
                    refundMoneyDetailVo.setRefundPrice(BigDecimal.ZERO);
                    refundMoneyDetailVo.setRefundWayDetails(Collections.emptyList());
                }
                break;
            case TPJ:
                refundDetail.setTuihuanKindMsg("退款");
                break;
        }
        //获取并设置业务公共信息
        //批量获取库存
        Map<Integer, Integer> kcPpidMap = productKcService.listKcCount(kcPpids, refundDetail.getSmallproAreaInfo().getId());
        Stream.concat(refundDetail.getHuanBasketList().stream(), refundDetail.getBillBasketList().stream())
                .forEach(b -> b.setKcCount(ObjectUtil.defaultIfNull(kcPpidMap.get(b.getPpriceId()), 0)));
        if(tuihuan != null) {
            setTuihuanDetail(refundDetail, smallpro);
        }else{
            refundDetail.setTradeDate(smallpro.getBuyDate());
            int overDays = Convert.toInt(ChronoUnit.DAYS.between(smallpro.getBuyDate(),smallpro.getInDate()));
            refundDetail.setTradeDay(overDays);
        }
        refundDetail.setIsShowGroupDetail(isGroupRefund(tuihuanKindEnum, refundDetail.getMaxRefundPrice()));
    }

    private void setTuihuanDetail(RefundSmallproDetailVo refundDetail, Smallpro smallpro) {
        ShouhouTuiHuanPo shouhouTuihuan = refundDetail.getTuihuan();
        refundDetail.setTuihuanId(shouhouTuihuan.getId());
        refundDetail.setTuihuanKind(shouhouTuihuan.getTuihuanKind());
        refundDetail.setZhejiaM(shouhouTuihuan.getZhejiaM());

        refundDetail.setTradeDate(shouhouTuihuan.getTradeDate());
        refundDetail.setInprice(shouhouTuihuan.getInprice());
        //按退款类型显示实际退款金额
        if(ObjectUtil.equal(shouhouTuihuan.getTuihuanKind(),TuihuanKindEnum.SMALL_PRO_HQTXH.getCode())){
            refundDetail.setMaxRefundPrice(shouhouTuihuan.getTuikuanM1());
            refundDetail.setSubIdM(shouhouTuihuan.getSubIdM());
            //通过basketId 查询订单信息
            Basket basket = SpringUtil.getBean(BasketService.class).lambdaQuery().eq(Basket::getBasketId, shouhouTuihuan.getBasketId())
                    .one();
            //重新赋值价格
            refundDetail.getHuanBasketList().stream().filter(hb -> ObjectUtil.equals(hb.getPpriceId(), Convert.toInt(basket.getPpriceid())))
                    .forEach(hb -> hb.setBasketCount(basket.getBasketCount()).setBasketId(basket.getBasketId()).setPrice(basket.getPrice()));
            refundDetail.setHuanSubId(Convert.toInt(basket.getSubId()));
            RefundMoneyDetailVo refundMoneyDetailVo = refundDetail.getRefundMoneyDetailVo();
            //一审命名为换货审核
            if(ShouhouRefundDetailVo.ProcessStatus.CHECK1.getCode().equals(refundMoneyDetailVo.getProcessStatus())) {
                refundMoneyDetailVo.setProcessName("换货审核");
            }
            //三审命名为换货办理
            if(ShouhouRefundDetailVo.ProcessStatus.CHECK3.getCode().equals(refundMoneyDetailVo.getProcessStatus())) {
                refundMoneyDetailVo.setProcessName("换货办理");
            }
            //二审不进行展示
            refundMoneyDetailVo.getCheckHistorys().removeIf(ch -> ShouhouRefundDetailVo.ProcessStatus.CHECK2.getCode().equals(ch.getNodeCode()));

            refundMoneyDetailVo.getCheckHistorys()
                    .forEach(ch -> {
                        if(ShouhouRefundDetailVo.ProcessStatus.CHECK1.getCode().equals(ch.getNodeCode())){
                            ch.setNodeName("换货审核");
                        }else if(ShouhouRefundDetailVo.ProcessStatus.CHECK3.getCode().equals(ch.getNodeCode())){
                            ch.setNodeName("换货办理");
                        }
                    });
        }else{
            refundDetail.setMaxRefundPrice(shouhouTuihuan.getTuikuanM());
        }

        if (smallpro.getInDate() != null && shouhouTuihuan.getTradeDate() != null) {
            refundDetail.setTradeDay(Convert.toInt(Duration.between(shouhouTuihuan.getTradeDate().truncatedTo(ChronoUnit.DAYS), smallpro.getInDate().truncatedTo(ChronoUnit.DAYS)).toDays()));
        }
    }

    private R<Integer> assertCheckSaveAndSet(RefundSmallproFormVo smallproFormVo) {
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, smallproFormVo.getTuihuanKind());
        //设置退款类型
        smallproFormVo.setTuihuanKindEnum(tuihuanKindEnum);
        smallproFormVo.setSubIdM(NumberUtil.null2Zero(smallproFormVo.getSubIdM()));
        Assert.isTrue(Objects.nonNull(tuihuanKindEnum),"不支持{}退款类型", smallproFormVo.getTuihuanKind());
        GroupTuihuanFormVo tuihuanFormVo = smallproFormVo.getTuihuanFormVo();
        Assert.isTrue(Objects.nonNull(tuihuanFormVo),"组合退信息不能为空");
        BigDecimal refundPrice = tuihuanFormVo.getRefundPrice();
        //设置tuikuanM1的值
        if (TuihuanKindEnum.SMALL_PRO_HQTXH == smallproFormVo.getTuihuanKindEnum()){
            tuihuanFormVo.setTuikuanM1(refundPrice);
        }
        Smallpro smallpro = refundSmallproService.getSmallproById(smallproFormVo.getShouhouId());
        Assert.notNull(smallpro,"小件单号[{}]错误",smallproFormVo.getShouhouId());
        smallproFormVo.setSmallpro(smallpro);
        AreaInfo currAreaInfo = getCurrAreaInfo(CommenUtil.currAreaId(smallpro.getToAreaId(), smallpro.getAreaId()))
                .orElseThrow(() -> new CustomizeException("小件单门店信息获取异常"));
        smallproFormVo.setSmallproAreaInfo(currAreaInfo);
        smallproFormVo.setCurrUser(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId());
        //校验退款地区
        if (ObjectUtil.notEqual(smallproFormVo.getCurrUser().getAreaId(),smallproFormVo.getSmallproAreaInfo().getId())){
            return R.error(StrUtil.format("地区不符，请切换至{}再操作！",currAreaInfo.getArea()));
        }
        // 获取并校验订单信息
        SmallproSubBuyInfoBo smallproSubBuyInfo = smallproDetailsExService.getSmallproSubBuyInfo(smallproFormVo.getShouhouId(), tuihuanKindEnum);
        Assert.isFalse(!smallproSubBuyInfo.getSubOpt().isPresent(),"订单信息不存在");
        BigDecimal totalPrice = smallproSubBuyInfo.getRefundSubInfoBo().getTotalPrice();
        Assert.isTrue(refundPrice.compareTo(totalPrice)<=0,
                "退款金额不能大于{}", totalPrice.setScale(2, RoundingMode.HALF_DOWN));
        Optional<Boolean> isJiuJiOpt = Optional.ofNullable(XtenantEnum.isJiujiXtenant()).filter(Boolean::booleanValue);
        //调用方法重新计算冲抵金额是否匹配
        BigDecimal allRefundPrice = refundPrice.add(smallproFormVo.getSubIdM());
        BigDecimal hqtxhCanPayPrice = isJiuJiOpt
                .map(isJiuji -> {
                    Optional<RefundSubInfoBo> refundSubInfoOpt = SpringUtil.getBean(SmallproTuiHuanKindService.class)
                            .getSuInfoWithMaxRefundPrice(smallproFormVo.getShouhouId(), tuihuanKindEnum.getCode());
                    return refundSubInfoOpt
                            .map(refundSubInfo -> getCanChangePrice(smallproFormVo.getZhejiaM(), allRefundPrice, refundSubInfo, tuihuanKindEnum))
                            .orElse(BigDecimal.ZERO);
                })
                .orElse(allRefundPrice);
        if(smallproFormVo.getSubIdM().compareTo(hqtxhCanPayPrice)>0){
            return R.error(StrUtil.format("金额错误！冲抵金额最大不能超过:{}", hqtxhCanPayPrice));
        }
        if (smallproFormVo.getTotalPrice().compareTo(smallproFormVo.getSubIdM().add(tuihuanFormVo.getTuikuanM1()).add(smallproFormVo.getZhejiaM())) != 0) {
            return R.error(StrUtil.format("金额错误！退款金额: {},冲抵金额:{},门店[{}]",tuihuanFormVo.getTuikuanM1(),smallproFormVo.getSubIdM(),smallproFormVo.getCurrUser().getArea()));
        }
        Assert.isFalse(refundPrice.compareTo(BigDecimal.ZERO)<0,"退款金额必须大于等于0");

        ShouhouTuiHuanPo shouhouTuiHuanPo = refundMoneyService.getRefund(null, smallproFormVo.getShouhouId(), tuihuanKindEnum);
        Assert.isNull(shouhouTuiHuanPo,"已经存在退款记录!");
        OaUserBO oaUserBO = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        smallproFormVo.setCurrUser(oaUserBO);
        return R.success(null);
    }

    private R<ZheJiaSmallproVo> checkAndSetZheJia(ZheJiaSmallproFormVo zheJiaFormVo) {
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, zheJiaFormVo.getTuihuanKind());
        try {
            Assert.notNull(tuihuanKindEnum, "退款类型不能为空");
            Assert.notNull(zheJiaFormVo.getXtenant(), "租户标识码不能为空");
            Assert.isFalse(CollUtil.isEmpty(zheJiaFormVo.getHuanBasketList()), "换货商品信息不能为空");
            Assert.isFalse(zheJiaFormVo.getHuanBasketList().stream().anyMatch(hb -> hb.getPrice() == null), "换货商品金额不能为空");
            zheJiaFormVo.setTuihuanKindEnum(tuihuanKindEnum);
            //初始化默认值
            zheJiaFormVo.setTotalPrice(ObjectUtil.defaultIfNull(zheJiaFormVo.getTotalPrice(),BigDecimal.ZERO));
        } catch (IllegalArgumentException e) {
            return R.error(e.getMessage());
        }
        return R.success(null);
    }
}
