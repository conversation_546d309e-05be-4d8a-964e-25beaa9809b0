package com.jiuji.oa.afterservice.smallpro.po.refund;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2022/5/7 10:50
 */
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class ProductRefundPo {

    /**
     * 商品id
     */
    @TableField("productId")
    private Integer productId;
    /**
     * 商品规格id
     */
    @TableField("ppriceid")
    private Integer ppriceid;
    /**
     * 品牌id
     */
    @TableField("brandID")
    private Integer brandID;
    /**
     * 分类id
     */
    @TableField("cid")
    private Integer cid;
    /**
     * 商品名称
     */
    @TableField("productName")
    private String productName;
    /**
     * 商品规格
     */
    @TableField("productColor")
    private String productColor;
    /**
     * 69码
     */
    @TableField("barCode")
    private String barCode;
    /**
     * 会员价格
     */
    @TableField("memberprice")
    private BigDecimal memberprice;

    /**
     * 天数
     */
    private Integer days;
}
