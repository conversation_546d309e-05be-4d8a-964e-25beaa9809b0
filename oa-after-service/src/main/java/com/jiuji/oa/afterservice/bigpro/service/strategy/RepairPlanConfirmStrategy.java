package com.jiuji.oa.afterservice.bigpro.service.strategy;

import com.jiuji.oa.afterservice.bigpro.po.RepairPlan;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlanMaster;
import com.jiuji.oa.afterservice.bigpro.vo.req.CorrelationInfo;
import com.jiuji.oa.afterservice.bigpro.vo.req.CostPriceNewReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.RepairPlanConfirmReq;

import java.util.List;

/**
 * 维修方案确认策略接口
 */
public interface RepairPlanConfirmStrategy {
    
    /**
     * 执行维修方案确认
     * @param master 维修方案主表
     * @param req 确认请求
     * @return 维修方案主表ID
     */
    Integer confirm(RepairPlanMaster master, RepairPlanConfirmReq req);


    /**
     * 添加维修方案费用
     * @param repairPlans
     * @param shouHouId
     * @return
     */
    List<CorrelationInfo> addWxFeeBos(List<RepairPlan> repairPlans, Integer shouHouId);
}
