package com.jiuji.oa.afterservice.config.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.service.ProductinfoService;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.vo.res.DisplayImportCheckVoRes;
import com.jiuji.oa.afterservice.config.dao.ShouhouPpidConfigMapper;
import com.jiuji.oa.afterservice.config.po.ShouhouPpidBind;
import com.jiuji.oa.afterservice.config.po.ShouhouPpidConfig;
import com.jiuji.oa.afterservice.config.service.ShouhouPpidBindService;
import com.jiuji.oa.afterservice.config.service.ShouhouPpidConfigService;
import com.jiuji.oa.afterservice.config.vo.req.BindPpidExcelVo;
import com.jiuji.oa.afterservice.config.vo.req.ShouhouPpidConfigQueryReq;
import com.jiuji.oa.afterservice.config.vo.req.ShouhouPpidOutPutAddReq;
import com.jiuji.oa.afterservice.config.vo.res.ShouhouPpidBindVo;
import com.jiuji.oa.afterservice.config.vo.res.ShouhouPpidOutPutConfig;
import com.jiuji.oa.afterservice.config.vo.res.XtenantInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@Service
public class ShouhouPpidConfigServiceImpl extends ServiceImpl<ShouhouPpidConfigMapper, ShouhouPpidConfig> implements ShouhouPpidConfigService {

    @Autowired
    private ShouhouPpidBindService shouhouPpidBindService;

    @Autowired
    @Lazy
    private ShouhouPpidConfigService shouhouPpidConfigService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;

    @Override
    public R<Page<ShouhouPpidOutPutConfig>> getShouhouPpidConfigPage(ShouhouPpidConfigQueryReq req) {
        Integer way = 0;
        if (req.getSearchKey() != null && CommenUtil.isNumer(req.getSearchKey())) {
            way = 1;
        }
        if (CommenUtil.isNullOrZero(req.getCurrent()) || CommenUtil.isNullOrZero(req.getSize())) {
            req.setCurrent(1);
            req.setSize(15);
        }
        Page<ShouhouPpidOutPutConfig> page = new Page<>(req.getCurrent(), req.getSize());
        page.setDesc("create_time");
        page = baseMapper.getShouhouPpidConfigList(page, req, way);
        List<ShouhouPpidOutPutConfig> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return R.success(page);
        }
        List<Integer> configIdList = records.stream().map(ShouhouPpidOutPutConfig::getId).collect(Collectors.toList());
        List<ShouhouPpidBind> bindList = shouhouPpidBindService.list(new LambdaQueryWrapper<ShouhouPpidBind>().in(ShouhouPpidBind::getConfigId, configIdList));
        List<XtenantInfo> xtenantInfo = this.getXtenantInfo();
        records = records.stream().map(item -> {
            List<ShouhouPpidBind> bindItem = bindList.stream().filter(bind -> bind.getConfigId().equals(item.getId())).collect(Collectors.toList());
            item.setBindList(bindItem);
            item.setXtenantName(Optional.of(xtenantInfo.stream()
                            .filter(x -> Objects.equals(item.getXtenant(), x.getXtenant()))
                            .findFirst()
                            .orElseGet(XtenantInfo::new))
                    .get().getPrintName());
            return item;
        }).collect(Collectors.toList());
        page.setRecords(records);
        return R.success(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> saveOrUpdateBindInfo(ShouhouPpidOutPutAddReq req) {

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        ShouhouPpidConfig config = new ShouhouPpidConfig();
        BeanUtils.copyProperties(req, config);
        //校验配件是否重复配置
        Integer count = baseMapper.selectCount(new LambdaQueryWrapper<ShouhouPpidConfig>()
                .eq(ShouhouPpidConfig::getPpid, config.getPpid())
                .eq(ShouhouPpidConfig::getStatus,1)
                .ne(config.getId() != null, ShouhouPpidConfig::getId, config.getId()));
        if(count>0){
            return R.error(String.format("ppid[%s]已配置,请勿重复配置!", config.getPpid()));
        }
        if(CommenUtil.isNullOrZero(config.getId())){
            req.setId(null);
        }else{
            List<Integer> updateIds = Optional.ofNullable(req.getBindList()).map(b -> b.stream().map(ShouhouPpidBind::getId)
                    .filter(ObjectUtils::isNotNull).collect(Collectors.toList()))
                    .orElseGet(Collections::emptyList);
            //移除已经删除的配件
            shouhouPpidBindService.remove(new LambdaQueryWrapper<ShouhouPpidBind>().eq(ShouhouPpidBind::getConfigId
                    , config.getId()).notIn(!updateIds.isEmpty(),ShouhouPpidBind::getId,updateIds));
        }
        configDefaultValue(req, oaUserBO, config);

        super.saveOrUpdate(config);
        Integer configId = config.getId();

        List<ShouhouPpidBind> bindList = req.getBindList();
        if (CollectionUtils.isNotEmpty(bindList)) {
            bindList = bindList.stream().map(bind -> {
                bindDefaultValue(oaUserBO, bind);
                bind.setConfigId(configId);
                return bind;
            }).collect(Collectors.toList());

            bindList.stream().forEach(e -> shouhouPpidBindService.saveOrUpdate(e));
        }
        return R.success("保存或更新成功");
    }

    private static void bindDefaultValue(OaUserBO oaUserBO, ShouhouPpidBind bind) {
        if (CommenUtil.isNullOrZero(bind.getId())) {
            bind.setCreateTime(LocalDateTime.now());
            bind.setCreateUser(oaUserBO.getUserName());
            if (bind.getNegative() == null) {
                bind.setNegative(Boolean.FALSE);
            }
            if (bind.getAutoOutPut() == null) {
                bind.setAutoOutPut(Boolean.FALSE);
            }
        }else{
            bind.setUpdateTime(LocalDateTime.now());
        }
    }

    private static void configDefaultValue(ShouhouPpidOutPutAddReq req, OaUserBO oaUserBO, ShouhouPpidConfig config) {
        if (CommenUtil.isNullOrZero(req.getId())) {
            config.setCreateTime(LocalDateTime.now());
            config.setCreateUser(oaUserBO.getUserName());
            if (config.getNegative() == null) {
                config.setNegative(Boolean.FALSE);
            }
        } else {
            config.setUpdateTime(LocalDateTime.now());
        }
    }

    @Override
    public R<List<ShouhouPpidBindVo>> getBindPpidInfo(Integer ppid, Integer xtenant, Integer areaId) {
        List<ShouhouPpidBindVo> bindList = shouhouPpidBindService.listBindPpidInfo(ppid, false,xtenant,areaId);

        return R.success(bindList);
    }

    @Override
    public List<XtenantInfo> getXtenantInfo() {
        return baseMapper.getXtenantInfo();
    }

    @Override
    public R<DisplayImportCheckVoRes<Dict>> importExcelBindPpid(InputStream inputStream) {
        List<BindPpidExcelVo> cloudOrderExcels = EasyExcel.read(inputStream).head(BindPpidExcelVo.class)
                .sheet().doReadSync();
        DisplayImportCheckVoRes<Dict> result = new DisplayImportCheckVoRes().setCount(0).setFailCheck(new LinkedList<>())
                .setFailCount(0).setSuccessCount(0).setSuccessCheck(new LinkedList<>());
        Function<String, List<Integer>> getBindPpidsFun = bindPpids -> StrUtil.splitTrim(bindPpids, "、").stream()
                .map(Convert::toInt).filter(Objects::nonNull).collect(Collectors.toList());
        //根据ppid批量获取配件信息
        List<Integer> pjPpidList = new LinkedList<>();
        List<Integer> allPpidList = cloudOrderExcels.stream().peek(coe -> pjPpidList.add(coe.getPpid()))
                .flatMap(coe -> Stream.concat(getBindPpidsFun.apply(coe.getBindPpids()).stream(), Stream.of(coe.getPpid())))
                .distinct().collect(Collectors.toList());
        //批量获取配件商品信息
        Map<Integer, Productinfo> productInfoMap = SpringUtil.getBean(ProductinfoService.class).getProductMapByPpids(allPpidList);
        List<ShouhouPpidConfig> shouhouPpidConfigs = CommonUtils.bigDataInQuery(pjPpidList, ids -> lambdaQuery()
                .in(ShouhouPpidConfig::getPpid, ids).eq(ShouhouPpidConfig::getStatus, 1).list());
        //批量获取绑定配件信息
        List<Integer> configIds = shouhouPpidConfigs.stream().map(ShouhouPpidConfig::getId).distinct().collect(Collectors.toList());
        Map<Integer, List<ShouhouPpidBind>> configIdBindMap = CommonUtils.bigDataInQuery(configIds,
                        ids -> shouhouPpidBindService.lambdaQuery().in(ShouhouPpidBind::getConfigId, ids).list())
                .stream().collect(Collectors.groupingBy(ShouhouPpidBind::getConfigId));
        Map<Integer, ShouhouPpidConfig> ppidConfigMap = shouhouPpidConfigs.stream()
                .collect(Collectors.toMap(ShouhouPpidConfig::getPpid, Function.identity(), (k1, k2) -> k1));
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        Consumer<BindPpidExcelVo> updateFun = coe -> {
            Optional<ShouhouPpidConfig> shouhouPpidConfigOpt = Optional.ofNullable(ppidConfigMap.get(coe.getPpid()));
            Productinfo productinfo = productInfoMap.get(coe.getPpid());
            List<Integer> bindPpids = getBindPpidsFun.apply(coe.getBindPpids());
            List<ShouhouPpidBind> shouhouPpidBinds = shouhouPpidConfigOpt.map(ShouhouPpidConfig::getId).map(configIdBindMap::get)
                    .orElse(new LinkedList<>());
            if(bindPpids.isEmpty()){
                result.setFailCount(result.getFailCount() + 1);
                result.getFailCheck().add(new Dict().set("ppid", coe.getPpid()).set("msg", "绑定配件为空"));
                return;
            }
            if (productinfo == null) {
                result.setFailCount(result.getFailCount() + 1);
                result.getFailCheck().add(new Dict().set("ppid", coe.getPpid()).set("msg", "配件不存在"));
                return;
            }
            for (Integer bindPpid : bindPpids) {
                Productinfo bindProductInfo = productInfoMap.get(bindPpid);
                if(bindProductInfo == null){
                    result.setFailCount(result.getFailCount() + 1);
                    result.getFailCheck().add(new Dict().set("ppid", coe.getPpid()).set("msg", StrUtil.format("绑定配件[{}]不存在", bindPpid)));
                    return;
                }

                Optional<ShouhouPpidBind> bindDbInfoOpt = shouhouPpidBinds.stream().filter(e -> Objects.equals(e.getPpid(), bindPpid)).findFirst();
                bindDbInfoOpt.filter(bindDbInfo -> StrUtil.isBlank(bindDbInfo.getName()))
                        .ifPresent(bindDbInfo -> shouhouPpidBindService.lambdaUpdate().eq(ShouhouPpidBind::getId, bindDbInfo.getId())
                                .set(ShouhouPpidBind::getName, bindProductInfo.getProductName()).update());
                if(bindDbInfoOpt.isPresent()){
                    continue;
                }

                ShouhouPpidBind shouhouPpidBind = new ShouhouPpidBind().setPpid(bindPpid).setNegative(Boolean.FALSE)
                        .setAutoOutPut(Boolean.FALSE).setCreateTime(LocalDateTime.now()).setOutPutLimit(1)
                        .setCreateUser(oaUser.getUserName()).setIsDel(Boolean.FALSE).setOutPutLimit(1).setName(bindProductInfo.getProductName())
                        .setConfigId(shouhouPpidConfigOpt.map(ShouhouPpidConfig::getId).orElse(null));
                shouhouPpidBinds.add(shouhouPpidBind);
            }
            R<Boolean> upR = shouhouPpidConfigService.saveOrUpdateBindInfo(new ShouhouPpidOutPutAddReq()
                    .setId(shouhouPpidConfigOpt.map(ShouhouPpidConfig::getId).orElse(null))
                    .setName(shouhouPpidConfigOpt.map(ShouhouPpidConfig::getName).orElse(productinfo.getProductName()))
                    .setXtenant(XtenantEnum.getXtenant()).setBindList(shouhouPpidBinds).setNegative(Boolean.FALSE)
                    .setPpid(coe.getPpid()).setStatus(1)
            );
            if(upR.isSuccess()){
                result.setSuccessCount(result.getSuccessCount() + 1);
                result.getSuccessCheck().add(new Dict().set("ppid", coe.getPpid()).set("msg", "绑定成功"));
            }else{
                result.setFailCount(result.getFailCount() + 1);
                result.getFailCheck().add(new Dict().set("ppid", coe.getPpid()).set("msg", upR.getMsg()));
            }
        };
        cloudOrderExcels.forEach(updateFun::accept);
        return R.success(result);
    }
}
