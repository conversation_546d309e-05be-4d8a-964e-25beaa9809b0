package com.jiuji.oa.afterservice.api.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ch999.common.util.utils.RegexUtils;
import com.ch999.common.util.vo.Result;
import com.google.common.collect.Lists;
import com.jiuji.cloud.after.util.JiujiServiceUtil;
import com.jiuji.cloud.after.vo.jiujiservice.ValidSaleJiujiServiceReqVo;
import com.jiuji.cloud.after.vo.jiujiservice.ValidSaleJiujiServiceResVo;
import com.jiuji.cloud.after.vo.refund.RecoverNoReasonReduceVo;
import com.jiuji.cloud.after.vo.req.*;
import com.jiuji.cloud.after.vo.res.PjtShouhouRes;
import com.jiuji.oa.afterservice.api.po.TaxPiao;
import com.jiuji.oa.afterservice.api.po.UserPointPushRecordEntity;
import com.jiuji.oa.afterservice.api.service.OaApiService;
import com.jiuji.oa.afterservice.api.service.TaxPiaoService;
import com.jiuji.oa.afterservice.api.service.UserPointPushRecordService;
import com.jiuji.oa.afterservice.batchreturn.bo.ExternalStatisticsBO;
import com.jiuji.oa.afterservice.batchreturn.vo.req.ExternalStatisticsReq;
import com.jiuji.oa.afterservice.batchreturn.vo.res.ExternalStatisticsRes;
import com.jiuji.oa.afterservice.bigpro.annotation.WcfCallCount;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouLogBo;
import com.jiuji.oa.afterservice.bigpro.bo.WaisongHexiaoApplyBO;
import com.jiuji.oa.afterservice.bigpro.controller.BaseController;
import com.jiuji.oa.afterservice.bigpro.po.ServiceRecord;
import com.jiuji.oa.afterservice.bigpro.po.WaiSongAlipayBindUserPO;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.statistics.service.ShouhouStatisticsService;
import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.ExpressDetailResVo;
import com.jiuji.oa.afterservice.cloud.service.IMCloud;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.ExcelWriterUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.rabbitmq.SmallproRabblitMq;
import com.jiuji.oa.afterservice.refund.service.RefundMachineService;
import com.jiuji.oa.afterservice.refund.service.RefundMoneyService;
import com.jiuji.oa.afterservice.refund.service.kind.BaseTuiHuanKindService;
import com.jiuji.oa.afterservice.refund.service.way.CardPayOriginWayService;
import com.jiuji.oa.afterservice.refund.service.way.OtherRefundService;
import com.jiuji.oa.afterservice.refund.service.way.ThirdOriginWayService;
import com.jiuji.oa.afterservice.refund.vo.req.CardPayReqVo;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.CardOriginRefundVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.OtherRefundVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.oa.afterservice.shouhou.vo.*;
import com.jiuji.oa.afterservice.shouhou.vo.req.ProductKcCountInfoQueryReq;
import com.jiuji.oa.afterservice.shouhou.vo.req.ShouhouDaiYongjiAttachmentsAddReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.AfterSaleResultRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouHouDetailRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRepairInfoRes;
import com.jiuji.oa.afterservice.shouhou.vo.sms.AppInfoVo;
import com.jiuji.oa.afterservice.small.ExchangeProductListVO;
import com.jiuji.oa.afterservice.small.SmallYuyueOrderOnGoingVO;
import com.jiuji.oa.afterservice.smallpro.service.SmallproService;
import com.jiuji.oa.afterservice.smallpro.vo.ShowPrintingEnumVOV2;
import com.jiuji.oa.afterservice.smallpro.vo.req.SmallproAddLogReq;
import com.jiuji.oa.afterservice.smallpro.vo.res.SmallproLogRes;
import com.jiuji.oa.afterservice.statistics.bo.shouhou.NotifyParamBo;
import com.jiuji.oa.afterservice.statistics.service.BigAreaStatisticsNoticeService;
import com.jiuji.oa.afterservice.statistics.service.PersonStatisticsService;
import com.jiuji.oa.afterservice.yuyue.vo.req.ShouhouYuyueReq;
import com.jiuji.oa.afterservice.yuyue.vo.res.ShouHouYuyueRes;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.IntConstant;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jodd.typeconverter.Convert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @author: Li quan
 * @date: 2020/6/28
 */
@Api(tags = "WCF：售后相关接口")
@RestController
@RequestMapping("/api/wcf")
@Slf4j
public class OaApiController extends BaseController {
    @Autowired
    private OaApiService oaApiService;
    @Autowired
    private ShouhouYuyueService shouhouYuyueService;
    @Autowired
    private TaxPiaoService taxPiaoService;
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private ShouhouExService shouhouExService;
    @Autowired
    private BasketService basketService;
    @Autowired
    private IMCloud imCloud;

    @Autowired
    private UserPointPushRecordService userPointPushRecordService;
    @Autowired
    private DaiyongjiService daiyongjiService;
    @Autowired
    private ServiceRecordService serviceRecordService;
    @Autowired
    private ShouhouWaisongHexiaoService waisongHexiaoService;
    @Resource
    private ShouhouStatisticsService shouhouStatisticsService;
    @Autowired
    private SmsService smsService;

    @Resource
    private SmallproService smallproService;

    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;



    /**
     * description: <添加小件接件进程>
     * translation: <Adding smallpro logs>
     *
     * @param smallproAddLogReq 小件接件进程Req
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 14:44 2019/12/13
     * @see SmallproService#addSmallproLogWithPush(SmallproAddLogReq, String)
     * @since 1.0.0
     **/
    @PostMapping("/addSmallproLog")
    @ApiOperation(value = "添加小件接件进程", httpMethod = "POST", response = SmallproLogRes.class)
    public R<SmallproLogRes> addSmallproLog(@RequestBody SmallproAddLogReq smallproAddLogReq) {
        if (smallproAddLogReq.getSmallproId() == null ||
                smallproAddLogReq.getUserName() == null ||
                smallproAddLogReq.getComment() == null ||
                smallproAddLogReq.getProductName() == null ||
                smallproAddLogReq.getToEmail() == null ||
                smallproAddLogReq.getToSms() == null ||
                smallproAddLogReq.getToWeixin() == null ||
                smallproAddLogReq.getShowType() == null) {
            return R.error("添加小件进程参数不全！");
        }
        OaUserBO oaUserBO = new OaUserBO();
        oaUserBO.setUserName(smallproAddLogReq.getUserName());
        log.warn("添加小件接件进程传入参数：{}", JSONUtil.toJsonStr(smallproAddLogReq));
        SmallproLogRes smallproLogRes = abstractCurrentRequestComponent
                .invokeWithUser(Convert.toLong(XtenantEnum.getXtenant()), oaUserBO, user -> smallproService.addSmallproLogWithPush(smallproAddLogReq, user.getUserName()));
        log.warn("添加小件接件进程返回结果：{}", JSONUtil.toJsonStr(smallproLogRes));
        return R.success(smallproLogRes);
    }


    @ApiOperation(value = "获取售后详情")
    @GetMapping("/getShouHouDetail")
    @WcfCallCount
    public R<ShouHouDetailRes> getShouHouDetail(@RequestParam(value = "id") Integer id,
                                                @RequestParam(value = "userid", required = false) Integer userid,
                                                @RequestParam(value = "imei", required = false) String imei) {
        return oaApiService.getShouHouDetail(id, ObjectUtil.defaultIfNull(abstractCurrentRequestComponent.getWebUserId(), userid), imei);
    }


    @ApiOperation(value = "获取预约信息详情")
    @GetMapping("/getYuYueDetail")
    @WcfCallCount
    public R<ShouHouYuyueRes> getYuYueDetail(@RequestParam(value = "id") Integer id,
                                             @RequestParam(value = "userid", required = false) Integer userid) {
        return oaApiService.getYuYueDetail(id, ObjectUtil.defaultIfNull(abstractCurrentRequestComponent.getWebUserId(), userid));
    }

    /**
     * 获取最新维修信息 维修专区调用
     *
     * @param rows 记录行数
     * @see .net:oaApi.getshouhou
     */
    @ApiOperation(value = "获取最新维修信息 维修专区调用")
    @GetMapping("/getAfterSale")
    @WcfCallCount
    public R<AfterSaleResultRes> getAfterSale(@RequestParam(required = false, defaultValue = "20") Integer rows) {
        return R.success(oaApiService.getAfterSale(rows));
    }

    @ApiOperation(value = "wcf新增售后预约")
    @PostMapping("/addShouhouYuyue")
    @WcfCallCount
    public R<Integer> addShouhouYuyue(@Valid @RequestBody ShouhouYuyueReq req) {
        return oaApiService.addShouhouYuyue(req);
    }

    @GetMapping("/delYuyue")
    @ApiOperation(value = "取消预约单")
    @WcfCallCount
    public R<Boolean> delYuyue(@RequestParam("yyid") Integer yyid, @RequestParam("cancelType") String cancelType,
                               @RequestParam("remark") String remark, @RequestParam(value = "userId", required = false) Integer userId) {
        return shouhouYuyueService.delYuyue(yyid, cancelType, remark,"",
                ObjectUtil.defaultIfNull(abstractCurrentRequestComponent.getWebUserId(), userId));
    }


    /**
     * 获取发票信息
     *
     * @param subId 订单id
     * @return R
     * @see .NET:oa999UI.Controllers.apiController.getpiaoinfo
     */
    @GetMapping("/getInvoice")
    @ApiOperation(value = "获取发票信息")
    public R<TaxPiao> getInvoice(@RequestParam Long subId) {
        return R.success(taxPiaoService.getInvoice(subId));
    }

    @ApiOperation(value = "获取门店主体信息", httpMethod = "GET")
    @GetMapping("/getAreaSubject")
    public R<AreaInfo> getAreaSubject() {
        OaUserBO currentUser = this.getCurrentUser();
        if (currentUser == null) {
            return R.error("登录已过期，请重新登录！");
        }
        return R.success(shouhouService.getAreaSubject(currentUser.getAreaId()));
    }

    /**
     * 查询物流信息
     *
     * @param company 快递公司
     * @param postId  快递单号
     * @return 物流信息
     */
    @ApiOperation(value = "查询物流信息", httpMethod = "GET")
    @GetMapping("/getExpress")
    public R<List<ExpressDetailResVo>> getExpress(@RequestParam String company,
                                                  @RequestParam String postId) {
        return shouhouExService.queryExpress(company, postId);
    }

    /**
     * 根据 basketids 获取对应的稀却状态
     *
     * @param basketids basketids
     * @return R
     * @see .net:oa999UI.Controllers.apiController.GetScarcityByBasketids
     */
    @ApiOperation(value = "根据 basketids 获取对应的稀却状态", httpMethod = "GET")
    @PostMapping("/getScarcityByBasketids")
    public R<Integer> getScarcityByBasketids(@RequestBody List<Integer> basketids) {
        return R.success(basketService.getScarcityByBasketids(basketids));
    }

    /**
     * imeiQueryApi
     *
     * @param q q
     * @return R
     * @see .net:oa999UI.Controllers.ShouhouController.ImeiQueryApi
     */
    @ApiOperation(value = "imeiQueryApi", httpMethod = "GET")
    @GetMapping("/imeiQueryApi")
    public R<String> imeiQueryApi(String q) {
        return R.success(oaApiService.imeiQueryApi(q));
    }

    /**
     * imeiQueryApi
     *
     * @param q q
     * @return R
     * @see .net:oa999UI.Controllers.ShouhouController.ImeiQueryApi
     */
    @ApiOperation(value = "imeiQueryApiV2", httpMethod = "GET")
    @GetMapping("imeiQueryApi/V2")
    public R<JSONObject> imeiQueryApiV2(String q) {
        return JSON.parseObject(oaApiService.imeiQueryApi(q), new TypeReference<R<JSONObject>>(){});
    }

    @ApiOperation(value = "根据会员ID查询会员是否修过手机")
    @RequestMapping("/getShouhouRepaireUserInfo")
    public R<List<ShouhouRepaireUserVo>> getShouhouRepaireUserInfo(@RequestBody List<Long> userIds) {
        return oaApiService.getShouhouRepaireUserInfo(userIds);
    }

    @ApiOperation(value = "根据第三方平台和订单查询Oa订单")
    @RequestMapping("/getSalesOrder")
    public R<List<SalesOrderVo>> getSalesOrder(@RequestBody SalesOrderVo req) {
        return oaApiService.getSalesOrder(req);
    }

    @ApiOperation(value = "第三方平台枚举")
    @GetMapping("/getSalesOrderEnum")
    public R<List<ShowPrintingEnumVOV2>> getSalesOrderEnum() {
        return oaApiService.getSalesOrderEnum();
    }

    @ApiOperation(value = "第三方平台枚举V2")
    @GetMapping("/getSalesOrderEnum/V2")
    public R<List<ShowPrintingEnumVOV2>> getSalesOrderEnumV2(@RequestParam(value = "xtenant", required = false) Integer xtenant) {
        return oaApiService.getSalesOrderEnumV2(xtenant);
    }

    @ApiOperation(value = "查询商品库存信息，传对应的ppid信息", httpMethod = "POST")
    @RequestMapping("/queryProductStockByPpid")
    public R<ProductKcCountInfoRes> queryProductStockByPpid(@RequestBody ProductKcCountInfoQueryReq req) {
        if (CollectionUtils.isEmpty(req.getBigProPpids()) && CollectionUtils.isEmpty(req.getSmallProPpids())) {
            return R.error("缺少必要参数信息");
        }
        List<ProductKcInfo> bigProList = new LinkedList<>();
        List<ProductKcInfo> smallProList = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(req.getBigProPpids())) {
            bigProList = oaApiService.queryProductStockByPpid(req.getBigProPpids(), 1);
        }
        if (CollectionUtils.isNotEmpty(req.getSmallProPpids())) {
            smallProList = oaApiService.queryProductStockByPpid(req.getSmallProPpids(), 2);
        }
        ProductKcCountInfoRes res = new ProductKcCountInfoRes();
        res.setBigProList(bigProList);
        res.setSmallProList(smallProList);
        return R.success(res);
    }

    @ApiOperation(value = "查询商品库存信息，其中小件库存统计逻辑变更，统计库存+在途", httpMethod = "POST")
    @RequestMapping("/queryProductStockByPpid/v2")
    public R<ProductKcCountInfoRes> queryProductStockByPpidV2(@RequestBody ProductKcCountInfoQueryReq req) {
        boolean bigEmpty = req.getBigProPpids().isEmpty();
        boolean smallEmpty = req.getSmallProPpids().isEmpty();

        if (bigEmpty && smallEmpty) {
            return R.error("缺少必要参数信息");
        }

        List<ProductKcInfo> bigProList = new LinkedList<>();
        List<ProductKcInfo> smallProList = new LinkedList<>();
        if (!bigEmpty) {
            bigProList = oaApiService.queryProductStockByPpid(req.getBigProPpids(), 1);
        }
        if (!smallEmpty) {
            smallProList = oaApiService.queryProductStockByPpid(req.getSmallProPpids(), 3);
        }

        ProductKcCountInfoRes res = new ProductKcCountInfoRes();
        res.setBigProList(bigProList);
        res.setSmallProList(smallProList);
        return R.success(res);
    }

    @ApiOperation(value = "查询商品库存信息，其中小件库存统计逻辑变更 ,剔除合作伙伴的库存", httpMethod = "POST")
    @RequestMapping("/queryProductStockByPpid/v3")
    public R<ProductKcCountInfoRes> queryProductStockByPpidV3(@RequestBody ProductKcCountInfoQueryReq req) {
        boolean bigEmpty = req.getBigProPpids().isEmpty();
        boolean smallEmpty = req.getSmallProPpids().isEmpty();

        if (bigEmpty && smallEmpty) {
            return R.error("缺少必要参数信息");
        }

        List<ProductKcInfo> bigProList = new LinkedList<>();
        List<ProductKcInfo> smallProList = new LinkedList<>();
        if (!bigEmpty) {
            bigProList = oaApiService.queryProductStockByPpidV3(req.getBigProPpids(), 1);
        }
        if (!smallEmpty) {
            smallProList = oaApiService.queryProductStockByPpidV3(req.getSmallProPpids(), 3);
        }

        ProductKcCountInfoRes res = new ProductKcCountInfoRes();
        res.setBigProList(bigProList);
        res.setSmallProList(smallProList);
        return R.success(res);
    }

    @ApiOperation(value = "主站调用：根据串号查询维修记录")
    @GetMapping("/getRepairRecordByImei")
    public R<ShouhouRepairInfoRes> getRepairRecordByImei(@RequestParam(value = "imei") String imei) {
        return R.success(shouhouService.getRepairRecordByImei(imei));
    }

    @ApiOperation(value = "主站调用：根据串号查询维修记录")
    @GetMapping("/execute")
    public R<String> execute(Integer size, Integer current) {
        IPage<UserPointPushRecordEntity> page = new Page<>(current, size, false);
        List<UserPointPushRecordEntity> total = userPointPushRecordService.page(page, new LambdaQueryWrapper<UserPointPushRecordEntity>()
                .eq(UserPointPushRecordEntity::getPushState, 999)).getRecords();
        List<List<UserPointPushRecordEntity>> partition = Lists.partition(total.parallelStream()
                .filter(p -> p.getPushState() == 999).collect(Collectors.toList()), 2000);
        ForkJoinPool forkJoinPool = new ForkJoinPool(50);
        for (List<UserPointPushRecordEntity> data : partition) {
            CompletableFuture.runAsync(() -> pushWeixin(data), forkJoinPool);
        }
        return R.success("成功");
    }

    public static final AtomicInteger i = new AtomicInteger(1);

    @ApiOperation(value = "主站调用：根据串号查询维修记录")
    @GetMapping("/push")
    public void pushWeixin(List<UserPointPushRecordEntity> data) {
        log.info(Thread.currentThread().getName() + "开始执行  ");
        String firstTem = "亲爱的%s，您有%s积分即将于2021年1月31日到期清零，请尽快前往会员俱乐部使用积分";
        List<UserPointPushRecordEntity> batch = new ArrayList<>();
        for (UserPointPushRecordEntity entity : data) {
            if (entity.getPushState() == 999) {
                String username = RegexUtils.checkMobile(entity.getUserName()) ?
                        desensitizedPhoneNumber(entity.getUserName()) : entity.getUserName();
                try {
                    Result<String> result = imCloud.sendPointsReminderMsg(
                            entity.getOpenid(),
                            "https://m.9ji.com/vip?from=jfql28",
                            String.format(firstTem, username, entity.getReducePoint()),
                            username,
                            "积分清零时间-2021年1月31日23:59:59",
                            "清除2019年获得且未使用积分",
                            "即将过期",
                            entity.getReducePoint() + "积分",
                            "可用积分",
                            entity.getLeftPoint() + "积分",
                            "会员积分超值换购，点击前往>"
                    );
                    entity.setPushState(result.getCode());
                    entity.setPushMsg(result.getMsg());
                    batch.add(entity);
                    if (batch.size() == 1000) {
                        userPointPushRecordService.updateBatchById(batch);
                        batch.clear();
                    }
                } catch (Exception ex) {
                    if (CollectionUtils.isNotEmpty(batch)) {
                        userPointPushRecordService.updateBatchById(batch);
                        batch.clear();
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(batch)) {
            userPointPushRecordService.updateBatchById(batch);
        }
        log.info(Thread.currentThread().getName() + "执行结束  ");
    }

    @ApiOperation(value = "代用机协议附件上传接口", httpMethod = "POST")
    @PostMapping("/daiYongjiAttachmentsUpLoad")
    public R<Boolean> daiYongjiAttachmentsUpLoad(@RequestBody ShouhouDaiYongjiAttachmentsAddReq req) {
        req.setUserId(ObjectUtil.defaultIfNull(abstractCurrentRequestComponent.getWebUserId(), req.getUserId()));
        return daiyongjiService.saveDaiYongJiAttachments(req);
    }

    @ApiOperation(value = "获取代用机协议附件接口", httpMethod = "GET")
    @GetMapping("/getYongjiAttachments")
    public R<List<FileReq>> getYongjiAttachments(@RequestParam(value = "shouhouId") Integer shouhouId,
                                                 @RequestParam(value = "userId", required = false) Integer userId) {
        return daiyongjiService.getDaiYongJiAttachments(shouhouId,
                ObjectUtil.defaultIfNull(abstractCurrentRequestComponent.getWebUserId(), userId));
    }

    /**九机服务*/
    @ApiOperation(value = "九机服务")
    @GetMapping("/getServiceInfo/v3")
    public R<ServiceInfoVO> getServiceInfoV3(@RequestParam(value = "imei") String imei,@RequestParam(value = "userId",required = false) Integer userId
                                             ,@RequestParam(value = "issave", defaultValue = "true", required = false) Boolean isSaveLog,
                                             @RequestParam(value = "removeAsterisk", defaultValue = "0", required = false) Integer removeAsterisk,
                                             @RequestParam(value = "subId", defaultValue = "0", required = false) Integer subId
    ) {
        ServiceInfoVO record = serviceRecordService.getRecord(imei, ObjectUtil.defaultIfNull(abstractCurrentRequestComponent.getWebUserId(), userId),
                subId, isSaveLog);
        return R.success(record).addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    /**九机服务*/
    @ApiOperation(value = "九机服务服务")
    @PostMapping("/listServiceInfo")
    public R<List<ServiceInfoVO>> listServiceInfo(@RequestBody List<String> imeis,
                                                  @RequestParam(value = "userId",required = false) Integer userId) {
        if(imeis.isEmpty()){
            return R.success(Collections.emptyList());
        }
        return R.success(imeis.parallelStream().map(imei->serviceRecordService.getRecord(imei,
                        ObjectUtil.defaultIfNull(abstractCurrentRequestComponent.getWebUserId(), userId), false))
                .collect(Collectors.toList()));
    }
    /**维修报销阿里用户绑定
     * @return*/
    @ApiOperation(value = "维修报销阿里用户绑定")
    @PostMapping("/alipayBindUser")
    public R<WaiSongAlipayBindUserPO> alipayBindUser(@RequestBody @Valid WaisongHexiaoApplyBO.AlipayBindUserBO alipayBindUserBO){
        return waisongHexiaoService.alipayBindUser(alipayBindUserBO.getWxkId(),alipayBindUserBO.getOperatorId()
                ,alipayBindUserBO.getUserId(),alipayBindUserBO.getUserAvatar(),alipayBindUserBO.getNickname());
    }

    /**
     * 外送报账财务审批
     * @param approve
     * @return
     */
    @ApiOperation(value = "外送报账财务审批")
    @PostMapping("/accountApprove")
    public R<Object> accountApprove(@RequestBody @Valid WaisongHexiaoApplyBO.AccountApproveBO approve){
        return waisongHexiaoService.accountApprove(approve.getIds(),approve.getAnswer(),approve.getAccountId(),approve.getOperator());
    }

    private static String desensitizedPhoneNumber(String phoneNumber) {
        if (StringUtils.isNotEmpty(phoneNumber) || phoneNumber.length() == 11) {
            phoneNumber = phoneNumber.replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2");
        }
        return phoneNumber;
    }

    /**
     * 计算维修毛利
     * @param req
     * @return
     */
    @PostMapping("/getWeiXiuMaoLi")
    @ApiOperation(value = "计算维修毛利", httpMethod = "POST")
    public R<List<ExternalStatisticsRes>> getWeiXiuMaoLi(@RequestBody ExternalStatisticsReq req) {
        if (req.getCh999IdList().isEmpty()){
            return R.error("人员不能为空！");
        }
        List<ExternalStatisticsBO> externalStatistics = shouhouStatisticsService.ExternalStatisticsList(req, 1);
        List<ExternalStatisticsRes> externalStatisticsRes = new ArrayList<>();
        externalStatistics.forEach(ex -> {
            ExternalStatisticsRes result = new ExternalStatisticsRes();
            result.setCh999Id(ex.getUserInfo().getCh999Id());
            result.setArea(ex.getUserInfo().getArea());
            result.setAreaId(ex.getUserInfo().getAreaId());
            result.setData(ex.getWeiXiuMaoLi());
            externalStatisticsRes.add(result);
        });
        return R.success(externalStatisticsRes);
    }

    /**
     * 计算维修毛利配比
     * @param req
     * @return
     */
    @PostMapping("/getWeiXiuMaoLvPeiBi")
    @ApiOperation(value = "计算维修毛利配比", httpMethod = "POST")
    public R<List<ExternalStatisticsRes>> getWeiXiuMaoLvPeiBi(@RequestBody ExternalStatisticsReq req) {
        if (req.getCh999IdList().isEmpty()){
            return R.error("人员不能为空！");
        }
        List<ExternalStatisticsBO> externalStatistics = shouhouStatisticsService.ExternalStatisticsList(req, NumberConstant.TWO);
        List<ExternalStatisticsRes> externalStatisticsRes = new ArrayList<>();
        externalStatistics.forEach(ex -> {
            ExternalStatisticsRes result = new ExternalStatisticsRes();
            result.setCh999Id(ex.getUserInfo().getCh999Id());
            result.setArea(ex.getUserInfo().getArea());
            result.setAreaId(ex.getUserInfo().getAreaId());
            result.setData(ex.getWeiXiuMaoLvPeiBi());
            externalStatisticsRes.add(result);
        });
        return R.success(externalStatisticsRes);
    }

    /**
     * 计算电池搭售率
     * @param req
     * @return
     */
    @PostMapping("/getDianChiDaShouLv")
    @ApiOperation(value = "计算电池搭售率", httpMethod = "POST")
    public R<List<ExternalStatisticsRes>> getDianChiDaShouLv(@RequestBody ExternalStatisticsReq req) {
        if (req.getCh999IdList().isEmpty()){
            return R.error("人员不能为空！");
        }
        List<ExternalStatisticsBO> externalStatistics = shouhouStatisticsService.ExternalStatisticsList(req, NumberConstant.THREE);
        List<ExternalStatisticsRes> externalStatisticsRes = new ArrayList<>();
        externalStatistics.forEach(ex -> {
            ExternalStatisticsRes result = new ExternalStatisticsRes();
            result.setCh999Id(ex.getUserInfo().getCh999Id());
            result.setArea(ex.getUserInfo().getArea());
            result.setAreaId(ex.getUserInfo().getAreaId());
            result.setData(ex.getDianChiDaShouLv());
            externalStatisticsRes.add(result);
        });
        return R.success(externalStatisticsRes);
    }

    /**
     * 计算屏幕搭售率
     * @param req
     * @return
     */
    @PostMapping("/getPingMuDaShouLv")
    @ApiOperation(value = "计算屏幕搭售率", httpMethod = "POST")
    public R<List<ExternalStatisticsRes>> getPingMuDaShouLv(@RequestBody ExternalStatisticsReq req) {
        if (req.getCh999IdList().isEmpty()){
            return R.error("人员不能为空！");
        }
        List<ExternalStatisticsBO> externalStatistics = shouhouStatisticsService.ExternalStatisticsList(req, NumberConstant.FOUR);
        List<ExternalStatisticsRes> externalStatisticsRes = new ArrayList<>();
        externalStatistics.forEach(ex -> {
            ExternalStatisticsRes result = new ExternalStatisticsRes();
            result.setCh999Id(ex.getUserInfo().getCh999Id());
            result.setArea(ex.getUserInfo().getArea());
            result.setAreaId(ex.getUserInfo().getAreaId());
            result.setData(ex.getPingMuDaShouLv());
            externalStatisticsRes.add(result);
        });
        return R.success(externalStatisticsRes);
    }

    /**
     * 计算放弃维修率
     * @param req
     * @return
     */
    @PostMapping("/getFlunkWeiXiuShouLv")
    @ApiOperation(value = "计算放弃维修率", httpMethod = "POST")
    public R<List<ExternalStatisticsRes>> getFlunkWeiXiuShouLv(@RequestBody ExternalStatisticsReq req) {
        if (req.getCh999IdList().isEmpty()){
            return R.error("人员不能为空！");
        }
        List<ExternalStatisticsBO> externalStatistics = shouhouStatisticsService.ExternalStatisticsList(req, NumberConstant.FIVE);
        List<ExternalStatisticsRes> externalStatisticsRes = new ArrayList<>();
        externalStatistics.forEach(ex -> {
            ExternalStatisticsRes result = new ExternalStatisticsRes();
            result.setCh999Id(ex.getUserInfo().getCh999Id());
            result.setArea(ex.getUserInfo().getArea());
            result.setAreaId(ex.getUserInfo().getAreaId());
            result.setData(ex.getFlunkWeiXiuShouLv());
            externalStatisticsRes.add(result);
        });
        return R.success(externalStatisticsRes);
    }


    /**
     * 售后购买的服务
     * @param imei
     * @return
     */
    @GetMapping("/getServiceRecordByImei")
    @ApiOperation(value = "售后购买的服务", httpMethod = "GET")
    public R<List<ServiceRecord>> getServiceRecordByImei(@RequestParam(value = "imei") String imei) {
        if (StringUtils.isEmpty(imei)){
            return R.error("imei不能为空！");
        }
        return R.success(serviceRecordService.list(new LambdaQueryWrapper<ServiceRecord>().eq(ServiceRecord::getImei,imei).eq(ServiceRecord::getIsdel,0)));
    }

    @PostMapping("/pushSmsAppInfo")
    @ApiOperation(value = "短信平台账号信息推送", httpMethod = "POST")
    @Deprecated
    public R pushSmsAppInfo(@Valid @RequestBody AppInfoVo appInfoVo){
        return smsService.saveOrUpdateAppInfo(appInfoVo);
    }

    /**
     * 获取用户壳膜订单
     * @param userId 会员id
     * @return 订单数量
     */
    @GetMapping("/getOrderClassByUserId")
    @ApiOperation(value = "获取用户壳膜订单", httpMethod = "GET")
    R<Integer> getOrderClassByUserId(@RequestParam(value = "userId") Integer userId){
        return oaApiService.getOrderClassByUserId(ObjectUtil.defaultIfNull(abstractCurrentRequestComponent.getWebUserId(), userId));
    }

    /**
     * 是否可以购买服务判断
     * @param saleJiujiServiceReq
     * @return
     */
    @PostMapping("/getValidSaleService")
    @ApiOperation(value = "是否可以购买服务判断", httpMethod = "POST")
    R<ValidSaleJiujiServiceResVo> getValidSaleService(@Valid @RequestBody ValidSaleJiujiServiceReqVo saleJiujiServiceReq){
        return serviceRecordService.getValidSaleService(saleJiujiServiceReq);
    }

    /**
     * 该串号下是否有贴膜信息
     * @param imei 串号
     * @return
     */
    @GetMapping("/getFilmByImei")
    @ApiOperation(value = "该串号下是否有贴膜信息", httpMethod = "GET")
    R<Boolean> getFilmByImei(@RequestParam("imei") String imei){
        return serviceRecordService.getFilmByImei(imei);
    }

    /**
     * 售后服务类型
     * @param shouhouServiceType 售后服务类型
     * @return
     */
    @GetMapping("/listServiceType")
    @ApiOperation(value = "售后获取服务类型", httpMethod = "GET")
    R<Set<Integer>> listServiceType(@RequestParam("shouhouServiceType") Integer shouhouServiceType){
        return R.success(JiujiServiceUtil.listServiceTypeByShouhouServiceType(shouhouServiceType));
    }

    @ApiOperation(value = "个人最近的业绩统计")
    @GetMapping("/person/recent/{ch999Id}")
    public R<?> personRecent(@PathVariable("ch999Id") Integer ch999Id,
                             @ApiParam("最近的几个月内的数据") @RequestParam("months") Integer months){
        return SpringUtil.getBean(PersonStatisticsService.class).personRecent(ch999Id,months);
    }

    @ApiOperation(value = "售后维修：增加处理日志")
    @PostMapping("/addShouHouLog")
    public R<Boolean> addShouhouLog(@Valid @RequestBody ShouhouLogAddReq req){
        ShouhouLogsService shouhouLogsService = SpringUtil.getBean(ShouhouLogsService.class);
        List<ShouhouLogBo> shLogs = ObjectUtil.defaultIfNull(shouhouLogsService.getListByShouHouIdAndOrder(req.getShouhouId(), 0),Collections.emptyList());
        if(shLogs.stream().anyMatch(sl -> Objects.equals(sl.getComment(),req.getComment()))){
            return R.error("请勿频繁提交重复内容的处理进程");
        }
        shouhouService.saveShouhouLog(req.getShouhouId(), req.getComment(), req.getOperator(),null,ObjectUtil.defaultIfNull(req.getIsWeb(),false));
        return R.success(Boolean.TRUE);
    }

    @ApiOperation(value = "小件换货：小件换货商品列表")
    @PostMapping("/listSmallExchangeProduct")
    public R<ExchangeProductListVO> listSmallExchangeProduct(@Valid @RequestBody SmallExchangeReq req) {
        // 分页页数默认参数
        Integer page = req.getPage();
        boolean pageUnSet = Objects.isNull(page) || page <= 0;
        if (pageUnSet) {
            page = IntConstant.ONE;
            req.setPage(page);
        }
        // 分页大小默认参数
        Integer pageSize = req.getSize();
        boolean pageSizeUnSet = Objects.isNull(pageSize) || pageSize <= 0;
        if (pageSizeUnSet) {
            pageSize = IntConstant.TEN;
            req.setSize(pageSize);
        }
        // 分页查询 offset
        Integer offset = (page - 1) * pageSize;
        req.setOffset(offset);
        return smallproService.getExchangeProductList(req);
    }

    @ApiOperation(value = "进行中的预约单和小件单")
    @PostMapping("/listSmallYuyueOrderOnGoing")
    public R<List<SmallYuyueOrderOnGoingVO>> listSmallYuyueOrderOnGoing(@Valid @RequestBody List<Integer> basketIds,
                                                                        @RequestParam(value = "userId", required = false) Integer userId) {

        return smallproService.listSmallYuyueOrderOnGoing(basketIds, ObjectUtil.defaultIfNull(abstractCurrentRequestComponent.getWebUserId(), userId));
    }

    @ApiOperation(value = "售后预约：改变预约单类型")
    @PostMapping("/setShouhouyuyueFromSource")
    public R<Boolean> setShouhouyuyueFromSource(@RequestBody ShouhouyuyueFromSource shouhouyuyueOriginTypeSetReq) {
        shouhouyuyueOriginTypeSetReq.setUserId(ObjectUtil.defaultIfNull(abstractCurrentRequestComponent.getWebUserId(),
                shouhouyuyueOriginTypeSetReq.getUserId()));
        return shouhouYuyueService.setShouhouyuyueFromSource(shouhouyuyueOriginTypeSetReq);
    }


    @ApiOperation(value = "售后预约：改变预约单类型")
    @PostMapping("/addPjtShouhou")
    public R<PjtShouhouRes> addPjtShouhou(@RequestBody @Valid PjtShouhouReq req) {
        PjtShouhouRes result = null;
        try {
            result = shouhouService.addPjtShouhou(req);
        } catch (Exception e) {
            RRExceptionHandler.logError(StrUtil.format("拍机堂回收单[{}]一键退款", req.getRecoverSubId()), req, e, smsService::sendOaMsgTo9JiMan);
            throw e;
        }
        return R.success(result);
    }



    @ApiOperation(value = "售后预约：改变预约单类型")
    @PostMapping("/addCutScreenReqLog")
    public R<Boolean> addCutScreenReqLog(@RequestBody @Valid CutScreenReq req) {
        Boolean result = smallproService.addCutScreenReqLog(req);
        return R.success(result);
    }

    @ApiOperation(value = "售后预约：改变预约单类型")
    @PostMapping("/addCutScreenReqLogShouhou")
    public R<Boolean> addCutScreenReqLogShouhou(@RequestBody @Valid CutScreenShouhouReq req) {
        Boolean result = shouhouService.addCutScreenReqLogShouhou(req);
        return R.success(result);
    }



    @ApiOperation(value = "下载缓存文件")
    @GetMapping("/download_cache_file/{fid}")
    public void downloadCacheFile(@PathVariable("fid") String fid,@RequestParam(value = "filename",required = false) String fileName, HttpServletResponse response) {
        StringRedisTemplate redisTemplate = SpringUtil.getBean(StringRedisTemplate.class);
        ExcelWriterUtil.UserCacheKey cacheKey = JSON.parseObject(redisTemplate.opsForValue().get(StrUtil.format(RedisKeys.EXCEL_CACHE_META, fid)), ExcelWriterUtil.UserCacheKey.class);
        if (cacheKey == null){
            throw new CustomizeException(StrUtil.format("{}缓存文件已过期",fid));
        }
        File cacheFile = ExcelWriterUtil.getFileByUserCache(cacheKey);
        if (cacheFile == null){
            throw new CustomizeException(StrUtil.format("{}缓存文件不存在",fid));
        }
        String downloadFileName = ObjectUtil.defaultIfBlank(fileName,cacheKey.getDownloadFileName());
        ServletUtil.write(response, IoUtil.toStream(cacheFile), FileUtil.getMimeType(downloadFileName), downloadFileName);
    }

    @ApiOperation(value = "售后统计通知")
    @GetMapping("/shouhouStatistics/notice")
    @RepeatSubmitCheck(seconds = 30, message = "请勿重复通知, 通知完成锁定30s")
    public R shouhouStatisticsNotice(NotifyParamBo notifyParamBo) {
        return SpringUtil.getBean(BigAreaStatisticsNoticeService.class).notice(notifyParamBo);
    }


    @PostMapping("/groupRefund/save")
    @ApiOperation(value = "组合退款提交", notes = "组合退款提交")
    public R<Integer> groupRefundSave(@RequestBody @Valid GroupTuihuanFormVo tuihuanFormVo, @RequestParam(value = "currAreaId", required = false) Integer currAreaId){
        // 模拟登录 系统用户信息
        if(abstractCurrentRequestComponent.getCurrentStaffId() == null){
            AreaInfo currAreaInfo = CommonUtils.getResultData(SpringUtil.getBean(AreaInfoClient.class).getAreaInfoById(currAreaId), userMsg -> {
                throw new CustomizeException(StrUtil.format("获取org门店信息[{}]异常", userMsg));
            });
            OaUserBO user = SpringUtil.getBean(SmallproRabblitMq.class).simulateUser(currAreaInfo, "系统");
            SpringContextUtil.getRequest().ifPresent(request ->  request.setAttribute(RequestAttrKeys.REQUEST_ATTR_OA_USER, user));
        }
        // 跳过用户验证
        SpringContextUtil.getRequest()
                .ifPresent(req -> req.setAttribute(RequestAttrKeys.PASS_GROUP_REFUND_IS_NEED_VALID,Boolean.TRUE));
        return SpringUtil.getBean(RefundMoneyService.class).save(tuihuanFormVo,
                tuihuanForm -> BaseTuiHuanKindService.getBean(tuihuanForm.getTuihuanKind())
                        .getSuInfoWithMaxRefundPrice(ObjectUtil.defaultIfNull(tuihuanForm.getShouhouId(),tuihuanForm.getSubId()),tuihuanForm.getTuihuanKind()));
    }

    @GetMapping("/groupRefund/enable")
    @ApiOperation(value = "是否启用组合退", notes = "是否启用组合退")
    public R<Boolean> groupRefundEnable(@RequestParam(value = "orderId",required = false) Integer orderId,
                                        @RequestParam(value = "tuihuanKinds",required = false) String tuihuanKinds,
                                        @RequestParam(value = "areaId",required = false) Integer areaId
    ){
        return R.success(SpringUtil.getBean(RefundMoneyService.class).enable(orderId, StrUtil.splitTrim(tuihuanKinds, StringPool.COMMA).stream()
                .map(cn.hutool.core.convert.Convert::toInt).filter(Objects::nonNull).collect(Collectors.toList()), areaId))
                .addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    @GetMapping("/groupRefund/listAllThirdShouYing")
    @ApiOperation(value = "获取所有三方收银列表", notes = "获取所有三方收银列表")
    public R<List<ThirdOriginRefundVo>> listAllThirdShouYing(@RequestParam(value = "orderId") Integer orderId, @RequestParam(value = "tuihuanKind") Integer tuihuanKind){
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuihuanKind);
        if(tuihuanKindEnum == null){
            return R.error("退款类型错误");
        }
        return R.success(SpringUtil.getBean(ThirdOriginWayService.class).listAll(orderId, tuihuanKindEnum))
                .addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }


    @GetMapping("/groupRefund/getCardPayInfo")
    @ApiOperation(value = "获取刷卡支付信息", notes = "获取刷卡支付信息")
    public R<List<CardOriginRefundVo>> getCardPayInfo(@RequestParam(value = "orderId") Integer orderId, @RequestParam(value = "tuihuanKind") Integer tuihuanKind){
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuihuanKind);
        if(tuihuanKindEnum == null){
            return R.error("退款类型错误");
        }
        return R.success(SpringUtil.getBean(CardPayOriginWayService.class).listAll(orderId, tuihuanKindEnum))
                .addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    @GetMapping("/groupRefund/listAllOtherRefund")
    @ApiOperation(value = "获取其他退款信息", notes = "获取其他退款信息")
    public R<List<OtherRefundVo>> listAllOtherRefund(@RequestParam(value = "orderId") Integer orderId, @RequestParam(value = "tuihuanKind") Integer tuihuanKind){
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuihuanKind);
        if(tuihuanKindEnum == null){
            return R.error("退款类型错误");
        }
        return R.success(SpringUtil.getBean(OtherRefundService.class).listAll(orderId, tuihuanKindEnum))
                .addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    /**
     * 获取良品扣减服务费信息
     */
    @GetMapping("/groupRefundMachine/getRecoverNoReasonReduce")
    @ApiOperation(value = "获取良品扣减服务费信息", notes = "获取良品扣减服务费信息")
    R<RecoverNoReasonReduceVo> getRecoverNoReasonReduce(@RequestParam(value = "userId") Integer userId){
    return SpringUtil.getBean(RefundMachineService.class).getRecoverNoReasonReduce(
            ObjectUtil.defaultIfNull(abstractCurrentRequestComponent.getWebUserId(), userId));
    }

    /**
     * 是否用户单据
     * @see BusinessTypeEnum
     * @param orderId
     * @param userId
     * @param orderType
     * @return
     */
    @GetMapping("/isUserOrder")
    @ApiOperation(value = "是否用户单据", notes = "是否用户单据")
    R<Boolean> isUserOrder(@RequestParam(value = "orderId") Integer orderId,
                           @RequestParam(value = "userId", required = false) Integer userId,
                           @RequestParam(value = "orderType") Integer orderType
    ){
        Integer userIdParam = ObjectUtil.defaultIfNull(abstractCurrentRequestComponent.getWebUserId(), userId);
        if(userIdParam == null){
            return R.error("用户id不能为空");
        }
        return oaApiService.isUserOrder(orderId, userIdParam, orderType);
    }
}
