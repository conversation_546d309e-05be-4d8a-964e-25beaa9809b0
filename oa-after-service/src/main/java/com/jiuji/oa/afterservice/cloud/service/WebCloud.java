package com.jiuji.oa.afterservice.cloud.service;

import com.ch999.common.util.vo.Result;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouComment;
import com.jiuji.oa.afterservice.cloud.fallbackfactory.WebCloudFallbackFactory;
import com.jiuji.oa.afterservice.cloud.vo.*;
import com.jiuji.oa.afterservice.cloud.vo.web.*;
import com.jiuji.oa.afterservice.cloud.vo.web.req.FilmAccessoriesReq;
import com.jiuji.oa.afterservice.cloud.vo.web.res.WebFilmAccessoriesRes;
import com.jiuji.oa.afterservice.machine.vo.RecomentProductVO;
import com.jiuji.tc.common.vo.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(value = "WEB",path = "web",fallbackFactory = WebCloudFallbackFactory.class)
public interface WebCloud {

    /**
     * 优惠券发布
     * @param xtenant
     * @param param
     * @return
     */
    @PostMapping("/api/order/sendRepairBuyCoupon/v1")
    public Result<List<RepairBuyCouponVO>> sendRepairBuyCoupon(@RequestBody SubCheckChangedParam param);

    /**
     * 校验接口
     * @param userId
     * @param orderId
     * @return
     */
    @GetMapping("/api/order/repairTuiFeeCheck/v1")
    public Result<RepairTuiFeeCheckVO> repairTuiFeeCheck(@RequestParam("userId") Integer userId, @RequestParam("orderId") Integer orderId);

    /**
     * 删
     * @param userId
     * @param orderId
     * @return
     */
    @GetMapping("/api/order/repairTuiFeeDel/v1")
    public Result repairTuiFeeDel(@RequestParam("userId") Integer userId, @RequestParam("orderId") Integer orderId, @RequestParam("repairIds") List<Integer> repairIds);

    /**
     * 会员图标查询
     * @param userClass
     * @return
     */
    @GetMapping("/api/memberClubBackground/getOneByUserClass/v1")
    Result<MemberClubBackground> getOneByUserClass(@RequestParam("userClass") Integer userClass);


    /**
     * 查询商品优惠码
     * @param ppid 商品id
     * @param cateId 分类id 回收加价券=20
     * @return
     */
    @GetMapping("/api/youhuima/getProductCouponList/v1")
    Result<List<HuishouYouHuiMa>> getProductCouponList(@RequestParam("ppid") Integer ppid, @RequestParam("cateId") Integer cateId);
    /**
     * 删除备用机协议
     *
     * @param dataId
     * @param imei
     * @return
     */
    @GetMapping("/api/document/removeSignatureByDataId/v1")
    Result<String> removeSignatureByDataId(
            @RequestHeader(value = "dataId") Integer dataId,
            @RequestHeader(value = "imei", required = false) String imei);

    /**
     * 通过产品id获取配件产品id
     * @param productId
     * @return
     */
    @GetMapping("/api/products/getProductRepirParts")
    Result<List<Integer>> getProductRepirParts(@RequestParam("productId") Integer productId);

    /**
     * 通过产品id获取配件产品id
     * @param productIds
     * @return
     */
    @PostMapping("/api/products/getMapPIdsByPpids")
    Result<Map<Integer,List<Integer>>> getMapPIdsByPpids(@RequestBody List<Integer> productIds);

    //{{local}}/web/api/products/getRecomentParts/v1?ppid=94568&cityId=530102&cids=4

    /**
     * 获取推荐商品
     * @return
     */
    @GetMapping("/api/products/getRecomentParts/v1")
    Result<List<RecomentProductVO>> getRecomentParts(@RequestParam("ppid") Integer ppid,@RequestParam("cityId") Integer cityId
            ,@RequestParam("cids") String cids,@RequestHeader("xtenant") Long xtenant);

    /**
     * 获取质保配置 根据ppid
     * @param ppid
     * @return
     */
    @GetMapping("/api/afterService/time/config/v1")
    Result<AfterServiceTimeCfg> getShouhouTimeConfig(@RequestParam(value = "ppid") Integer ppid, @RequestHeader("xtenant") Long xtenant);
    /**
     * 批量获取质保配置 根据ppids
     * @param ppids
     * @return
     */
    @PostMapping("/api/afterService/time/listConfig/v1")
    Result<List<AfterServiceTimeCfg>> listShouhouTimeConfig(@RequestBody List<Integer> ppids, @RequestHeader("xtenant") Long xtenant);


    /**
     * 根据服务ppid获取服务绑定关系
     * @return
     */
    @GetMapping("/api/product/opening/getServices/v1")
    Result<List<ProductServiceOpeningVO>> openingGetServices(@RequestParam("ppids") String ppids, @RequestHeader("xtenant") Integer xtenant);
    /**
     * 获取商品下的服务价格
     */
    @GetMapping("/api/9ji_shield/getServiceList/v1")
    Result<JiuJiServiceVO> get9jiShieldServiceList(@RequestParam("productId") Integer productId, @RequestHeader("xtenant") Integer xtenant);

    /**
     * 获取服务绑定关系
     * @return
     */
    @GetMapping("/api/products/getServices/v1")
    Result<List<ProRelateInfoService>> productsGetServicesV1(@RequestParam("ppriceid") String ppriceid, @RequestHeader("xtenant") Integer xtenant);

    /**
     * 更具商品ppid获取服务绑定关系
     * @return
     */
    @GetMapping("/api/products/listServices/v1")
    Result<Map<Integer, List<ProRelateInfoService>>> listProductServicesV1(@RequestBody List<Integer> ppriceids, @RequestHeader("xtenant") Integer xtenant);
    /**
     * 获取售后预约的时间
     * @param shopId
     * @param xtenant
     * @return
     */
    @GetMapping("/api/afterService/getShopTime/v1")
    Result<List<SelectsVo.SelectVo>> getAfterServiceShopTime(@RequestParam("shopId") Integer shopId, @RequestParam("date") String date
            , @RequestHeader("xtenant") Integer xtenant);

    /**
     * 获取服务绑定关系 新接口
     * @param productId 接件的商品id
     * @param ppriceids 维修配件的id
     * @param xtenant  xtenant
     * @return
     */
    @PostMapping("/api/product/opening/serviceList/{productId}/v1")
    Result<Map<Integer, List<ProRelateInfoService>>> listProductServicesV2(@PathVariable(value = "productId") Integer productId,@RequestBody List<Integer> ppriceids, @RequestHeader("xtenant") Integer xtenant);



    /**
     * 获取服务绑定关系 新接口
     * @param productId 接件的商品id
     * @param ppriceids 维修配件的id
     * @param xtenant  xtenant
     * @return
     */
    @GetMapping("/api/product/opening/getServices/v1?ppids={ppids}")
    Result<List<ProductServiceOpeningVO>> listProductServicesByPpids(@PathVariable("ppids") String ppids);



    /**
     * 获取售后政策
     * @return
     */
    @GetMapping("/api/document/getWarrantyMainInfoList")
    Result<List<WarrantyMainInfoVO>> getWarrantyMainInfoList(@RequestBody List<Integer> ppriceids, @RequestHeader("xtenant") Integer xtenant);


    @GetMapping("/api/afterService/getYuYueDetailByUserId/v1")
    Result<YuYueDetailVO> getYuYueDetailByUserId(@RequestParam("userId") Integer userId, @RequestParam("id") Integer id,  @RequestHeader("xtenant") Integer xtenant);

    /**
     * 自定义短连接
     *
     * @param param
     * @param xtenant
     * @return
     */
    @PostMapping("/api/urlRelevant/generateShortUrl/v2")
    Result<String> generateShortUrl(@RequestBody ShortUrlParam param, @RequestHeader("xtenant") Integer xtenant);

    /**
     * 查询维修类的优惠码
     *
     * @param userId
     * @return
     */
    @GetMapping("/api/youhuima/getRepairMaList/v1")
    Result<YouHuiMaList3VO> getRepairMaList(@RequestParam("userId") Integer userId);

    /**
     * 提交故障订单前,查询需要检测和维修的项目.例如"屏幕故障"-true,"外壳故障"-false,"其他故障"-true
     *
     * @param productId
     * @return
     */
    @GetMapping("/api/afterService/accessories/v1")
    Result<AccessoriesVO> getAccessories(@RequestParam(value = "productId",required = false) Integer productId,
                                         @RequestParam(value = "ppid",required = false) Integer ppid);

    /**
     * 查询售后服务
     * @param productId 主商品id
     * @param ppids 配件ppid
     * @return
     */
    @PostMapping("api/product/opening/serviceList/{productId}/v1")
    Result<Map<Integer,List<ProRelateInfoServiceVO>>> getServiceList(@PathVariable("productId") Integer productId, @RequestBody List<Integer> ppids);

    /**
     * 获取文档详情
     *
     * @param id
     * @return
     */
    @GetMapping("/api/document/detail/v1")
    Result<DocumentVO> getDocumentDetail(@RequestParam("id") Integer id);

    /**
     * 根据电子签章类型获取对应的文档id
     *
     * @param typeId
     * @param xtenant
     * @return
     */
    @GetMapping("/api/document/getDocumentIdByTypeIdAndXtenant/v1")
    Result<ElectronicTypeDocumentVO> getDocumentIdByTypeIdAndXtenant(@RequestParam(value = "typeId" ,required = false) Integer typeId,
                                                                     @RequestParam(value = "xtenant",required = false) Integer xtenant);

    @GetMapping("/api/oa/searchExchangeConfProduct/v1")
    R<List<WebFilmAccessoriesRes>> getFilmAccessories(FilmAccessoriesReq req);
}
