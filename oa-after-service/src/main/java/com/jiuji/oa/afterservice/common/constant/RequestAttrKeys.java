package com.jiuji.oa.afterservice.common.constant;

/**
 * 请求attr的key
 * @author: xie<PERSON>ongkun
 * @date: 2022/4/3
 */
public class RequestAttrKeys {
    /**
     * 错误信息临时存到request attribute中 key
     */
    public static final String REQUEST_ATTR_ERROR = "__request_attr_error__";
    /**
     * 业务日志临时存到request attribute中 key
     */
    public static final String REQUEST_ATTR_BUSINESS_LOG = "__request_attr_business_log__";

    /**
     * 售后业务日志临时存到request attribute中 key
     */
    public static final String REQUEST_ATTR_SHOUHOU_LOG_LAMBDA = "__request_attr_shouhou_log_lambda__";

    /**
     * 用户信息存到request attribute中 key
     */
    public static final String REQUEST_ATTR_OA_USER = "__request_attr_oa_user__";

    /**
     * 售后接件时间存到request attribute中 key
     */
    public static final String AFTER_MODIDATE  = "__request_attr_after_modedate__";

    /**
     * 大件退款传递折价前的金额
     */
    public static final String SHOUHOU_TOTAL_PRICE  = "__request_attr_shouhou_total_price__";

    /**
     * 大件退款传递折价后的金额
     */
    public static final String SHOUHOU_MAX_REFUND_PRICE  = "__request_attr_shouhou_max_refund_price__";

    /**
     * 故障类型
     */
    public static final String FAULT_TYPE  = "__request_attr_fault_type__";
    /**
     * 审核类型
     */
    public static final String CHECK_TYPE  = "__request_attr_check_Type__";

    /**
     * 指定小件最大可退款金额
     */
    public static final String SMALLPRO_MAX_REFUND_PRICE  = "__request_attr_smallpro_max_refund_price__";

    /**
     * 小件贴膜计算折价信息传递
     */
    public static final String SMALLPRO_ORDER_INFO  = "__request_attr_smallpro_order_info__";

    /**
     * 售后预约 客服确认来源
     */
    public static final String SHOUHOU_YUYUE_CONFIRM_SOURCE  = "__request_attr_shouhou_yuyue_confirm_source__";

    /**
     * 没有操作权限信息传递
     */
    public static final String NOT_WORK_RANK  = "__request_attr_not_work_rank__";


    /**
     * 家庭年包串号
     */
    public static final String ANNUAL_PACKAGE_IMEI  = "__request_annual_package_imei__";

    /**
     * 用户级别的文件缓存key 结果为包含参数和用户信息的md5值
     */
    public static final String USER_EXPORT_FILE_CACHE_KEY = "__request_user_level_cache__";


    /**
     * 用户信息的来源保存
     */
    public static final String OA_USER_FROM_SOURCE = "__request_attr_oa_user_from_source__";

    /**
     * 用户级别的文件缓存key 结果为包含参数和用户信息的md5值
     */
    public static final String NOT_NOTICE_USER = "__request_not_notice_user__";

    /**
     * 组合退是否跳过用户验证信息
     */
    public static final String PASS_GROUP_REFUND_IS_NEED_VALID = "__request_attr_pass_group_refund_is_need_valid__";

    /**
     * 发送短信验证的电话号码信息
     */
    public static final String SEND_SMS_PHONE_NUMBER = "__request_attr_send_sms_phone_number___";

    /**
     * 用户级别的文件缓存key 结果为包含参数和用户信息的md5值
     */
    public static final String FASTJSON_REQUEST_BODY = "__request_fastjson_requestbody_obj__";

    /**
     * 当前请求缓存前缀 结合StrUtil.format使用
     */
    public static final String REQUEST_CACHE_PREV = "__request_cache_prev_{}__";

    /**
     * 售后退换信息
     */
    public static final String SHOUHOU_TUIHUAN_INFO = "__request_shouhou_tuihuan_info__";

    /**
     * 组合退 交易完成时间缓存
     */
    public static final String GROUP_REFUND_ORDER_TRADEDATE = "__request_group_refund_order_tradedate__";


    /**
     *
     */
    public static final String GROUP_REFUND_ORDER_BASKET_IDS = "__request_group_refund_order_basket_id__";

    /**
     * 售后接件时间存到request attribute中 key
     */
    public static final String NOW_DATE  = "__request_attr_now_date__";

    /**
     * 获取真实的退款类型request attribute中 key
     */
    public static final String GET_REALTUIHUAN_KIND  = "__request_attr_get_realtuihuan_kind_{}_{}__";
    /**
     * 是否为售后绑定配件
     */
    public static String IS_SHOUHOU_BIND_PEIJIAN = "__request_attr_is_shouhou_bind_peijian__";


    /**
     * 接口响应计时
     */
    public static final String RESPONSE_STOP_WATCH = "__request_response_stop_watch__";


    /**
     * 抖音可退金额, 不抛出异常
     */
    public static final String DOUYIN_SERVICE_CAN_REFUND_PRICE_NOT_EX = "__request_ThirdDouyinService.canRefundPrice_not_ex__";


    /**
     * 日志消息传递
     */
    public static final String LOG_PARAM_NOTICE = "__request_log_param_notice__";
}

