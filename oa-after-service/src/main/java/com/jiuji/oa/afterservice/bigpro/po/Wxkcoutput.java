package com.jiuji.oa.afterservice.bigpro.po;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.cloud.after.enums.BaoXiuTypeEnum;
import com.jiuji.oa.afterservice.lossReview.enums.SpecialQualityAssuranceEnum;
import com.jiuji.oa.afterservice.refund.enums.ZheJiaPayEnum;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 *   1. 维修配件出库时机: 已修好 或 修不好 进行出库 代码位置: com/jiuji/oa/afterservice/bigpro/service/impl/ShouhouServiceImpl.java:2910
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-31
 */
@Data
@TableName("wxkcoutput")
public class Wxkcoutput extends Model<Wxkcoutput> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer wxid;

    private String name;

    private String inuser;

    private LocalDateTime dtime;

    private String area;

    /**
     * 绑定id  为wxkcoutput表的id
     */
    private Integer bindId;

    /**
     * 实际卖出多少钱
     */
    private BigDecimal price;

    /**
     * 卖价
     */
    private BigDecimal price1;

    private Boolean tui;

    private Boolean lock1;

    private LocalDateTime lock1dtime;

    private LocalDateTime tuidtime;

    /**
     * 成本价
     */
    private BigDecimal inprice;

    private Integer ppriceid;

    /**
     * 工时费用
     */
    private BigDecimal priceGs;

    /**
     * @see StatusEnum
     */
    @ApiModelProperty("状态: 0 未审核 1 已审核  2 已结算 3 已撤销")
    private Integer stats;
    /**
     * 退款状态 1 仅退款 2 退货退款
     * @see TuiStatusEnum
     */
    @ApiModelProperty(value = "退款状态 1 仅退款 2 退货退款")
    private Integer tuiStatus;
    /**
     * 分摊优惠费用
     */
    @ApiModelProperty(value = "分摊优惠费用")
    private BigDecimal youhuifeiyong;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundPrice;

    @ApiModelProperty(value = "已退金额")
    private BigDecimal refundedPrice;

    private Integer dianping;

    private Integer areaid;

    private Boolean isna;

    private LocalDateTime natime;

    private Boolean islockkc;

    private Double wxtongjitotal;

    private Boolean isyouhuima;

    @ApiModelProperty(value = "乐捐单号")
    @TableField("punish_sub")
    private String punishSub;

    @ApiModelProperty(value = "乐捐时间")
    @TableField("punish_sub_time")
    private LocalDateTime punishSubTime;

    /**
     * @see SpecialQualityAssuranceEnum
     */
    @ApiModelProperty("特殊质保")
    @TableField("special_quality_assurance")
    private Integer specialQualityAssurance;

    @ApiModelProperty("老维修单号")
    @TableField("old_shouhou_id")
    private Integer oldShouhouId;

    @ApiModelProperty("特殊质保原因")
    @TableField("special_quality_assurance_reason")
    private String specialQualityAssuranceReason;

    private LocalDateTime outputDtime;

    @ApiModelProperty(value = "分摊后优惠码的cardId，优惠码以‘|’隔开")
    private String discountCode;
    @ApiModelProperty(value = "折扣金额")
    private BigDecimal memberDiscountAmount;
    /**
     * @see BaoXiuTypeEnum
     */
    @ApiModelProperty(value = "出险服务类型")
    @TableField("service_type")
    @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
    private Integer serviceType;
    /**
     * shouhou_tuihuan的id
     */
    @TableField("fk_tuihuan_id")
    private Integer fkTuihuanId;


    /**
     * 服务记录id
     */
    @TableField("service_record_id")
    private Integer serviceRecordId;

    /**
     * 配件类型
     * @see PartTypeEnum
     */
    @TableField("part_type")
    private Integer partType;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusEnum implements CodeMessageEnumInterface {
        UNCHECKED(0, "未审核"),
        APPROVED(1, "已审核"),
        SETTLED(2, "已结算"),
        CANCELED(3, "已撤销");

        private final Integer code;
        private final String message;
    }

    /**
     * 退款状态
     */
    @Getter
    @RequiredArgsConstructor
    public enum TuiStatusEnum implements CodeMessageEnumInterface {
        REFUND_ONLY(1, "仅退款"),
        RETURN_AND_REFUND(2, "退货退款");
        @EnumValue
        private final Integer code;
        private final String message;
    }



    /**
     * 退款状态
     */
    @Getter
    @RequiredArgsConstructor
    public enum PartTypeEnum implements CodeMessageEnumInterface {
        ZHE_JIA_PAY(1, "国补折价支付配件", true),
        HUI_SHOU_ZHE_JIA_PAY(2, "回收换新补贴加价支付配件", true),
        NORMAL_ZHE_JIA_PAY(3, "退换折价配件", true),
        NORMAL_PEI_ZHI_ZHE_JIA_PAY(4, "退换附件折价配件", true),
        NORMAL_FA_PIAO_ZHE_JIA_PAY(5, "退换发票折价配件", true),
        ;
        @EnumValue
        private final Integer code;
        private final String message;
        /**
         * 是否为退款单独收费配件
         */
        private final boolean refundPay;

        public static List<Integer> getGuoBuHuiShouAllPayCode(ZheJiaPayEnum zheJiaPayEnum, BigDecimal huiShouPrice) {
            List<PartTypeEnum> result = new LinkedList<>();
            if (zheJiaPayEnum != null) {
                result.add(ZHE_JIA_PAY);
            }
            if (NumberUtil.null2Zero(huiShouPrice).compareTo(BigDecimal.ZERO) > 0) {
                result.add(HUI_SHOU_ZHE_JIA_PAY);
            }
            return result.stream().map(PartTypeEnum::getCode).collect(Collectors.toList());

        }

        public static List<Integer> getAllPayCode(){
            return Stream.concat(Stream.of(ZHE_JIA_PAY, HUI_SHOU_ZHE_JIA_PAY, NORMAL_ZHE_JIA_PAY)
                            .map(PartTypeEnum::getCode), getNormalAllPayCode().stream()).collect(Collectors.toList());
        }

        public static List<Integer> getNormalAllPayCode(){
            return Stream.of(NORMAL_ZHE_JIA_PAY, NORMAL_PEI_ZHI_ZHE_JIA_PAY, NORMAL_FA_PIAO_ZHE_JIA_PAY)
                    .map(PartTypeEnum::getCode).collect(Collectors.toList());
        }
    }

}
