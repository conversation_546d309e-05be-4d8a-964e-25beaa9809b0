package com.jiuji.oa.afterservice.refund.vo.res.machine;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo;
import com.jiuji.oa.afterservice.common.vo.res.ListBean;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiGiftBasketListVo;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 退换机管理详情对象
 * <AUTHOR>
 * @since 2022/11/16 17:22
 */
@Data
@Accessors(chain = true)
@ApiModel("退换机详情实体vo")
public class RefundMachineDetailVo {
    @ApiModelProperty("退换机申请id")
    private Integer tuihuanId;
    @ApiModelProperty("售后id")
    private Integer shouhouId;
    @ApiModelProperty("故障类型")
    private String faultType;
    @ApiModelProperty("审核类型")
    private String checkType;
    @ApiModelProperty("退换类型")
    private Integer tuihuanKind;
    @ApiModelProperty("审核类别")
    private Integer cType;
    /**
     * 以*开头标红
     */
    @ApiModelProperty("注意事项")
    private List<String> notes;
    @ApiModelProperty("购机时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime tradeDate;
    @ApiModelProperty("购机天数")
    private Integer tradeDay;
    @ApiModelProperty("设备类型 1、新机 2、优品 3、良品")
    private Integer tradeType;
    @ApiModelProperty("设备类型描述")
    private String tradeTypeDesc;
    /***
     * sub_buypriceM 已经扣减不可退金额
     */
    @ApiModelProperty("总金额")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal totalRefundPrice;
    /***
     * price1 当时销售价
     */
    @ApiModelProperty("当时销售价")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal originPrice;

    @ApiModelProperty("换其他型号订单冲抵金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal subIdM;
    @ApiModelProperty("总折价金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal zhejiaM;
    @ApiModelProperty("退款折价支付金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal payZhejiaM;
    @ApiModelProperty("九机币")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal coinM;
    @ApiModelProperty("赠品金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal giftPrice;
    @ApiModelProperty("白条金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal baitiaoPrice;
    @ApiModelProperty("酷白条金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal kuBaitiaoPrice;
    @ApiModelProperty("微信红包金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal wxHongBao;
    @ApiModelProperty("已付金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal yifuM;
    /**
     * 单号(交易单号)
     */
    @ApiModelProperty(value = "单号")
    private Integer subId;
    @ApiModelProperty("大件商品id")
    private Integer basketId;
    @ApiModelProperty("所换机型编号")
    private Integer newBasketId;
    @ApiModelProperty("会员编号")
    private Integer userId;
    @ApiModelProperty("备注")
    private String comment;
    /**
     * @see ShouhouRefundDetailVo.ProcessStatus
     */
    @ApiModelProperty("流程状态 1 提交 2 审核1 3 审核2 4 退款办理 5 不可操作")
    private Integer currProcessStatus;
    /**
     * @see ShouhouRefundDetailVo.ProcessStatus
     */
    @ApiModelProperty("流程状态 1 提交 2 审核1 3 审核2 4 退款办理 5 不可操作")
    private Integer processStatus;
    @ApiModelProperty("流程名称")
    private String processName;
    @ApiModelProperty("审核记录")
    private List<ShouhouRefundDetailVo.CheckHistoryVo> checkHistorys;
    @ApiModelProperty("是否可以撤销 true可以 false不可以")
    private Boolean isCanCancel;
    @ApiModelProperty("是否需要验证")
    private Boolean isNeedValid;
    @ApiModelProperty("售后折价次数")
    private Integer afterServicesDisCount;
    @ApiModelProperty("换货信息实体类")
    private HuanInfoVo huanInfo;
    /**
     * 组合退款的信息对象
     */
    @ApiModelProperty("组合退款的信息对象")
    private RefundMoneyDetailVo refundMoneyDetailVo;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String peizhi;
    @ApiModelProperty("附件折价金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal peizhiPrice;
    @ApiModelProperty("附件折价金额")
    @JSONField(format = "0.00@DOWN")
    @DecimalMin(value = "0.00", message = "附件折价金额不能小于0")
    private BigDecimal payPeizhiPrice;
    @ApiModelProperty("发票折价金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal piaoPrice;
    @ApiModelProperty("支付的发票折价金额")
    @JSONField(format = "0.00@UP")
    private BigDecimal payPiaoPrice;
    @ApiModelProperty("实际可退金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal refundPrice;
    @ApiModelProperty("退款金额")
    @JSONField(format = "0.00@HALF_UP")
    private BigDecimal tuikuanM;
    @ApiModelProperty("商品的成本")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal inprice;
    /**
     * @see com.jiuji.oa.afterservice.bigpro.enums.ShouhouPiaoTypeEnum
     * 发票类别 1、普票(纸质发票) 2、专票 3、普票(电子发票)
     */
    private Integer piaoType;
    /**
     * 发票类别描述
     */
    private String piaoTypeDesc;
    /**
     * 发票收回 1 已收回 2 未收回
     * @see com.jiuji.oa.afterservice.refund.enums.PiaoInfoEnum
     */
    @ApiModelProperty("发票收回")
    private Integer piaoInfo;
    @ApiModelProperty(value = "发票绑定微信openid")
    private String fpOpenid;
    @ApiModelProperty(value = "发票支付状态")
    private Boolean fpPayState;
    @ApiModelProperty(value = "发票支付时间")
    private LocalDateTime fpPayTime;
    @ApiModelProperty("赠品配件")
    private List<TuiGiftBasket> giftBasketList;


    /**
     * 良品附件
     */
    @ApiModelProperty("良品附件")
    private List<TuiGiftBasketListVo> goodProductAccessoriesList;


    /**
     * 故障类型选项列表
     */
    @ApiModelProperty("故障类型选项列表")
    private ListBean faultTypeList;
    /**
     * 审核类别选项列表
     */
    @ApiModelProperty("审核类别选项列表")
    private ListBean cTypeList;
    /**
     * 发票收回 "" 发票收回 1 已收回 2 未收回
     */
    @ApiModelProperty("发票收回")
    private ListBean piaoInfoList;
    @ApiModelProperty("售后门店id")
    private Integer areaId;
    @ApiModelProperty("售后门店编码")
    private String area;
    @ApiModelProperty("售后租户编码")
    private Integer xtenant;

    @ApiModelProperty("是否显示组合退明细")
    private Boolean isShowGroupDetail;

    /**内部流转字段 start*/
    /**
     * 售后信息
     */
    @ApiModelProperty(value = "售后信息",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private Shouhou shouhou;
    /**
     * 退换信息
     */
    @ApiModelProperty(value = "售后信息",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private ShouhouTuiHuanPo tuihuan;
    /**
     * 售后门店信息
     */
    @ApiModelProperty(value = "售后门店信息",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private AreaInfo shouhouAreaInfo;
    /**内部流转字段 end*/

    @ApiModelProperty("故障类型")
    public static final String FAULT_TYPE_KEY="faultType";
    @ApiModelProperty("发票收回类型")
    public static final String PIAOINFO_TYPE_KEY="piaoInfoType";
    @ApiModelProperty("审核类型")
    public static final String CHECK_TYPE_KEY="checkType";
    @ApiModelProperty("退款类型")
    public static final String TUIHUAN_KIND_KEY="tuihuanKind";
    @ApiModelProperty("审核类别")
    public static final String C_TYPE_KEY="cType";
}
