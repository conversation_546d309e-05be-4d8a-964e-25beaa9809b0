package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlan;

/**
 * <p>
 * 维修方案表 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
public interface RepairPlanService extends IService<RepairPlan> {

    /**
     * 取消绑定
     */
    void cancelBind(Integer correlationId, Integer correlationType,String userName);

    /**
     * 取消关联信息
     * @param plan
     */
    void cancelCorrelationInfo(Integer planId);

} 