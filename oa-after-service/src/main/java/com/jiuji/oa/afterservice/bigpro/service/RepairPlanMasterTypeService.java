package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.dto.PlanTypeParam;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlanMasterType;
import com.jiuji.oa.afterservice.bigpro.vo.RepairPlanMasterTypeVO;

import java.util.List;

/**
 * <p>
 * 维修方案主表类型 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
public interface RepairPlanMasterTypeService extends IService<RepairPlanMasterType> {

    /**
     * 根据主表ID查询维修方案主表类型
     *
     * @param masterId 主表ID
     * @return 维修方案主表类型
     */
    List<RepairPlanMasterTypeVO> getByMasterId(Integer masterId);


    String saveBatchByParam(PlanTypeParam param);

}
