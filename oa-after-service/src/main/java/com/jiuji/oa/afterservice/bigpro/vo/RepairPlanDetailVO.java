package com.jiuji.oa.afterservice.bigpro.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 维修方案详情VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@ApiModel(value = "RepairPlanDetailVO对象", description = "维修方案详情VO")
public class RepairPlanDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "售后单ID")
    private Integer shouHouId;

    /**
     * 类型
     */
    private List<RepairPlanMasterTypeVO> planTypeList;

    @ApiModelProperty(value = "确认人")
    private String confirmUser;

    /**
     * @see com.jiuji.oa.afterservice.bigpro.enums.RepairPlanMasterStatusEnum
     */
    private Integer status;

    @ApiModelProperty(value = "确认时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmTime;

    @ApiModelProperty(value = "维修故障列表")
    private List<RepairFaultVO> faultList;
} 