package com.jiuji.oa.afterservice.bigpro.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 关联id类型枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Getter
@AllArgsConstructor
public enum CorrelationTypeEnum {
    
    /**
     * wxkcoutput 表的id
     */
    WXKCOUTPUT(1, "wxkcoutput表"),
    
    /**
     * shouhou_apply 表的id
     */
    SHOUHOU_APPLY(2, "shouhou_apply表");
    
    private final Integer code;
    private final String desc;
    
    /**
     * 根据code获取枚举
     * @param code 代码
     * @return 枚举
     */
    public static CorrelationTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CorrelationTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取描述
     * @param code 代码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        CorrelationTypeEnum enumItem = getByCode(code);
        return enumItem == null ? "" : enumItem.getDesc();
    }
    
    /**
     * 获取所有枚举值的列表
     * @return 枚举值列表
     */
    public static Map<Integer, String> getAll() {
        Map<Integer, String> map = new HashMap<>();
        for (CorrelationTypeEnum item : values()) {
            map.put(item.getCode(), item.getDesc());
        }
        return map;
    }
}
