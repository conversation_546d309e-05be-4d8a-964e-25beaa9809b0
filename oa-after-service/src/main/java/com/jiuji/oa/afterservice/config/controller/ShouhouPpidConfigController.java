package com.jiuji.oa.afterservice.config.controller;


import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.config.service.ShouhouPpidConfigService;
import com.jiuji.oa.afterservice.config.vo.req.ShouhouPpidConfigQueryReq;
import com.jiuji.oa.afterservice.config.vo.req.ShouhouPpidOutPutAddReq;
import com.jiuji.oa.afterservice.config.vo.res.ShouhouPpidBindVo;
import com.jiuji.oa.afterservice.config.vo.res.ShouhouPpidOutPutConfig;
import com.jiuji.oa.afterservice.config.vo.res.XtenantInfo;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@RestController
@Api(tags = "售后：维修配件出库绑定功能")
@RequestMapping("/api/shouhou/ppid/config")
public class ShouhouPpidConfigController {
    @Autowired
    private ShouhouPpidConfigService shouhouPpidConfigService;
    @Autowired
    private AbstractCurrentRequestComponent currentRequestComponent;

    @ApiOperation(value = "获取 配件出入库绑定列表", notes = "维修配件出库绑定功能", httpMethod = "POST",response = ShouhouPpidOutPutConfig.class)
    @PostMapping("/getShouhouPpidOutPutBindConfigList")
    public R<Page<ShouhouPpidOutPutConfig>> getShouhouPpidConfigPage(@RequestBody ShouhouPpidConfigQueryReq req){
        return shouhouPpidConfigService.getShouhouPpidConfigPage(req);
    }

    @ApiOperation(value = "保存修改接口", notes = "维修配件出库绑定功能", httpMethod = "POST")
    @PostMapping("/saveOrUpdateBindInfo")
    public R<Boolean> saveOrUpdateBindInfo(@Validated @RequestBody ShouhouPpidOutPutAddReq req){
        return shouhouPpidConfigService.saveOrUpdateBindInfo(req);
    }

    @ApiOperation(value = "获取绑定配件信息",notes = "维修详情页调用",httpMethod = "GET")
    @GetMapping("/getBindPpidInfo")
    public R<List<ShouhouPpidBindVo>> getBindPpidInfo(@RequestParam Integer ppid){
        OaUserBO userBo = currentRequestComponent.getCurrentStaffId();
        return shouhouPpidConfigService.getBindPpidInfo(ppid,userBo.getXTenant(),userBo.getAreaId());
    }

    @ApiOperation(value = "获取租户信息",notes = "获取租户信息",httpMethod = "GET")
    @GetMapping("/getXtenantInfo")
    public R<List<XtenantInfo>> getXtenantInfo(){
        return R.success(shouhouPpidConfigService.getXtenantInfo());
    }

    @PostMapping("/importExcelBindPpid")
    @ApiOperation("批量导入供应商")
    @SneakyThrows
    public R importExcelBindPpid(MultipartFile file) {
        return shouhouPpidConfigService.importExcelBindPpid(file.getInputStream());
    }

}

