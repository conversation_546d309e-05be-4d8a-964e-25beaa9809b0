package com.jiuji.oa.afterservice.bigpro.dto;

import com.jiuji.oa.afterservice.bigpro.vo.RepairPlanMasterTypeVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 维修方案类型处理参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlanTypeParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 方案类型列表
     */
    private List<RepairPlanMasterTypeVO> planTypeList;
    
    /**
     * 主表ID
     */
    private Integer masterId;
    
    /**
     * 当前用户
     */
    private String currentUser;
    
    /**
     * 当前时间
     */
    private LocalDateTime now;

    /**
     * 售后单ID
     */
    private Integer shouHouId;

    private boolean isNew;
} 