package com.jiuji.oa.afterservice.bigpro.dto;

import com.jiuji.oa.afterservice.bigpro.po.RepairFault;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 处理故障的参数类
 */
@Data
public class FaultParam {
    private String currentUser;
    private LocalDateTime now;
    private Integer shouHouId;
    private Map<Integer, RepairFault> existingFaultMap;

    public FaultParam(String currentUser, LocalDateTime now, Integer shouHouId, 
                Map<Integer, RepairFault> existingFaultMap) {
        this.currentUser = currentUser;
        this.now = now;
        this.shouHouId = shouHouId;
        this.existingFaultMap = existingFaultMap;
    }


} 