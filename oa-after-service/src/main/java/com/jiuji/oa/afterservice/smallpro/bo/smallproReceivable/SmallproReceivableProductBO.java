package com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * description: <可接件小件商品信息BO>
 * translation: <Accessible small product information BO>
 *
 * <AUTHOR>
 * @date 2019/12/2
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproReceivableProductBO implements Serializable, ISmallproProduct {

    private static final long serialVersionUID = 3555144395159854160L;
    /**
     * 条目Id
     */
    private Integer basketId;
    /**
     * 商品SKU Id
     */
    private Integer ppriceId;
    /**
     * 商品绑定Ppid
     */
    private Integer targetPpriceId;
    /**
     * 联系人Id
     */
    private Integer userId;
    /**
     * 购买时间
     */
    private LocalDateTime tradeDate;
    /**
     * 联系人姓名
     */
    private String userName;
    /**
     * 联系人电话
     */
    private String mobile;
    /**
     * 联系人真实姓名
     */
    private String realName;
    /**
     * 是否为大件
     */
    private Integer isMobile;
    /**
     * 商品Id
     */
    private Integer productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品尺寸
     */
    private String productColor;
    /**
     * 条目数量
     */
    private Integer basketCount;
    /**
     * 订单条目价格
     */
    private BigDecimal price;
    /**
     * 商品原价格
     */
    private BigDecimal memberPrice;
    /**
     * 分类Id
     */
    private Integer cid;
    /**
     * 是否为钢化膜
     */
    private Integer isTemperedFilm;
    /**
     * 商品串号
     */
    private String barCode;
    /**
     * 是否绑定SN码
     */
    private Boolean isSn;

    /**
     * 商品类型
     */
    private Integer basketType;

    /**
     * 贴膜id
     */
    private Integer tieMoId;

    private Integer useCount;

}
