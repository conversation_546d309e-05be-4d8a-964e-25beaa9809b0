package com.jiuji.oa.afterservice.bigpro.service.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.jiuji.oa.afterservice.bigpro.bo.WxFeeBo;
import com.jiuji.oa.afterservice.bigpro.enums.ConfirmSourceEnum;
import com.jiuji.oa.afterservice.bigpro.po.RepairFault;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlan;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlanMaster;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.service.strategy.RepairPlanConfirmStrategy;
import com.jiuji.oa.afterservice.bigpro.vo.ClearRepairPlanAccessoriesParam;
import com.jiuji.oa.afterservice.bigpro.vo.req.CorrelationInfo;
import com.jiuji.oa.afterservice.bigpro.vo.req.CostPriceNewReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.RepairPlanConfirmReq;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 大疆维修策略实现
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Service
public class DJIRepairStrategy extends AbstractSimpleRepairStrategy implements RepairPlanConfirmStrategy {

    @Resource
    private RepairPlanService repairPlanService;

    @Resource
    private RepairPlanMasterService repairPlanMasterService;

    @Resource
    private ShouhouService shouhouService;

    @Resource
    private RepairAccessoriesService repairService;

    @Resource
    private RepairFaultService repairFaultService;

    @Resource
    private ProductinfoService productinfoService;

    @Resource
    private ShouHouPjService shouHouPjService;
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer confirm(RepairPlanMaster master, RepairPlanConfirmReq req) {
        Integer shouHouId = req.getShouHouId();
        String confirmUser = req.getConfirmUser();
        List<Integer> planIds = req.getPlanIds();
        //校验必填
        checkPlan(master, planIds);
        // 更新维修方案主表
        updateRepairPlanMaster(master, confirmUser);
        // 更新维修方案主表类型表
        updateRepairPlanMasterType(req.getRepairPlanMasterTypeId());
        // 查询维修方案列表
        if (CollUtil.isEmpty(planIds)) {
            return master.getId();
        }
        List<RepairPlan> repairPlans = repairPlanService.lambdaQuery().in(RepairPlan::getId, planIds).list();
        // 清除与当前故障相关的其他方案的配件关联
        ClearRepairPlanAccessoriesParam param = new ClearRepairPlanAccessoriesParam();
        param.setRepairPlans(repairPlans);
        param.setPlanIds(planIds);
        param.setShouHouId(shouHouId);
        param.setOperateUser(confirmUser);
        clearOtherRepairPlanAccessories(param);
        // 添加维修配件
        List<CorrelationInfo> correlationInfos = addWxFeeBos(repairPlans, shouHouId);

        // 更新维修方案主表
        updateRepairPlanMaster(master, confirmUser);
        // 更新维修方案主表类型表
        updateRepairPlanMasterType(req.getRepairPlanMasterTypeId());
        // 更新维修方案并记录日志
        correlationInfos.forEach(item -> {
            repairPlans.stream()
                .filter(plan -> ObjectUtil.equal(plan.getId(), item.getRepairPlanId()))
                .findFirst()
                .ifPresent(plan -> updateRepairPlan(plan, item.getCorrelationId(),item.getCorrelationType(), confirmUser, shouHouId));
        });
        
        return master.getId();
    }

    /**
     * 校验必填选项
     * @param master
     * @param planIds
     */
    private void checkPlan(RepairPlanMaster master, List<Integer> planIds) {
        // 查询所有故障项（必选和非必选）
        List<RepairFault> allFaults = repairFaultService.lambdaQuery()
                .eq(RepairFault::getMasterId, master.getId())
                .list();

        if (CollUtil.isEmpty(allFaults)) {
            return;
        }

        // 按照是否必选进行分组
        Map<Integer, List<RepairFault>> faultsByRequired = allFaults.stream()
                .collect(Collectors.groupingBy(RepairFault::getIsRequired));

        // 获取所有故障ID
        List<Integer> allFaultIds = allFaults.stream()
                .map(RepairFault::getId)
                .collect(Collectors.toList());

        // 查询所有故障的维修方案
        List<RepairPlan> allPlans = CollUtil.isEmpty(allFaultIds) ? new ArrayList<>() :
                repairPlanService.lambdaQuery()
                        .in(RepairPlan::getFaultId, allFaultIds)
                        .list();

        if (CollUtil.isEmpty(allPlans)) {
            return;
        }

        // 按照故障ID分组
        Map<Integer, List<RepairPlan>> plansByFault = allPlans.stream()
                .collect(Collectors.groupingBy(RepairPlan::getFaultId));

        // 处理必选故障项 (isRequired=1)
        List<RepairFault> requiredFaults = faultsByRequired.getOrDefault(NumberConstant.ONE, Collections.emptyList());
        for (RepairFault fault : requiredFaults) {
            checkFaultPlan(fault, plansByFault, planIds, true);
        }

        // 处理非必选故障项 (isRequired=0)
        List<RepairFault> optionalFaults = faultsByRequired.getOrDefault(NumberConstant.ZERO, Collections.emptyList());
        for (RepairFault fault : optionalFaults) {
            checkFaultPlan(fault, plansByFault, planIds, false);
        }
    }

    /**
     * 检查故障的维修方案选择是否符合规则
     * @param fault 故障项
     * @param plansByFault 按故障ID分组的维修方案
     * @param selectedPlanIds 已选择的维修方案ID列表
     * @param isRequired 是否为必选故障
     */
    private void checkFaultPlan(RepairFault fault, Map<Integer, List<RepairPlan>> plansByFault,
                                List<Integer> selectedPlanIds, boolean isRequired) {
        Integer faultId = fault.getId();
        List<RepairPlan> faultPlans = plansByFault.get(faultId);

        if (CollUtil.isEmpty(faultPlans)) {
            // 如果该故障没有任何可用方案，直接跳过
            return;
        }

        // 检查该故障下选中的方案数量
        int selectedCount = Convert.toInt(faultPlans.stream()
                .filter(plan -> selectedPlanIds.contains(plan.getId()))
                .count());

        String faultName = fault.getFaultName();

        if (isRequired) {
            // 必选故障：必须选择且只能选择一个方案
            if (selectedCount == 0) {
                String format = String.format("必填故障 %s 需要选择一个维修方案", faultName);
                throw new CustomizeException(format);
            } else if (selectedCount > 1) {
                String format = String.format("必填故障 %s 只能选择一个维修方案", faultName);
                throw new CustomizeException(format);
            }
        } else {
            // 非必选故障：可以不选，但如果选了，只能选一个
            if (selectedCount > 1) {
                String format = String.format("非必填故障 %s 最多只能选择一个维修方案", faultName);
                throw new CustomizeException(format);
            }
        }
    }


    /**
     * 更新维修方案并记录日志
     * 
     * @param plan 维修方案
     * @param correlationId 维修配件输出ID
     * @param confirmUser 确认用户
     * @param shouHouId 售后单ID
     */
    private void updateRepairPlan(RepairPlan plan, Integer correlationId,Integer correlationType, String confirmUser, Integer shouHouId) {
        boolean updateRepairPlan = repairPlanService.lambdaUpdate().eq(RepairPlan::getId, plan.getId())
                .set(RepairPlan::getCorrelationId, correlationId)
                .set(RepairPlan::getConfirmSource, ConfirmSourceEnum.USER.getCode())
                .set(RepairPlan::getCorrelationType, correlationType)
                .set(RepairPlan::getUpdateUser, confirmUser)
                .set(RepairPlan::getUpdateTime, LocalDateTime.now())
                .update();
        if (!updateRepairPlan) {
            throw new CustomizeException("更新维修方案信息失败");
        }
        shouhouService.saveShouhouLog(shouHouId, "确认维修方案：" + plan.getPlanName() + "，确认人：" + "用户", "用户");
    }

    /**
     * 清除与当前故障相关的其他方案的配件关联
     *
     * @param param 清除维修方案配件关联的参数
     */
    private void clearOtherRepairPlanAccessories(ClearRepairPlanAccessoriesParam param) {
        if (ObjectUtil.isNull(param) || CollUtil.isEmpty(param.getRepairPlans())) {
            return;
        }

        // 获取当前维修方案关联的故障ID列表
        List<Integer> faultIds = param.getRepairPlans().stream()
                .map(RepairPlan::getFaultId)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(faultIds)) {
            return;
        }

        // 直接查询非当前选中但wxKcOutPutId不为空的方案
        List<RepairPlan> existingConfiguredPlans = repairPlanService.lambdaQuery()
                .in(RepairPlan::getFaultId, faultIds)
                .isNotNull(RepairPlan::getCorrelationId)
                .notIn(CollUtil.isNotEmpty(param.getPlanIds()), RepairPlan::getId, param.getPlanIds())
                .list();

        if (CollUtil.isEmpty(existingConfiguredPlans)) {
            return;
        }

        // 清除已存在的配件关联并记录日志
        for (RepairPlan plan : existingConfiguredPlans) {
            // 保存原来的correlationId，避免空指针
            Integer originalCorrelationId = plan.getCorrelationId();

            // 清除wxKcOutPutId
            boolean updated = repairPlanService.lambdaUpdate()
                    .eq(RepairPlan::getId, plan.getId())
                    .set(RepairPlan::getCorrelationId, null)
                    .set(RepairPlan::getUpdateUser, param.getOperateUser())
                    .set(RepairPlan::getUpdateTime, LocalDateTime.now())
                    .update();

            if (!updated) {
                throw new CustomizeException("清除旧维修方案配件关联失败");
            } else {
                // 记录日志
                shouhouService.saveShouhouLog(param.getShouHouId(), "清除旧维修方案配件关联：" + plan.getPlanName(), param.getOperateUser());
                //进行维修配件撤销，使用保存的原始correlationId
                if (ObjectUtil.isNotNull(originalCorrelationId)) {
                    R<Boolean> tui = shouHouPjService.editWxPj(param.getShouHouId(), "tui", "", originalCorrelationId, null, null, null, 0);
                    if(!tui.isSuccess()){
                        throw new CustomizeException("进行维修配件撤销失败:"+Optional.ofNullable(tui.getUserMsg()).orElse(tui.getMsg()));
                    }
                }
            }
        }
    }
}
