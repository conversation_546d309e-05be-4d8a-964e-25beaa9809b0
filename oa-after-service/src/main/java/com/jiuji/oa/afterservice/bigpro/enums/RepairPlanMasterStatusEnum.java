package com.jiuji.oa.afterservice.bigpro.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 维修方案类型枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Getter
@AllArgsConstructor
public enum RepairPlanMasterStatusEnum {
    
    /**
     * 以旧换新
     */
    TO_BE_CONFIRMED(0, "待确认"),
    
    /**
     * 以换代修
     */
    USER_CONFIRMATION(1, "用户确认");
    
    private final Integer code;
    private final String desc;
    
    /**
     * 根据code获取枚举
     * @param code 代码
     * @return 枚举
     */
    public static RepairPlanMasterStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RepairPlanMasterStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取描述
     * @param code 代码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        RepairPlanMasterStatusEnum enumItem = getByCode(code);
        return enumItem == null ? "" : enumItem.getDesc();
    }
    
    /**
     * 获取所有枚举值的列表
     * @return 枚举值列表
     */
    public static Map<Integer, String> getAll() {
        Map<Integer, String> map = new HashMap<>();
        for (RepairPlanMasterStatusEnum item : values()) {
            map.put(item.getCode(), item.getDesc());
        }
        return map;
    }
} 