package com.jiuji.oa.afterservice.csharp.returns.service;

import com.jiuji.oa.afterservice.csharp.returns.vo.*;
import com.jiuji.tc.common.vo.R;

import javax.validation.Valid;
import java.util.List;

/**
 * c# 退款服务接口
 * <AUTHOR>
 * @since 2022/7/20 17:35
 */
public interface CsharpReturnService {
    /**
     * 获取符合条件的退款方式列表
     * @param returnWayReq
     * @return
     */
    R<List<String>> getReturnWayJava(@Valid ReturnWayReqVo returnWayReq);

    /**
     * 取消diy(洛克订单)
     * @return
     */
    R<Boolean> cancelDiyOrder(CancelDiyReq cancelDiyReq);

    /**
     * 取消wms订单
     * @return
     */
    R<Boolean> cancelWmsOrder(CancelWmsReq cancelWmsReq);

    /**
     * 查询微信支付状态
     * @param refundResultReq
     * @return {@link }
     */
    R<WechatPayResultRes> queryMiaoTuiResultTips(WechatPayResultReq refundResultReq);

    /**
     * 重新发起微信支付
     * @param retryPayReq
     * @return {@link }
     */
    R<Boolean> retryMiaoTuiPay(WechatRetryPayReq retryPayReq);

    R<Boolean> tuihuanDel(Integer tuiHuanId,String marck);

    /**
     * 退换机管理审核1
     * @param tuiHuanId
     */
    R<Integer> refundMachineCheck1(Integer tuiHuanId);

    /**
     * 退换机管理审核2
     * @param tuiHuanId
     * @param tuiHuanKind
     * @return
     */
    R<Integer> refundMachineCheck2(Integer tuiHuanId, Integer tuiHuanKind);

    /**
     * 退换机办理
     * @param tuiHuanId
     * @param newBasketId
     * @return
     */
    R<Integer> refundMachineTransact(Integer tuiHuanId, Integer newBasketId,TransactVo transactVo);
}
