package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.dao.ShouHouPjMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouQudaoMapper;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouInfoReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouJieJianQueryReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouQuDaoBatchWaiSongReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouInfoRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouJieJianInfo;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.tc.utils.common.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-11
 */
@Service
public class ShouhouQudaoServiceImpl extends ServiceImpl<ShouhouQudaoMapper, ShouhouQudao> implements ShouhouQudaoService {

    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Lazy
    @Autowired
    private ShouhouMsgService shouhouMsgService;
    @Autowired
    private ShouHouPjMapper shouHouPjMapper;
    @Autowired
    private NumberCardService numberCardService;
    @Autowired
    private CardLogsService cardLogsService;
    @Lazy
    @Autowired
    private ShouhouService shouhouService;
    @Lazy
    @Autowired
    private RepairService repairService;
    @Autowired
    private Ok3wQudaoService ok3wQudaoService;
    @Autowired
    private ShouhouTimerService shouhouTimerService;
    @Autowired
    private ShouhouToareaService shouhouToareaService;
    @Resource
    private WxkcoutputService wxkcoutputService;


    @Override
    public ShouhouQudaoTelBo getShouhouQudaoTel(Integer shouhouId) {
        if (shouhouId == null || shouhouId == 0) {
            return null;
        }
        return baseMapper.getShouhouQudaoTel(shouhouId);
    }

    @Override
    public ShouhouQudao getShouhouQudaoByShouhouId(Integer shouhouId) {
        return baseMapper.selectList(new LambdaQueryWrapper<ShouhouQudao>().eq(ShouhouQudao::getShouhouid, shouhouId)).stream().findFirst().orElse(null);
    }

    @Override
    public ShouhouQudao loadOrCreateShouhouQudao(Shouhou sh, ShouhouInfoReq fm, List<ShouhouLogNewBo> shLogs) {
        Integer shouhouId = sh.getId();
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        ShouhouInfoRes.ShouhouQudao qudaoInfo = fm.getQudaoInfo();

        List<ShouhouQudao> qudaoList = super.list(new LambdaQueryWrapper<ShouhouQudao>().eq(ShouhouQudao::getShouhouid, shouhouId));
        ShouhouQudao qudaoEntity = null;
        if (CollectionUtils.isNotEmpty(qudaoList)) {
            qudaoEntity = qudaoList.get(0);
        }
        if (qudaoInfo == null || StringUtils.isEmpty(qudaoInfo.getShqd2Name()) || qudaoInfo.getShqd2Id() == null) {
            if (CollectionUtils.isNotEmpty(qudaoList)) {
                super.removeById(qudaoList.get(0).getId());
            }
            return null;
        }
        if (qudaoEntity == null) {
            qudaoEntity = new ShouhouQudao();
            qudaoEntity.setInuser(oaUserBO.getUserName());
            qudaoEntity.setDtime(LocalDateTime.now());
            qudaoEntity.setShouhouid(shouhouId);
            qudaoEntity.setStarttime(sh.getModidate() == null ? LocalDateTime.now() : sh.getModidate());
            qudaoEntity.setEndtime(LocalDateTime.now().plusYears(100));

            if (StringUtils.isNotEmpty(qudaoInfo.getShqd2Name()) && !qudaoInfo.getShqd2Name().equals(qudaoEntity.getShqd2name())) {
                shouhouMsgService.sendCollectMsg(shouhouId, "已外送到【" + qudaoInfo.getShqd2Name() + "】进行维修", oaUserBO.getUserName());
            }

            //如果使用了优惠码，优惠码返回
            if (sh.getYouhuifeiyong() != null && sh.getYouhuifeiyong().compareTo(BigDecimal.ZERO) > 0) {
                //使用过优惠码，则返回优惠码
                List<Integer> cardLogIds = shouHouPjMapper.getCardIdfromCardLogsByWxId(shouhouId, 3, null);
                if (CollUtil.isNotEmpty(cardLogIds)){
                    for (Integer cardLogId : cardLogIds) {
                        if (CommenUtil.isNotNullZero(cardLogId)) {
                            boolean isCheckManual = false;
                            if (XtenantEnum.isJiujiXtenant()){
                                //校验是否存在手工费 存在手工费，返还优惠码  否则直接添加外送渠道
                                //获取当前维修单所有配件信息
                                List<HexiaoBo> hexiaoBoList = wxkcoutputService.getHexiao(sh.getId());
                                //为true的话 在维修中存在手工费
                                if (CollUtil.isNotEmpty(hexiaoBoList)) {
                                    isCheckManual = hexiaoBoList.stream().anyMatch(hexiaoBo -> CommenUtil.isNullOrZero(hexiaoBo.getPpid()));
                                    if (!isCheckManual){
                                        continue;
                                    }
                                }
                            }
                            numberCardService.update(new LambdaUpdateWrapper<NumberCard>().setSql("use_count=use_count-1,State=0").eq(NumberCard::getId, cardLogId));
                            cardLogsService.remove(new QueryWrapper<CardLogs>().lambda()
                                    .eq(CardLogs::getCardid, cardLogId).eq(CardLogs::getUseType, 1));
                            shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getYouhuima, null).set(Shouhou::getYouhuifeiyong, 0).eq(Shouhou::getId, shouhouId));
                            ShouhouLogNewBo logs = new ShouhouLogNewBo();
                            logs.setShouhouId(shouhouId);
                            logs.setComment("优惠码仅限自修使用，使用的优惠码已返还给客户");
                            if (isCheckManual) {
                                logs.setComment("取消优惠券使用，原因：维修单外送手工费不能使用优惠券");
                            }
                            logs.setInuser(oaUserBO.getUserName());
                            logs.setIsWeb(true);
                            shLogs.add(logs);
                        } else {
                            Integer yuyueId = sh.getYuyueid();
                            if (CommenUtil.isNotNullZero(yuyueId)) {
                                cardLogIds = shouHouPjMapper.getCardIdfromCardLogsByWxId(sh.getYuyueid(), 4, null);
                                if (CollUtil.isNotEmpty(cardLogIds)){
                                    for (Integer logId : cardLogIds) {
                                        if (CommenUtil.isNotNullZero(logId)) {
                                            numberCardService.update(new LambdaUpdateWrapper<NumberCard>().setSql("use_count=use_count-1,State=0").eq(NumberCard::getId, logId));
                                            cardLogsService.remove(new QueryWrapper<CardLogs>().lambda()
                                                    .eq(CardLogs::getCardid, logId).eq(CardLogs::getUseType, 1));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                //撤销优惠码以后更新维修费
                repairService.updateShouhouFeiyong(shouhouId);
            }
        }
        //撤销折扣,由折扣那边保证数据的一致性
        SpringUtil.getBean(ShouhouMemberDiscountService.class).cancelDiscount(shouhouId,null);

        Ok3wQudao qudao = ok3wQudaoService.getById(qudaoInfo.getShqd2Id());
        if (!qudaoInfo.getShqd2Name().equals(qudaoEntity.getShqd2name()) && qudao != null) {
            ShouhouMsgPushMessageBo message = new ShouhouMsgPushMessageBo();
            message.setMsgId(21);
            message.setShouhouId(shouhouId);
            message.setAreaId(sh.getAreaid() == null ? 0 : sh.getAreaid());
            message.setUserId(sh.getUserid() == null ? 0 : sh.getUserid().intValue());
            message.setOptUser(oaUserBO.getUserName());
            Map<String, String> tmpData = new HashMap<>();
            tmpData.put("qudaoname", qudao.getCompanyJc());

            message.setTmpData(tmpData);
            shouhouService.pushMessage(message, true);

            if (sh.getModidate() != null && !sh.getModidate().isEqual(LocalDateTime.MIN)) {
                ShouhouTimer shouhouTimer = new ShouhouTimer();
                shouhouTimer.setShouhouid(shouhouId);
                Duration duration = Duration.between(LocalDateTime.now(), sh.getModidate());
                shouhouTimer.setChuli((int) Math.abs(duration.toMinutes()));

                shouhouTimerService.addShouhouTimer(shouhouTimer);

                LocalDateTime dateDiffTime = sh.getModidate();

                Integer toareaCount = shouhouToareaService.count(new LambdaQueryWrapper<ShouhouToarea>().eq(ShouhouToarea::getShouhouId, shouhouId));
                if (CommenUtil.isNotNullZero(toareaCount)) {
                    Integer toareaId = CommenUtil.isNotNullZero(sh.getToareaid()) ? sh.getToareaid() : sh.getAreaid() == null ? 0 : sh.getAreaid();
                    List<ShouhouToarea> toareaList = shouhouToareaService.list(new LambdaQueryWrapper<ShouhouToarea>().eq(ShouhouToarea::getShouhouId, shouhouId).eq(ShouhouToarea::getToareaid, toareaId).orderByDesc(ShouhouToarea::getId));
                    if (CollectionUtils.isNotEmpty(toareaList)) {
                        LocalDateTime checktTime = toareaList.get(0).getCheckdtime();
                        if (checktTime != null) {
                            dateDiffTime = checktTime;
                        }
                    }
                }

                shouhouTimer = new ShouhouTimer();
                shouhouTimer.setShouhouid(shouhouId);
                shouhouTimer.setWaisongqd((int) Duration.between(dateDiffTime, LocalDateTime.now()).toMinutes());
                shouhouTimerService.addShouhouTimer(shouhouTimer);
            }
        }

        if (CommenUtil.isNotNullZero(qudaoInfo.getShqdId())) {
            qudaoEntity.setShqdid(qudaoInfo.getShqdId());
        } else {
            if (qudao != null) {
                qudaoEntity.setShqdid(qudao.getInsourceid());
            }
        }

        qudaoEntity.setShqd2id(qudaoInfo.getShqd2Id());
        qudaoEntity.setShqd2name(qudaoInfo.getShqd2Name());
        Integer type = 0;
        if (Objects.equals(sh.getUserid(), 76783L)) {
            type |= 1 << 1;
        } else {
            type |= 1;
        }

        qudaoEntity.setEndtime(LocalDateTime.now().plusYears(100));

        if (CommenUtil.isNullOrZero(sh.getBaoxiu()) || sh.getBaoxiu().equals(2) || (sh.getServiceType() != null && sh.getServiceType().equals(1))) {
            type |= 1 << 2;
        } else {
            type &= ~1;
        }
        List<Shouhou> shouhouList = CommenUtil.autoQueryHist(()->shouhouService.list(new LambdaQueryWrapper<Shouhou>().eq(Shouhou::getImei, sh.getImei())));
        //List<Shouhou> shouhouList = shouhouService.list(new LambdaQueryWrapper<Shouhou>().eq(Shouhou::getImei, sh.getImei()));
        if (shouhouList.size() > 1 && !shouhouList.get(0).getModidate().isEqual(shouhouList.get(1).getModidate())) {
            type |= 1 << 3;
        }
        qudaoEntity.setType(type);
        super.saveOrUpdate(qudaoEntity);
        return qudaoEntity;
    }

    @Override
    public void updateFanxiuShouhouQudao(String imei, Integer currentQudaoId) {
        ShouhouQudao qudaoEntity = baseMapper.getshouhouQudaoByImeiAndCurrentQudaoId(imei, currentQudaoId);
        Integer type = 0;
        if (qudaoEntity != null) {
            type |= 1 << 3;
            qudaoEntity.setType(type);
            super.saveOrUpdate(qudaoEntity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateWaiSongBatch(ShouhouQuDaoBatchWaiSongReq req) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();

        List<Integer> shouhouIdList = req.getShouhouIdList();
        //外送单号需要做重复提交校验处理
        List<ShouhouQudao> list = super.list(new LambdaQueryWrapper<ShouhouQudao>().select(ShouhouQudao::getShouhouid).in(ShouhouQudao::getShouhouid, shouhouIdList));
        if (CollectionUtils.isNotEmpty(list)) {
            List<Integer> collect = list.stream().map(ShouhouQudao::getShouhouid).collect(Collectors.toList());
            shouhouIdList.removeIf(shId -> collect.contains(shId));
        }

        if (CollectionUtils.isEmpty(shouhouIdList)) {
            return Boolean.FALSE;
        }

        List<ShouhouLogNewBo> shLogs = new LinkedList<>();

        //查询售后信息
        List<Shouhou> shouhouList =  CommenUtil.autoQueryHist(()->shouhouService.list(new LambdaQueryWrapper<Shouhou>().in(Shouhou::getId, shouhouIdList)));
//        List<Shouhou> shouhouList
//                = shouhouService.list(new LambdaQueryWrapper<Shouhou>().in(Shouhou::getId, shouhouIdList));

        shouhouList.stream().forEach(sh -> {
            //如果当前维修单使用了优惠码，优惠码返回
            if (sh.getYouhuifeiyong() != null && sh.getYouhuifeiyong().compareTo(BigDecimal.ZERO) > 0) {
                //使用过优惠码，则返回优惠码
                List<Integer> cardLogIds = shouHouPjMapper.getCardIdfromCardLogsByWxId(sh.getId(), 3, null);
                if (CollUtil.isNotEmpty(cardLogIds)){
                    for (Integer cardLogId : cardLogIds) {
                        if (CommenUtil.isNotNullZero(cardLogId)) {
                            boolean isCheckManual = false;
                            if (XtenantEnum.isJiujiXtenant()){
                                //校验是否存在手工费 存在手工费，返还优惠码  否则直接添加外送渠道
                                //获取当前维修单所有配件信息
                                List<HexiaoBo> hexiaoBoList = wxkcoutputService.getHexiao(sh.getId());
                                //为true的话 在维修中存在手工费
                                if (CollUtil.isNotEmpty(hexiaoBoList)) {
                                    isCheckManual = hexiaoBoList.stream().anyMatch(hexiaoBo -> CommenUtil.isNullOrZero(hexiaoBo.getPpid()));
                                    if (!isCheckManual){
                                        continue;
                                    }
                                }
                            }
                            numberCardService.update(new LambdaUpdateWrapper<NumberCard>().setSql("use_count=use_count-1,State=0").eq(NumberCard::getId, cardLogId));
                            cardLogsService.remove(new QueryWrapper<CardLogs>().lambda()
                                    .eq(CardLogs::getId, cardLogId).eq(CardLogs::getUseType, 1));
                            shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getYouhuima, null).set(Shouhou::getYouhuifeiyong, 0).eq(Shouhou::getId, sh.getId()));
                            ShouhouLogNewBo logs = new ShouhouLogNewBo();
                            logs.setShouhouId(sh.getId());
                            logs.setComment("优惠码仅限自修使用，使用的优惠码已返还给客户");
                            if (isCheckManual) {
                                logs.setComment("取消优惠券使用，原因：维修单外送手工费不能使用优惠券");
                            }
                            logs.setInuser(oaUserBO.getUserName());
                            logs.setIsWeb(true);
                            shLogs.add(logs);
                        } else {
                            Integer yuyueId = sh.getYuyueid();
                            if (CommenUtil.isNotNullZero(yuyueId)) {
                                cardLogIds = shouHouPjMapper.getCardIdfromCardLogsByWxId(sh.getYuyueid(), 4, null);
                                if (CollUtil.isNotEmpty(cardLogIds)){
                                    for (Integer logId : cardLogIds) {
                                        if (CommenUtil.isNotNullZero(logId)) {
                                            numberCardService.update(new LambdaUpdateWrapper<NumberCard>().setSql("use_count=use_count-1,State=0").eq(NumberCard::getId, logId));
                                            cardLogsService.remove(new QueryWrapper<CardLogs>().lambda()
                                                    .eq(CardLogs::getId, logId).eq(CardLogs::getUseType, 1));
                                        }
                                    }
                                }

                            }
                        }
                    }
                }
                //撤销优惠码以后更新维修费
                repairService.updateShouhouFeiyong(sh.getId());
            }

            if (StringUtils.isNotEmpty(req.getQdName())) {
//                ShouhouMsgPushMessageBo message = new ShouhouMsgPushMessageBo();
//                message.setMsgId(21)
//                        .setShouhouId(sh.getId())
//                        .setAreaId(sh.getAreaid() == null ? 0 : sh.getAreaid())
//                        .setUserId(sh.getUserid() == null ? 0 : sh.getUserid().intValue())
//                        .setOptUser(oaUserBO.getUserName());
//                Map<String, String> tmpData = new HashMap<>();
//                tmpData.put("qudaoname", qudao.getCompanyJc());
//                message.setTmpData(tmpData);
//                shouhouService.pushMessage(message, true);

                String comment = "您的设备已送往【" + req.getQdName() + "服务中心】，预计48小时内工作人员将与你联系。";
                ShouhouLogNewBo log = new ShouhouLogNewBo();
                log.setShouhouId(sh.getId());
                log.setComment(comment);
                log.setInuser(oaUserBO.getUserName());
                log.setIsWeb(true);
                shLogs.add(log);
                shouhouMsgService.sendCollectMsg(sh.getId(), "已外送到【" + req.getQdName() + "】进行维修", oaUserBO.getUserName());
                if (sh.getModidate() != null && !sh.getModidate().isEqual(LocalDateTime.MIN)) {
                    ShouhouTimer shouhouTimer = new ShouhouTimer();
                    shouhouTimer.setShouhouid(sh.getId());
                    Duration duration = Duration.between(LocalDateTime.now(), sh.getModidate());
                    shouhouTimer.setChuli((int) Math.abs(duration.toMinutes()));
                    shouhouTimerService.addShouhouTimer(shouhouTimer);

                    LocalDateTime dateDiffTime = sh.getModidate();

                    Integer toareaCount = shouhouToareaService.count(new LambdaQueryWrapper<ShouhouToarea>().eq(ShouhouToarea::getShouhouId, sh.getId()));
                    if (CommenUtil.isNotNullZero(toareaCount)) {
                        Integer toareaId = CommenUtil.isNotNullZero(sh.getToareaid()) ? sh.getToareaid() : sh.getAreaid() == null ? 0 : sh.getAreaid();
                        List<ShouhouToarea> toareaList = shouhouToareaService.list(new LambdaQueryWrapper<ShouhouToarea>().eq(ShouhouToarea::getShouhouId, sh.getId()).eq(ShouhouToarea::getToareaid, toareaId).orderByDesc(ShouhouToarea::getId));
                        if (CollectionUtils.isNotEmpty(toareaList)) {
                            LocalDateTime checktTime = toareaList.get(0).getCheckdtime();
                            if (checktTime != null) {
                                dateDiffTime = checktTime;
                            }
                        }
                    }

                    shouhouTimer = new ShouhouTimer();
                    shouhouTimer.setShouhouid(sh.getId());
                    shouhouTimer.setWaisongqd((int) Duration.between(dateDiffTime, LocalDateTime.now()).toMinutes());
                    shouhouTimerService.addShouhouTimer(shouhouTimer);
                }
            }

            //添加外送渠道信息
            ShouhouQudao qudao = new ShouhouQudao();
            qudao.setDtime(LocalDateTime.now())
                    .setShouhouid(sh.getId())
                    .setInuser(oaUserBO.getUserName())
                    .setShqd2id(req.getQdId())
                    .setShqd2name(req.getQdName())
                    .setStarttime(sh.getModidate() == null ? LocalDateTime.now() : sh.getModidate())
                    .setEndtime(CommonUtils.getEndOfDay(LocalDateTime.now()).plusYears(1))
                    .setType(0)
                    .setShqdid(0);
            super.save(qudao);
            if (!Objects.equals(sh.getWeixiuren(),sh.getInuser())) {
                String comment = "维修人由 【" + sh.getWeixiuren() + "】 变更为 【" + oaUserBO.getUserName() + "】";
                ShouhouLogNewBo logs = new ShouhouLogNewBo();
                logs.setShouhouId(sh.getId());
                logs.setComment(comment);
                logs.setInuser(oaUserBO.getUserName());
                logs.setIsWeb(Boolean.FALSE);
                shLogs.add(logs);
            }
        });

        shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getWeixiuren,oaUserBO.getUserName()).in(Shouhou::getId,shouhouIdList));

        if (CollectionUtils.isNotEmpty(shLogs)) {
            shLogs.stream().forEach(logs -> {
                shouhouService.saveShouhouLog(logs.getShouhouId(), logs.getComment(), oaUserBO.getUserName(), null, logs.getIsWeb());
            });
        }

        return Boolean.TRUE;
    }

    @Override
    public List<ShouhouJieJianInfo> getJieJianInfoListByIds(List<Integer> shouhouIds) {
        if (CollectionUtils.isEmpty(shouhouIds)){
            return new ArrayList<>();
        }
        return CommenUtil.autoQueryHist(()->baseMapper.getJieJianInfoListByIds(shouhouIds));
    }

    @Override
    public List<ShouhouQuDaoList>  getShouHouQuDaoList(ShouhouJieJianQueryReq req) {
        List<ShouhouQuDaoList> shouHouQuDaoList =CommenUtil.autoQueryHist(()->baseMapper.getShouHouQuDaoList(req));
        return shouHouQuDaoList;
    }

    @Override
    public boolean isWaisongQuDao(Integer shouhouId) {
        return baseMapper.existsQuDao(shouhouId);
    }
}
