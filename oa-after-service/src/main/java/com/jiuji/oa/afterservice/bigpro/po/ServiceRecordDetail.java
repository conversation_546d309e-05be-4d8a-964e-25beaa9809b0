package com.jiuji.oa.afterservice.bigpro.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 服务记录详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("service_record_detail")
public class ServiceRecordDetail extends Model<ServiceRecordDetail> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    private Integer id;

    @TableField("shouhou_id")
    @ApiModelProperty("售后ID")
    private Integer shouhouId;

    @TableField("service_record_id")
    @ApiModelProperty("服务记录ID")
    private Integer serviceRecordId;

    @ApiModelProperty("价格")
    private BigDecimal price;

    @TableField("create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除")
    private Boolean isDel;



    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
