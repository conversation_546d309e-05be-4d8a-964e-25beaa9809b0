package com.jiuji.oa.afterservice.bigpro.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 确认来源枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Getter
@AllArgsConstructor
public enum ConfirmSourceEnum {
    
    /**
     * 用户
     */
    USER(1, "用户"),
    
    /**
     * 门店员工
     */
    STORE_EMPLOYEE(2, "门店员工");
    
    private final Integer code;
    private final String desc;
    
    /**
     * 根据code获取枚举
     * @param code 代码
     * @return 枚举
     */
    public static ConfirmSourceEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ConfirmSourceEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取描述
     * @param code 代码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        ConfirmSourceEnum enumItem = getByCode(code);
        return enumItem == null ? "" : enumItem.getDesc();
    }
    
    /**
     * 获取所有枚举值的列表
     * @return 枚举值列表
     */
    public static Map<Integer, String> getAll() {
        Map<Integer, String> map = new HashMap<>();
        for (ConfirmSourceEnum item : values()) {
            map.put(item.getCode(), item.getDesc());
        }
        return map;
    }
}
