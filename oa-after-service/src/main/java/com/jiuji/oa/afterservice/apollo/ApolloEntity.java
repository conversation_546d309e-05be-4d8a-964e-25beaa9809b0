package com.jiuji.oa.afterservice.apollo;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class ApolloEntity {

    /**
     * 系统开关
     */
    @Value("${apollo.test:12}")
    private Integer system;


    /**
     * 盲盒ppid
     */
    @Value("${apollo.blindBoxPpid:}")
    private String blindBoxPpid;

    /**
     * 售后接件first文案
     */
    @Value("${afterSale.electronicTicket.connectorFirst:亲爱的%s，您的电子小票已生成。请核对您的处理项目，确认告知书，并签字确认维修。点击即可查看小票详情}")
    private String afterSalesConnectorFirst;

    /**
     * 售后回执first文案
     */
    @Value("${afterSale.electronicTicket.receiptFirst:亲爱的%s，您已付款成功，电子小票已生成，请核对您的维修内容及价格，了解维修质保须知。点击即可查看小票详情}")
    private String afterSalesReceiptFirst;

    /**
     * 大件售后服务类型
     */
    @Value("${afterSale.electronicTicket.bigHandleType:售后服务}")
    private String afterSalesBigHandleType;
    /**
     * 小件售后服务类型
     */
    @Value("${afterSale.electronicTicket.smallHandleType:配件售后服务}")
    private String afterSalesSmallHandleType;

    /**
     * 接件小票推送当前进度文案
     */
    @Value("${afterSale.electronicTicket.connectorLogType:接件电子小票生成成功}")
    private String afterSalesConnectorLogType;


    /**
     * 回执小票推送当前进度文案
     */
    @Value("${afterSale.electronicTicket.receiptLogType:取机电子小票生成成功}")
    private String afterSalesReceiptLogType;

    /**
     * 接件单地址
     */
    @Value("${afterSale.electronicTicket.connectorUrl:/after-service/electronic-ticket/connector?orderId=%s&isMobile=%s&tomp=1}")
    private String connectorUrl;

    /**
     * 回执单地址
     */
    @Value("${afterSale.electronicTicket.receiptUrl:/after-service/electronic-ticket/receipt?orderId=%s&isMobile=%s&tomp=1}")
    private String receiptUrl;

    /**
     * 获取回收信息地址
     */
    @Value("${afterSale.electronicTicket.RepairInfoUrl:/ajaxapi/GetRepairPrintData?subId=%s}")
    private String repairInfoUrl;

    /**
     * 推送门店id
     */
    @Value("${afterSale.electronicTicket.pushMsgAreaId:}")
    private String pushMsgAreaId;

    /**
     * 九机新组合退允许开放门店
     */
    @Value("${afterSale.refund.refundAreaId:}")
    private String refundAreaId;

    /**
     * 九机新组合退允许开放门店
     * 当开关打开的时候那就是只有refundAreaId配置的门店才走新版的组合退
     */
    @Value("${afterSale.refund.prohibitSwitch:false}")
    private Boolean prohibitSwitch;



    /**
     * 允许新版九机组合退功能的退款类型
     */
    @Value("${afterSale.refund.kind:6,8}")
    private String kind;

    /**
     * 允许新版九机组合退功能的退款类型
     * @see ApolloConfig#buildRefundKindXtenantMap()
     */
    private Map<String,String> kindXtenantMap;

    /**
     * 允许新版九机组合退功能的退款类型
     */
    @Value("${afterSale.refund.hqtxhAreaId:}")
    private String hqtxhAreaId;

    /**
     * 年包复购使用完毕推送时间配置
     *
     */
    @Value("${afterSale.repurchaseBuy.day:7}")
    private Integer repurchaseBuyDay;

    /**
     * 年包复购到期前时间配置
     *
     */
    @Value("${afterSale.repurchaseBuy.delayDay:7}")
    private Integer repurchaseBuyDelayDay;



    /**
     * 短信配置开启的租户
     */
    @Value("${afterSale.smsConfig.openXtenant:0}")
    private String smsConfigOpenXtenant;





    /**
     * 短信配置开启的租户
     */
    @Value("${afterSale.smsConfig.repairAccessoriesAreaId:949}")
    private String RepairAccessoriesAreaId;


    /**
     * 新版维修单
     */
    @Value("${afterSale.smsConfig.repairOrderNewAreaId:949}")
    private String repairOrderNewAreaId;


    /**
     * 新版维修单
     */
    @Value("${afterSale.smsConfig.RedeemAreaId:949}")
    private String redeemAreaId;

    /*
     * 预约单新UI
     */
    @Value("${afterSale.smsConfig.NewUIAreaId:949}")
    private String yuYueNewUIAreaId;




    /**
     * 新版小件单流程
     */
    @Value("${afterSale.smsConfig.smallProNewAreaId:949,1}")
    private String smallProNewAreaId;

    /**
     * 短信配置开启的租户
     */
    @Value("${afterSale.smsConfig.automaticScrapFilmCid:662}")
    private String automaticScrapFilmCid;

    /**
     * 短信配置开启的租户
     */
    @Value("${smallpro.not_refund.ppids:}")
    private String smallproNotRefundPpids;
}
