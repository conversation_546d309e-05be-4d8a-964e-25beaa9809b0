package com.jiuji.oa.afterservice.refund.service.way.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouRefundService;
import com.jiuji.oa.afterservice.common.constant.RequestCacheKeys;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.refund.bo.DetailParamBo;
import com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo;
import com.jiuji.oa.afterservice.refund.dao.way.CardPayOriginWayMapper;
import com.jiuji.oa.afterservice.refund.enums.ThirdRefundTypeEnum;
import com.jiuji.oa.afterservice.refund.enums.TuiGroupEnum;
import com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanDetailPo;
import com.jiuji.oa.afterservice.refund.service.BaseRefundService;
import com.jiuji.oa.afterservice.refund.service.impl.BaseRefundServiceImpl;
import com.jiuji.oa.afterservice.refund.service.way.CardPayOriginWayService;
import com.jiuji.oa.afterservice.refund.service.way.OtherRefundService;
import com.jiuji.oa.afterservice.refund.utils.RefundMoneyUtil;
import com.jiuji.oa.afterservice.refund.vo.req.CardPayReqVo;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundWayDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.CardOriginRefundVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.OtherRefundVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 刷卡支付原路径服务
 * <AUTHOR>
 * @since 2022/7/19 15:28
 */
@Service
@Slf4j
public class CardPayOriginWayServiceImpl extends BaseRefundServiceImpl<CardOriginRefundVo> implements CardPayOriginWayService {
    @Resource
    @Lazy
    private CardPayOriginWayService cardPayOriginWayService;
    @Resource
    private CardPayOriginWayMapper cardPayOriginWayMapper;

    @Override
    protected void setGroupDataByRefundWays(DetailParamBo detailParamBo, RefundWayDetailVo<CardOriginRefundVo> refundWayDetailVo, R<RefundMoneyDetailVo> result) {
        RefundMoneyDetailVo detailVo = result.getData();
        Optional.of(detailVo.getRefundWays().stream().filter(this::containMyGroup).map(RefundMoneyDetailVo.RefundWayVo::getName)
                .flatMap(this::tuiWayMatchDb)
                .collect(Collectors.toSet())).filter(CollUtil::isNotEmpty)
                .map(originWays ->cardPayOriginWayService.getCardPayInfo(originWays,null,detailParamBo))
                .ifPresent(refundWayDetailVo::setGroupData);
        refundWayDetailVo.setDataTemplate(new CardOriginRefundVo().setGroupCode(getMyGroup().getCode()));
    }

    /**
     * 获取刷卡支付信息
     * @param originWays
     * @param posIds
     * @param detailParamBo
     * @return
     */
    @Override
    public List<CardOriginRefundVo> getCardPayInfo(Set<String> originWays, Set<Integer> posIds, DetailParamBo detailParamBo){
        TuihuanKindEnum tuiHuanKind = detailParamBo.getTuihuanKindEnum();
        Integer shouyingOrderId = detailParamBo.getRefundSubInfo().getOrderId();
        return cardPayOriginWayService.getCardPayInfo(new CardPayReqVo().setTuiWays(originWays).setTuihuanKind(tuiHuanKind)
                .setPosIds(posIds).setSubId(shouyingOrderId).setIsOriginalRefund(Boolean.TRUE));
    }


    /**
     * 获取刷卡支付信息
     * @param cardPayReqVo
     * @return
     */
    @Override
    public List<CardOriginRefundVo> getCardPayInfo(CardPayReqVo cardPayReqVo) {
        return SpringContextUtil.reqCache(() -> invokeGetCardPayInfo(cardPayReqVo), RequestCacheKeys.GET_CARD_PAY_INFO, JSON.toJSONString(cardPayReqVo));
    }

    /**
     * 获取刷卡支付信息
     * @param cardPayReqVo
     * @return
     */
    private List<CardOriginRefundVo> invokeGetCardPayInfo(CardPayReqVo cardPayReqVo) {
        Set<String> originWays = cardPayReqVo.getTuiWays();
        Set<Integer> posIds = cardPayReqVo.getPosIds();
        TuihuanKindEnum tuiHuanKind = cardPayReqVo.getTuihuanKind();
        Integer shouyingOrderId = cardPayReqVo.getSubId();
        boolean isOriginalRefund = Boolean.TRUE.equals(cardPayReqVo.getIsOriginalRefund());
        //获取父子订单号
        Set<Integer> subIds = RefundMoneyUtil.parentChildOrderIds(shouyingOrderId, tuiHuanKind);

        Set<String> shouyingTypes = RefundMoneyUtil.getShouyingTypes(tuiHuanKind);
        List<CardOriginRefundVo> result = CommenUtil.autoQueryHist(() -> cardPayOriginWayMapper.listCardPayRecordInfo(subIds, shouyingTypes, originWays,
                posIds, isOriginalRefund, XtenantEnum.getXtenant()), MTableInfoEnum.SHOUYING_SUB_ID, cardPayReqVo.getSubId(), shouyingTypes);
        List<CardOriginRefundVo> rmrList = new LinkedList<>();
        if(!result.isEmpty()){
            Map<Integer, CardOriginRefundVo> groupPosIdMap = result.stream()
                    // 设置分组代码
                    .peek(cor -> cor.setGroupCode(getMyGroup().getCode()))
                    .collect(Collectors.toMap(CardOriginRefundVo::getPosId, Function.identity(),
                    (cor1, cor2) -> {
                        // posId 相同的进行合并,单号相同的sql已经合并,处理单号不同的情况
                        if(cor1.getSubIds() == null){
                            cor1.setSubIds(new LinkedList<>());
                            cor1.getSubIds().add(cor1.getSubId());
                        }
                        cor1.getSubIds().addAll(Optional.ofNullable(cor2.getSubIds()).filter(CollUtil::isNotEmpty)
                                .orElseGet(()->Collections.singletonList(cor2.getSubId())));
                        cor1.setRefundPrice(cor1.getRefundPrice().add(cor2.getRefundPrice()));
                        cor1.setActualPayPrice(cor1.getActualPayPrice().add(cor2.getActualPayPrice()));
                        //放到待移除列表
                        rmrList.add(cor2);
                        return cor1;
                    }));
            //移除重复的数据
            if(CollUtil.isNotEmpty(rmrList)){
                result.removeAll(rmrList);
            }

            //获取已退金额
            cardPayOriginWayMapper.sumRefundedPriceGroupPosId(subIds, tuiHuanKind.getCode(),
                    result.stream().map(CardOriginRefundVo::getPosId).collect(Collectors.toSet()))
            .forEach(dic -> {
                BigDecimal refundedPrice = dic.getBigDecimal(CardOriginRefundVo.REFUNDED_PRICE);
                CardOriginRefundVo cor = groupPosIdMap.get(dic.getInt(CardOriginRefundVo.POS_ID));
                //设置已退金额
                cor.setRefundPrice(NumberUtil.max(BigDecimal.ZERO,cor.getRefundPrice().subtract(refundedPrice)))
                        .setRefundedPrice(refundedPrice);
            });
        }
        return result;

    }



    @Override
    protected List<CardOriginRefundVo> toGroupDataByDetail(DetailParamBo detailParamBo, List<ShouhouTuihuanDetailPo> myTuihuanDetailPos) {
        Map<Integer, ShouhouTuihuanDetailPo> groupPosIdMap = myTuihuanDetailPos.stream()
                .collect(Collectors.toMap(ShouhouTuihuanDetailPo::getRecordId, Function.identity(), (k1, k2) -> k1));
        Set<Integer> posIds = myTuihuanDetailPos.stream().map(ShouhouTuihuanDetailPo::getRecordId).collect(Collectors.toSet());
        return cardPayOriginWayService.getCardPayInfo(null,posIds,detailParamBo).stream()
                .filter(cor -> groupPosIdMap.get(cor.getPosId()) != null)
                .map(CardOriginRefundVo::copy)
                .peek(cor ->{
                    ShouhouTuihuanDetailPo std = groupPosIdMap.get(cor.getPosId());
                    cor.setId(std.getId());
                    cor.setGroupCode(std.getTuiGroup());
                    cor.setRefundBusinessType(std.getRefundBusinessType());
                    cor.setRefundPrice(std.getRefundPrice());
                    //需要减去当前退款金额
                    cor.setRefundedPrice(cor.getRefundedPrice().subtract(std.getRefundPrice()));
                })
                .collect(Collectors.toList());
    }

    @Override
    protected RefundWayDetailVo<CardOriginRefundVo> initRefundWayDetailVo(DetailParamBo detailParamBo) {
        LambdaBuild<LinkedList<RefundWayDetailVo.GroupColumns>> columns = LambdaBuild.create(new LinkedList<RefundWayDetailVo.GroupColumns>())
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("payTerminalNo").setTitle(TuiGroupEnum.CARD_PAY_ORIGIN_WAY.getMessage()).setType(RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode()))
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("actualPayPrice").setTitle("已付金额").setType(RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode()))
                ;
        if(Optional.ofNullable(detailParamBo).map(DetailParamBo::getTuiHuanPo).map(ShouhouTuiHuanPo::getCheck3)
                .map(Boolean.FALSE::equals).orElse(Boolean.TRUE)){
            columns.set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("refundedPrice").setTitle("已退款金额")
                    .setType(RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode()));
        }
        columns.set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("refundPrice").setTitle("本次退款金额")
                .setType(DecideUtil.iif(detailParamBo.getTuiHuanPo() == null, RefundWayDetailVo.GroupColumnTypeEnum.INPUT.getCode(), RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode())));
        return new RefundWayDetailVo<CardOriginRefundVo>().setGroupColumns(columns.build());
    }

    @Override
    public boolean isMyGroup(Integer tuiGroup) {
        return Objects.equals(tuiGroup,getMyGroup().getCode());
    }

    @Override
    public TuiGroupEnum getMyGroup() {
        return TuiGroupEnum.CARD_PAY_ORIGIN_WAY;
    }

    @Override
    protected BaseRefundService<CardOriginRefundVo> getMyService() {
        return cardPayOriginWayService;
    }

    @Override
    protected void doAssertCheckSave(GroupTuihuanFormVo tuihuanForm, List<CardOriginRefundVo> tuiWayDetails) {
        List<CardOriginRefundVo> cardPayInfos = cardPayOriginWayService.getCardPayInfo(new CardPayReqVo().setTuihuanKind(tuihuanForm.getTuihuanKindEnum())
                .setSubId(tuihuanForm.getSubInfoBo().getOrderId()).setIsOriginalRefund(Boolean.TRUE));
        Set<CardOriginRefundVo> overMaxRefunds = cardPayInfos.stream()
                .filter(cpi -> tuiWayDetails.stream().filter(twd -> Objects.equals(twd.getPosId(), cpi.getPosId()))
                        .anyMatch(twd -> twd.getRefundPrice().compareTo(cpi.getActualPayPrice().subtract(cpi.getRefundedPrice())) > 0))
                .collect(Collectors.toSet());
        Set<CardOriginRefundVo> notFindRecords = tuiWayDetails.stream()
                .filter(twd -> cardPayInfos.stream().noneMatch(appi -> Objects.equals(appi.getPosId(), twd.getPosId())))
                .collect(Collectors.toSet());
        Assert.isTrue(overMaxRefunds.isEmpty(),overMaxRefunds.stream()
                .map(omr -> StrUtil.format("pos机[{}]退款金额不能大于{}",omr.getPayTerminalNo(),omr.getActualPayPrice().
                        subtract(omr.getRefundedPrice()))).collect(Collectors.joining(StringPool.SLASH)));
        Assert.isTrue(notFindRecords.isEmpty(),notFindRecords.stream()
                .map(nfr -> StrUtil.format("pos机[{}]找不到支付记录",nfr.getPayTerminalNo())).collect(Collectors.joining(StringPool.SLASH)));
        // 政府补贴,pos机限制只能退余额
        assertCheckLimitOnlyYuERefund(tuihuanForm, cardPayInfos, tuiWayDetails);
    }

    /**
     * 政府补贴,pos机限制只能退余额
     *
     * @param tuihuanForm
     * @param cardPayInfos
     * @param tuiWayDetails
     */
    private void assertCheckLimitOnlyYuERefund(GroupTuihuanFormVo tuihuanForm, List<CardOriginRefundVo> cardPayInfos, List<CardOriginRefundVo> tuiWayDetails) {

        List<CardOriginRefundVo> onlyYuERefundList = cardPayInfos.stream()
                .filter(this::isGovSubsidy)
                .collect(Collectors.toList());
        if(onlyYuERefundList.isEmpty()){
            // 无相关退款方式, 无需校验
            return;
        }
        //仅支持余额目前可退总和
        BigDecimal onlyYuERefundPrice =onlyYuERefundList.stream().map(CardOriginRefundVo::getRefundPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        if(onlyYuERefundPrice.compareTo(BigDecimal.ZERO) <=0){
            //已经退完了, 无需校验
            return;
        }

        if(tuiWayDetails.stream().anyMatch(twd -> onlyYuERefundList.stream().anyMatch(oyr -> ObjectUtil.equals(twd.getPosId(), oyr.getPosId())))){
            // 刷卡原路径退也不允许
            throw new CustomizeException(StrUtil.format("{}刷卡支付是政府补贴，只能使用余额退款", onlyYuERefundList.stream()
                    .map(CardOriginRefundVo::getReturnWayName).distinct().filter(StringUtils::isNotEmpty)
                    .collect(Collectors.joining(StringPool.COMMA))));
        }


        // 其他方式退款列表
        List<OtherRefundVo> otherRefundDetailList = SpringUtil.getBean(OtherRefundService.class).getMyClassDetailList(tuihuanForm.getRefundWayDetails());

        //当前余额退总金额
        BigDecimal currYuERefundPrice =  otherRefundDetailList.stream().filter(ord -> OtherRefundService.YU_E.equals(ord.getReturnWayName()))
                .map(OtherRefundVo::getRefundPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //总最大可退金额(包括父子订单)
        RefundSubInfoBo subInfoBo = tuihuanForm.getSubInfoBo();
        BigDecimal allSubMaxRefundPrice = subInfoBo.getAllYifuM().subtract(subInfoBo.getNotRefundPrice());
        //当前退款金额
        BigDecimal refundPrice = tuihuanForm.getRefundPrice();
        Assert.isFalse(ObjectUtil.defaultIfNull(tuihuanForm.getOtherRefundPrice(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO)>0
                        && refundPrice.subtract(currYuERefundPrice).add(onlyYuERefundPrice).compareTo(allSubMaxRefundPrice)>0,
                "{}刷卡支付是政府补贴，只能使用余额退款", onlyYuERefundList.stream()
                        .map(CardOriginRefundVo::getReturnWayName).distinct().filter(StringUtils::isNotEmpty)
                        .collect(Collectors.joining(StringPool.COMMA)));

    }

    private boolean isGovSubsidy(CardOriginRefundVo cpi) {
        if(XtenantEnum.isSaasXtenant()){
            return false;
        }
        Integer posId = ObjectUtil.defaultIfNull(cpi.getPosId(), 0);
        if (posId >= 2167 && posId <= 2465 && cpi.getDtime()
                .isBefore(LocalDateTime.of(2024, 12, 31, 23, 59, 59))) {
            return true;
        }
        return false;
    }

    @Override
    public void preHandleSaveData(GroupTuihuanFormVo tuihuanForm, List<JSONObject> tuihuanDetailJsons) {
        //获取刷卡支付所有金额
        List<CardOriginRefundVo> cardPayInfos = cardPayOriginWayService.getCardPayInfo(null, null, new DetailParamBo()
                .setOrderId(tuihuanForm.getSubId()).setTuihuanKindEnum(tuihuanForm.getTuihuanKindEnum()).setRefundSubInfo(tuihuanForm.getSubInfoBo()));
        List<CardOriginRefundVo> tuiWayDetails = getMyClassDetailList(tuihuanDetailJsons);
        handleOtherRefund(tuihuanForm, cardPayInfos,tuiWayDetails);
    }

    private void handleOtherRefund(GroupTuihuanFormVo tuihuanForm, List<CardOriginRefundVo> cardPayInfos, List<CardOriginRefundVo> tuiWayDetails) {
        //支持其他退款方式, 剩余可退金额
        List<CardOriginRefundVo> otherRefundList = cardPayInfos.stream()
                // 复制对象, 需要调整可退金额
                .map(CardOriginRefundVo::copy)
                // 找到对应原路径退,进行扣减
                .peek(tor -> tuiWayDetails.stream().filter(twd -> ObjectUtil.equal(twd.getPosId(),tor.getPosId()))
                        .findFirst().map(twd -> tor.getRefundPrice().subtract(twd.getRefundPrice())).ifPresent(tor::setRefundPrice))
                .collect(Collectors.toList());
        //其他退款方式剩余可退金额总和
        BigDecimal otherRefundPriceSum = otherRefundList.stream()
                .map(CardOriginRefundVo::getRefundPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal currOtherRefundPrice = NumberUtil.min(otherRefundPriceSum,tuihuanForm.getOtherRefundPrice());
        List<CardOriginRefundVo> otherGovSubsidys = new LinkedList<>();
        //按退款金额增加记录
        while(!otherRefundList.isEmpty() && currOtherRefundPrice.compareTo(BigDecimal.ZERO)>0) {
            //每次都获取距离最近的退款金额,来进行扣减
            CardOriginRefundVo apd = findNearestRefund(otherRefundList,currOtherRefundPrice)
                    .orElseThrow(() -> new CustomizeException("获取不到最近的刷卡支付退款记录(不可能发生)"));
            //每个只能扣减一次, 需要从列表中移除
            otherRefundList.remove(apd);
            CardOriginRefundVo temp = apd.copy();
            temp.setRefundPrice(NumberUtil.min(currOtherRefundPrice, temp.getRefundPrice()));
            temp.setIsDel(Boolean.TRUE);
            temp.setGroupCode(getMyGroup().getCode());
            //增加到提交列表
            tuihuanForm.getRefundWayDetails().add(JSON.parseObject(JSON.toJSONString(temp)));
            //更新当前剩余其他方式退款金额
            currOtherRefundPrice = currOtherRefundPrice.subtract(temp.getRefundPrice());
            //被其他方式退了的需要进行扣减
            tuihuanForm.setOtherRefundPrice(tuihuanForm.getOtherRefundPrice().subtract(temp.getRefundPrice()));
            // 是否为政府补贴
            if(isGovSubsidy(temp)){
                otherGovSubsidys.add(temp);
            }
        }
        handleOtherGovSubsidy(tuihuanForm, otherGovSubsidys);

    }

    private void handleOtherGovSubsidy(GroupTuihuanFormVo tuihuanForm, List<CardOriginRefundVo> otherGovSubsidys) {
        if(CollUtil.isEmpty(otherGovSubsidys)) {
            return;
        }
        // 其他方式退款列表
        OtherRefundService otherRefundService = SpringUtil.getBean(OtherRefundService.class);
        List<OtherRefundVo> otherRefundDetailList = otherRefundService.getMyClassDetailList(tuihuanForm.getRefundWayDetails());
        // 政府补贴拆分为独立的余额
        BigDecimal otherGovSubsidyPrice = otherGovSubsidys.stream().map(CardOriginRefundVo::getRefundPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<OtherRefundVo> yuERefundList = otherRefundDetailList.stream().filter(ord -> OtherRefundService.YU_E.equals(ord.getReturnWayName()))
                .collect(Collectors.toList());
        // 余额总金额
        BigDecimal yuERefundPrice = yuERefundList.stream().map(OtherRefundVo::getRefundPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 余额退款
        if(yuERefundPrice.compareTo(otherGovSubsidyPrice) <0){
            throw new CustomizeException(StrUtil.format("余额[{}]不能小于其他方式退的政府补贴金额[{}]", yuERefundPrice, otherGovSubsidyPrice));
        }

        //按退款金额增加记录
        while(!yuERefundList.isEmpty() && otherGovSubsidyPrice.compareTo(BigDecimal.ZERO)>0) {
            //每次都获取距离最近的退款金额,来进行扣减
            OtherRefundVo ornr = otherRefundService.findNearestRefund(yuERefundList, otherGovSubsidyPrice)
                    .orElseThrow(() -> new CustomizeException("获取不到最接近的余额退款记录(不可能发生)"));
            //每个只能扣减一次, 需要从列表中移除
            yuERefundList.remove(ornr);
            OtherRefundVo temp = ornr;
            if(otherGovSubsidyPrice.compareTo(ornr.getRefundPrice()) <0){
                temp = ornr.copy();
                temp.setRefundPrice(otherGovSubsidyPrice);
                ornr.setRefundPrice(ornr.getRefundPrice().subtract(otherGovSubsidyPrice));
                //更新为只做账
                temp.setThirdRefundType(ThirdRefundTypeEnum.RECORD_ONLY_NO_REFUND.getCode());
                //增加到提交列表
                tuihuanForm.getRefundWayDetails().add(JSON.parseObject(JSON.toJSONString(temp)));
            }else{
                //更新为只做账
                temp.setThirdRefundType(ThirdRefundTypeEnum.RECORD_ONLY_NO_REFUND.getCode());
            }
            //更新当前剩余政府补贴退款金额
            otherGovSubsidyPrice = otherGovSubsidyPrice.subtract(temp.getRefundPrice());
        }
    }

    /**
     * 程序退款方式匹配库退款方式
     * @param tuiWay
     * @return
     */
    @Override
    protected Stream<String> tuiWayMatchDb(String tuiWay) {
        tuiWay = StrUtil.removeSuffix(tuiWay, "返回");
        return Stream.of(tuiWay);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSave(GroupTuihuanFormVo tuihuanForm, List<CardOriginRefundVo> myTuiWayDetails) {
        for (CardOriginRefundVo myTuiWayDetail : myTuiWayDetails) {
            if(!Boolean.TRUE.equals(myTuiWayDetail.getIsDel())){
                //非删除才需要增加返回
                myTuiWayDetail.setReturnWayName(StrUtil.addSuffixIfNot(myTuiWayDetail.getReturnWayName(), ShouhouRefundService.REFUND_WAY_ORIGIN));
            }
        }
        cardPayOriginWayMapper.batchInsert(tuihuanForm, myTuiWayDetails);
    }

    @Override
    protected boolean isDoAssertCheckSave(List<CardOriginRefundVo> myClassList) {
        //刷卡支付需要一直都要检测,因为存在其他退款方式退款的情况
        return true;
    }

    @Override
    public boolean isOriginWay() {
        return true;
    }

    @Override
    public List<CardOriginRefundVo> listAll(Integer orderId, TuihuanKindEnum tuihuanKindEnum) {
        return cardPayOriginWayService.getCardPayInfo(new CardPayReqVo().setTuiWays(null).setTuihuanKind(tuihuanKindEnum)
                        .setPosIds(null).setSubId(orderId).setIsOriginalRefund(Boolean.TRUE));
    }
}
