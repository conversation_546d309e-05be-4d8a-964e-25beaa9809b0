package com.jiuji.oa.afterservice.bigpro.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 维修故障DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@ApiModel(value = "RepairFaultDTO对象", description = "维修故障DTO")
public class RepairFaultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID，新增时为null")
    private Integer id;

    @ApiModelProperty(value = "故障名称", required = true)
    private String faultName;

    @ApiModelProperty(value = "是否必填(0:否 1:是)", required = true)
    private Integer isRequired;

    @ApiModelProperty(value = "维修方案列表", required = true)
    private List<RepairPlanDTO> planList;
} 