package com.jiuji.oa.afterservice.common.config.xtenant;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StopWatch;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

/**
 * Created by lee on 17-8-8.
 */
@Slf4j
public class MultitenancyInterceptor extends HandlerInterceptorAdapter {

    public static final String PLAT_FORM = "Platform";
    public static final String IS_REQUEST_FROM_PAD = "is_request_from_pad";
    public static final String COOKIE_IS_PDA = "isPDA";
    public static final String TENANT_HEADER_NAME = "xtenant";
    private Logger logger = LoggerFactory.getLogger(MultitenancyInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String tenantId = request.getHeader(TENANT_HEADER_NAME);
        if(tenantId==null){
            tenantId=getCookiefromRequest(request,TENANT_HEADER_NAME);
        }
        if(tenantId==null){
            tenantId=request.getParameter(TENANT_HEADER_NAME);
        }
        if(tenantId == null){
            tenantId = Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class)).map(AbstractCurrentRequestComponent::getCurrentStaffId)
                    .map(OaUserBO::getXTenant).map(String::valueOf).orElse(null);
        }
        if(tenantId==null){
            //logger.debug("从header和cookie都取不出来多租户标示：" + TENANT_HEADER_NAME + "，就设置为默认命名空间");
            Namespaces.set(Namespaces.SHARED_NAMESPACE_NAME);
        } else {
            try{
               Long tid= Long.parseLong(tenantId);
               Namespaces.set(tid);
               com.ch999.common.util.tenant.Namespaces.set(tid);
               // logger.debug("租户ID:" + tid);
            } catch (Exception e){
                log.error("租户ID必须为数字",e);
                Namespaces.set(Namespaces.ERROR_NAMESPACE_NAME);
            }
        }
        //增加接口响应计时
        StopWatch stopWatch = new StopWatch("接口响应计时");
        stopWatch.start();
        request.setAttribute(RequestAttrKeys.RESPONSE_STOP_WATCH, stopWatch);
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        //Namespaces.set(Namespaces.ERROR_NAMESPACE_NAME);

        StopWatch stopWatch = (StopWatch) request.getAttribute(RequestAttrKeys.RESPONSE_STOP_WATCH);
        if(stopWatch != null){
            stopWatch.stop();
        }
        if(stopWatch != null && stopWatch.getTotalTimeSeconds() > 15){
            log.warn("{}{}", stopWatch.prettyPrint(), RRExceptionHandler.makeContent(request,null));
        }else{
            //根据redis拦截特定接口打印参数
            Optional.ofNullable(SpringUtil.getBean(StringRedisTemplate.class).opsForValue()
                            .get(StrUtil.format(RedisKeys.PRINT_URL_REQ_PARAM,request.getRequestURI())))
                    .ifPresent(v -> {
                        if(stopWatch != null){
                            log.warn("{}{}", stopWatch.prettyPrint(), RRExceptionHandler.makeContent(request,null));
                        }else{
                            log.warn(RRExceptionHandler.makeContent(request,null));
                        }
                    });
        }
    }

    private String getCookiefromRequest(final HttpServletRequest request, final String cookieName) {
        final Cookie[] cookies = request.getCookies();
        String ret = null;

        if (cookies != null) {
            for (Cookie c : cookies) {
                if (c.getName().equals(cookieName)) {
                    ret = c.getValue();
                    break;
                }
            }
        }

        return ret;
    }

}
