package com.jiuji.oa.afterservice.bigpro.service.servicerecord.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.bigpro.bo.externalrepair.SimpleServiceSubInfoBo;
import com.jiuji.oa.afterservice.bigpro.dao.servicerecord.ServiceRecordSubMapper;
import com.jiuji.oa.afterservice.bigpro.po.ServiceRecord;
import com.jiuji.oa.afterservice.bigpro.service.servicerecord.ServiceRecordSubService;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.enums.UserClassEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.DecideUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 外修服务购买
 * <AUTHOR>
 * @since 2022/1/11 10:17
 */
@Service
@Slf4j
public class ServiceRecordSubServiceImpl implements ServiceRecordSubService {
    @Autowired
    private ServiceRecordSubMapper recordSubMapper;


    @Override
    public SimpleServiceSubInfoBo getSimpleSubInfo(String imei, List<String> imeis, Integer userId, Integer xTenant, Integer subId) {
        ServiceRecordSubService serviceRecordSubService = SpringUtil.getBean(ServiceRecordSubService.class);
        //输出查询生效的就行,九机只查询已完成的订单
        boolean isSaas = !XtenantEnum.isJiujiXtenant(xTenant);
        return Optional.ofNullable(serviceRecordSubService.getSimpleNormalSub(imeis, userId, isSaas, subId))
                .map(Optional::of).map(Optional::of)
                //九机从存档库中获取
                .orElseGet(() -> Optional.of(XtenantEnum.isJiujiXtenant(xTenant)).filter(Boolean::booleanValue)
                        .map(isHist -> serviceRecordSubService.getSimpleHistorySub(imeis, userId, isSaas, subId))
                        .map(ssi -> ssi.setIsHistory(Boolean.TRUE)).map(Optional::of))
                //存档获取不到从历史记录中获取,非存档库
                .orElseGet(() -> Optional.of(XtenantEnum.isJiujiXtenant(xTenant)).filter(Boolean::booleanValue)
                        .map(isHist -> serviceRecordSubService.getSimpleHistoryRecord(imei, userId))
                        .map(ssi -> ssi.setOrderType(SimpleServiceSubInfoBo.OrderTypeEnum.HISTORY_RECORD_ORDER)))
                .map(ssi -> ssi.setOrderType(ObjectUtil.defaultIfNull(ssi.getOrderType(), SimpleServiceSubInfoBo.OrderTypeEnum.NEW_ORDER))
                            .setIsHistory(ObjectUtil.defaultIfNull(ssi.getIsHistory(),Boolean.FALSE)))
                .orElse(null);
    }

    @Override
    public ServiceInfoVO getServiceInfo(Integer subId, String imei, boolean isHistory, boolean isHistRecord) {
        //通过订单号获取订单信息
        ServiceRecordSubService serviceRecordSubService = SpringUtil.getBean(ServiceRecordSubService.class);
        ServiceInfoVO result;
        if (isHistory) {
            result = serviceRecordSubService.getHistorySub(subId,imei);
        } else if (isHistRecord) {
            result = serviceRecordSubService.getHistoryRecord(subId,imei);
        } else {
            result = serviceRecordSubService.getNormalSub(subId,imei);
        }
        return Optional.ofNullable(result)
                .map(h -> h.setOrderType(DecideUtil.iif(isHistRecord, () -> SimpleServiceSubInfoBo.OrderTypeEnum.HISTORY_RECORD_ORDER.getCode(),
                        () -> SimpleServiceSubInfoBo.OrderTypeEnum.NEW_ORDER.getCode())).setIsHistory(isHistory).setHuishou(Boolean.FALSE))
                .orElse(null);
    }

    @Override
    public List<ServiceRecord> list9jiServiceRecord(Integer basketId, String imei, boolean isHistory) {
        //通过商品id获取九机服务
        ServiceRecordSubService serviceRecordSubService = SpringUtil.getBean(ServiceRecordSubService.class);
        // 服务记录不再迁移历史库
        return serviceRecordSubService.listNormal9jiServiceRecord(basketId,imei);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public SimpleServiceSubInfoBo getSimpleNormalSub(List<String> imeis, Integer userId, boolean isEffect, Integer subId) {
        return recordSubMapper.getSimpleSub(imeis,userId,isEffect, subId);
    }

    @Override
    @DS(DataSourceConstants.OA_NEW_HIS)
    public SimpleServiceSubInfoBo getSimpleHistorySub(List<String> imeis, Integer userId, boolean isEffect, Integer subId) {
        return recordSubMapper.getSimpleSub(imeis,userId,isEffect, subId);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public ServiceInfoVO getNormalSub(Integer subId, String imei) {
        return recordSubMapper.getSubById(subId, imei);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public ServiceInfoVO getHistoryRecord(Integer subId, String imei) {
        return Optional.ofNullable(recordSubMapper.getHistoryRecord(subId,imei))
                .map(hrsi->hrsi.setUserClassName(Optional.ofNullable(UserClassEnum.valueOfByCode(hrsi.getUserClass()))
                .map(UserClassEnum::getMessage).orElse(""))).orElse(null);
    }

    @Override
    @DS(DataSourceConstants.OA_NEW_HIS)
    public ServiceInfoVO getHistorySub(Integer subId, String imei) {
        return recordSubMapper.getSubById(subId,imei);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public List<ServiceRecord> listNormal9jiServiceRecord(Integer basketId, String imei) {
        return recordSubMapper.list9jiServiceRecord(basketId,imei);
    }

    @Override
    @DS(DataSourceConstants.OA_NEW_HIS)
    public List<ServiceRecord> listHistory9jiServiceRecord(Integer basketId, String imei) {
        return recordSubMapper.list9jiServiceRecord(basketId,imei);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public SimpleServiceSubInfoBo getSimpleHistoryRecord(String imei, Integer userId) {
        return recordSubMapper.getSimpleHistoryRecord(imei,userId);
    }
}
