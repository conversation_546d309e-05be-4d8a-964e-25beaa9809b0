package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.cloud.after.enums.*;
import com.jiuji.cloud.after.util.BaoXiuUtil;
import com.jiuji.cloud.after.vo.baoxiu.BaoXiuParam;
import com.jiuji.cloud.after.vo.baoxiu.BaoXiuVo;
import com.jiuji.cloud.after.vo.jiujiservice.ValidSaleJiujiServiceReqVo;
import com.jiuji.cloud.after.vo.jiujiservice.ValidSaleJiujiServiceResVo;
import com.jiuji.oa.afterservice.bigpro.bo.ImeiSearchLogBo;
import com.jiuji.oa.afterservice.bigpro.bo.externalrepair.SimpleServiceSubInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.servicerecord.StopShouhouInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.servicerecord.UseServiceRecordBo;
import com.jiuji.oa.afterservice.bigpro.dao.ServiceRecordMapper;
import com.jiuji.oa.afterservice.bigpro.enums.HuiJiBaoRegisterStateEnum;
import com.jiuji.oa.afterservice.bigpro.enums.YadingRegisterStateEnum;
import com.jiuji.oa.afterservice.bigpro.enums.YiwaiBaoVersionEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.service.servicerecord.ServiceRecordExternalRepairService;
import com.jiuji.oa.afterservice.bigpro.service.servicerecord.ServiceRecordRecoverSubService;
import com.jiuji.oa.afterservice.bigpro.service.servicerecord.ServiceRecordSubService;
import com.jiuji.oa.afterservice.bigpro.service.servicerecord.ValidJiujiSaleServiceService;
import com.jiuji.oa.afterservice.cloud.vo.AfterServiceTimeCfg;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.*;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.UserClassEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.mapstruct.CommonStructMapper;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceDetailVO;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceVO;
import com.jiuji.oa.afterservice.smallpro.enums.BasketTypeEnum;
import com.jiuji.oa.afterservice.smallpro.service.SmallproDetailsExService;
import com.jiuji.oa.afterservice.stock.enums.ESubCheckEnum;
import com.jiuji.oa.afterservice.stock.service.XcMkcService;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.res.MemberBasicRes;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.LambdaCaseWhen;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Slf4j
@Service
public class ServiceRecordServiceImpl extends ServiceImpl<ServiceRecordMapper, ServiceRecord> implements ServiceRecordService {

    @Autowired
    private ImeisearchlogsService imeisearchlogsService;
    @Autowired
    private ShouhouImeichangeService shouhouImeichangeService;
    @Autowired
    private ServiceRecordMapper serviceRecordMapper;
    @Autowired
    private ProductinfoService productinfoService;
    @Autowired
    private ShouhouTuihuanService shouhouTuihuanService;
    @Autowired
    private AreaInfoClient areaInfoClient;
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private SubService subService;
    @Autowired
    private XcMkcService xcMkcService;
    @Autowired
    private MemberClient memberClient;
    @Autowired
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Autowired
    private ServiceRecordSubService serviceRecordSubService;
    @Autowired
    private ServiceRecordRecoverSubService serviceRecoverSubService;
    @Autowired
    private ServiceRecordExternalRepairService serviceExternalRepairService;
    @Resource
    private ServiceRecordDetailService serviceRecordDetailService;

    @Resource
    private AreainfoService areainfoService;


    /**
     * @param imei
     * @param userIdParam
     * @param isSaveLog 是否
     * @return not null
     */
    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public ServiceInfoVO getRecord(String imei, Integer userIdParam, boolean isSaveLog) {
        return getRecord(imei, userIdParam,null, isSaveLog);
    }


    /**
     * @param imei
     * @param userIdParam
     * @param isSaveLog 是否
     * @return not null
     */
    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public ServiceInfoVO getRecord(String imei, Integer userIdParam, Integer subId, boolean isSaveLog) {
        //支持从redis指定用户id
        Integer userId = Optional.ofNullable(userIdParam).filter(uId -> uId>0)
                .orElseGet(()->Convert.toInt(SpringUtil.getBean(StringRedisTemplate.class).opsForValue()
                        .get(StrUtil.format(RedisKeys.IMEI_SERVICE_RECORD_USER_ID_KEY,imei))));
        OaUserBO currentUser = Optional.ofNullable(currentRequestComponent.getCurrentStaffId())
                .orElseGet(() -> Optional.ofNullable(userId).map(uId -> memberClient.getMemberBasicInfo(uId))
                        .filter(r -> r.getCode() == ResultCode.SUCCESS).map(R::getData).map(ServiceRecordServiceImpl::memberToOaUser)
                        .orElseThrow(() -> new CustomizeException("登录已过期，请重新登录！")));
        ServiceRecordService serviceRecordService = (ServiceRecordService) AopContext.currentProxy();
        Integer xTenant = currentUser.getXTenant();
        try {
        LocalDateTime today = SpringContextUtil.getRequest().map(req -> (LocalDateTime)req.getAttribute(RequestAttrKeys.AFTER_MODIDATE)).orElseGet(LocalDateTime::now);
        List<String> imeis = new LinkedList<>();
        imeis.add(imei);
        //如果存在售后转出,需要查询原串号
        ShouhouImeichange imeichange = shouhouImeichangeService.getTransferOutImeiChange(imei);
        if (imeichange != null) {
            imeis.add(imeichange.getImei1());
        }
        //根据串号查询订单信息(主表订单或存档订单)
        SimpleServiceSubInfoBo simpleSubInfo = getSimpleSubInfo(imei, userId, imeis, xTenant, subId);
        ServiceInfoVO serviceInfoVo = getServiceInfoWithService(imei, userId, serviceRecordService, today, simpleSubInfo);

        boolean histFlag = Boolean.TRUE.equals(simpleSubInfo.getIsHistory());
        //查询订单其他信息
        this.getSubOtherInfo(currentUser, serviceRecordService, imeis, histFlag, serviceInfoVo);
        //串号查询记录
        List<ImeiSearchLogBo> searchLogBos = ObjectUtil.defaultIfNull(imeisearchlogsService.getImeiSearchLogsByImei(imei), Collections.emptyList());
        for (ImeiSearchLogBo searchLogBo : searchLogBos) {
            searchLogBo.setClear(Duration.between(today, searchLogBo.getDTime()).toDays() <= NumberConstant.THREE);
        }

        serviceInfoVo.setImeilist(searchLogBos);
        //最新的订单是否为回收单
        serviceInfoVo.setLastRecoverFlag(isLastRecoverFlag(imei, serviceInfoVo));
        //会员该设备的历史维修次数
        if (userId != null) {
            serviceInfoVo.setShouhouTimes(shouhouService.countShouhouTimes(serviceInfoVo.getImei(), userId, null));
        }
        if (simpleSubInfo.getOrderType() == SimpleServiceSubInfoBo.OrderTypeEnum.WAI_XIU_ORDER) {
            //清理外修购买服务的单号相关信息
            serviceInfoVo.setSubId(0);
        }
        return serviceInfoVo;
        } finally {
            //记录查询日志
            if (isSaveLog) {
                MultipleTransaction.query(DataSourceConstants.DEFAULT,() ->
                        imeisearchlogsService.saveImeiSearchLogs(imei, currentUser.getAreaId(), currentUser.getUserName()));
            }
        }
    }

    /**
     * 获取订单和服务信息
     *
     * @param imei
     * @param userId
     * @param serviceRecordService
     * @param today
     * @param simpleSubInfo
     * @return
     */
    private ServiceInfoVO getServiceInfoWithService(String imei, Integer userId, ServiceRecordService serviceRecordService,
                                                    LocalDateTime today, SimpleServiceSubInfoBo simpleSubInfo) {
        boolean histFlag = Boolean.TRUE.equals(simpleSubInfo.getIsHistory());
        ServiceInfoVO serviceInfoVo = this.getServiceInfo(imei,simpleSubInfo);
        //补充userid信息,既然通过userId查询,获取的信息也应该跟该userId有关
        if(CommenUtil.isNullOrZero(serviceInfoVo.getUserId()) && CommenUtil.isNotNullZero(userId)){
            serviceInfoVo.setUserId(userId);
        }
        if (serviceInfoVo.getMkcAreaId() != null) {
            serviceInfoVo.setMkcArea(Optional.ofNullable(areaInfoClient.getAreaInfoById(serviceInfoVo.getMkcAreaId()))
                    .map(R::getData).map(AreaInfo::getArea).orElse(""));
        }

        //服务
        buildShowService(serviceInfoVo);
        //处理九机服务信息
        handleService(imei, serviceRecordService, today, simpleSubInfo, histFlag, serviceInfoVo);
        //处理服务展示项 移除默认不展示且没有购买的选项
        serviceInfoVo.getServiceVos().removeIf(service -> service.getDetailInfo() == null && !Boolean.TRUE.equals(service.getDisplay()));
        return serviceInfoVo;
    }

    private void handleService(String imei, ServiceRecordService serviceRecordService, LocalDateTime today, SimpleServiceSubInfoBo simpleSubInfo, boolean histFlag, ServiceInfoVO serviceInfoVo) {
        serviceInfoVo.setHuishou(ObjectUtil.defaultIfNull(serviceInfoVo.getHuishou(),false));
        switch (simpleSubInfo.getOrderType()) {
            case LP_ORDER:
                //处理良品订单
                handleLpSubService(today, histFlag, serviceInfoVo);
            case NEW_ORDER:
                handleNewSubService(imei, serviceRecordService, today, histFlag, serviceInfoVo);
                break;
            case WAI_XIU_ORDER:
                handleWaiXiuOrderJiuJiService(today, simpleSubInfo, serviceInfoVo);
                break;
        }
        if(Objects.nonNull(simpleSubInfo.getExternalRepairSimpleInfo())
                && simpleSubInfo.getOrderType() != SimpleServiceSubInfoBo.OrderTypeEnum.WAI_XIU_ORDER){
            //单独购买服务 查询对应的服务信息
            handleWaiXiuOrderJiuJiService(today, simpleSubInfo.getExternalRepairSimpleInfo(), serviceInfoVo);
        }
        handleShouhouService(imei, serviceRecordService, today, histFlag, serviceInfoVo);
    }

    private void buildShowService(ServiceInfoVO serviceInfoVo) {
        serviceInfoVo.setServiceVos(Arrays.stream(ServiceEnum.values())
                .sorted(Comparator.comparing(ServiceEnum::getRank))
                .map(s -> {
                    ServiceVO service = new ServiceVO();
                    if(ServiceEnum.JIU_JI_SHIELD.getCode().equals(s.getCode())){
                        service.setName(areainfoService.getShieldName(serviceInfoVo.getMkcAreaId()));
                    } else {
                        service.setName(s.getMessage());
                    }
                    //因为只是九机做名字修改  所以这里特殊判断一下
                    if(XtenantEnum.isJiujiXtenant() && ServiceEnum.HUI_JI_BAO.getCode().equals(s.getCode())){
                        service.setName("随心换");
                    }
                    service.setType(s.getCodeStr());
                    service.setTypeCode(s.getCode());
                    service.setDisplay(s.isDisplay());
                    return service;
                }).collect(Collectors.toList()));
    }

    private void handleLpSubService(LocalDateTime today, boolean histFlag, ServiceInfoVO serviceInfoVo) {
        Optional<ServiceVO.DetailInfo> baoXiuOpt = getLpBaoXiuServiceOpt(today, serviceInfoVo);
        baoXiuOpt.ifPresent(detail -> concatServiceDetail(serviceInfoVo, detail, "liangping"));
        handleLpSubJiuJiService(today, histFlag, serviceInfoVo, baoXiuOpt.orElse(null));
    }

    private void handleLpSubJiuJiService(LocalDateTime today, boolean histFlag, ServiceInfoVO serviceInfoVo, ServiceVO.DetailInfo baoXiu) {
        //获取九机的所有服务
        List<ServiceRecord> jiServiceRecords = serviceRecoverSubService.list9jiServiceRecord(serviceInfoVo.getBasketId(), serviceInfoVo.getImei(), histFlag);
        for (ServiceRecord jiServiceRecord : jiServiceRecords) {
            getServiceOpt(today, baoXiu, jiServiceRecord).ifPresent(detailInfo -> concatServiceDetail(serviceInfoVo, detailInfo, "jiuji"));
        }
    }

    private SimpleServiceSubInfoBo getSimpleSubInfo(String imei, Integer userId, List<String> imeis, Integer xTenant, Integer subId) {
        SimpleServiceSubInfoBo recoverOrSubInfo = Optional.ofNullable(serviceRecoverSubService.getSimpleSubInfo(imei, userId, xTenant, subId))
                .orElseGet(() -> serviceRecordSubService.getSimpleSubInfo(imei, imeis, userId, xTenant, subId));
        //九机盾是服务, 不应该通过订单来查询
        SimpleServiceSubInfoBo externalRepairsimpleSubInfo = serviceExternalRepairService.getSimpleSubInfo(imei, userId, xTenant, null, false);
        return getSimpleSubInfo(imei, recoverOrSubInfo, externalRepairsimpleSubInfo);
    }

    private SimpleServiceSubInfoBo getSimpleSubInfo(String imei, SimpleServiceSubInfoBo recoverOrSubInfo, SimpleServiceSubInfoBo externalRepairsimpleSubInfo) {
        if (recoverOrSubInfo == null && externalRepairsimpleSubInfo == null) {
            return new SimpleServiceSubInfoBo().setIsHistory(Boolean.FALSE).setOrderType(SimpleServiceSubInfoBo.OrderTypeEnum.NOT_SUB)
                    .setImei(imei);
        }
        if (recoverOrSubInfo == null) {
            return externalRepairsimpleSubInfo;
        }
        if (externalRepairsimpleSubInfo == null) {
            return recoverOrSubInfo;
        }
        //对订单的时间
        int dateCompare = CompareUtil.compare(recoverOrSubInfo.getOrderDate(), externalRepairsimpleSubInfo.getOrderDate(), false);
        if (dateCompare > 0) {
            //订单日期大于购买服务的信息 直接返回 订单信息
            return recoverOrSubInfo;
        }
        recoverOrSubInfo.setExternalRepairSimpleInfo(externalRepairsimpleSubInfo);
        return recoverOrSubInfo;
    }

    private static OaUserBO memberToOaUser(MemberBasicRes member) {
        OaUserBO oaUser = new OaUserBO();
        oaUser.setUserId(member.getId());
        oaUser.setUserIp(SpringContextUtil.getRequest().map(ServletUtil::getClientIP).orElse(null));
        oaUser.setUserName(member.getRealName());
        oaUser.setAreaId(member.getAreaId());
        oaUser.setArea(member.getArea());
        oaUser.setXTenant(member.getXtenant());
        return oaUser;
    }

    private static void concatServiceDetail(ServiceInfoVO serviceInfoVo, ServiceVO.DetailInfo detail, String parentType) {
        //是否为套餐
        Optional<JiujiServiceTypeEnum> boughtPlusEnumOpt = JiujiServiceTypeEnum.valueOfByCode(detail.getTypeCode()).filter(JiujiServiceTypeEnum::isBoughtPlus);
        //默认值处理
        if(ObjectUtil.defaultIfNull(detail.getClassification(),0) == 0){
            detail.setClassification(ServiceClassificationEnum.SELF.getCode());
        }
        List<Integer> selfClassCodeList = Arrays.asList(ServiceClassificationEnum.SELF.getCode(), ServiceClassificationEnum.LIMIT.getCode());
        //是否为自营服务
        boolean isSelfService = selfClassCodeList.contains(detail.getClassification());
        Integer serviceRecordId = detail.getId();
        if (boughtPlusEnumOpt.isPresent() && isSelfService) {
            JiujiServiceTypeEnum boughtPlusEnum = boughtPlusEnumOpt.get();
            serviceInfoVo.setBoughtPlus(new ServiceInfoVO.BoughtPlusVO().setName(boughtPlusEnum.getMessage()).setCode(detail.getTypeCode())
                    .setYears(detail.getYears()).setStartTime(detail.getStartTime()).setEndTime(detail.getEndTime()).setEffective(detail.getEffective())
                    .setEffectiveDes(detail.getEffectiveDes()).setPpriceid(detail.getPpriceid()).setId(serviceRecordId).setTradeDate(detail.getTradeDate())
                    .setPrice(detail.getPrice()).setFeiyong(detail.getFeiyong()));
            return;
        }
        List<ServiceVO> thirdServices = new ArrayList<>();
        AtomicInteger yaDingRegisterState = new AtomicInteger(0);
        AtomicInteger huiJiBaoRegisterState = new AtomicInteger(0);
        AtomicBoolean errorTip = new AtomicBoolean(false);
        try {
            if(ObjectUtil.isNotNull(serviceRecordId)){
                ServiceRecordMapper recordMapper = SpringUtil.getBean(ServiceRecordMapper.class);
                Optional.ofNullable(recordMapper.getYaDingRegisterStateNew(serviceRecordId)).ifPresent(yaDingRegisterState::set);
                Optional.ofNullable(recordMapper.getHuiJiBaoRegisterStateNew(serviceRecordId)).ifPresent(huiJiBaoRegisterState::set);
            }
        }catch (Exception e){
            log.error("汇机保亚丁数据查询异常，serviceRecordId: {}, 将降级使用老版本方法", serviceRecordId, e);
            errorTip.set(true);
        }
        serviceInfoVo.getServiceVos().stream()
                .filter(s -> Objects.equals(detail.getType(), s.getType()))
                .map(s -> {
                    if (isSelfService) {
                        //自营的直接使用
                        return s;
                    }
                    //三方服务,复制一份
                    ServiceVO ts = s.copy();
                    //修改三方的服务名称等信息
                    String tName = ThirdServiceEnum.valueOf(s.getTypeCode(), detail.getClassification()).map(ThirdServiceEnum::getMessage)
                            .orElseGet(() -> StrUtil.format("{}({})", ts.getName(), EnumUtil.getMessageByCode(ServiceClassificationEnum.class, detail.getClassification())));
                    ts.setName(tName);
                    ServiceVO.DetailInfo.EffectiveEnum oldEffectiveEnum = EnumUtil.getEnumByCode(ServiceVO.DetailInfo.EffectiveEnum.class, detail.getEffective());
                    //这么设计的原因是因为getYaDingEffectiveEnumNew是对getYaDingEffectiveEnum的升级 防止新升级的功能异常之后可以回滚所以做这个处理
                    ServiceVO.DetailInfo.EffectiveEnum effectiveEnum;
                    if(errorTip.get()){
                        // 如果查询数据时出现异常，直接使用老版本方法
                        effectiveEnum = getYaDingEffectiveEnum(serviceRecordId, oldEffectiveEnum,s.getTypeCode());
                        log.warn("数据查询异常，降级使用老版本方法, serviceRecordId: {}", serviceRecordId);
                    } else {
                        try {
                            effectiveEnum = getYaDingEffectiveEnumNew(yaDingRegisterState.get(), huiJiBaoRegisterState.get(),oldEffectiveEnum,s.getTypeCode());
                        } catch (Exception e){
                            effectiveEnum = getYaDingEffectiveEnum(serviceRecordId, oldEffectiveEnum,s.getTypeCode());
                            RRExceptionHandler.logError("新版获取亚丁汇机保功能异常",  Dict.create().set("serviceInfoVo",serviceInfoVo)
                                    .set("detail",detail).set("parentType",parentType) , e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
                        }
                    }
                    if (ServiceVO.DetailInfo.EffectiveEnum.NOT_REGISTER.equals(effectiveEnum)
                            || ServiceVO.DetailInfo.EffectiveEnum.NOT_PASS.equals(effectiveEnum)) {
                        detail.setStartTime(null);
                        detail.setEndTime(null);
                        detail.setYears(BigDecimal.ZERO);
                    }
                    Integer effective = effectiveEnum.getCode();
                    String effectiveDes = effectiveEnum.getMessage();
                    detail.setEffective(effective);
                    detail.setEffectiveDes(effectiveDes);
                    thirdServices.add(ts);
                    return ts;
                })
                .findFirst()
                .ifPresent(s -> {
                    s.setDetailInfo(detail);
                    s.setParentType(parentType);
                });
        //追加三方的服务信息
        serviceInfoVo.getServiceVos().addAll(thirdServices);
        if (Objects.equals(detail.getType(), ServiceEnum.BAO_XIU.getCodeStr())) {
            //更新保修状态
            serviceInfoVo.setBaoXiu(ServiceVO.DetailInfo.EffectiveEnum.ACTIVE.getCode().equals(detail.getEffective()));
        }
    }


    private static ServiceVO.DetailInfo.EffectiveEnum getYaDingEffectiveEnumNew(Integer yaDingRegisterState,Integer huiJiBaoRegisterState,ServiceVO.DetailInfo.EffectiveEnum oldEffectiveEnum, Integer typeCode) {
        ServiceVO.DetailInfo.EffectiveEnum effectiveEnum = oldEffectiveEnum;
         huiJiBaoRegisterState = ObjectUtil.defaultIfNull(huiJiBaoRegisterState, HuiJiBaoRegisterStateEnum.UNREGISTERED.getCode());
        //汇机保的逻辑
        if(ServiceEnum.HUI_JI_BAO.getCode().equals(typeCode)){
            HuiJiBaoRegisterStateEnum huiJiBaoRegisterStateEnum = EnumUtil.getEnumByCode(HuiJiBaoRegisterStateEnum.class, huiJiBaoRegisterState);
            switch (huiJiBaoRegisterStateEnum) {
                case UNREGISTERED:
                    effectiveEnum = ServiceVO.DetailInfo.EffectiveEnum.NOT_REGISTER;
                    break;
                case REGISTERED:
                    break;
                case REGISTING:
                    effectiveEnum = ServiceVO.DetailInfo.EffectiveEnum.NOT_REGISTER;
                    break;
                case UNAPPROVE:
                    effectiveEnum = ServiceVO.DetailInfo.EffectiveEnum.NOT_PASS;
                    break;
                default:
            }
        } else {
            yaDingRegisterState = Objects.isNull(yaDingRegisterState) ? YadingRegisterStateEnum.UNREGISTERED.getCode() : yaDingRegisterState;
            YadingRegisterStateEnum yadingRegisterStateEnum = EnumUtil.getEnumByCode(YadingRegisterStateEnum.class, yaDingRegisterState);
            switch (yadingRegisterStateEnum) {
                case UNREGISTERED:
                    effectiveEnum = ServiceVO.DetailInfo.EffectiveEnum.NOT_REGISTER;
                    break;
                case REGISTERED:
                    break;
                case REGISTING:
                    effectiveEnum = ServiceVO.DetailInfo.EffectiveEnum.NOT_REGISTER;
                    break;
                case UNAPPROVE:
                    effectiveEnum = ServiceVO.DetailInfo.EffectiveEnum.NOT_PASS;
                    break;
                default:
            }
        }
        return effectiveEnum;
    }




    private static ServiceVO.DetailInfo.EffectiveEnum getYaDingEffectiveEnum(Integer serviceRecordId, ServiceVO.DetailInfo.EffectiveEnum oldEffectiveEnum,Integer typeCode) {
        ServiceRecordMapper serviceRecordMapper = SpringUtil.getBean(ServiceRecordMapper.class);
        ServiceVO.DetailInfo.EffectiveEnum effectiveEnum = oldEffectiveEnum;
        //汇机保的逻辑
        if(ServiceEnum.HUI_JI_BAO.getCode().equals(typeCode)){
            ServiceRecord serviceRecord = serviceRecordMapper.selectById(serviceRecordId);
            if(ObjectUtil.isNotNull(serviceRecord)){
                Integer huiJiBaoRegisterState = ObjectUtil.defaultIfNull(serviceRecordMapper.getHuiJiBaoRegisterState(serviceRecord.getBasketId()), 0);
                HuiJiBaoRegisterStateEnum huiJiBaoRegisterStateEnum = EnumUtil.getEnumByCode(HuiJiBaoRegisterStateEnum.class, huiJiBaoRegisterState);
                switch (huiJiBaoRegisterStateEnum) {
                    case UNREGISTERED:
                        effectiveEnum = ServiceVO.DetailInfo.EffectiveEnum.NOT_REGISTER;
                        break;
                    case REGISTERED:
                        break;
                    case REGISTING:
                        effectiveEnum = ServiceVO.DetailInfo.EffectiveEnum.NOT_REGISTER;
                        break;
                    case UNAPPROVE:
                        effectiveEnum = ServiceVO.DetailInfo.EffectiveEnum.NOT_PASS;
                        break;
                    default:
                }
            }
        } else {
            Integer yaDingRegisterState = serviceRecordMapper.getYaDingRegisterState(serviceRecordId);
            yaDingRegisterState = Objects.isNull(yaDingRegisterState) ? YadingRegisterStateEnum.UNREGISTERED.getCode() : yaDingRegisterState;
            YadingRegisterStateEnum yadingRegisterStateEnum = EnumUtil.getEnumByCode(YadingRegisterStateEnum.class, yaDingRegisterState);
            switch (yadingRegisterStateEnum) {
                case UNREGISTERED:
                    effectiveEnum = ServiceVO.DetailInfo.EffectiveEnum.NOT_REGISTER;
                    break;
                case REGISTERED:
                    break;
                case REGISTING:
                    effectiveEnum = ServiceVO.DetailInfo.EffectiveEnum.NOT_REGISTER;
                    break;
                case UNAPPROVE:
                    effectiveEnum = ServiceVO.DetailInfo.EffectiveEnum.NOT_PASS;
                    break;
                default:
            }
        }
        return effectiveEnum;
    }

    private void handleNewSubService(String imei, ServiceRecordService serviceRecordService, LocalDateTime today, boolean histFlag, ServiceInfoVO serviceInfoVo) {
        Optional<ServiceVO.DetailInfo> baoXiuOpt;
        //保修服务
        if (ObjectUtil.defaultIfNull(serviceInfoVo.getTradeDate(), serviceInfoVo.getSubDate()) == null
                || Stream.of(SimpleServiceSubInfoBo.OrderTypeEnum.NEW_ORDER,SimpleServiceSubInfoBo.OrderTypeEnum.HISTORY_RECORD_ORDER)
                .noneMatch(ot -> Objects.equals(ot.getCode(),serviceInfoVo.getOrderType()))) {
            //没有交易时间或者非新机单都不查询新机的保修期
            baoXiuOpt = Optional.empty();
        } else {
            baoXiuOpt = this.getBaoxiuServiceOpt(imei, serviceInfoVo, today);
            baoXiuOpt.ifPresent(detailInfo -> concatServiceDetail(serviceInfoVo, detailInfo, "sub"));
        }

        if (serviceInfoVo.getBasketId() != null) {
            //半价换新服务
            Integer basketId = serviceInfoVo.getBasketId();
            this.getBanjiaHuanxinServiceOpt(histFlag, basketId, today).ifPresent(detail -> concatServiceDetail(serviceInfoVo, detail, "sub"));
            //九机服务
            handleNewSubJiuJiService(today, histFlag, serviceInfoVo, baoXiuOpt.orElse(null), basketId);
        }
        //九机 若设备同时含有“碎屏保，随心换”服务，且“碎屏保”服务已经使用，“随心换”服务的状态 需要调整为“不在保”
        if(XtenantEnum.isJiujiXtenant()){
            try {
                List<ServiceVO> serviceVos = serviceInfoVo.getServiceVos();
                if(CollectionUtil.isNotEmpty(serviceVos)){
                    serviceVos.stream().filter(serviceVO -> {
                        boolean type = ServiceEnum.SUI_PING_BAO.getCode().equals(serviceVO.getTypeCode());
                        ServiceVO.DetailInfo detailInfo = Optional.ofNullable(serviceVO.getDetailInfo()).orElse(new ServiceVO.DetailInfo());
                        Boolean use = Optional.ofNullable(detailInfo.getUse()).orElse(Boolean.FALSE);
                        return type && use;
                    }).findFirst().ifPresent(item->{
                        serviceVos.stream()
                                .filter(serviceVO -> ServiceEnum.HUAN_JI_BAO.getCode().equals(serviceVO.getTypeCode()) && ObjectUtil.isNotNull(serviceVO.getDetailInfo()))
                                .findFirst()
                                .ifPresent(serviceVO -> {
                                    Optional.ofNullable(serviceVO.getDetailInfo()).ifPresent(detailInfo ->{
                                        detailInfo.setEffective(ServiceVO.DetailInfo.EffectiveEnum.INVALID.getCode());
                                        detailInfo.setEffectiveDes(ServiceVO.DetailInfo.EffectiveEnum.INVALID.getMessage());
                                        //碎屏保的出险时间复制到‘随心换’的结束时间
                                        Optional.ofNullable(Optional.ofNullable(item.getDetailInfo()).orElse(new ServiceVO.DetailInfo()).getStopShouhouId()).ifPresent(shouHouId->{
                                            Shouhou shouhou = Optional.ofNullable(CommenUtil.autoQueryHist(() ->shouhouService.getById(shouHouId), MTableInfoEnum.SHOUHOU, shouHouId)).orElse(new Shouhou());
                                            //Shouhou shouhou = Optional.ofNullable(shouhouService.getById(shouHouId)).orElse(new Shouhou());
                                            Optional.ofNullable(shouhou.getOfftime()).ifPresent(detailInfo::setEndTime);
                                        });
                                    });
                                });
                    });
                }
            }catch (Exception e){
                RRExceptionHandler.logError("随心换服务的状态处理异常", imei, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            }

        }

    }

    private void handleShouhouService(String imei, ServiceRecordService serviceRecordService, LocalDateTime today, boolean histFlag,
                                      ServiceInfoVO serviceInfoVo) {
        //历史订单售后服务,需要同时查询主库
        List<ServiceRecord> serviceRecords = Optional.of(histFlag).filter(Boolean::booleanValue)
                .map(isHist -> serviceRecordService.listHistoryShouhouService(imei, serviceInfoVo.getTransactionDate()))
                .map(histsr -> (List<ServiceRecord>) CollUtil.addAll(histsr, serviceRecordService.listShouhouService(imei, serviceInfoVo.getTransactionDate())))
                .orElseGet(() -> serviceRecordService.listShouhouService(imei, serviceInfoVo.getTransactionDate()));

        for (ServiceRecord serviceRecord : serviceRecords) {
            getServiceOpt(today, null, serviceRecord)
                    .ifPresent(detailInfo -> concatServiceDetail(serviceInfoVo, detailInfo, "shouhou"));
        }
    }

    private void handleNewSubJiuJiService(LocalDateTime today, boolean histFlag, ServiceInfoVO serviceInfoVo, ServiceVO.DetailInfo baoXiu, Integer basketId) {
        //九机服务
        if (!Objects.equals(serviceInfoVo.getBasketType(), BasketTypeEnum.BASKET_TYPE_AUCTION.getCode())) {
            //非拍卖品,查询是否为瑕疵机
            Optional.ofNullable(xcMkcService.getLockXcMkcByMkcId(serviceInfoVo.getMkcId()))
                    .ifPresent(xcMkc -> {
                        serviceInfoVo.setXcMkc(true);
                        //获取瑕疵机描述信息
                        Optional.ofNullable(xcMkcService.getXcDescription(serviceInfoVo.getMkcId()))
                                .ifPresent(serviceInfoVo::setXcMkcInfo);
                    });
            //获取九机的所有服务
            List<ServiceRecord> jiServiceRecords = serviceRecordSubService.list9jiServiceRecord(basketId, serviceInfoVo.getImei(), histFlag);
            for (ServiceRecord jiServiceRecord : jiServiceRecords) {
                getServiceOpt(today, baoXiu, jiServiceRecord).ifPresent(detailInfo -> concatServiceDetail(serviceInfoVo, detailInfo, "jiuji"));
            }
        }
    }


    private void handleWaiXiuOrderJiuJiService(LocalDateTime today, SimpleServiceSubInfoBo simpleSubInfo, ServiceInfoVO serviceInfoVo) {
        //九机服务
        //获取九机的所有服务
        List<ServiceRecord> jiServiceRecords = serviceExternalRepairService.list9jiServiceRecord(simpleSubInfo.getSubId(), simpleSubInfo.getImei(), Boolean.TRUE.equals(simpleSubInfo.getIsHistory()));
        for (ServiceRecord jiServiceRecord : jiServiceRecords) {
            getServiceOpt(today, null, jiServiceRecord).ifPresent(detailInfo -> concatServiceDetail(serviceInfoVo, detailInfo, "jiuji"));
        }
    }

    private boolean isLastRecoverFlag(String imei, ServiceInfoVO serviceInfoVo) {
        boolean isLastRecoverFlag = false;
        //串号查询转售单 查询最新的一次转售单 时间
        LocalDateTime basketDate = SpringUtil.getBean(IRecoverMarketsubinfoService.class).selectLastReSale(imei);

        if (serviceInfoVo.getTransactionDate() != null) {
            isLastRecoverFlag = Optional.ofNullable(CommenUtil.autoQueryHist(() -> baseMapper.getRecoverSubPayTime(imei)))
                    .map(recoverSubPayTime -> {
                        if(ObjectUtil.isNotNull(basketDate)){
                            return recoverSubPayTime.isAfter(serviceInfoVo.getTransactionDate()) && basketDate.isBefore(recoverSubPayTime);
                        } else {
                           return recoverSubPayTime.isAfter(serviceInfoVo.getTransactionDate());
                        }
                    }).orElse(false);
        }
        return isLastRecoverFlag;
    }

    private Optional<ServiceVO.DetailInfo> getLpBaoXiuServiceOpt(LocalDateTime today, ServiceInfoVO serviceInfoVo) {
        ServiceVO.DetailInfo detailInfo = new ServiceVO.DetailInfo();
        detailInfo.setType(ServiceEnum.BAO_XIU.getCodeStr());
        detailInfo.setTypeCode(ServiceEnum.BAO_XIU.getCode());
        //订单完成时间
        LocalDateTime transactionDate;

        if(serviceInfoVo.getTransactionDate() != null){
            transactionDate = serviceInfoVo.getTransactionDate();
        }else if(serviceInfoVo.getTradeDate() != null){
            transactionDate = serviceInfoVo.getTradeDate();
        }else{
            transactionDate = serviceInfoVo.getSubDate();
        }

        detailInfo.setTradeDate(transactionDate);
        LocalDateTime startTime = transactionDate;
        LambdaCaseWhen<LocalDateTime, LocalDateTime> endTimeCaseWhen = LambdaCaseWhen.lambdaCase(transactionDate);
        LocalDateTime tradeDateStartOfDay = transactionDate.toLocalDate().atStartOfDay();
        LocalDateTime endTime = endTimeCaseWhen
                //订单完成时间 2018-03-15 前 保修期 91天
                .when(td -> LocalDateTime.of(NumberConstant.TWO_THOUSAND_AND_EIGHTEEN, NumberConstant.THREE, NumberConstant.FIFTEEN, 0, 0).isAfter(td), () -> {
                    detailInfo.setVersion("1.0");
                    detailInfo.setVersionDes(detailInfo.getVersion());
                    return tradeDateStartOfDay.plus((long) NumberConstant.SIXTY + NumberConstant.THIRTY_ONE, ChronoUnit.DAYS)
                            .minus(1, ChronoUnit.SECONDS);
                })
                //订单完成时间 2018-10-15 前 保修期 181天
                .when(td -> LocalDateTime.of(NumberConstant.TWO_THOUSAND_AND_EIGHTEEN, NumberConstant.TEN, NumberConstant.FIFTEEN, 0, 0).isAfter(td), () -> {
                    detailInfo.setVersion("2.0");
                    detailInfo.setVersionDes(detailInfo.getVersion());
                    return tradeDateStartOfDay.plus((long) NumberConstant.ONE_HUNDRED_EIGHTY + NumberConstant.ONE, ChronoUnit.DAYS)
                            .minus(1, ChronoUnit.SECONDS);
                })
                //否则 保修期 366天
                .endElse(() -> {
                    detailInfo.setVersion("3.0");
                    detailInfo.setVersionDes(detailInfo.getVersion());
                    return tradeDateStartOfDay.plus((long) NumberConstant.THREE_HUNDRED_FIVE + NumberConstant.ONE, ChronoUnit.DAYS)
                            .minus(1, ChronoUnit.SECONDS);
                });
        long months = ChronoUnit.MONTHS.between(tradeDateStartOfDay, endTime.plusDays(1).toLocalDate().atStartOfDay());
        if(XtenantEnum.isJiujiXtenant()){
            BaoXiuParam baoXiuParam = BaoXiuParam.builder().orderTypeEnum(BaoXiuParam.OrderTypeEnum.GOOD_PRODUCT)
                    .isMobile(serviceInfoVo.getIsMobile()).tradeCompleteTime(serviceInfoVo.getTransactionDate()).subDateTime(serviceInfoVo.getSubDate())
                    .outStockTime(serviceInfoVo.getTradeDate()).xtenant(Convert.toLong(XtenantEnum.getXtenant()))
                    .build();
            BaoXiuVo baoXiuVo = BaoXiuUtil.calculateBaoXiu(baoXiuParam);
            startTime = baoXiuVo.getWarrantyTimeRange().getStartTime();
            endTime = baoXiuVo.getWarrantyTimeRange().getEndTime();
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                    "参数: {}, 统一保修计算结果: {}", baoXiuParam, baoXiuVo);
            // startDate 和 endDate 相差的月数
            Period period = Period.between(startTime.toLocalDate(), endTime.plusDays(1).toLocalDate());
            months = period.getYears() * 12 + period.getMonths();
        }

        ServiceVO.DetailInfo.getEffectiveValue(today, startTime, endTime)
                .ifPresent(ee -> {
                    detailInfo.setEffective(ee.getCode());
                    detailInfo.setEffectiveDes(ee.getMessage());
                });

        //有效期计算
        Shouhou notBaoxiu = shouhouService.getNotBaoxiu(serviceInfoVo.getImei(), true, startTime, endTime);
        if (notBaoxiu == null) {
            detailInfo.setUse(false);
        } else {
            //在保期间使用了,依然在保
            detailInfo.setUse(true);
            detailInfo.setStopShouhouId(notBaoxiu.getId());
            detailInfo.setStopShouhouTime(notBaoxiu.getOfftime());
        }

        detailInfo.setYears(BigDecimal.valueOf(months).divide(BigDecimal.valueOf(NumberConstant.TWELVE), 1, RoundingMode.HALF_UP));
        detailInfo.setStartTime(startTime);
        detailInfo.setEndTime(endTime);
        detailInfo.setPrice(serviceInfoVo.getPrice());
        detailInfo.setFeiyong(serviceInfoVo.getPrice());
        return Optional.ofNullable(detailInfo);
    }

    private Optional<ServiceVO.DetailInfo> getServiceOpt(LocalDateTime today, ServiceVO.DetailInfo baoXiu, ServiceRecord jiServiceRecord) {
        //1 意外保一年 2 意外二年 3 延保一年 4:电池保两年 5 碎屏保一年 6:延保2年  7 进水保 8:屏背保一年 9 碎屏保2年 10: 屏背保两年 11 以换代修九机服务 12 意外保半年 13 碎屏保半年
        return Optional.ofNullable(jiServiceRecord).map(jsr -> getServiceLambda(today, jiServiceRecord, baoXiu));
    }

    /**
     * 获取出险的单号信息
     * @param jiServiceRecord
     * @param serviceEnum
     * @param startTime
     * @param endTime
     * @param callBack
     */
    @Override
    public void getStopServiceId(ServiceRecord jiServiceRecord, ServiceEnum serviceEnum, LocalDateTime startTime, LocalDateTime endTime,
                                 BiConsumer<BusinessTypeEnum, Integer> callBack){
        BusinessTypeEnum stopSubType;
        Integer stopShouHouId;
        Optional<Integer> shouhouIdOpt = Optional.ofNullable(jiServiceRecord.getServerShouhouId());
        if(ObjectUtil.defaultIfNull(jiServiceRecord.getDiscountBasketId(),0)>0
                && Stream.of(ServiceEnum.HUAN_JI_BAO).anyMatch(se -> Objects.equals(se.getCode(),jiServiceRecord.getServiceType()))){
            //查询通过回收单出险的服务
            stopSubType = BusinessTypeEnum.RECOVER_ORDER;
            stopShouHouId = shouhouIdOpt.orElseGet(() -> CommenUtil.autoQueryHist(() -> serviceRecordMapper
                    .getStopOrderId(jiServiceRecord.getDiscountBasketId(),BusinessTypeEnum.RECOVER_ORDER.getCode()), MTableInfoEnum.RECOVER_BASKET, jiServiceRecord.getDiscountBasketId()));
        }else if(ObjectUtil.defaultIfNull(jiServiceRecord.getDiscountBasketId(),0)>0){
            //默认查询新机单
            stopSubType = BusinessTypeEnum.SALE_ORDER;
            stopShouHouId = shouhouIdOpt.orElseGet(() -> CommenUtil.autoQueryHist(() -> serviceRecordMapper
                    .getStopOrderId(jiServiceRecord.getDiscountBasketId(),BusinessTypeEnum.SALE_ORDER.getCode()), MTableInfoEnum.BASKET, jiServiceRecord.getDiscountBasketId()));
        }
        else{
            Optional<ServiceEnum> serviceEnumOpt = Optional.ofNullable(serviceEnum);
            Optional<ServiceEnum> outServiceOpt = serviceEnumOpt
                    // 出险后费用大于0,限定出险才查询使用的售后id
                    .filter(senum -> ObjectUtil.defaultIfNull(jiServiceRecord.getFeiyong(), 0D) > 0);
            Function<Optional<ServiceEnum>, Optional<StopShouhouInfoBo>> getStopShouhouInfoFun = seOpt -> seOpt.map(ServiceEnum::getShouHouType).map(BaoXiuTypeEnum::getCode)
                    .map(code -> CommenUtil.autoQueryHist(()->serviceRecordMapper.getStopShouhouInfo(jiServiceRecord.getImei(), code, startTime, endTime)));

            stopSubType = BusinessTypeEnum.REPAIR_ORDER;
            stopShouHouId = shouhouIdOpt.orElseGet(()-> getStopShouhouInfoFun.apply(outServiceOpt).map(StopShouhouInfoBo::getId).orElse(null));
            // 兼容处理历史问题数据,只处理匹配单条服务记录的情况
            LocalDateTime now = LocalDateTime.now();
            if(stopShouHouId == null && !outServiceOpt.isPresent() && ObjectUtil.defaultIfNull(jiServiceRecord.getTradedate(), now)
                    .isBefore(LocalDate.of(2023,6, 10).atStartOfDay())){
                Optional<StopShouhouInfoBo> stopShouHouInfoOpt = getStopShouhouInfoFun.apply(serviceEnumOpt)
                        // 只考虑固定时间之前的数据, 后续数据不会再出现这个问题
                        .filter(shInfo -> shInfo.getServersOutDtime().isBefore(LocalDate.of(2023,9, 20).atStartOfDay()))
                        .filter(shInfo ->{
                            //获取售后与授权服务对应枚举
                            Set<Integer> serviceTypes = Arrays.stream(ServiceEnum.values()).filter(sEnum -> sEnum.getShouHouType() != null)
                                    .filter(sEnum -> ObjectUtil.equal(sEnum.getShouHouType().getCode(), shInfo.getServiceType()))
                                    // 兼容旧编码
                                    .flatMap(sEnum -> Stream.concat(Stream.of(sEnum.getCode()), Arrays.stream(JiujiServiceTypeEnum.values())
                                            .filter(jjsEnum -> ObjectUtil.equal(jjsEnum.getNewCode(), sEnum.getCode())).map(JiujiServiceTypeEnum::getCode)))
                                    .collect(Collectors.toSet());
                            //查询shouhouId 是否只匹配一个, 如果匹配多个就跳过
                            return CommenUtil.autoQueryMergeHist(() -> serviceRecordMapper
                                    .list9jiServiceRecord(serviceTypes, jiServiceRecord.getImei(), shInfo.getServersOutDtime())).size() <= 1;
                        });
                if(stopShouHouInfoOpt.isPresent()){
                    stopShouHouId = stopShouHouInfoOpt.get().getId();
                }
            }
        }
        callBack.accept(stopSubType,stopShouHouId);
    }

    private ServiceVO.DetailInfo getServiceLambda(LocalDateTime today, ServiceRecord jiServiceRecord, ServiceVO.DetailInfo baoXiu) {
        ServiceVO.DetailInfo service = new ServiceVO.DetailInfo();
        service.setId(jiServiceRecord.getId());
        service.setBoughtPlusId(jiServiceRecord.getServicesTypeBindId());
        service.setTypeCode(jiServiceRecord.getServiceType());
        service.setPpriceid(jiServiceRecord.getPpriceid());
        service.setClassification(jiServiceRecord.getClassification());
        Optional<JiujiServiceTypeEnum> serviceTypeOpt = JiujiServiceTypeEnum.valueOfByCode(jiServiceRecord.getServiceType());
        Optional<ServiceEnum> serviceEnumOpt = Optional.ofNullable(serviceTypeOpt.map(JiujiServiceTypeEnum::getNewCode).map(newCode -> EnumUtil.getEnumByCode(ServiceEnum.class, newCode))
                .orElseGet(() -> EnumUtil.getEnumByCode(ServiceEnum.class, jiServiceRecord.getServiceType())));
        //售后
        if (serviceTypeOpt.isPresent()) {
            JiujiServiceTypeEnum serviceType = serviceTypeOpt.get();
            //兼容旧数据
            LocalDateTime startTime = Optional.ofNullable(baoXiu).filter(bx -> serviceType.isBaoxiuDaoqi())
                    .map(ServiceVO.DetailInfo::getEndTime).orElse(jiServiceRecord.getTradedate())
                    .toLocalDate().atStartOfDay()
                    .plus(serviceType.getDelayDays(), ChronoUnit.DAYS);
            service.setStartTime(startTime);
            service.setEndTime(startTime.toLocalDate().atStartOfDay().plus(serviceType.getMoths(), ChronoUnit.MONTHS)
                    .minus(NumberConstant.ONE, ChronoUnit.SECONDS));
        }

        if (serviceEnumOpt.isPresent()) {
            ServiceEnum serviceEnum = serviceEnumOpt.get();
            service.setType(serviceEnum.getCodeStr());
            service.setTypeCode(serviceEnum.getCode());
            if (Objects.equals(ServiceEnum.YI_WAI_BAO, serviceEnum)) {
                YiwaiBaoVersionEnum.valueOf(jiServiceRecord.getTradedate())
                        .ifPresent(ybv -> {
                            service.setVersion(ybv.getMessage());
                            service.setVersionDes(ybv.getMessage());
                        });
            } else {
                //是否为v4版本的意外保
                boolean isV4 = YiwaiBaoVersionEnum.V40.contain(jiServiceRecord.getTradedate());
                service.setVersion(DecideUtil.iif(isV4, "v2.0", "v1.0"));
                service.setVersionDes(DecideUtil.iif(isV4, "new", "old"));
            }
        }

        service.setTradeDate(jiServiceRecord.getTradedate());
        if (jiServiceRecord.getStartTime() != null && jiServiceRecord.getEndTime() != null) {
            //数据源保存期限,以数据源的为准
            service.setStartTime(jiServiceRecord.getStartTime().toLocalDate().atStartOfDay());
            service.setEndTime(jiServiceRecord.getEndTime().toLocalDate().plusDays(NumberConstant.ONE).atStartOfDay()
                    .minusNanos(NumberConstant.ONE));
        }else if (service.getStartTime() != null || service.getEndTime() != null){
            //修复旧数据, 以便出险使用
            AtomicReference<Boolean> upR = new AtomicReference<>();
            MultipleTransaction.CommitFunction execFun = ()-> upR.set(lambdaUpdate().eq(ServiceRecord::getId,jiServiceRecord.getId())
                    .set(service.getStartTime() != null, ServiceRecord::getStartTime, service.getStartTime())
                    .set(service.getEndTime() != null, ServiceRecord::getEndTime, service.getEndTime())
                    .update());
            MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, execFun).commit();
            if(!Boolean.TRUE.equals(upR.get())){
                MultipleTransaction.build().execute(DataSourceConstants.OA_NEW_HIS_WRITE, execFun).commit();
            }
        }
        if (service.getStartTime() == null || service.getEndTime() == null) {
            service.setEffective(ServiceVO.DetailInfo.EffectiveEnum.UNKNOWN.getCode());
        } else {
            ServiceVO.DetailInfo.getEffectiveValue(today, service.getStartTime(), service.getEndTime())
                    .ifPresent(ee -> {
                        service.setEffective(ee.getCode());
                        service.setEffectiveDes(ee.getMessage());
                    });

            long months = ChronoUnit.MONTHS.between(service.getStartTime(), service.getEndTime().plusDays(1).toLocalDate().atStartOfDay());
            service.setYears(BigDecimal.valueOf(months).divide(BigDecimal.valueOf(NumberConstant.TWELVE), 1, RoundingMode.HALF_UP));
        }
        AtomicReference<Integer> stopShouHouIdRef = new AtomicReference<>();

        getStopServiceId(jiServiceRecord,serviceEnumOpt.orElse(null),service.getStartTime(), service.getEndTime(),
                (stopSubType,stopOrderId) -> {
                    if(stopSubType != null){
                        service.setStopSubType(stopSubType.getCode());
                    }
                    stopShouHouIdRef.set(stopOrderId);
                });
        service.setUse(stopShouHouIdRef.get() != null);
        if (Boolean.TRUE.equals(service.getUse())) {
            service.setStopShouhouId(stopShouHouIdRef.get());
            service.setEffective(ServiceVO.DetailInfo.EffectiveEnum.USED.getCode());
            service.setEffectiveDes(ServiceVO.DetailInfo.EffectiveEnum.USED.getMessage());
        }
        service.setPrice(BigDecimal.valueOf(jiServiceRecord.getPrice()));
        service.setFeiyong(BigDecimal.valueOf(jiServiceRecord.getFeiyong()));
        //封装额度服务类型
        if(ObjectUtil.isNotNull(service.getId()) && ServiceClassificationEnum.LIMIT.getCode().equals(service.getClassification())){
            Integer serviceId = service.getId();
            List<ServiceRecordDetail> list = serviceRecordDetailService.lambdaQuery().eq(ServiceRecordDetail::getServiceRecordId, serviceId).list();
            if(CollUtil.isNotEmpty(list)){
                List<ServiceDetailVO> detailVOS = list.stream().map(item -> BeanUtil.copyProperties(item, ServiceDetailVO.class)).collect(Collectors.toList());
                service.setRecordAppearingList(detailVOS);
            }
            service.setRemainingCost(Optional.ofNullable(service.getPrice()).orElse(BigDecimal.ZERO).subtract(Optional.ofNullable(service.getFeiyong()).orElse(BigDecimal.ZERO)));
        }
        return service;
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public List<ServiceRecord> listShouhouService(String imei, LocalDateTime transactionDate) {
        List<Integer> shouhouServiceTypes = Arrays.stream(ServiceEnum.values()).filter(s -> Boolean.TRUE.equals(s.isShouHouService()))
                .filter(ServiceEnum::isDisplay)
                .map(ServiceEnum::getCode).collect(Collectors.toList());
        List<ServiceRecord> serviceRecords = baseMapper.listShouhouService(imei,shouhouServiceTypes, transactionDate);
        if (serviceRecords == null) {
            return Collections.emptyList();
        }
        return serviceRecords;
    }

    @Override
    @DS(DataSourceConstants.OA_NEW_HIS)
    public List<ServiceRecord> listHistoryShouhouService(String imei, LocalDateTime transactionDate) {
        return this.listShouhouService(imei, transactionDate);
    }

    /**
     * 获取订单信息
     *
     *
     * @param imei
     * @param simpleServiceSubInfoBo
     * @return
     */
    private ServiceInfoVO getServiceInfo(String imei, SimpleServiceSubInfoBo simpleServiceSubInfoBo) {
        //查询订单
        boolean histFlag = Boolean.TRUE.equals(simpleServiceSubInfoBo.getIsHistory());
        ServiceInfoVO serviceInfoVo;
        switch (simpleServiceSubInfoBo.getOrderType()) {
            case LP_ORDER:
                serviceInfoVo = serviceRecoverSubService.getServiceInfo(simpleServiceSubInfoBo.getSubId(), simpleServiceSubInfoBo.getImei(), histFlag);
                break;
            case NEW_ORDER:
                serviceInfoVo = serviceRecordSubService.getServiceInfo(simpleServiceSubInfoBo.getSubId(), simpleServiceSubInfoBo.getImei(), histFlag, false);
                break;
            case HISTORY_RECORD_ORDER:
                serviceInfoVo = serviceRecordSubService.getServiceInfo(simpleServiceSubInfoBo.getSubId(), simpleServiceSubInfoBo.getImei(), histFlag, true);
                break;
            case WAI_XIU_ORDER:
                serviceInfoVo = serviceExternalRepairService.getServiceInfo(simpleServiceSubInfoBo.getSubId(), simpleServiceSubInfoBo.getImei(), histFlag);
                break;
            default:
                serviceInfoVo = null;
                break;
        }
        if(serviceInfoVo == null){
            serviceInfoVo = new ServiceInfoVO().setImei(simpleServiceSubInfoBo.getImei());
        }
        serviceInfoVo.setOriginImei(imei);

        return serviceInfoVo;
    }

    /**
     * 获取半价服务
     *
     * @param histFlag
     * @param basketId
     * @return
     */
    private Optional<ServiceVO.DetailInfo> getBanjiaHuanxinServiceOpt(boolean histFlag, Integer basketId, LocalDateTime today) {
        Sub baojiaHuanxinSub = DecideUtil.iif(histFlag, () -> subService.getHistoryServiceSub(basketId, PpidConstants.BAN_JIA_HUAN_XIN)
                , () -> subService.getServiceSub(basketId, PpidConstants.BAN_JIA_HUAN_XIN));
        return Optional.ofNullable(baojiaHuanxinSub).map(banJia -> getBanJiaServiceLambda(banJia, today));
    }

    private static ServiceVO.DetailInfo getBanJiaServiceLambda(Sub banJia, LocalDateTime today) {
        ServiceVO.DetailInfo banjiaService = new ServiceVO.DetailInfo();
        banjiaService.setType(ServiceEnum.BAN_JIA_HUAN_XIN.getCodeStr());
        banjiaService.setTypeCode(ServiceEnum.BAN_JIA_HUAN_XIN.getCode());
        banjiaService.setVersion("v1.0");
        banjiaService.setVersionDes("");

        banjiaService.setTradeDate(banJia.getTradeDate());
        LocalDateTime tradeDateStartOfDay = banJia.getTradeDate().toLocalDate().atStartOfDay();
        banjiaService.setStartTime(tradeDateStartOfDay.plus(NumberConstant.SIX, ChronoUnit.MONTHS).minus(NumberConstant.ONE, ChronoUnit.SECONDS));
        banjiaService.setEndTime(tradeDateStartOfDay.plus(NumberConstant.TWELVE, ChronoUnit.MONTHS).minus(NumberConstant.ONE, ChronoUnit.SECONDS));
        //在有效期内且没有使用过 即有效
        ServiceVO.DetailInfo.getEffectiveValue(today, banjiaService.getStartTime(), banjiaService.getEndTime())
                .ifPresent(ee -> {
                    banjiaService.setEffective(ee.getCode());
                    banjiaService.setEffectiveDes(ee.getMessage());
                });

        banjiaService.setYears(BigDecimal.valueOf(NumberConstant.SIX).divide(BigDecimal.valueOf(NumberConstant.TWELVE), 1, RoundingMode.HALF_UP));
        banjiaService.setUse(false);
        banjiaService.setStopShouhouId(null);
        banjiaService.setPrice(BigDecimal.ZERO);
        banjiaService.setFeiyong(BigDecimal.ZERO);
        return banjiaService;
    }

    /**
     * 获取保修服务
     *
     * @param imei
     * @param serviceInfoVo
     * @return
     */
    private Optional<ServiceVO.DetailInfo> getBaoxiuServiceOpt(String imei, ServiceInfoVO serviceInfoVo, LocalDateTime today) {
        //根据订单完成时间计算保修期
        ServiceVO.DetailInfo baoxiuService = new ServiceVO.DetailInfo();
        //保修期限
        int months;
        /**
         *  1. productId: 38276, 38775, 38844 订单完成时间+18月
         *  2. productId: 64967, 58982 订单完成时间+24月
         *  3. 其他  订单完成时间+12月
         */
        if (ServiceTimeLimitConstants.LIMIT_MOTH18.contains(serviceInfoVo.getProductId())) {
            months = NumberConstant.TEN + NumberConstant.EIGHT;
        } else if (ServiceTimeLimitConstants.LIMIT_MOTH24.contains(serviceInfoVo.getProductId())) {
            months = NumberConstant.TWENTY_FOUR;
        } else {
            months = NumberConstant.TWELVE;
        }
        //计算保修的截止日期
        LocalDateTime transactionDate;
        if(serviceInfoVo.getTransactionDate() != null){
            transactionDate = serviceInfoVo.getTransactionDate();
        }else if(serviceInfoVo.getTradeDate() != null){
            transactionDate = serviceInfoVo.getTradeDate();
        }else{
            transactionDate = serviceInfoVo.getSubDate();
        }

        LocalDateTime startDate;
        LocalDateTime endDate;

        if(XtenantEnum.isJiujiXtenant()){
            Map<String, AfterServiceTimeCfg> afterTimeCfgMap = SpringUtil.getBean(SmallproDetailsExService.class)
                    .getAfterTimeCfgMap(Collections.singletonList(serviceInfoVo.getPpriceId()));
            AfterServiceTimeCfg afterServiceTimeCfg = afterTimeCfgMap.get(Convert.toStr(serviceInfoVo.getPpriceId()));
            BaoXiuParam.OrderTypeEnum orderTypeEnum;
            if(BasketTypeEnum.BASKET_TYPE_DEFECT_MACHINE.getCode().equals(serviceInfoVo.getBasketType())){
                orderTypeEnum = BaoXiuParam.OrderTypeEnum.EXCELLENT_PRODUCT;
            }else{
                orderTypeEnum = BaoXiuParam.OrderTypeEnum.NEW_MACHINE;
            }
            BaoXiuParam baoXiuParam = BaoXiuParam.builder().orderTypeEnum(orderTypeEnum)
                    .isMobile(serviceInfoVo.getIsMobile()).tradeCompleteTime(serviceInfoVo.getTransactionDate()).subDateTime(serviceInfoVo.getSubDate())
                    .outStockTime(serviceInfoVo.getTradeDate()).xtenant(Convert.toLong(XtenantEnum.getXtenant()))
                    .build();
            SpringUtil.getBean(CommonStructMapper.class).setBaoXiuParam(afterServiceTimeCfg, baoXiuParam);
            BaoXiuVo baoXiuVo = BaoXiuUtil.calculateBaoXiu(baoXiuParam);
            startDate = baoXiuVo.getWarrantyTimeRange().getStartTime();
            endDate = baoXiuVo.getWarrantyTimeRange().getEndTime();
            // startDate 和 endDate 相差的月数
            Period period = Period.between(startDate.toLocalDate(), endDate.plusDays(1).toLocalDate());
            months = period.getYears() * 12 + period.getMonths();
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                    "参数: {}, 统一保修计算结果: {}", baoXiuParam, baoXiuVo);
        }else{
            startDate = transactionDate;
            endDate = startDate.toLocalDate().atStartOfDay().plus(months, ChronoUnit.MONTHS)
                    .minus(1, ChronoUnit.SECONDS);
        }

        Shouhou notBaoxiu = shouhouService.getNotBaoxiu(imei, false, startDate, endDate);
        Integer stopShouhouId = null;
        LocalDateTime stopShouhouTime = null;
        boolean use = false;
        ServiceVO.DetailInfo.EffectiveEnum effective;
        if (notBaoxiu != null) {
            stopShouhouId = notBaoxiu.getId();
            use = true;
            stopShouhouTime = notBaoxiu.getOfftime();
            //使用了 也要计算质保期
            effective = ServiceVO.DetailInfo.getEffectiveValue(today, startDate,endDate)
                    .orElse(ServiceVO.DetailInfo.EffectiveEnum.UNKNOWN);
        } else if (Objects.equals(serviceInfoVo.getBasketType(), BasketTypeEnum.BASKET_TYPE_AUCTION.getCode())) {
            //拍卖品标志未已使用,与老接口一致
            effective = ServiceVO.DetailInfo.EffectiveEnum.INVALID;
        } else {
            effective = ServiceVO.DetailInfo.getEffectiveValue(today, startDate, endDate)
                    .orElse(ServiceVO.DetailInfo.EffectiveEnum.UNKNOWN);
        }

        baoxiuService.setType(ServiceEnum.BAO_XIU.getCodeStr());
        baoxiuService.setTypeCode(ServiceEnum.BAO_XIU.getCode());
        baoxiuService.setVersion("v1.0");
        baoxiuService.setVersionDes("");

        baoxiuService.setTradeDate(transactionDate);
        baoxiuService.setStartTime(startDate);
        baoxiuService.setEndTime(endDate);
        //非拍卖和在保修期内且没有不在保的售后单 即有效
        baoxiuService.setUse(use);
        baoxiuService.setStopShouhouId(stopShouhouId);
        baoxiuService.setStopShouhouTime(stopShouhouTime);
        baoxiuService.setEffective(effective.getCode());
        baoxiuService.setEffectiveDes(effective.getMessage());
        baoxiuService.setYears(BigDecimal.valueOf(months).divide(BigDecimal.valueOf((long) NumberConstant.TWELVE), 1, RoundingMode.HALF_UP));
        baoxiuService.setPrice(BigDecimal.ZERO);
        baoxiuService.setFeiyong(BigDecimal.ZERO);
        return Optional.of(baoxiuService);
    }

    private void getSubOtherInfo(OaUserBO currentUser, ServiceRecordService serviceRecordService, List<String> imeis,
                                 boolean histFlag, ServiceInfoVO serviceInfoVo) {
        //获取产品信息
        Optional.ofNullable(productinfoService.getProductinfoByPpid(serviceInfoVo.getPpriceId()))
                .ifPresent(productinfo -> {
                    serviceInfoVo.setProductId(productinfo.getProductId());
                    serviceInfoVo.setProductName(productinfo.getProductName());
                    serviceInfoVo.setProductColor(StrUtil.trim(productinfo.getProductColor()));
                    serviceInfoVo.setBrandId(productinfo.getBrandID());
                    serviceInfoVo.setCid(productinfo.getCid());
                });

        //拍卖
        if (Objects.equals(serviceInfoVo.getBasketType(), BasketTypeEnum.BASKET_TYPE_AUCTION.getCode())) {
            serviceInfoVo.setMsg("拍卖商品不保修");
        }
        //实现用户租户隔离
        BbsxpUsers bbsxpUser = Optional.ofNullable(serviceInfoVo.getUserId())
                .map(userId -> serviceRecordMapper.getBbsxpUser(serviceInfoVo.getUserId(), serviceInfoVo.getSubMobile(), currentUser.getXTenant()))
                .orElse(null);

        if (bbsxpUser == null) {
            serviceInfoVo.setUserId(0);
            serviceInfoVo.setUserClass(UserClassEnum.USER_CLASS_BRONZE.getCode());
            serviceInfoVo.setUserClassName(UserClassEnum.USER_CLASS_BRONZE.getMessage());
            serviceInfoVo.setUsername("");
        } else {
            //设置用户信息
            serviceInfoVo.setUserId(bbsxpUser.getId());
            //租户用户 afterServicesDiscount 数量
            serviceInfoVo.setAfterServicesDiscount(shouhouTuihuanService.countAfterServicesDiscount(bbsxpUser.getId()));
            serviceInfoVo.setUserClass(bbsxpUser.getUserclass());
            serviceInfoVo.setUserClassName(Optional.ofNullable(UserClassEnum.valueOfByCode(bbsxpUser.getUserclass()))
                    .map(UserClassEnum::getMessage).orElse(""));
            serviceInfoVo.setUsername(bbsxpUser.getUsername());
            serviceInfoVo.setBlacklist(bbsxpUser.getBlacklist());
            //是否为当前租户的订单
            serviceInfoVo.setXtenantType(DecideUtil.iif(Objects.equals(bbsxpUser.getId(), serviceInfoVo.getUserId()), 0, 1));
        }
    }

    /**
     * 原来care+获取方式,现在已经不需要了,获取serviceRecord 已经包含 21的care+记录
     *
     * @param serviceRecordService
     * @param imeis
     * @param histFlag
     * @param serviceInfoVo
     */
    @Deprecated
    private void oldCarePlus(ServiceRecordService serviceRecordService, List<String> imeis, boolean histFlag, ServiceInfoVO serviceInfoVo) {
        Integer boughtCarePlusId;
        if (histFlag) {
            boughtCarePlusId = serviceRecordService.getHistoryCareId(imeis, serviceInfoVo.getUserId());
        } else {
            boughtCarePlusId = serviceRecordService.getCareId(imeis, serviceInfoVo.getUserId());
        }
        if (boughtCarePlusId != null) {
            //老数据的care+
            ServiceInfoVO.BoughtPlusVO boughtPlus = new ServiceInfoVO.BoughtPlusVO().setId(boughtCarePlusId).setName(JiujiServiceTypeEnum.CARE_PLUS.getMessage())
                    .setCode(JiujiServiceTypeEnum.CARE_PLUS.getCode()).setYears(JiujiServiceTypeEnum.CARE_PLUS.getYears())
                    .setStartTime(serviceInfoVo.getTradeDate())
                    .setEndTime(serviceInfoVo.getTradeDate().toLocalDate().atStartOfDay().plus(JiujiServiceTypeEnum.CARE_PLUS.getMoths(), ChronoUnit.MONTHS)
                            .minus(NumberConstant.ONE, ChronoUnit.SECONDS));
            ServiceVO.DetailInfo.getEffectiveValue(LocalDateTime.now(), boughtPlus.getStartTime(), boughtPlus.getEndTime())
                    .ifPresent(ee -> {
                        boughtPlus.setEffective(ee.getCode());
                        boughtPlus.setEffectiveDes(ee.getMessage());
                    });
            boughtPlus.setPpriceid(PpidConstants.CARE_PLUS);
            serviceInfoVo.setBoughtPlus(boughtPlus);
        }
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public Integer getCareId(List<String> imeis, Integer userId) {
        return baseMapper.getCareId(imeis, userId);
    }

    @Override
    @DS(DataSourceConstants.OA_NEW_HIS)
    public Integer getHistoryCareId(List<String> imeis, Integer userId) {
        return baseMapper.getCareId(imeis, userId);
    }

    /**
     * 获取部分服务信息 只包含 订单信息和 服务信息
     * @param imei
     * @return
     */
    @Override
    public ServiceInfoVO getPartServiceInfo(String imei){
        //如果存在售后转出,需要查询原串号
        ShouhouImeichange imeichange = shouhouImeichangeService.getTransferOutImeiChange(imei);
        List<String> imeis = new LinkedList<>();
        imeis.add(imei);
        if (imeichange != null) {
            imeis.add(imeichange.getImei1());
        }
        //根据串号查询订单信息(主表订单或存档订单)
        Integer xTenant = XtenantEnum.getXtenant();
        SimpleServiceSubInfoBo recoverOrSubInfo = Optional.ofNullable(serviceRecoverSubService.getSimpleSubInfo(imei, null, xTenant, null))
                .orElseGet(() -> serviceRecordSubService.getSimpleSubInfo(imei, imeis, null, xTenant, null));
        SimpleServiceSubInfoBo externalRepairsimpleSubInfo = serviceExternalRepairService.getSimpleSubInfoEffect(imei, null, xTenant);
        ServiceRecordService serviceRecordService = (ServiceRecordService) AopContext.currentProxy();
        LocalDateTime today = LocalDateTime.now();
        if (Objects.nonNull(externalRepairsimpleSubInfo)
                && !Objects.equals(externalRepairsimpleSubInfo.getSubCheck(),ESubCheckEnum.THREE.getCode())){
            //九机盾未完成 直接取九机盾的订单信息
            return this.getServiceInfo(imei,externalRepairsimpleSubInfo).setServiceVos(Collections.emptyList());
        }
        SimpleServiceSubInfoBo simpleSubInfo = getSimpleSubInfo(imei, recoverOrSubInfo, externalRepairsimpleSubInfo);
        return getServiceInfoWithService(imei, null, serviceRecordService, today, simpleSubInfo);
    }

    @Override
    public R<ValidSaleJiujiServiceResVo> getValidSaleService(ValidSaleJiujiServiceReqVo saleJiujiServiceReq) {
        return SpringUtil.getBean(ValidJiujiSaleServiceService.class).getValidSaleService(saleJiujiServiceReq);
    }

    /**
     * 该串号下是否有贴膜信息
     * @param imei 串号
     * @return
     */
    @Override
    public R<Boolean> getFilmByImei(String imei) {
        if (StrUtil.isEmpty(imei)) {
            return R.success(Boolean.FALSE);
        }
        //根据串号查询
        Integer filmByImei = serviceRecordMapper.getFilmByImei(imei);
        if (CommenUtil.isNotNullZero(filmByImei)) {
            return R.success(Boolean.TRUE);
        }
        return R.success(Boolean.FALSE);
    }

    @Override
    public boolean useServiceRecord(Shouhou shouhou, Integer serviceRecordId) {
        return retBool(serviceRecordMapper.useServiceRecord(shouhou, serviceRecordId));
    }

    @Override
    public UseServiceRecordBo getTop1UseServiceRecord(Shouhou shouhou, List<Integer> serviceTypes, boolean isGaoJiOutService, Set<String> outServiceImeis) {
        return serviceRecordMapper.getTop1UseServiceRecord(shouhou, serviceTypes, isGaoJiOutService, outServiceImeis);
    }
}
