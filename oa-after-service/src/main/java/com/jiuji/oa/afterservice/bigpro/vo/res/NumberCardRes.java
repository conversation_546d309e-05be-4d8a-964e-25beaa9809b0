package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 用户绑定优惠码
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class NumberCardRes {

    private String GName;

    private BigDecimal Total;

    private BigDecimal limitprice;

    private String CardID;

    private String CardIDShow;

    @JSONField(format = "yyyy-MM-dd")
    private LocalDate StartTime;

    @JSONField(format = "yyyy-MM-dd")
    private LocalDate EndTime;

    private Integer limit;

    private Integer limit1;

    private String limit1name;

    private String limit2name;

    private Boolean limit2;

    private String limitids;

    @JSONField(format = "yyyy-MM-dd")
    private LocalDate AddTime;

    private Integer stats;

    private String msg;

    private Integer limitType;

    /**
     * 优惠码类别
     */
    private Integer ch999Id;
    /**
     * 排除的ppid
     */
    @TableField("excludePpIds")
    private String excludePpIds;

    /**
     * 规则编码
     */
    private String rulecode;

    /**
     * wxkcoutput的分类
     */
    private String pjCidFamily;

    /**
     * wxkcoutput的商品Id
     */
    private Integer pjProductId;

    /**
     * wxkcoutput的ppid
     */
    private Integer pjPpriceid;

    /**
     * wxkcoutput的brandId
     */
    private Integer pjBrandId;
}
