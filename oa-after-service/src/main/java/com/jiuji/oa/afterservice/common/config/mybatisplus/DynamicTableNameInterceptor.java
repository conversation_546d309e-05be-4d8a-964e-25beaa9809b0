package com.jiuji.oa.afterservice.common.config.mybatisplus;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.config.DynamicContextHolder;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;

import java.sql.Connection;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Properties;

/**
 * 动态表名拦截器 - 兼容MyBatis-Plus 3.1.0版本
 * 主要用于处理attachments表名的动态变化
 * 
 * <AUTHOR>
 */
@Slf4j
@Intercepts({
    @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class DynamicTableNameInterceptor implements Interceptor {

    private static final String ATTACHMENTS = "attachments";
    private static final String DBO_ATTACHMENTS = "dbo.attachments";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);

        // 使用安全方法获取BoundSql对象
        BoundSql boundSql = getBoundSqlSafely(statementHandler, metaObject);

        if (boundSql == null) {
            log.warn("无法获取BoundSql对象，跳过动态表名处理");
            return invocation.proceed();
        }

        String originalSql = boundSql.getSql();
        String lowerCaseSql = originalSql.toLowerCase();
        // 先检查SQL中是否包含dbo.attachments表
        if (lowerCaseSql.contains(DBO_ATTACHMENTS.toLowerCase())) {
            String newTableName = initAttachments(originalSql);
            if(!DBO_ATTACHMENTS.toLowerCase().equals(newTableName.toLowerCase())){
                replaceTableName(boundSql, originalSql, DBO_ATTACHMENTS, newTableName);
            }
        } else if (lowerCaseSql.contains(ATTACHMENTS.toLowerCase())) {
            // 如果不包含dbo.attachments，检查是否包含attachments
            String newTableName = initAttachments(originalSql);
            if(!ATTACHMENTS.toLowerCase().equals(newTableName.toLowerCase())){
                replaceTableName(boundSql, originalSql, ATTACHMENTS, newTableName);
            }
        }
        return invocation.proceed();
    }

    /**
     * 替换SQL中的表名
     * @param boundSql BoundSql对象
     * @param originalSql 原始SQL
     * @param oldTableName 旧表名
     * @param newTableName 新表名
     */
    private void replaceTableName(BoundSql boundSql, String originalSql, String oldTableName, String newTableName) {
        // 替换表名
        String newSql = originalSql.replaceAll("(?i)\\b" + oldTableName + "\\b", newTableName);
        // 使用MyBatis框架提供的MetaObject修改BoundSql中的sql，避免使用反射
        try {
            MetaObject boundSqlMetaObject = SystemMetaObject.forObject(boundSql);
            boundSqlMetaObject.setValue("sql", newSql);
            log.warn("成功替换sql: {} -> {}", originalSql, newSql);
        } catch (Exception e) {
            log.error("修改SQL失败: {}", e.getMessage(), e);
            RRExceptionHandler.logError("修改SQL失败", newSql, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
    }

    @Override
    public Object plugin(Object target) {
        // 只对StatementHandler进行代理
        if (target instanceof StatementHandler) {
            return Plugin.wrap(target, this);
        }
        return target;
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以在这里设置属性
    }

    /**
     * 安全获取BoundSql对象
     * 兼容不同版本的MyBatis和MyBatis-Plus
     */
    private BoundSql getBoundSqlSafely(StatementHandler statementHandler, MetaObject metaObject) {
        try {
            // 方法1：直接调用getBoundSql方法
            BoundSql boundSql = statementHandler.getBoundSql();
            if (boundSql != null) {
                return boundSql;
            } // 方法2：尝试通过MetaObject获取boundSql
            if (metaObject.hasGetter("boundSql")) {
                return (BoundSql) metaObject.getValue("boundSql");
            }
            return null;
        } catch (Exception e) {
            RRExceptionHandler.logError("获取BoundSql失败", JSONUtil.toJsonStr(statementHandler), e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            return null;
        }
    }

    /**
     * 初始化attachments表名
     * 根据当前数据源和租户信息动态确定表名
     */
    private String initAttachments(String sql) {
        // 获取当前数据源
        String currentDataSource = Optional.ofNullable(DynamicContextHolder.getDB()).orElse(DataSourceConstants.DEFAULT);
        // 只有当数据源为ch999oanew DEFAULT 时才进行value+ATTACHMENTS操作
        List<String> list = Arrays.asList(DataSourceConstants.CH999_OA_NEW, DataSourceConstants.DEFAULT);
        if (list.contains(currentDataSource)) {
            //数据库查询前缀
            SysConfigClient sysConfigClient = SpringUtil.getBean(SysConfigClient.class);
            R<String> res = sysConfigClient.getValueByCode(SysConfigConstant.OA_EXTENDED_DATABASE_PREFIX);
            log.warn("OA扩展数据库前缀获取域名结果：{}，传入参数：{}", JSONUtil.toJsonStr(res), SysConfigConstant.OA_EXTENDED_DATABASE_PREFIX);
            if(res.isSuccess()){
                String value = res.getData();
                if(StrUtil.isNotEmpty(value)){
                    return value + ATTACHMENTS;
                }
            }
        }
        return ATTACHMENTS;
    }
}
