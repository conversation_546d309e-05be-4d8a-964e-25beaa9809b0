package com.jiuji.oa.afterservice.smallpro.bo;

import com.jiuji.oa.afterservice.smallpro.po.refund.SmallRefundConfigPo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 16:40
 * @Description
 */
@Data
public class NewRefundAmountV2BO  implements Serializable {
    //id
    private Integer id;
    //折价金额
    private BigDecimal price;
    //折价比例
    private BigDecimal differentPricePercent;

    private SmallRefundConfigPo refundConfig;
}
