package com.jiuji.oa.afterservice.smallpro.bo;

import com.jiuji.oa.afterservice.smallpro.enums.SmallRefundServiceTypeEnum;
import com.jiuji.tc.utils.common.FieldModified;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/24 16:24
 * @Description 数据对比实体
 */
@Data
@Accessors(chain = true)
public class SmallRefundConfigContrastBO {
    /**
     * 配置名称
     */
    @FieldModified(displayName = "标题")
    private String title;
    /**
     * 服务类型
     *
     * @see SmallRefundServiceTypeEnum
     */
    @FieldModified(displayName = "服务类型")
    private Integer serviceType;

    /**
     * 开始天数
     */
    @FieldModified(displayName = "开始天数")
    private Integer startDays;
    /**
     * 结束天数
     */
    @FieldModified(displayName = "结束天数")
    private Integer endDays;
    /**
     * 故障类型(1无故障 2有故障 )
     */
    @FieldModified(displayName = "故障类型")
    private Integer hitchType;
    /**
     * 退款方式(1正常退款 2特殊退款 )
     */
    @FieldModified(displayName = "退款方式")
    private Integer refundType;
    /**
     * 故障类型(1无故障 2有故障 )
     */
    private List<Integer> hitchTypeList;
    /**
     * 退款方式(1正常退款 2特殊退款 )
     */
    private List<Integer> refundTypeList;
    /**
     * 补差价百分比
     */
    @FieldModified(displayName = "补差比例")
    private BigDecimal differentPricePercent;
    /**
     * 是否附件折价
     */
    @FieldModified(displayName = "附件折价")
    private Boolean isPartsDifferent;
    /**
     * 政策内容
     */
    @FieldModified(displayName = "换货政策")
    private String policyContent;
}