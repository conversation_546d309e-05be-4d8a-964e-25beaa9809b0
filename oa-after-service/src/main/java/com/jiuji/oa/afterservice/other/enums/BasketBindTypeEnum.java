package com.jiuji.oa.afterservice.other.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 购物车绑定类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BasketBindTypeEnum implements CodeMessageEnumInterface {
    /**
     * 绑定类型枚举
     */
    NORMAL_BIND(0, "正常绑定"),
    UNKNOWN_FUNCTION_BIND(1, "未知功能绑定"),
    FILM_GIFT_BIND(2, "贴膜赠送绑定");

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 编码对应信息
     */
    private final String message;
}
