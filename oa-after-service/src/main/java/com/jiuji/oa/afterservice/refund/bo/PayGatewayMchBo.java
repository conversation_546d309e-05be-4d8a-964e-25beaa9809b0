package com.jiuji.oa.afterservice.refund.bo;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/5/12 11:50
 * @Description 支付网关商户信息表
 */
@Data
@ApiModel(description = "支付网关商户信息表")
public class PayGatewayMchBo implements Serializable {
    private static final long serialVersionUID = -8768006201437000371L;

    /**
     * 自增长主键
     */
    @ApiModelProperty(value = "自增长主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商户编号
     */
    @ApiModelProperty(value = "商户编号")
    private String mchNo;

    /**
     * 商户名
     */
    @ApiModelProperty(value = "商户名")
    private String mchName;

    /**
     * 商户私钥
     */
    @ApiModelProperty(value = "商户私钥")
    private String mchPrivateKey;

    /**
     * 商户公钥
     */
    @ApiModelProperty(value = "商户公钥")
    private String mchPublicKey;

    /**
     * 支付网关地址
     */
    @ApiModelProperty(value = "支付网关地址")
    private String gatewayHost;

    /**
     * 支付网关公钥
     */
    @ApiModelProperty(value = "支付网关公钥")
    private String gatewayPublicKey;

    /**
     * 支付网关AES秘钥
     */
    @ApiModelProperty(value = "支付网关AES秘钥")
    private String gatewayAesKey;
}