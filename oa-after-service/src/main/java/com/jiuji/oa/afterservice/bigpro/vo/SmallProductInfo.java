package com.jiuji.oa.afterservice.bigpro.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class SmallProductInfo {

    private String productName;

    private String productColor;

    private BigDecimal price;
    private Integer basketId;

    /**
     * 订单数量(不变)
     */
    private Integer basketCount;

    /**
     * 退货数量
     */
    private Integer count;

    private Integer ppid;

    private List<String> barCodeList;
}
