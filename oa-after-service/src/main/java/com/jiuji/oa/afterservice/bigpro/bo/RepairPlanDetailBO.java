package com.jiuji.oa.afterservice.bigpro.bo;

import com.jiuji.oa.afterservice.bigpro.po.RepairFault;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlan;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlanMaster;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 维修方案详情BO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@ApiModel(value = "RepairPlanDetailBO对象", description = "维修方案详情BO")
public class RepairPlanDetailBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "维修方案主表信息")
    private RepairPlanMaster master;

    @ApiModelProperty(value = "维修故障列表")
    private List<RepairFaultDetailBO> faultList;
} 