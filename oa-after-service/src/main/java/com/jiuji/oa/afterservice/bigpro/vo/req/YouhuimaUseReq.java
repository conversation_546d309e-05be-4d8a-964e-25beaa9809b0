package com.jiuji.oa.afterservice.bigpro.vo.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class YouhuimaUseReq {

    @NotNull(message = "维修单号不能为空")
    private Integer shouHouId;

    @NotBlank(message = "优惠码不能为空")
    private String code;

    @NotBlank(message = "操作人不能为空")
    private String operateUser;

    /**
     * 用户
     */
    private Integer userId;
}
