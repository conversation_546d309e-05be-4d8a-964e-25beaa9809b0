package com.jiuji.oa.afterservice.bigpro.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jiuji.oa.afterservice.bigpro.dto.RepairPlanSaveDTO;
import com.jiuji.oa.afterservice.bigpro.enums.AccessoryTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.PlanTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.RepairPlanMasterStatusEnum;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.service.RedeemService;
import com.jiuji.oa.afterservice.bigpro.service.RepairPlanMasterService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.vo.RepairFaultVO;
import com.jiuji.oa.afterservice.bigpro.vo.RepairPlanDetailVO;
import com.jiuji.oa.afterservice.bigpro.vo.RepairPlanMasterTypeVO;
import com.jiuji.oa.afterservice.bigpro.vo.req.RepairPlanConfirmReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.RepairPlanDetailReq;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.annotation.LogRecordAround;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

/**
 * <p>
 * 维修方案 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@RestController
@RequestMapping("/api/bigpro/repair-plan")
@Api(tags = "维修方案控制器")
public class RepairPlanController {

    @Resource
    private RepairPlanMasterService repairPlanMasterService;
    @Resource
    private ShouhouService shouhouService;
    @Resource
    private RedeemService redeemService;

    @PostMapping("/detail")
    @ApiOperation(value = "根据售后单ID查询维修方案详情")
    public R<RepairPlanDetailVO> getRepairPlanDetail(@RequestBody @Validated RepairPlanDetailReq req) {
        RepairPlanDetailVO repairPlanDetailVO = repairPlanMasterService.getRepairPlanDetailByShouHouId(req);
        //用户确认之后过滤没有选中的数据
        if(RepairPlanMasterStatusEnum.USER_CONFIRMATION.getCode().equals(repairPlanDetailVO.getStatus())){
            List<RepairFaultVO> faultList = repairPlanDetailVO.getFaultList();
            //如果所有计划列表为空或者所有计划列表中没有生成配件 直接移除
            if(CollUtil.isNotEmpty(faultList)){
                faultList.removeIf(item-> item.getPlanList().stream().noneMatch(plan -> Boolean.TRUE.equals(plan.getIsGenerateAccessory())));
            }
        }
        return R.success(repairPlanDetailVO);
    }

    @LogRecordAround(value = "网站查询大疆维修方法")
    @PostMapping("/detailToWeb")
    @ApiOperation(value = "根据售后单ID查询维修方案详情")
    public R<RepairPlanDetailVO> getRepairPlanDetailToWeb(@RequestBody @Validated RepairPlanDetailReq req) {
        Integer userId = req.getUserId();
        if(Objects.isNull(userId) || NumberConstant.ZERO.equals(userId)){
            throw new CustomizeException("用户ID不能为空");
        }
        RepairPlanDetailVO repairPlanDetailByShouHouId = repairPlanMasterService.getRepairPlanDetailByShouHouId(req);
        repairPlanDetailByShouHouId.getPlanTypeList().removeIf(item -> ObjectUtil.isNull(item.getId()) || NumberConstant.ZERO.equals(item.getId()));
        return R.success(repairPlanDetailByShouHouId);
    }

    @RepeatSubmitCheck
    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "保存或更新维修方案")
    public R<Integer> saveOrUpdateRepairPlan(@RequestBody  RepairPlanSaveDTO saveDTO) {
        return R.success(repairPlanMasterService.saveOrUpdateRepairPlan(saveDTO));
    }

    @RepeatSubmitCheck
    @LogRecordAround(value = "网站查询大疆维修方案提交确认")
    @PostMapping("/confirm")
    @ApiOperation(value = "确认维修方案")
    public R<Integer> confirmRepairPlan(@RequestBody @Validated RepairPlanConfirmReq req) {
        // 模拟登录 系统用户信息
        Integer shouHouId = req.getShouHouId();
        Shouhou shouhou = Optional.ofNullable(shouhouService.lambdaQuery().eq(Shouhou::getId, shouHouId).eq(Shouhou::getUserid, req.getUserId()).one())
                .orElseThrow(() -> new CustomizeException("未找到指定的售后单"));
        redeemService.simulateLogin(Optional.ofNullable(shouhou.getToareaid()).orElse(shouhou.getAreaid()));
        return R.success(repairPlanMasterService.confirmRepairPlan(req));
    }

    @GetMapping("/enums")
    @ApiOperation(value = "获取维修方案相关枚举")
    public R<Map<String, List<EnumVO>>> getEnums() {
        Map<String, List<EnumVO>> enumMap = new HashMap<>();
        List<EnumVO> planTypeEnum = EnumUtil.toEnumVOList(PlanTypeEnum.class);
        List<EnumVO> accessoryTypeEnum = EnumUtil.toEnumVOList(AccessoryTypeEnum.class);
        enumMap.put("planTypes", planTypeEnum);
        enumMap.put("accessoryTypes", accessoryTypeEnum);
        return R.success(enumMap);
    }

} 