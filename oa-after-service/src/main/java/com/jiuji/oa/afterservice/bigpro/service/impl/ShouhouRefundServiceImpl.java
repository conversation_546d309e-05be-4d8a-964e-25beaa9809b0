package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jiuji.oa.afterservice.batchreturn.service.BatchReturnService;
import com.jiuji.oa.afterservice.bigpro.bo.OpenIdInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.OpenValidInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouLogNoticeBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouRefundMapper;
import com.jiuji.oa.afterservice.bigpro.enums.BigShouhouOrderTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.refund.AfterTuikuanTransactBo;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.service.douyin.ThirdDouyinService;
import com.jiuji.oa.afterservice.bigpro.vo.refund.TuihuanFormVo;
import com.jiuji.oa.afterservice.bigpro.vo.refund.TuihuanHistoryVo;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouLogReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.DouYinCouponLogRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.db.MyDynamicDataSource;
import com.jiuji.oa.afterservice.common.constant.NumberConsts;
import com.jiuji.oa.afterservice.common.constant.PayConstants;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.*;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.csharp.returns.service.CsharpReturnService;
import com.jiuji.oa.afterservice.csharp.returns.vo.ReturnWayReqVo;
import com.jiuji.oa.afterservice.other.service.PayConfigService;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import com.jiuji.oa.afterservice.refund.service.way.ThirdOriginWayService;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.oa.afterservice.sys.service.Password2ValidService;
import com.jiuji.oa.afterservice.sys.service.ValidMemberService;
import com.jiuji.oa.afterservice.sys.vo.req.ValidReq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.OaVerifyUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 售后退款
 *
 * <AUTHOR>
 * @since 2021/10/26 14:53
 */
@Service
@Slf4j
public class ShouhouRefundServiceImpl implements ShouhouRefundService {

    private static final Pattern PATH_MATCH_NAME = Pattern.compile("^(([\u4e00-\u9fa5]{2,8})|([a-zA-Z]{2,16}))$");

    @Autowired
    private SysConfigClient sysConfigClient;
    @Autowired
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Autowired
    private ShouhouService shouhouService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ShouhouRefundMapper shouhouRefundMapper;
    @Resource
    private ValidMemberService validMemberService;
    @Resource
    private ShouhouTuihuanService shouhouTuihuanService;
    @Resource
    private ShouhouLogsService shouhouLogsService;

    @Autowired
    private TuiHuanOpenIdService tuiHuanOpenIdService;

    @Override
    public R<ShouhouRefundDetailVo> detail(Integer shouhouId) {
        if (shouhouId == null) {
            return R.error("售后id不能为空!");
        }
        ShouhouRefundDetailVo shouhouRefundDetail = new ShouhouRefundDetailVo();
        shouhouRefundDetail.setShouhouId(shouhouId);
        ShouhouTuiHuanPo tuiHuan = shouhouRefundMapper.getLastNotCompleteRefund(shouhouId, TuihuanKindEnum.TWXF.getCode());
        //尝试查找最新未完成的退款单
        if (tuiHuan != null) {
            shouhouRefundDetail.setRefundWay(tuiHuan.getTuiWay());
            shouhouRefundDetail.setRefundPrice(tuiHuan.getTuikuanM());
            shouhouRefundDetail.setComment(tuiHuan.getComment());
            shouhouRefundDetail.setTuihuanId(tuiHuan.getId());
            shouhouRefundDetail.setBankName(tuiHuan.getBankname());
            shouhouRefundDetail.setBankFuming(tuiHuan.getBankfuming());
            shouhouRefundDetail.setBankNumber(tuiHuan.getBanknumber());
            //设置流程信
            setProcessInfo(shouhouRefundDetail, tuiHuan);
            setRefundWayValid((url, info) -> {
                shouhouRefundDetail.setOpenIdUrl(url);
                shouhouRefundDetail.setOpenIdInfo(info);
            }, OpenValidInfoBo.from(tuiHuan).setOrderBusinessTypeEnum(OpenValidInfoBo.OrderBusinessTypeEnum.WEIXIU).setSubId(shouhouId));
            //原路径退款设置支付编号信息
            if (StrUtil.endWith(tuiHuan.getTuiWay(), REFUND_WAY_ORIGIN)) {
                shouhouRefundDetail.setPayRecords(shouhouRefundMapper.listPayRecordByTuihuanId(tuiHuan.getId()));
            }
        } else {
            R<ShouhouRefundDetailVo> rwR = addInfo(shouhouId, shouhouRefundDetail);
            if (!rwR.isSuccess()) {
                return rwR;
            }
            shouhouRefundDetail.setIsCanCancel(Boolean.FALSE);
        }
        R<ShouhouRefundDetailVo> result = R.success(shouhouRefundDetail);
        //租户隔离 租户不展示会员识别码
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        List<EnumVO> enumVOS = EnumUtil.toEnumVOList(TuihuanFormVo.ValidTypeEnum.class);
        if (!CommenUtil.isJiuJiXtenant(oaUserBO.getXTenant())) {
            enumVOS.removeIf(item -> Objects.equals(item.getValue(), NumberConsts.ONE));
        }
        result.put("validTypes", enumVOS);
        return result.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    /**
     * 设置退款方式的验证地址
     * @param callBack
     * @param validInfoBo
     */
    @Override
    public void setRefundWayValid(BiConsumer<OpenIdInfoBo.OpenIdUrlBo, OpenIdInfoBo> callBack, OpenValidInfoBo validInfoBo) {
        OpenIdInfoBo.OpenType openType = Optional.ofNullable(validInfoBo.getOpenType())
                .orElseGet(()->{
                    OpenIdInfoBo.OpenType ot = tuiHuanOpenIdService.getOpenType(validInfoBo.getTuiWay());
                    validInfoBo.setOpenType(ot);
                    return ot;
                });
        if (openType == null) {
            return;
        }
        boolean isNotValid = isNotValid(validInfoBo, openType);
        //返回地址信息
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        if (isNotValid) {
            //支付宝生成原路径退款,传递类型都为退款
            if (XtenantEnum.isJiujiXtenant()) {
                callBack.accept(tuiHuanOpenIdService.getValidUrlV2(validInfoBo, oaUser).orElse(new OpenIdInfoBo.OpenIdUrlBo()), null);
            } else {
                callBack.accept(tuiHuanOpenIdService.getValidUrl(validInfoBo, oaUser).orElse(new OpenIdInfoBo.OpenIdUrlBo()), null);
            }
        } else {
            //返回用户信息 支付宝生成原路径退款,传递类型都为退款
            callBack.accept(null,tuiHuanOpenIdService.getOpenUserInfo(openType, validInfoBo, oaUser).orElse(new OpenIdInfoBo()));
        }
    }

    @Override
    public boolean isNotValid(OpenValidInfoBo validInfoBo, OpenIdInfoBo.OpenType openType) {
        boolean isNotValid = StrUtil.isBlank(validInfoBo.getPayOpenId()) && openType == OpenIdInfoBo.OpenType.WE_CHAT
                || validInfoBo.getIdTypeEnum() != OpenValidInfoBo.IdTypeEnum.TUIHUAN_DETAIL_ID && openType == OpenIdInfoBo.OpenType.ALIPAY
                && !tuiHuanOpenIdService.getMemberAlipayAuthInfo(validInfoBo.getId(),validInfoBo.getIdTypeEnum()).isPresent()
                //详情的验证 payOpenId 必须不为空
                || validInfoBo.getIdTypeEnum() == OpenValidInfoBo.IdTypeEnum.TUIHUAN_DETAIL_ID && openType == OpenIdInfoBo.OpenType.ALIPAY
                && StrUtil.isBlank(validInfoBo.getPayOpenId());
        return isNotValid;
    }



    @Override
    @RepeatSubmitCheck(expression = "#{classFullName}:#{tuihuanForm.shouhouId}",message = "存在未完成的退款")
    @Transactional(rollbackFor = RuntimeException.class)
    public R<Integer> save(TuihuanFormVo tuihuanForm) {
        //校验数据的合法性
        assertCheckSave(tuihuanForm);
        //插入退款表
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        //sql控制并发
        if (shouhouRefundMapper.insertTuihuan(tuihuanForm, oaUser) < 1) {
            throw new CustomizeException("已经存在未完成的退款");
        }
        //如果是大件退维修费，则走售后审核逻辑
        if (tuihuanForm.getTuihuanKind().equals(TuihuanKindEnum.TWXF.getCode())){
            ShouhouTuihuanService shouhouTuihuanService = SpringUtil.getBean(ShouhouTuihuanService.class);
            shouhouTuihuanService.autoTuihuanCheck(tuihuanForm.getTuihuanId());
        }
        //原路径退款,插入netPayRefundInfo,sql控制并发,数据量少循环插入
        if (CollUtil.isNotEmpty(tuihuanForm.getPayRecords())) {
            //有一个插入失败全部回滚
            for (TuihuanFormVo.PayRecordVo payRecord : tuihuanForm.getPayRecords()) {
                if (shouhouRefundMapper.insertNetPayRefundInfo(payRecord, tuihuanForm.getTuihuanId(), NumberConstant.TWO, oaUser) < 1) {
                    throw new CustomizeException(StrUtil.format("{}交易号退款金额超额", payRecord.getTradeNo()));
                }
                if (shouhouRefundMapper.updateNetPayRefundPrice(payRecord.getId(),payRecord.getTradeNo(),payRecord.getMoney()) < 1) {
                    throw new CustomizeException(StrUtil.format("{}交易号退款金额超额", payRecord.getTradeNo()));
                }
            }
        }
        return R.success(tuihuanForm.getTuihuanId());
    }

    private void assertCheckSave(TuihuanFormVo tuihuanForm) {
        try {
            OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
            //获取售后单信息
            //Shouhou shouhou = shouhouService.getById(tuihuanForm.getShouhouId());
            Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(tuihuanForm.getShouhouId()), MTableInfoEnum.SHOUHOU,tuihuanForm.getShouhouId());
            //未取机,不允许退款
            Assert.isTrue(Boolean.TRUE.equals(shouhou.getIsquji()), "未取机不允许退款");
            //增加微信的限制
            Assert.isFalse(ObjectUtil.equal(tuihuanForm.getRefundWay(), ShouhouRefundService.WECHAT_REFUND_WAY)
                    && StrUtil.isBlank(tuihuanForm.getBankFuming()), "微信限制，需录入客户真实姓名，以方便退款！");
            //增加银行转账的验证
            Assert.isFalse(ObjectUtil.equal(tuihuanForm.getRefundWay(), ShouhouRefundService.REFUND_WAY_BANK)
                            && (StrUtil.isBlank(tuihuanForm.getBankFuming()) || StrUtil.isBlank(tuihuanForm.getBankName())
                            || StrUtil.isBlank(tuihuanForm.getBankNumber()))
                    , "银行转账开户银行姓名卡号都不能为空！");
            if (ObjectUtil.equal(tuihuanForm.getRefundWay(), ShouhouRefundService.WECHAT_REFUND_WAY)
                    && StrUtil.isNotBlank(tuihuanForm.getBankFuming())) {
                boolean isCorrectName = PATH_MATCH_NAME.matcher(tuihuanForm.getBankFuming()).matches();
                Assert.isFalse(!isCorrectName, "收款姓名录入有误，请检查");
            }
            //验证地区是否一致
            AreaInfoClient areaInfoClient = SpringUtil.getBean(AreaInfoClient.class);
            AreaInfo areaInfo = areaInfoClient.getAreaInfoById(shouhou.getAreaid()).getData();
            Assert.isTrue(Objects.equals(shouhou.getAreaid(), oaUser.getAreaId()), StrUtil.format("地区不符，请切换至{}再操作！", areaInfo.getArea()));
            Assert.isTrue(Objects.nonNull(shouhou) && Boolean.TRUE.equals(shouhou.getXianshi()), "售后单不存在");
            Assert.isTrue(tuihuanForm.getRefundPrice().compareTo(BigDecimal.ZERO) > 0, "退款金额必须大于0");
            BigDecimal maxRefundPrice = ShouhouRefundService.getMaxRefundPrice(shouhou);
            Assert.isTrue(tuihuanForm.getRefundPrice().compareTo(maxRefundPrice) <= 0, "退款金额必须小于等于{}", maxRefundPrice);
            //调用校验接口
            LambdaBuild<ValidReq> validReqBuild = LambdaBuild.create(new ValidReq());
            validReqBuild.set(ValidReq::setOrderId,Long.valueOf(tuihuanForm.getShouhouId())).set(ValidReq::setValidCode,tuihuanForm.getValidCode())
                    .set(ValidReq::setValidType,tuihuanForm.getValidType()).set(ValidReq::setBusinessType,BusinessTypeV1Enum.AFTER_REFUND.getCode());
            R<Boolean> verifyR = validMemberService.validByAssertCheck(validReqBuild.build(),tuihuanForm);
            if (!verifyR.isSuccess()) {
                throw new CustomizeException(verifyR.getUserMsg(), verifyR.getCode());
            }
            //加盟店判断
            Assert.isFalse(areaInfo.getKind1() != 1 && ("自提点余额".equals(tuihuanForm.getRefundWay()) || "余额".equals(tuihuanForm.getRefundWay())), "加盟店余额支付不可用");
            //支付配置验证 是否为原路径退款
            boolean isPayOnline = isPayOnline(tuihuanForm.getRefundWay(),oaUser);
            //非原路径退款 支付记录必须为空
            Assert.isFalse(!isPayOnline && CollUtil.isNotEmpty(tuihuanForm.getPayRecords()), "非原路径退款,支付记录必须为空");
            Assert.isFalse(isPayOnline && CollUtil.isEmpty(tuihuanForm.getPayRecords()), "原路径退款,退款交易号不能为空");
            //原路径退款退款,金额校验在insert进行校验,这不进行校验,只需重新计算tuikuanM的金额
            Optional<BigDecimal> tuikuanMOpt = tuihuanForm.getPayRecords().stream().map(TuihuanFormVo.PayRecordVo::getMoney).reduce((m1, m2) -> m1.add(m2));
            tuikuanMOpt.ifPresent(tuihuanForm::setRefundPrice);
            //如果有未完成的退款,不能重复提交
            ShouhouTuiHuanPo lastNcr = shouhouRefundMapper.getLastNotCompleteRefund(tuihuanForm.getShouhouId(), TuihuanKindEnum.TWXF.getCode());
            Assert.isTrue(lastNcr == null, "存在未完成的退款,退款id:{}", Optional.ofNullable(lastNcr).map(ShouhouTuiHuanPo::getId).orElse(0));
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }
        //校验通过
    }

    @Override
    public boolean isPayOnline(String refundWay, OaUserBO oaUser) {
        PayConfigService payConfigService = SpringUtil.getBean(PayConfigService.class);
        boolean isPayOnline = StrUtil.isNotBlank(payConfigService.getNickNameByPay(refundWay));
        if(XtenantEnum.isJiujiXtenant()){
            //九机的退款方式需要特殊处理
            isPayOnline = isPayOnline || PayConstants.checkOnlinePay(refundWay);
        }
        return isPayOnline;
    }

    private void setProcessInfo(ShouhouRefundDetailVo shouhouRefundDetail, ShouhouTuiHuanPo tuiHuan) {
        //设置流程信息
        List<ShouhouRefundDetailVo.CheckHistoryVo> checkHistorys = new LinkedList<>();
        ShouhouRefundDetailVo.ProcessStatus processStatus = ShouhouRefundService.addProcessInfo(tuiHuan, ShouhouRefundDetailVo.ProcessStatus.CHECK1, checkHistorys);
        ShouhouRefundDetailVo.ProcessStatus nextStatus = ShouhouRefundDetailVo.ProcessStatus.nextProcess(processStatus);
        if(nextStatus != ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK){
            shouhouRefundDetail.setIsCanCancel(Boolean.TRUE);
        }else{
            shouhouRefundDetail.setIsCanCancel(Boolean.FALSE);
        }

        //设置流程权值控制
        nextStatus = processStatusRank(nextStatus, tuiHuan.getTuiWay());
        shouhouRefundDetail.setProcessStatus(nextStatus.getCode());
        shouhouRefundDetail.setProcessName(nextStatus.getMessage());
        shouhouRefundDetail.setCheckHistorys(checkHistorys);
    }

    private ShouhouRefundDetailVo.ProcessStatus processStatusRank(ShouhouRefundDetailVo.ProcessStatus nextStatus, String tuiWay) {
        List<String> ranks = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).map(OaUserBO::getRank).orElseGet(Collections::emptyList);
        if (ranks.isEmpty()) {
            return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
        }
        switch (nextStatus) {
            case CHECK2:
                if (!RankEnum.hasAuthority(ranks, RankEnum.SHOUHOU_TUIHUAN_CHECK2)) {
                    return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                }
                break;
            case CHECK3:
                boolean isNoAllowCheck3 = !((RankEnum.hasAuthority(ranks, RankEnum.SHOUHOU_TUIHUAN_CHECK3_BANK) && "银行转账".equals(tuiWay))
                        || RankEnum.hasAuthority(ranks, RankEnum.XIAN_JIN_SHOUYIN)
                        || (RankEnum.hasAuthority(ranks, RankEnum.YU_E_SHOUYIN) && !"银行转账".equals(tuiWay)));
                if (isNoAllowCheck3) {
                    return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                }
                break;
            default:
                break;
        }
        return nextStatus;
    }

    private R<ShouhouRefundDetailVo> addInfo(Integer shouhouId, ShouhouRefundDetailVo shouhouRefundDetail) {
        //获取售后单信息
        //Shouhou shouhou = shouhouService.getById(shouhouId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU,shouhouId);
        if (shouhou == null || !Boolean.TRUE.equals(shouhou.getXianshi())) {
            return R.error("售后信息不存在");
        }

        BigDecimal maxRefundPrice = ShouhouRefundService.getMaxRefundPrice(shouhou);
        shouhouRefundDetail.setMaxRefundPrice(Optional.of(maxRefundPrice).filter(mrp -> mrp.compareTo(BigDecimal.ZERO) >= 0).orElse(BigDecimal.ZERO));
        //默认为最大金额
        shouhouRefundDetail.setRefundPrice(shouhouRefundDetail.getMaxRefundPrice());
        R<List<String>> rwR = listRefundWay(shouhouId, TuihuanKindEnum.TWXF.getCode());
        if (!rwR.isSuccess()) {
            return R.error(rwR.getUserMsg());
        }
        shouhouRefundDetail.setRefundWays(rwR.getData());
        // 大疆维修移除 现金和余额
        if(BigShouhouOrderTypeEnum.DJI.equals(shouhouService.getOrderType(shouhouId))){
            // 移除现金和余额退款方式
            List<String> refundWays = shouhouRefundDetail.getRefundWays();
            refundWays.removeIf(way -> Stream.of(ShouhouRefundService.XIAN_JIN, ShouhouRefundService.YU_E,
                    ShouhouRefundService.WECHAT_REFUND_WAY, ShouhouRefundService.ALIPAY_REFUND_WAY).anyMatch(wayConst -> wayConst.equals(way)));
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "大疆维修单移除现金和余额退款方式");

        }

        //九机三方收银的处理
        jiujiThirdShouyingReturnPrice(shouhou,shouhouRefundDetail);
        //设置流程状态
        ShouhouRefundDetailVo.ProcessStatus processStatus;
        if (shouhouRefundDetail.getMaxRefundPrice().compareTo(BigDecimal.ZERO) <= 0) {
            processStatus = ShouhouRefundDetailVo.ProcessStatus.NOT_SUBMIT;
        } else {
            processStatus = ShouhouRefundDetailVo.ProcessStatus.SUBMIT;
        }
        shouhouRefundDetail.setProcessStatus(processStatus.getCode());
        shouhouRefundDetail.setProcessName(processStatus.getMessage());
        return R.success(null);
    }

    /**
     * 九机三方收银退款处理
     * @param shouhou
     * @param shouhouRefundDetail
     */
    private void jiujiThirdShouyingReturnPrice(Shouhou shouhou, ShouhouRefundDetailVo shouhouRefundDetail) {
        //计算剩余可退金额
        BigDecimal yifuM = ObjectUtil.defaultIfNull(shouhou.getYifum(),BigDecimal.ZERO);
        List<ThirdOriginRefundVo> thirdOriginRefundVos = SpringUtil.getBean(ThirdOriginWayService.class).listAll(shouhouRefundDetail.getShouhouId(), TuihuanKindEnum.TWXF);
        List<Integer> currencyPay = StrUtil.splitTrim(CommonUtils.getResultData(sysConfigClient.getValueByCode(SysConfigConstant.NINE_MACHINE_CURRENCY_PAY_WAY),
                userMsg -> {
                    throw new CustomizeException("获取九机币配置异常");
                }), StringPool.COMMA).stream().map(Convert::toInt).filter(Objects::nonNull)
                .collect(Collectors.toList());
        BigDecimal totalThirdNotRefundPrice = thirdOriginRefundVos.stream()
                .peek(tor -> SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"[{}]收银id[{}]已退金额[{}],金额[{}]不可退",
                        tor.getReturnWayName(),tor.getShouyingId(),tor.getRefundedPrice(),tor.getRefundPrice()))
                // 过滤掉九机币的类型
                .filter(tor -> !currencyPay.contains(tor.getOtherType()))
                .map(ThirdOriginRefundVo::getRefundPrice)
                .filter(ObjectUtil::isNotNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //扣减掉抖音团购卷的可退金额
            totalThirdNotRefundPrice = totalThirdNotRefundPrice.subtract(SpringUtil.getBean(ThirdDouyinService.class)
                    .canRefundPrice(shouhou.getId(), DouYinCouponLogRes.SubKindEnum.AFTERSALES.getCode()));


        BigDecimal totalMaxRefundPrice = NumberUtil.max(yifuM.subtract(totalThirdNotRefundPrice),BigDecimal.ZERO);
        //扣减三方之后还有可退金额
        if (totalMaxRefundPrice.compareTo(BigDecimal.ZERO) >0){
            shouhouRefundDetail.setMaxRefundPrice(NumberUtil.min(shouhouRefundDetail.getMaxRefundPrice(),totalMaxRefundPrice));
        }else{
            //强制修改退款方式为三方原路径退
            shouhouRefundDetail.setRefundWays(thirdOriginRefundVos.stream().map(ThirdOriginRefundVo::getReturnWayName)
                    .map(returnWayName -> StrUtil.format("{}{}",returnWayName, ShouhouRefundService.REFUND_WAY_ORIGIN))
                    .distinct().collect(Collectors.toList()));
        }

    }

    @Override
    public R<List<ShouhouRefundDetailVo.PayRecordVo>> listPayRecord(Integer subId, String refundWay, Integer shouyinType) {
        try {
            Assert.isTrue(StrUtil.isNotBlank(refundWay), "退款方式不能为空");
            Assert.isTrue(Objects.nonNull(subId), "售后id不能为空");
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }
        return R.success(shouhouRefundMapper.listPayRecord(subId, shouyinType, StrUtil.replace(refundWay, REFUND_WAY_ORIGIN, "")));
    }

    @Override
    public R<List<TuihuanHistoryVo.TuihuanSimpleHistoryVo>> listHistoryBatch(Integer orderId, Integer tuihuanKind, Integer tuiKinds) {
        List<TuihuanHistoryVo.TuihuanSimpleHistoryVo> histBatchs = shouhouRefundMapper.listSimpleHistory(orderId, tuihuanKind, tuiKinds);
        return R.success(histBatchs);
    }

    @Override
    public R<TuihuanHistoryVo> getHistory(Integer id) {
        TuihuanHistoryVo result = Optional.ofNullable(shouhouRefundMapper.getHistory(id)).map(sth -> {
            TuihuanHistoryVo hist = new TuihuanHistoryVo();
            hist.setId(sth.getId()).setComment(sth.getComment()).setTuikuanM(sth.getTuikuanM())
                    .setTuiWay(sth.getTuiWay()).setPzId(sth.getPzId()).setCheckHistorys(new LinkedList<>());
            //审批流程信息
            ShouhouRefundService.addProcessInfo(sth, ShouhouRefundDetailVo.ProcessStatus.CHECK1, hist.getCheckHistorys());
            //设置退款用户信息
            setRefundWayValid((url, info) -> {
                hist.setOpenIdInfo(info);
            }, OpenValidInfoBo.from(sth).setOrderBusinessTypeEnum(OpenValidInfoBo.OrderBusinessTypeEnum.WEIXIU).setSubId(sth.getShouhouId()));
            //原路径退款串号信息
            if (StrUtil.endWith(sth.getTuiWay(), REFUND_WAY_ORIGIN)) {
                hist.setPayRecords(shouhouRefundMapper.listPayRecordByTuihuanId(sth.getId()));
            }
            return hist;
        }).orElse(null);

        return R.success(result).addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    @Override
    @RepeatSubmitCheck(argIndexs = {0})
    @Transactional(rollbackFor = RuntimeException.class)
    public R<Integer> cancelRefund(Integer tuiHuanId) {
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        //先处理退款id
        int result = shouhouRefundMapper.cancelRefund(tuiHuanId,oaUser);
        if (result < 1) {
            throw new CustomizeException("只有退款办理前的才可以撤销");
        }
        //删除returnDetails
        shouhouRefundMapper.deleteReturnDetails(tuiHuanId);
        //删除支付批次
        shouhouRefundMapper.cancelNetpayRecord(tuiHuanId);
        //更新退款金额
        shouhouRefundMapper.cancelNetPayRefundInfo(tuiHuanId);
        //退换审核流程优化需求撤销申请后需写入日志
        Integer shouhouId = shouhouTuihuanService.getShouhouIdbyTuihuanId(tuiHuanId);
        Optional.ofNullable(shouhouId).ifPresent(id -> {
            ShouhouLogReq shouhouLogReq = new ShouhouLogReq();
            shouhouLogReq.setWxId(id);
            shouhouLogReq.setType(1);
            shouhouLogReq.setContent("撤销申请操作，业务类型：大件退维修费");
            ShouhouLogNoticeBo shouhouLogNoticeBo = new ShouhouLogNoticeBo();
            shouhouLogNoticeBo.setNeedNotice(false);
            shouhouLogReq.setNotice(shouhouLogNoticeBo);
            shouhouLogsService.addShouhouLog(oaUser.getUserName(), shouhouLogReq.getWxId(), shouhouLogReq.getType(), shouhouLogReq.getContent(), shouhouLogReq.getNotice(), false, shouhouLogReq.getMessageTplId());
        });
        return R.success(result);
    }

    @Override
    @RepeatSubmitCheck(expression = "#{classFullName}:#{tuiHuan.shouhouId}")
    @Transactional(rollbackFor = RuntimeException.class)
    public R<Integer> submitCheck(ShouhouTuiHuanPo tuiHuan, Integer processStatus, String password2) {
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        ShouhouRefundDetailVo.ProcessStatus status = EnumUtil.getEnumByCode(ShouhouRefundDetailVo.ProcessStatus.class, processStatus);
        List<String> ranks = Optional.ofNullable(oaUser).map(OaUserBO::getRank).orElseGet(Collections::emptyList);
        try {
            Assert.isFalse(Objects.isNull(tuiHuan),"退款申请信息不存在");
            //获取售后单信息
            //Shouhou shouhou = shouhouService.getById(tuiHuan.getShouhouId());
            Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(tuiHuan.getShouhouId()), MTableInfoEnum.SHOUHOU,tuiHuan.getShouhouId());
            BigDecimal maxRefundPrice = ShouhouRefundService.getMaxRefundPrice(shouhou);
            //再次校验退款金额
            Assert.isTrue(tuiHuan.getTuikuanM().compareTo(maxRefundPrice) <= 0, "退款金额必须小于等于{}", maxRefundPrice);
            Assert.isTrue(status != null, "流程状态错误");
            Optional<OpenIdInfoBo.OpenType> openTypeOpt = Optional.ofNullable(tuiHuanOpenIdService.getOpenType(tuiHuan.getTuiWay()));
            //校验库的状态是否与当前状态一致
            ShouhouRefundDetailVo.ProcessStatus currStatus = ShouhouRefundService.addProcessInfo(tuiHuan, ShouhouRefundDetailVo.ProcessStatus.CHECK1, new LinkedList<>());
            Assert.isTrue(ShouhouRefundDetailVo.ProcessStatus.nextProcess(currStatus) == status,"审批失败,已经审核");
            Password2ValidService password2ValidService = SpringUtil.getBean(Password2ValidService.class);
            Assert.isFalse(status == ShouhouRefundDetailVo.ProcessStatus.CHECK2 && !password2ValidService.valid(password2, oaUser.getUserId())
                    ,"二级密码校验失败");
            Assert.isFalse(openTypeOpt.isPresent() && isNotValid(OpenValidInfoBo.from(tuiHuan), openTypeOpt.orElse(null))
                    , "需要先进行{}验证,才能审核", openTypeOpt.map(OpenIdInfoBo.OpenType::getMessage).orElse(""));
            Assert.isFalse(processStatusRank(status, tuiHuan.getTuiWay()) == ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK
                    , "你没有{}的权限", status.getMessage());
            //校验退款地区
            Integer shouhouAreaId = CommenUtil.currAreaId(shouhou.getToareaid(), shouhou.getAreaid());
            if (ObjectUtil.notEqual(oaUser.getAreaId(), shouhouAreaId)){
                return R.error(StrUtil.format("地区不符，请切换至{}再操作！",Optional.ofNullable(SpringUtil.getBean(AreaInfoClient.class)
                        .getAreaInfoById(shouhouAreaId)).map(R::getData).map(AreaInfo::getArea).orElse(Convert.toStr(shouhouAreaId))));
            }
            //二审,如果秒退使用了现金的金额,需要呼叫中心进行审核
            /*Assert.isFalse(status == ShouhouRefundDetailVo.ProcessStatus.CHECK2
                    && StrUtil.endWithIgnoreCase(tuiHuan.getTuiWay(),ShouhouRefundService.REFUND_WAY_SECONDS)
                    && !RankEnum.SMALL_PRO_CALL_CENTER.hasAuthority(ranks)
                    && maxRefundPrice.subtract(tuiHuan.getTuikuanM())
                            .compareTo(ObjectUtil.defaultIfNull(shouhouRefundMapper.remainingCashMoney(shouhou.getId()),BigDecimal.ZERO))<0,
                    "秒退金额大于非现金收银金额，请联系呼叫中心进行{}", status.getMessage());*/

            switch (status) {
                case CHECK1:
                case CHECK2:
                    int n = shouhouRefundMapper.updateCheck(tuiHuan.getId(), processStatus, oaUser);
                    Assert.isTrue(n > 0, "审批失败,已经审核");
                    return R.success(StrUtil.format("{}审批通过!", status.getMessage()));
                case CHECK3:
                    //退款办理操作 c# 接口
                    mobileHttpTuikuan(tuiHuan, oaUser);
                    return R.success("办理成功");
                default:
                    return R.error("流程状态错误");
            }
        } catch (IllegalArgumentException e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error(e.getMessage());
        }
    }

    @Override
    @RepeatSubmitCheck(argIndexs = {0})
    @Transactional(rollbackFor = RuntimeException.class)
    public R<Boolean> sendCode(Integer shouhouId, Integer tuihuanId, Integer tuihuanKind) {
        String mobile = CommenUtil.autoQueryHist(()->shouhouRefundMapper.getShouHouMobile(shouhouId),MTableInfoEnum.SHOUHOU,shouhouId);
        try {
            Assert.isTrue(StrUtil.isNotBlank(mobile), "售后单电话号码不能为空");
        } catch (IllegalArgumentException e) {
            return R.error(e.getMessage());
        }
        //发送验证码
        int code = RandomUtil.randomInt(1000, 10000);
        stringRedisTemplate.opsForValue().set(StrUtil.format(RedisKeys.SHOUHOU_TUIHUAN_VALID_CODE, shouhouId, tuihuanId, tuihuanKind)
                , String.valueOf(code), Duration.ofMinutes(NumberConstant.FIVE));
        log.info("售后id:{},退款分类:{},退款验证码:{}", shouhouId, tuihuanKind, code);
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        SmsService smsService = SpringUtil.getBean(SmsService.class);
       // String msg = StrUtil.format("尊敬的客户，验证码{}，5分钟内有效，切勿泄露。您正在通过短信验证方式，对售后单{}进行退款操作。如有疑问可致电咨询：************。", code, shouhouId);
        String msg = StrUtil.format("您正在进行[订单{}退款]操作，验证码{}。如非本人操作请忽略。", shouhouId,code);
        R<Boolean> result = smsService.sendSms(mobile, msg
                , DateUtil.localDateTime2LocalDate(LocalDateTime.now().minusMinutes(NumberConstant.TWO))
                , oaUser.getUserName(), smsService.getSmsChannelByTenant(oaUser.getAreaId(), ESmsChannelTypeEnum.VERIFICATION_CODE));
        if (result.isSuccess()) {
            return R.success("验证码发送成功", Boolean.TRUE);
        } else {
            return new R(ResultCode.SERVER_ERROR, result.getMsg(), result.getUserMsg()).addAllBusinessLog(result.businessLogs());
        }
    }

    /**
     * 获取符合条件退款方式列表
     *
     * @param id
     * @param type
     * @return
     */
    @Override
    public R<List<String>> listRefundWay(Integer id, Integer type) {
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        //sub_id = 1 能使用微信秒退和验证
        return SpringUtil.getBean(CsharpReturnService.class).getReturnWayJava(new ReturnWayReqVo().setAreaId(oaUser.getAreaId())
                .setSupportSeconds(1).setId(id).setTuiHuanKind(type));
    }

    @Override
    public void mobileHttpTuikuan(ShouhouTuiHuanPo tuiHuan, OaUserBO oaUser) {
        if(MyDynamicDataSource.isTaxModel()){
            //税务模式, 直接跳过退款办理
            return;
        }
        String inWcfUrl = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST).getData();
        String url = StrUtil.format("{}/oaApi.svc/rest/Tuikuan_Linqi", inWcfUrl);
        BatchReturnService batchReturnService = SpringUtil.getBean(BatchReturnService.class);
        String secret = batchReturnService.getSecretByCode(SecretEnum.SH_THJ.getCode());
        AfterTuikuanTransactBo data = new AfterTuikuanTransactBo().setId(tuiHuan.getId())
                .setAreaid(oaUser.getAreaId()).setUser(oaUser.getUserName()).setRank(oaUser.getRank());
        String inputJson = JSON.toJSONString(getInput(secret, data));
        HttpRequest request = HttpUtil.createPost(url).header(AbstractCurrentRequestComponent.REQUEST_HEADER_TOKEN
                , SpringContextUtil.getRequest().map(req -> req.getHeader(AbstractCurrentRequestComponent.REQUEST_HEADER_TOKEN)).orElse(""))
                .header(AbstractCurrentRequestComponent.REQUEST_HEADER_XTENANT, oaUser.getXTenant() + "");
        Optional.ofNullable(request.body(inputJson).execute())
                .ifPresent(sResult -> {
                    log.debug("退款请求地址:{} 退款输入参数: {}", url, inputJson);
                    if (sResult.isOk()) {
                        log.debug("退款返回结果:{}", sResult.body());
                        R r = JSON.parseObject(StrUtil.replace(StrUtil.strip(sResult.body(), "\""), "\\\"", "\""), R.class);
                        if (r.getCode() != ResultCode.SUCCESS) {
                            RRExceptionHandler.logError("退款办理", Dict.create().set("url",url).set("参数",inputJson),
                                    new CustomizeException(StrUtil.blankToDefault(r.getMsg(), r.getUserMsg())),
                                    msg -> {
                                        throw new CustomizeException(msg);
                                    });
                        }
                    } else {
                        //接口发生系统异常
                        RRExceptionHandler.logError("退款办理", Dict.create().set("url",url).set("参数",inputJson),
                                new CustomizeException(StrUtil.toString(sResult.getStatus())),
                                msg -> {
                                    throw new CustomizeException(msg, ResultCode.WEBSERVER_ERROR);
                                });
                    }
                });
    }

    private static Dict getInput(String secret, Object data) {
        long timeStamp = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        return Dict.create().set("item", Dict.create().set("sign", OaVerifyUtil.getOaSign(secret, data, timeStamp))
                .set("timestamp", timeStamp).set("Data", data));
    }

    @Override
    public ShouhouTuiHuanPo getLastNotCompleteRefund(Integer shouhouId, TuihuanKindEnum kindEnum){
        return shouhouRefundMapper.getLastNotCompleteRefund(shouhouId, kindEnum.getCode());
    }

    @Override
    public List<ShouhouRefundDetailVo.PayRecordVo> listPayRecordByTuihuanId(Integer tuiHuanId){
        return shouhouRefundMapper.listPayRecordByTuihuanId(tuiHuanId);
    }

    @Override
    public int updateNetPayRefundPrice(Integer id, String tradeNo, BigDecimal money) {
        return shouhouRefundMapper.updateNetPayRefundPrice(id,tradeNo, money);
    }

    @Override
    public int insertNetPayRefundInfo(TuihuanFormVo.PayRecordVo payRecord, Integer tuihuanId, Integer payType, OaUserBO inUser) {
        return shouhouRefundMapper.insertNetPayRefundInfo(payRecord, tuihuanId, payType, inUser);
    }

    @Override
    public int updateNetPayRefundMoney(BigDecimal newMoney, Integer id, BigDecimal oldMoney) {
        return shouhouRefundMapper.updateNetPayRefundMoney(newMoney,id, oldMoney);
    }

    @Override
    public int deleteNetPayRefundById(Integer refundId, Integer netRecordId) {
        return shouhouRefundMapper.deleteNetPayRefundById(refundId, netRecordId);
    }

}
