package com.jiuji.oa.afterservice.refund.vo.res.machine;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput;
import com.jiuji.oa.afterservice.refund.enums.ZheJiaPayEnum;
import com.jiuji.tc.utils.fastjson.TrimDeSerializeCodec;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 折价支付表单实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("折价支付表单实体")
public class ZheJiaPayFormVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 售后ID
     */
    @ApiModelProperty("售后ID")
    @NotNull(message = "售后ID不能为空")
    private Integer shouhouId;

    /**
     * @see ZheJiaPayEnum
     */
    @ApiModelProperty("折价类型编码")
    @NotNull(message = "折价类型编码")
    private Integer zheJiaPayCode;

    /**
     * 折价金额
     */
    @ApiModelProperty("折价应支付金额")
    @NotNull(message = "折价应支付金额不能为空")
    @DecimalMin(value="0", message = "折价应支付金额不能小于0")
    private BigDecimal zheJiaPayPrice;

    /**
     * 绑定配件类型支付列表
     */
    private List<PartTypePay> bindPartTypePayList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("绑定配件支付")
    public static class PartTypePay {
        /**
         * 配件类型
         * @see Wxkcoutput.PartTypeEnum
         */
        @TableField("part_type")
        @NotNull(message = "绑定支付的配件类型不能为空")
        private Integer partType;
        @DecimalMin(value = "0",message = "价格必须大于等于0")
        @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
        private BigDecimal price;
        /**
         * 绑定支付配件名称
         */
        @ApiModelProperty("绑定支付配件名称")
        @NotBlank(message = "绑定支付配件名称")
        private String productName;
    }
}
