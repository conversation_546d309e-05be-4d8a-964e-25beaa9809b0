package com.jiuji.oa.afterservice.bigpro.service;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.cloud.after.util.BaoXiuUtil;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.bo.yuyue.ShouhouYuYueBasicInfo;
import com.jiuji.oa.afterservice.bigpro.m.bo.RepairRecords;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.vo.req.ChaoshiZengpinListReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ToAreaSetReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.other.bo.BBSXPUsersMoneyBo;
import com.jiuji.oa.afterservice.other.vo.req.ShouhouTuihuanReq;
import com.jiuji.tc.common.vo.R;
import jodd.typeconverter.Convert;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
public interface ShouhouExService extends IService<Shouhou> {
    /**
     * 无理由文本
     */
    String WU_LI_YOU_TEXT = "天无理由退货";
    BigDecimal NO_REASON_REDUCE_MONEY = new BigDecimal("100.00");

    /**
     * 获取无理由的天数
     * @return
     */
    static int getWuLiYouDays(){
        return BaoXiuUtil.getWuLiYouDays(Convert.toLong(XtenantEnum.getXtenant()));
    }


    /**
     * 判断是否可以使用售后优惠码
     * @param shouhou
     * @param dr
     * @return
     */
    R<Boolean> isUseShouHouYouhuiMa(Shouhou shouhou,NumberCard dr);

    /**
     * 优惠码校验是否可用
     * @param shouhouId
     * @param youhuima
     * @return
     */
    R<UseYouhuiMaInfo> checkYouhuiMa(Integer shouhouId, String youhuima);

    /**
     * 获取维修单赠品
     *
     * @param shouhouId
     * @param type
     * @return
     */
    ShouHouZengpinBo getZengpinByShouhouid(Integer shouhouId, Integer type);

    /**
     * 获取取机已审核信息
     *
     * @param shouhouId
     * @return
     */
    String getCheckInfo(Integer shouhouId, ShouhouBo info);

    /**
     * 判断取机审核
     *
     * @param shouhouId
     * @return
     */
    String getCheckIng(Integer shouhouId, ShouhouBo info);

    /**
     * 服务出险记录查询
     *
     * @param imei
     * @param wxId
     * @return
     */
    List<ShouhouServiceOutBo> getServersList(String imei, Integer wxId);

    /**
     * 获取进水保详情
     *
     * @param shouhouId
     * @param serviceType 默认为进水保
     * @return
     */
    R<JinshuibaoInfoRes> getJinshuibaoInfo(Integer shouhouId, Integer serviceType);

    /**
     * 计算进水保费用
     *
     * @param price
     * @param tradeDate
     * @param serviceType
     * @return
     */
    BigDecimal getJinshuibaoFeiyong(BigDecimal price, LocalDateTime tradeDate, Integer serviceType);

    /**
     * 售后退换提交
     *
     * @param tuihuan
     * @return
     */
    R<String> shouhouTuihuan(ShouhouTuihuanReq tuihuan);

    /**
     * 提交售后退换（赠品退货） 扩展 带事务
     *
     * @param shouhouId
     * @param inuser
     * @param tuihuanKind
     * @param areaId
     * @return
     */
    R<String> submitTuiEx(Integer shouhouId, String inuser, Integer tuihuanKind, Integer areaId);

    /**
     * 根据售后编号 获取对应订单信息
     *
     * @param shouhouId
     * @return
     */
    ShouhouSubDetailBo loadSubInfoByShouhouid(Integer shouhouId);

    /**
     * 退换操作
     *
     * @param tuihuan
     * @param userid
     * @param basketId
     * @return
     */
    R<String> beginTuihuan(ShouhouTuihuanReq tuihuan, Integer userid, Integer basketId);

    /**
     * 获取最后一条测试信息
     *
     * @param shouhouId
     * @return
     */
    ShouhoutestInfo getLastTestInfoByShId(Integer shouhouId);

    /**
     * 获取售后维修周期
     *
     * @param shouhouId
     * @return
     */
    ShouhouWxzqRes getShouHouWeiXiuZqByShId(Integer shouhouId);


    /**
     * 客户数据备份相关操作
     *
     * @param stats
     * @param shouhouId
     * @return
     */
    R<String> bakDataOp(Integer stats, Integer shouhouId, String reason);

    /**
     * 获取所有故障类型
     *
     * @return
     */
    @Cached(name = "getTroubleList", expire = 30, timeUnit = TimeUnit.DAYS)
    List<ShouhouTroubleListRes> getTroubleList();

    /**
     * 获取售后表单相关故障
     *
     * @param shouhouId
     * @return
     */
    List<Integer> getShouhouTroubles(Integer shouhouId);

    /**
     * 服务拍照上传
     *
     * @param actName
     * @param fuwupic
     * @return
     */
    R<String> setFuwuPic(String actName, ShouhouFuwupic fuwupic);

    /**
     * 查询锁屏密码
     *
     * @param shouhouId
     * @return
     */
    R<ShouhouDeviceInfoRes> getLockPwd(Integer shouhouId);

    /**
     * 添加售后锁屏密码查看记录
     *
     * @param shouhouId
     * @return
     */
    R<String> addShouhouPwdLookLog(Integer shouhouId);

    /**
     * 获取锁屏密码查看记录
     *
     * @param shouhouId
     * @return
     */
    R<List<ShouhouPasswordlooklog>> getPwdlookLogsByShId(Integer shouhouId);

    /**
     * 查询物流信息
     *
     * @param company
     * @param number
     * @return
     */
    R<List<ExpressDetailResVo>> queryExpress(String company, String number);

    /**
     * 九机服务取机验证码
     *
     * @param shouhouId
     * @param subId
     * @return
     */
    R<Boolean> sendSrcode(Integer shouhouId, Integer subId);

    /**
     * 保存换货验证码
     *
     * @param shouhouId
     * @param code
     * @return
     */
    R<Boolean> saveSrcode(Integer shouhouId, String code);

    /**
     * 授权验证码
     *
     * @param shouhouId
     * @return
     */
    R<Boolean> saveSrcodebyAdmin(Integer shouhouId);

    /**
     * 更新售后单故障类型
     *
     * @param shouhouId
     * @param troubleIds
     */
    void updateShouhouTroubles(Integer shouhouId, List<Integer> troubleIds);

    /**
     * 修改售后单关联订单购买地区
     *
     * @param shouhouId
     * @param subId
     * @param isHuishou
     */
    void changeBuyAreaId(Integer shouhouId, Integer subId, Integer isHuishou);


    /**
     * 历史记录统一接口
     *
     * @param imei
     * @return
     */
    R<ShouhouHistoryRes> getHistory(String imei, Integer userId);

    /**
     * 硬件历史记录
     *
     * @param imei
     * @return
     */
    List<ShouhouHardwareHistoryRes> getHardwareHistory(String imei);

    /**
     * 软件历史记录
     *
     * @param imei
     * @return
     */
    List<Msoft> getMSoft(String imei);

    /**
     * 客户维修记录
     *
     * @param userId
     * @return
     */
    List<ShouhouUserHistoryRes> getUserHistory(Integer userId);

    /**
     * 配件点评提交
     *
     * @param dpid
     * @param dianping
     * @return
     */
    R<Boolean> pjEvaluate(Integer dpid, Integer dianping);

    /**
     * 查询支付宝优惠码对应订单id
     *
     * @param shouhouId
     * @param aliPayYouhuiPpids
     * @return
     */
    Integer queryAlipayYouhuiSubId(Integer shouhouId, List<Integer> aliPayYouhuiPpids);

    /**
     * 获取转地区信息
     *
     * @param id
     * @return
     */
    R<ShouhouToAreaRes> toArea(Integer id);

    /**
     * 售后转地区删除
     *
     * @param id
     * @param shouhouId
     * @return
     */
    R<Boolean> delToArea(Integer id, Integer shouhouId);

    /**
     * 转地区提交
     *
     * @param req
     * @return
     */
    R<Boolean> toAreaSubmit(ToAreaSetReq req);


    /**
     * 串号变更提交
     *
     * @param id
     * @param imei
     * @param comment
     * @return
     */
    R<String> imeiApply(Integer id, String imei, String comment);

    /**
     * 串号变更 审核
     *
     * @param id
     * @return
     */
    R<String> imeiApplyCheck(Integer id);

    /**
     * 串号变更撤销
     *
     * @param id
     * @return
     */
    R<String> imeiApplyDel(Integer id);

    /**
     * 查询串号信息
     *
     * @param id
     * @return
     */
    ShouhouImeiInfoBo getImeiInfo(Integer id);

    /**
     * 余额管理
     *
     * @param userId
     * @param basketId
     * @param reduceSaveMoney
     * @param inuser
     * @param comment
     * @param eKind
     * @param areaId
     * @param originAreaId
     * @param isSms
     * @param transferKind
     * @param limitSaveMoney
     * @return
     */
    R<String> saveMoney(Integer userId, Integer basketId, BigDecimal reduceSaveMoney, String inuser, String comment, Integer eKind, Integer areaId, Integer originAreaId, Boolean isSms, Integer transferKind, Boolean limitSaveMoney);

    /**
     * @param userId
     * @return
     */
    List<BBSXPUsersMoneyBo> queryBBsXpUserMoneyInfo(Integer userId);

    /**
     * 查询fuedu参数信息
     *
     * @param userId
     * @return
     */
    BigDecimal getFueduByUserId(Integer userId);

    /**
     * 更新维修费用
     *
     * @param shouhouId
     */
    void updateCostPriceById(Integer shouhouId);

    /**
     * 确认收银 （一般用先付款，再改价的情况。比如160，用户支付150，然后员工改价到150，这样维修单依然是未收银，但是实际已经收银完成了）
     *
     * @param shouhouId
     * @return
     */
    R<Boolean> enterShouyin(Integer shouhouId);

    /**
     * 快修使用优惠码
     *
     * @param shouhouId
     * @param code
     * @return
     */
    R<Boolean> useYouhuiMa(Integer shouhouId, String code,String operateUser);

    /**
     * 超时赠品查询
     *
     * @param req
     * @return
     */
    R<Page<ChaoshiZengPinRes>> getZengpingPageList(ChaoshiZengpinListReq req);

    /**
     * 赠送赠品
     *
     * @param ppid
     * @param shouhouId
     * @param csDays
     * @param type
     * @param remark
     * @return
     */
    R<String> zengSongPpid(Integer ppid, Integer shouhouId, BigDecimal csDays, Integer type, String remark);

    /**
     * 超时赔送赠品
     *
     * @param ppid
     * @param shouhouId
     * @param zpName
     * @param type
     * @param remark
     * @return
     */
    R<Boolean> zengSong(Integer ppid, Integer shouhouId, String zpName, BigDecimal memberprice, Integer type, String remark);

    /**
     * 删除售后退换（赠品退货） 扩展 带事务
     *
     * @param shouhouId   售后编号
     * @param tuihuanKind 退款类别
     * @return
     */
    R<Boolean> delTuiEx(Integer shouhouId, Integer tuihuanKind);

    /**
     * 根据特许授权获取渠道信息
     *
     * @return
     */
    R<List<Insource>> insourceList();

    /**
     * 取消渠道
     *
     * @param shouhouId
     * @return
     */
    R<Boolean> quxiaoQudao(Integer shouhouId);

    /**
     * 测试结果详情
     *
     * @param shouhouId
     * @param testId
     * @param showInfo
     * @return
     */
    R<ShouhouTestInfoRes> getShouhouTestInfo(Integer shouhouId, Integer testId, Boolean showInfo);

    /**
     * 售后评价
     *
     * @param id
     * @param pingjia
     * @param pjKind
     * @return
     */
    R<Boolean> pingJia(Integer id, String pingjia, Integer pjKind);

    /**
     * 取机
     *
     * @param shouhouId
     * @param remark
     * @return
     */
    R<Boolean> quJi(Integer shouhouId, String remark);

    /**
     * 获取核销金额
     * @param shouhouId
     * @return
     */
    BigDecimal getHeXiaoJE(Integer shouhouId);

    /**
     * 计算维修周期
     * @param wxzq 维修周期
     * @param shouhouInfoRes
     * @return
     */
    ShouhouWxzqRes calcDate(Integer wxzq, Shouhou shouhouInfoRes);

    List<Integer> getShouhouIdByFromid(Integer fromId);

    /**
     * 退款校验
     *
     * @param shouhouId
     * @return
     */
    R<Boolean> tuiCheck(Integer shouhouId);


    /**
     * 获取小件信息
     * @param shouhouId
     * @return
     */
    R<List<SmallProInfo>> getSmallProInfo(Integer shouhouId);

    /**
     *
     * @param smallId
     * @return
     */
    R<Integer> getBigProId(Integer smallId);

    /**
     * 官方延保
     * @param subId subId
     * @return String
     */
    String getOfficialInsurance(Integer subId);

    /**
     * 根据手机号查询预约单信息
     * @param mobile
     * @return
     */
    R<List<ShouhouYuYueBasicInfo>> getYuYueListByMobile(String mobile);


    /**
     * 接件提示文案信息
     * @param imei
     * @return
     */
    R<String> getSomeUsefulTipsWhenAfter(String imei);

    /**
     * 根据串号或手机号查询维修记录
     * @param imei
     * @param mobile
     * @return
     */
    List<RepairRecords> getRepairRecordsByImeiOrMobile(String imei,String mobile);

    /**
     * 接件时是否支持自定义机型、规格信息
     * @return
     */
    R<Boolean> isSupportModifyProductInfoWhenReceive();

    /**
     * imei的维修记录
     * @param imei
     * @return
     */
    R<Integer> countRepairRecord(String imei);

    /**
     * 亏损是否需要备注
     * @param shouhouId
     * @return
     */
    R<Boolean> qujiLossNeedRemark(Integer shouhouId);

    /**
     * 获取能够使用优惠码的配件
     * @param dr
     * @param shouhou
     * @return
     */
    R<Set<Integer>> listYouhuimaPj(NumberCard dr, Shouhou shouhou);

    boolean removeExcludePpidWxkcId(String excludePpIdsStr, Set<Integer> wxkcIds, List<Wxkcoutput> idPpidList);

    /**
     * 优惠码费用分摊
     * @param youhuimaTotal
     * @param wxkcoutputs
     */
    void youhuiMaAllocation(BigDecimal youhuimaTotal, List<Wxkcoutput> wxkcoutputs);

    /**
     * 存在使用了普通优惠卷
     * @param shouhouId
     * @return
     */
    boolean existsOrdinaryCoupon(Integer shouhouId);

    /**
     * 处理默认标签
     * @param maxPpirceId
     * @return
     */
    Boolean handleDeFaultLabel(Integer maxPpirceId);

    /**
     * 判断是否应该跳过成本校验
     * 满足任意条件不进行校验：
     * （1）条件①：维修单内只有：服务工时费（硬件）[ppid：420051]，或 拆机调试与主板进液清洗 [ppid：414747] 商品
     * （2）条件②：维修单内只有：维修耗材（分类id：801）商品（但不能超过2个）
     * （3）条件③：维修单内既有条件①，也有条件②的商品，且没有其他商品
     * @param shouhouId 维修单ID
     * @return true-跳过成本校验，false-需要进行成本校验
     */
    Boolean shouldBypassCostCheck(Integer shouhouId);
}
