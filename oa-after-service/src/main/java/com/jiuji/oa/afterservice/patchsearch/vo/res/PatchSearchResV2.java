package com.jiuji.oa.afterservice.patchsearch.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiuji.cloud.oaapi.vo.response.FilmServerItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Auther: qiweiqing
 * @Date: 2020/03/05
 * @Description:
 */
@Data
public class PatchSearchResV2 {

    /**
     * 小件信息-
     */
    private List<PatchSearchRes> patchList;
    /**
     * 小件信息- 年包赠送
     */
    private List<PatchGive> patchGiveList;

    /**
     * 订单信息
     */
    private SubInfo subInfo;

    @Data
    public static class SubInfo {
        private Integer subId;
        private String productName;
        private Integer priceId;
        private String originProductName;
        private String productColor;

        private FilmServerItem filmServerItem;
    }

    @Data
    public static  class PatchGive {

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @ApiModelProperty(value = "领取时间")
        private LocalDateTime receiverTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @ApiModelProperty(value = "赠送时间")
        private LocalDateTime sendTime;
        @ApiModelProperty(value = "状态")
        private Integer status;
        @ApiModelProperty(value = "状态")
        private String statusStr;
        @ApiModelProperty(value = "领取人UserId")
        private Integer receiverId;
        @ApiModelProperty(value = "领取人(手机号)")
        private String mobile;

    }
}
