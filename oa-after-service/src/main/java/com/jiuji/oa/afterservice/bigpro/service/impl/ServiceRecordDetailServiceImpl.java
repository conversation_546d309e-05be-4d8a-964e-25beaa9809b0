package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.ServiceRecordDetailMapper;
import com.jiuji.oa.afterservice.bigpro.po.ServiceRecordDetail;
import com.jiuji.oa.afterservice.bigpro.service.ServiceRecordDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 服务记录详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@Service
public class ServiceRecordDetailServiceImpl extends ServiceImpl<ServiceRecordDetailMapper, ServiceRecordDetail> implements ServiceRecordDetailService {



}
