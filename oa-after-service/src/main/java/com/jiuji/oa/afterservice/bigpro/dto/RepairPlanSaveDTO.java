package com.jiuji.oa.afterservice.bigpro.dto;

import com.jiuji.oa.afterservice.bigpro.vo.RepairPlanMasterTypeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 维修方案保存DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@ApiModel(value = "RepairPlanSaveDTO对象", description = "维修方案保存DTO")
public class RepairPlanSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID，新增时为null")
    private Integer id;

    @ApiModelProperty(value = "售后单ID")
    private Integer shouHouId;

    /**
     * 类型
     */
    private List<RepairPlanMasterTypeVO> planTypeList;

    /**
     * 修改原因
     */
    private String updateReason;

    @ApiModelProperty(value = "维修故障列表")
    private List<RepairFaultDTO> faultList;
}