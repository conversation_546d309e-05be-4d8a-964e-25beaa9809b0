package com.jiuji.oa.afterservice.common.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2021/4/30 10:16
 */
@Getter
@AllArgsConstructor
public enum RankEnum  implements CodeMessageEnumInterface{
    WX_STOCK_CHECK("2c2","维修配件采购审核"),
    WX_PRICE_EDIT("2d5","维修报价编辑"),
    RECOVER_SALE("6f8","旧件销售权值"),
    SHOUHOU_TUIHUAN_MACHINE_CHECK2("6c1","售后退换审核2"),
    RECOVER_CHECK("6c9","旧件审核权值"),
    WX_REIMBURSEMENT_CHECK("wxbx","维修报销审批权限"),
    WX_KC_TRANS_KC("2c0","转现"),
    WX_KC_TRANS_XC("xcz","转瑕疵"),
    SMALL_PRO_OLD_ZX("6f2","售后小件退换旧件转现"),
    SMALL_PRO_OLD_SCRAPPED("6f1","售后小件退换旧件报废"),
    SMALL_PRO_CALL_CENTER("777","呼叫中心权值")
    ,SMALL_PRO_CHANGE_PRICE("dgj2","小件单修改费用")
    ,SHOUHOU_TUIHUAN_CHECK2("63","售后退订单审核2")
    ,BATCH_TUIHUAN_CHECK2("65","批量退款审核2")
    ,SHOUHOU_TUIHUAN_CHECK3_BANK("92","售后退订单审核3银行转账")
    ,YU_E_SHOUYIN("11","余额收银")
    ,XIAN_JIN_SHOUYIN("27","现金收银")
    ,SHOUHOU_STATISTICS("5c2","各门店售后维修业绩详细数据")
    ,JIUJI_SERVICE_ADVANCE("jjfu","九机服务高级授权")
    ,SMS_AUTH("dxsq","短信授权")
    ,SALE_TDJ_CHECK2("19","新机单退订审核2")
    ,SALE_TPJ_CHECK2("52","新机单小件退款审核2")
    ,TAX("tax","税收的权值")
    ,XJ_TK("xjtk","退款办理—现金")
    ,YE_TK("yetk","退款办理—余额")
    ,MIAO_TK("mt","退款办理—微信支付宝秒退")
    ,YHZZ_TK("yhzz","退款办理—银行转账")
    ,NET_PAY_ORIGIN_TK("yljt","退款办理—网上支付原路径退款")
    ,OTHER_TK("qttk","退款办理—其他方式退款")
    ,MFKZ("mfkz","秒付控制权限")
    ,TKSQ("tksq","退款授权")
    ,GJZB("gjzb","高级质保修改权限")
    ,XHSH("xhsh","现货审核")
    ,XJT_NORMAL("xjt1","小件正常退款二审")
    ,XJT_SPECIAL("xjt2","小件特殊退款二审")
    ,XJT_NORMAL_9JI("xth1","小件正常退换审核(九机)")
    ,XJT_HUAN_NORMAL_9JI("xth2","小件换货审核(正常换)(九机)")
    ,XJT_SPECIAL_9JI("xth3","小件特殊退换审核(九机)")
    ,XJT_YYTK("yytk","小件退款二审 运营商业务退款审核")
    ,SHOUHOU_TUIHUAN_MACHINE_CHECK1_9JI("th1s","大件售后退换一审(九机)")
    ,SHOUHOU_TUIHUAN_MACHINE_CHECK2_9JI_REGULAR_NO_FAULT("th21","大件正常退换&无故障二审(九机)")
    ,SHOUHOU_TUIHUAN_MACHINE_CHECK2_9JI_REGULAR_HAS_FAULT("th22","大件正常退换&有故障二审(九机)")
    ,SHOUHOU_TUIHUAN_MACHINE_CHECK2_9JI_SPECIAL("th23","大件特殊退换二审(九机)")


    ;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String message;

    /**
     * 是否有权限
     * @param rank
     * @param rankEnum
     * @return
     */
    public static boolean hasAuthority(List<String> rank, @NonNull RankEnum rankEnum) {
        return rank.contains(rankEnum.getCode());
    }

    public boolean hasAuthority(List<String> rank) {
        return rank.contains(this.getCode());
    }

    public boolean noHasAuthority(List<String> rank) {
        return !hasAuthority(rank);
    }
}
