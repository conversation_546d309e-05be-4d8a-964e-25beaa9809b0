package com.jiuji.oa.afterservice.patchsearch.service;


import com.jiuji.oa.afterservice.patchsearch.vo.req.PatchSearchReq;
import com.jiuji.oa.afterservice.patchsearch.vo.req.PatchSearchReqV2;
import com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchRes;
import com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchResV2;
import com.jiuji.tc.common.vo.R;

import java.util.List;

/**
 * @Auther: qiweiqing
 * @Date: 2020/02/06
 * @Description:
 */
public interface PatchSearchService{


    /**
     * 根据basket查询我的贴膜列表
     *
     * @param personChangesReq
     */
    List<PatchSearchRes> listPageById(PatchSearchReq personChangesReq);

    /**
     * 根据basket查询我的贴膜列表
     *
     * @param personChangesReq
     */
    R<PatchSearchResV2> getPatchById(PatchSearchReqV2 personChangesReq);
}
