package com.jiuji.oa.afterservice.bigpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 配件类型枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Getter
@AllArgsConstructor
public enum AccessoryTypeEnum implements CodeMessageEnumInterface {
    
    /**
     * 维修配件（默认）
     */
    REPAIR_ACCESSORY(1, "维修配件"),
    
    /**
     * 维修成本
     */
    REPAIR_COST(2, "维修成本");
    
    private final Integer code;
    private final String message;
    
    /**
     * 根据code获取枚举
     * @param code 代码
     * @return 枚举
     */
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return ""; // 默认为维修配件
        }
        for (AccessoryTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getMessage();
            }
        }
        return "";
    }

    
    /**
     * 获取所有枚举值的列表
     * @return 枚举值列表
     */
    public static Map<Integer, String> getAll() {
        Map<Integer, String> map = new HashMap<>();
        for (AccessoryTypeEnum item : values()) {
            map.put(item.getCode(), item.getMessage());
        }
        return map;
    }
} 