package com.jiuji.oa.afterservice.other.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.bo.TuihuanBuyPriceOutBo;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo;
import com.jiuji.oa.afterservice.other.bo.ShouhouTuiHuanInfo;
import com.jiuji.oa.afterservice.other.bo.TuihuanBuyPriceBo;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.other.vo.req.ShouhouTuihuanReq;
import com.jiuji.oa.afterservice.other.vo.res.RecoverSubLogRes;
import com.jiuji.oa.afterservice.other.vo.res.ZhonyiRes;
import com.jiuji.tc.common.vo.R;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-15
 */
public interface ShouhouTuihuanService extends IService<ShouhouTuihuan> {

    /**
     * 良品自动退款办理判断
     * @param shouhouTuiHuanPo
     * @return
     */
    Boolean lpAutomaticRefund(ShouhouTuiHuanPo shouhouTuiHuanPo);


    /**
     * 良品自动退款办理判断
     * @param shouhouTuiHuanPo
     * @return
     */
    Boolean lpAutomaticRefund(Integer tuiHuanId);

    /**
     * description: <SqlServer-list>
     * translation: <SqlServer-list>
     *
     * @param wrapper 筛选条件
     * @return java.util.List<com.jiuji.oa.afterservice.po.ShouhouTuihuan>
     * <AUTHOR>
     * @date 16:12 2019/11/29
     * @since 1.0.0
     **/
    List<ShouhouTuihuan> listSqlServer(@Param(Constants.WRAPPER) Wrapper wrapper);

    /**
     * description: <SqlServer-getById>
     * translation: <SqlServer-getById>
     *
     * @param id 主键
     * @return com.jiuji.oa.afterservice.other.po.ShouhouTuihuan
     * <AUTHOR>
     * @date 10:57 2020/3/16
     * @since 1.0.0
     **/
    ShouhouTuihuan getByIdSqlServer(Integer id);

    /**
     * 退换显示 获取退换金额
     *
     * @return
     */
    TuihuanBuyPriceOutBo getTuihuanBuyPrice(TuihuanBuyPriceOutBo tuihuanBuyPrice);


    /**
     * 获取商品退换信息
     *
     * @param imei
     * @param mkcId
     * @param ishuishou
     * @return
     */
    TuihuanBuyPriceBo getTuihuanInfo(String imei, Integer mkcId, Integer ishuishou);

    /**
     * 获取ovg 返现金额
     *
     * @param basket_id
     * @param userid
     * @return
     */
    BigDecimal getOvgReturnPrice(Integer basket_id, Integer userid);

    /**
     * 获取中移金服减免金额
     *
     * @param shouhouId
     * @return
     */
    ZhonyiRes getZhonyiMoney(Integer shouhouId);

    /**
     * 提交 换订、退货 cjn
     *
     * @param th
     * @return
     */
    R tuihuan(ShouhouTuihuanReq th);

    /**
     * 验证退款短信
     *
     * @param sub_id
     * @param returnType
     * @param validtWay
     * @param code
     * @return
     */
    R<Boolean> validtReturnCode(Integer sub_id, String returnType, String validtWay, String code);

    /**
     * 验证支付密码
     *
     * @param userid
     * @param payPwd
     * @return
     */
    R<Boolean> validatePayPwd(Integer userid, String payPwd);

    /**
     * 支付宝优惠券ppriceid
     *
     * @return
     */
    List<Integer> aliPayYouhuiPpriceids();

    /**
     * 获取回收订单日志
     *
     * @param subId
     * @param type
     * @return
     */
    List<RecoverSubLogRes> getRecoverSubLogs(Integer subId, String type);

    /**
     * 校验是否绑定了openId
     *
     * @param tuihuanId
     * @return
     */
    Boolean checkHasOpenID(Integer tuihuanId);

    boolean deleteById(ShouhouTuihuan shouhouTuihuan);

    BigDecimal getSaveMoneyByUserId(Integer userId);

    /**
     * 根据用户编号获取1年内折价退换数量
     *
     * @param userId 用户id
     * @return 退换次数
     */
    List<ShouhouTuiHuanInfo> getAfterServicesDiscount(Integer userId);

    /**
     * 根据用户编号获取1年内折价退换数量
     *
     * @param userId 用户id
     * @return 退换次数
     */
    Integer countAfterServicesDiscount(Integer userId);

    /**
     * 获取良品无故障退款, 无折价数量
     * @param userId
     * @param year
     * @return
     */
    List<Integer> getDiscountSubIds(Integer userId, int year);

    /**
     * 自动退换审核接口
     * @param tuihuanId
     * @return {@link ShouhouRefundDetailVo.ProcessStatus}
     */
    R<Integer> autoTuihuanCheck(Integer tuihuanId);

    /**
     * 通过tuihuan Id 获取shouhou Id
     * @param tuiHuanId
     * @return
     */
    Integer getShouhouIdbyTuihuanId(Integer tuiHuanId);

    /**
     * 根据订单号获取发票类型
     *
     * @param subId 订单号
     * @param isHuiShou
     * @return
     */
    Integer getFapiaoKindBySubId(Integer subId, Integer isHuiShou);

    /**
     * 查询扣减保价
     *
     * @param basketId
     * @return
     */
    BigDecimal getKoujianBaojia(Integer basketId);

    /**
     * 根据售后id获取是否是回收单
     *
     * @param shouhouId 售后单号
     * @return 这里仅返回ishuishou和subId两个字段
     */
    Shouhou getIsHuishouByShouhouId(Integer shouhouId);

    /**
     * 根据新机单号获取售后退换记录id
     *
     * @param subId 新机单号
     */
    Integer getShThBySubId(Integer subId);
}
