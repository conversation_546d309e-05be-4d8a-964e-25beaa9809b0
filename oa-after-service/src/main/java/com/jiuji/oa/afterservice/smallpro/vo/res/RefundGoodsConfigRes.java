package com.jiuji.oa.afterservice.smallpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProServiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/23 14:19
 * @Description 小件退货配置实体
 */
@Data
@Accessors(chain = true)
@ApiModel("小件退货配置实体")
public class RefundGoodsConfigRes {
    @ApiModelProperty(value = "编号")
    private Integer id;
    /**
     * 配置名称
     */
    @ApiModelProperty(value = "配置名称/标题")
    private String title;
    /**
     * 服务类型
     * @see SmallProServiceTypeEnum
     */
    @ApiModelProperty(value = "服务类型")
    private Integer serviceType;

    /**
     * 服务类型名称
     */
    @ApiModelProperty(value = "服务类型名称")
    private String serviceTypeName;

    /**
     * 开始天数
     */
    @ApiModelProperty(value = "开始天数")
    private Integer startDays;
    /**
     * 结束天数
     */
    @ApiModelProperty(value = "结束天数")
    private Integer endDays;
    /**
     * 故障类型(1无故障 2有故障 )
     */
    @ApiModelProperty(value = "故障类型")
    private Integer hitchType;
    /**
     * 退款方式(1正常退款 2特殊退款 )
     */
    @ApiModelProperty(value = "退款方式")
    private Integer refundType;
    /**
     * 故障类型(1无故障 2有故障 )
     */
    @ApiModelProperty(value = "故障类型")
    private List<Integer> hitchTypeList;
    /**
     * 故障类型(1无故障 2有故障 )
     */
    @ApiModelProperty(value = "故障类型名称")
    private List<String> hitchTypeName;
    /**
     * 退款方式(1正常退款 2特殊退款 )
     */
    @ApiModelProperty(value = "退款方式")
    private List<Integer> refundTypeList;
    /**
     * 退款方式(1正常退款 2特殊退款 )
     */
    @ApiModelProperty(value = "退款方式名称")
    private List<String> refundTypeName;
    /**
     * 补差价百分比
     */
    @ApiModelProperty(value = "折价比例")
    private BigDecimal differentPricePercent;
    /**
     * 是否附件折价
     */
    @ApiModelProperty(value = "是否附件折价")
    private Boolean isPartsDifferent;
    /**
     * 政策内容
     */
    @ApiModelProperty(value = "政策内容")
    private String policyContent;
    /**
     * 租户
     */
    @ApiModelProperty(value = "租户")
    private String xtenant;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 配置值
     */
    @ApiModelProperty(value = "配置值类型")
    private List<RefundGoodsConfigValueBO> refundGoodsConfigValueBO;
    /**
     * 配置值名称 以逗号隔开
     */
    @ApiModelProperty(value = "配置值名称 以逗号隔开")
    private String configValueName;


    @Data
    public static class RefundGoodsConfigValueBO implements Serializable {
        /**
         * 配置类型(1 品牌 2分类 3 SPU 4 SKU)
         */
        private Integer configType;

        /**
         * 配置值
         */
        private List<Integer> configValue;
    }
}
