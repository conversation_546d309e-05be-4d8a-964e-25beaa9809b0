package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.cloud.after.enums.ServiceEnum;
import com.jiuji.cloud.after.vo.jiujiservice.ValidSaleJiujiServiceReqVo;
import com.jiuji.cloud.after.vo.jiujiservice.ValidSaleJiujiServiceResVo;
import com.jiuji.oa.afterservice.bigpro.bo.servicerecord.UseServiceRecordBo;
import com.jiuji.oa.afterservice.bigpro.po.ServiceRecord;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.function.BiConsumer;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
public interface ServiceRecordService extends IService<ServiceRecord> {



    /**
     * 获取九机服务信息
     * @param imei
     * @param userId
     * @param isSaveLog 是否
     * @return
     */
    ServiceInfoVO getRecord(String imei, Integer userId, boolean isSaveLog);

    ServiceInfoVO getRecord(String imei, Integer userIdParam, Integer subId, boolean isSaveLog);

    /**
     * 获取出险的单号信息
     * @param jiServiceRecord
     * @param serviceEnum
     * @param startTime
     * @param endTime
     * @param callBack
     */
    void getStopServiceId(ServiceRecord jiServiceRecord, ServiceEnum serviceEnum, LocalDateTime startTime, LocalDateTime endTime,
                          BiConsumer<BusinessTypeEnum, Integer> callBack);

    /**
     * 售后购买的服务
     *
     * @param imei
     * @param transactionDate
     * @return
     */
    @DS("ch999oanew")
    List<ServiceRecord> listShouhouService(String imei, LocalDateTime transactionDate);

    /**
     * 售后购买的服务
     *
     * @param imei
     * @param transactionDate
     * @return
     */
    @DS("oanew_his")
    List<ServiceRecord> listHistoryShouhouService(String imei, LocalDateTime transactionDate);

    /**
     * 主表存在care+信息
     * @param imeis
     * @param userId
     * @return
     */
    @DS("ch999oanew")
    Integer getCareId(List<String> imeis, Integer userId);

    /**
     * 存档表存在care+信息
     * @param imeis
     * @param userId
     * @return
     */
    @DS("oanew_his")
    Integer getHistoryCareId(List<String> imeis, Integer userId);

    /**
     * 获取部分服务信息 只包含 订单信息和 服务信息
     * @param imei
     * @return
     */
    abstract ServiceInfoVO getPartServiceInfo(String imei);

    /**
     * 是否可以购买服务判断
     * @param saleJiujiServiceReq
     * @return
     */
    R<ValidSaleJiujiServiceResVo> getValidSaleService(ValidSaleJiujiServiceReqVo saleJiujiServiceReq);

    /**
     * 该串号下是否有贴膜信息
     * @param imei 串号
     * @return
     */
    R<Boolean> getFilmByImei(String imei);

    /**
     * 服务出险
     * @param shouhou
     * @param serviceRecordId
     * @return
     */
    boolean useServiceRecord(Shouhou shouhou, Integer serviceRecordId);

    /**
     * 获取出险的服务记录
     * @param shouhou
     * @param serviceTypes
     * @param isGaoJiOutService
     * @param outServiceImeis
     * @return
     */
    UseServiceRecordBo getTop1UseServiceRecord(Shouhou shouhou, List<Integer> serviceTypes, boolean isGaoJiOutService, Set<String> outServiceImeis);
}
