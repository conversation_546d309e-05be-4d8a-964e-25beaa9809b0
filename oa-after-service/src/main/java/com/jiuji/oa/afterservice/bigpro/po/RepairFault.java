package com.jiuji.oa.afterservice.bigpro.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 维修故障表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("repair_fault")
@ApiModel(value = "RepairFault对象", description = "维修故障表")
public class RepairFault extends Model<RepairFault> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "关联维修方案主表ID")
    @TableField("master_id")
    private Integer masterId;

    @ApiModelProperty(value = "故障名称")
    @TableField("fault_name")
    private String faultName;

    @ApiModelProperty(value = "是否必填(0:否 1:是)")
    @TableField("is_required")
    private Integer isRequired;

    @ApiModelProperty(value = "创建人")
    @TableField("create_user")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField("update_user")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除(0:未删除 1:已删除)")
    @TableLogic
    @TableField("is_del")
    private Integer isDel;

} 