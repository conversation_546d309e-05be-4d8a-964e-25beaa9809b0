package com.jiuji.oa.afterservice.bigpro.vo.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 获取预约商品
 * @author: gengjiaping
 * @date: 2020/3/27
 */
@ApiModel
@Data
public class YuyueProductInfoRes {
    @ApiModelProperty("订单basketId")
    private Integer basketId;
    @ApiModelProperty("门店id")
    private Integer areaId;
    @ApiModelProperty("商品名称")
    private String productName;
    @ApiModelProperty("规格")
    private String productColor;
    @ApiModelProperty("价格")
    private BigDecimal price;
    @ApiModelProperty("用户id")
    private Integer userId;
    @ApiModelProperty("用户姓名")
    private String userName;
    @ApiModelProperty("真实姓名")
    private String realName;
    @ApiModelProperty("是否大件")
    private Boolean isMobile;
    @ApiModelProperty("订单商品数量")
    private Integer basketCount;
    @ApiModelProperty("商品ppid")
    private Integer ppid;

    private Integer pid;
    @ApiModelProperty("手机号码")
    private String mobile;
    @ApiModelProperty("订单类型")
    private Integer type;
    @ApiModelProperty("串号")
    private String imei;
    /**
     * 购买时间
     */
    private LocalDateTime subDate;

    private List<String> barCodeList;
}
