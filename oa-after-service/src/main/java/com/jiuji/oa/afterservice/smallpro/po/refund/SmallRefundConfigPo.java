package com.jiuji.oa.afterservice.smallpro.po.refund;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jiuji.oa.afterservice.smallpro.enums.SmallRefundServiceTypeEnum;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 小件退货实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName("small_refund_config")
public class SmallRefundConfigPo {
    /**
     * 自增长
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 配置名称
     */
    private String title;
    /**
     * 服务类型
     *
     * @see SmallRefundServiceTypeEnum
     */
    private Integer serviceType;

    /**
     * 开始天数
     */
    private Integer startDays;
    /**
     * 结束天数
     */
    private Integer endDays;
    /**
     * 故障类型(1无故障 2有故障 )
     */
    private Integer hitchType;
    /**
     * 退款方式(1正常退款 2特殊退款 )
     */
    private Integer refundType;
    /**
     * 补差价百分比
     */
    private BigDecimal differentPricePercent;
    /**
     * 是否附件折价
     */
    private Boolean isPartsDifferent;
    /**
     * 政策内容
     */
    private String policyContent;
    /**
     * 租户
     */
    private String xtenant;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    private Boolean isDel;

    /**
     * ppid的配置
     */
    @TableField(exist = false)
    private Integer ppid;

    /**
     * 可换货商品配置方式(1 品牌 2分类 3  SPU 4 SKU)
     */
    @Getter
    @AllArgsConstructor
    public enum RefundProductConfigTypeEnum implements CodeMessageEnumInterface {
        BRAND(1,"品牌",4,"brandID"),
        CATEGORY(2,"分类",3,"cid"),
        SPU(3,"SPU",2,"productid"),
        SKU(4,"SKU",1,"ppriceid")
        ;

        /**
         * 编码
         */
        private Integer code;

        /**
         * 编码对应信息
         */
        private String message;

        /**
         * 排序
         */
        private Integer rank;
        /**
         * 查询配置的列字段名
         */
        private String columnName;
    }

    /**
     * 故障类型(1无故障 2有故障 )
     */
    @Getter
    @AllArgsConstructor
    public enum HitchTypeEnum implements CodeMessageEnumInterface {
        NO_FAULT(1,"无故障"),
        FAULTY(2,"有故障"),
        ALL_FAULTY(3,"无故障+有故障")
        ;

        /**
         * 编码
         */
        private Integer code;

        /**
         * 编码对应信息
         */
        private String message;
    }

    /**
     * 退款方式(1正常退款 2特殊退款 )
     */
    @Getter
    @AllArgsConstructor
    public enum RefundTypeEnum implements CodeMessageEnumInterface {
        NORMAL_REFUND(1,"正常退款"),
        SPECIAL_REFUND(2,"特殊退款"),
        ALL_REFUND(3,"正常退款+特殊退款")
        ;

        /**
         * 编码
         */
        private Integer code;

        /**
         * 编码对应信息
         */
        private String message;
    }

    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class ConfigTypeAndValue{

        /**
         * 配置类型
         */
        @ApiModelProperty("配置类型")
        private SmallRefundConfigPo.RefundProductConfigTypeEnum configType;
        /**
         * 配置值
         */
        @ApiModelProperty("配置值")
        private Integer configValue;
    }
}