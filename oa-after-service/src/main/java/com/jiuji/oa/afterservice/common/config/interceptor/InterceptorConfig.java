package com.jiuji.oa.afterservice.common.config.interceptor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.cloud.oaapi.service.AuthenticationCloud;
import com.jiuji.cloud.oaapi.vo.response.UserInfoAuthCloudVO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.xtenant.MultitenancyInterceptor;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.interceptor.TraceIdInterceptor;
import com.jiuji.tc.utils.token.RemoteAnyTokenInterceptor;
import com.jiuji.tc.utils.token.TokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

/**
 * @author: gengjiaping
 * @date: 2019/11/6
 */
@Configuration
@Slf4j
public class InterceptorConfig implements WebMvcConfigurer {

    @Resource
    private OaTokenInterceptor oaTokenInterceptor;

    @Resource
    private StockLocationInterceptor stockLocationInterceptor;

    @Resource
    private RemoteAnyTokenInterceptor remoteAnyTokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        TraceIdInterceptor traceInterceptor = new TraceIdInterceptor();
        traceInterceptor.setInheritable(true);
        registry.addInterceptor(traceInterceptor).addPathPatterns("/**").order(-9999);
        registry.addInterceptor(new MultitenancyInterceptor());
        List<String> anyTokenList = Arrays.asList(
                "/api/wcf/getShouHouDetail",
                "/api/wcf/getYuYueDetail",
                "/api/wcf/addShouhouYuyue",
                "/api/wcf/delYuyue",
                "/api/wcf/getRepairRecordByImei",
                "/api/wcf/getServiceInfo/v3",
                "/api/wcf/listServiceInfo",
                "/api/wcf/getOrderClassByUserId",
                "/api/wcf/setShouhouyuyueFromSource",
                "/api/wcf/listSmallYuyueOrderOnGoing",
                "/api/wcf/groupRefundMachine/getRecoverNoReasonReduce",
                "/api/wcf/getYongjiAttachments",
                "/api/wcf/daiYongjiAttachmentsUpLoad",
                "/api/wcf/isUserOrder",
                "/api/afterElectronicTicket/verifyStatus/v1",
                "/api/afterElectronicTicket/selectTicketInfo/v1",
                "/api/Redeem/youhuimaUse",
                "/api/apiAfterSalesController/**"

        );
        // region oa token拦截
        registry.addInterceptor(oaTokenInterceptor).addPathPatterns("/api/**")
                .excludePathPatterns("/api/wcf/**")
                .excludePathPatterns("/api/bigpro/shouhou/getRepairRecordByImei")
                .excludePathPatterns("/api/smallpro/correctStatus")
                .excludePathPatterns("/api/smallpro/receiptPrinting")
                .excludePathPatterns("/api/bigpro/shouhouYuyue/shouhouyuyueExport")
                .excludePathPatterns("/api/bigpro/shouhou/addShouhouLog")
                .excludePathPatterns("/inapi/bigpro/attachments/getBatchUploadTemplateList")
                .excludePathPatterns("/api/sys/sysConfig/getDeployInfo")
                .excludePathPatterns("/api/sub/returnCheckBefore/submitCheckInfoWithOutToken")
                .excludePathPatterns("/api/electronic/check/getAppRoute")
                .excludePathPatterns("/api/bigpro/appointment/sendNoticeMsgToStaffBeforeCustomerArriveStore")
                //维修单采购配件入库时自动出库,不需要用户拦截
                .excludePathPatterns("/api/bigpro/productinfo/autoOutStockWhenCaiGouInStock")
                .excludePathPatterns("/api/ListOrganization/updateMobile")
                .excludePathPatterns("/api/apollo/**")
                .excludePathPatterns("/api/after/statistics/person/app/v1")
                .excludePathPatterns("/api/smallpro/filmCard/pushRepurchaseBuyMsg/v1")
                .excludePathPatterns("/api/bigpro/shouhou/handleDeFaultLabel/v1")
                .excludePathPatterns("/api/lossReview/pageFix")
                .excludePathPatterns("/bigpro/daiyongji/forwardMqtt")
                .excludePathPatterns("/api/SmallproConfig/selectConfigToWeb")
                .excludePathPatterns("/api/recover-price-config/getReviewConfigWeight/v1")
                .excludePathPatterns("/api/smallpro/handlePhoneCasePurchaseBuy/v1")
                .excludePathPatterns("/api/SmallproConfig/selectChangeInfoRes")
                .excludePathPatterns("/api/smallpro/recover/listReceiveSendCount")
                .excludePathPatterns("/api/apiAfterSalesController/getZheJiaPayEnum/v1")
                .excludePathPatterns("/api/smallpro/selectProductInfoListByImei/v1")
                .excludePathPatterns("/api/apiAfterSalesController/getZheJiaPayEnumBatch/v1")
                .excludePathPatterns("/api/Redeem/canUseCoupon")
                .excludePathPatterns("/api/Redeem/getYouHuiMaBySub")
                .excludePathPatterns("/api/bigpro/shouhou/isDJIRepairOrder")
                .excludePathPatterns("/api/bigpro/repair-plan/detailToWeb")
                .excludePathPatterns("/api/bigpro/repair-plan/confirm")
                .excludePathPatterns(anyTokenList)
        ;
        // endregion
        registry.addInterceptor(stockLocationInterceptor).addPathPatterns("/api/stock/stockLocationKc/*");
        if(!anyTokenList.isEmpty()){
            registry.addInterceptor(remoteAnyTokenInterceptor).addPathPatterns(anyTokenList)
                    .excludePathPatterns("/api/apiAfterSalesController/getZheJiaPayEnum/v1")
                    .excludePathPatterns("/api/apiAfterSalesController/getZheJiaPayEnumBatch/v1");
        }
    }

    @Bean
    public RemoteAnyTokenInterceptor remoteAnyTokenInterceptor() {
        Function<HttpServletRequest, Object> oaFun = request -> {
            return SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        };
        Function<HttpServletRequest, Object> webFun = request -> {
            String webToken = TokenUtil.getWebToken(request);
            if(StrUtil.isBlank(webToken)) {
                return null;
            }
            R<UserInfoAuthCloudVO> authR = SpringUtil.getBean(AuthenticationCloud.class).authenticationV2(webToken);
            if(authR.isSuccess()){
                return authR.getData();
            }else{
                log.warn("调用授权接口返回异常: {}", authR.getUserMsg());
            }
            return null;
        };
        return new RemoteAnyTokenInterceptor(Arrays.asList(oaFun, webFun));
    }

}
