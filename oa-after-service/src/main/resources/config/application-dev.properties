# data1 sqlserver
sqlserver.data.host=sqlserver.dev.ch999.cn
sqlserver.data.port=1433

## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.data.host}
sqlserver.ch999oanew.port=${sqlserver.data.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=devops
sqlserver.ch999oanew.password=devops00
## sqlserver:ch999oanew2
sqlserver.ch999oanew2.host=${sqlserver.data.host}
sqlserver.ch999oanew2.port=${sqlserver.data.port}
sqlserver.ch999oanew2.dbname=ch999oanew2
sqlserver.ch999oanew2.username=devops
sqlserver.ch999oanew2.password=devops00
sqlserver.ch999oanew2.is-enable=true
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.data.host}
sqlserver.ch999oanewReport.port=${sqlserver.data.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=devops
sqlserver.ch999oanewReport.password=devops00
## sqlserver:office
sqlserver.office.host=${sqlserver.data.host}
sqlserver.office.port=${sqlserver.data.port}
sqlserver.office.dbname=office
sqlserver.office.username=devops
sqlserver.office.password=devops00
## sqlserver:office2
sqlserver.office2.host=${sqlserver.data.host}
sqlserver.office2.port=${sqlserver.data.port}
sqlserver.office2.dbname=office2
sqlserver.office2.username=devops
sqlserver.office2.password=devops00
sqlserver.office2.is-enable=true
## sqlserver:ershou
sqlserver.ershou.host=${sqlserver.data.host}
sqlserver.ershou.port=${sqlserver.data.port}
sqlserver.ershou.dbname=ershou
sqlserver.ershou.username=devops
sqlserver.ershou.password=devops00
## sqlserver:web99
sqlserver.web999.host=${sqlserver.data.host}
sqlserver.web999.port=${sqlserver.data.port}
sqlserver.web999.dbname=web999
sqlserver.web999.username=devops
sqlserver.web999.password=devops00
## sqlserver:web99_other
sqlserver.web999_other.host=${sqlserver.data.host}
sqlserver.web999_other.port=${sqlserver.data.port}
sqlserver.web999_other.dbname=web999_other
sqlserver.web999_other.username=devops
sqlserver.web999_other.password=devops00
## sqlserver:after_write
sqlserver.after_write.host=${sqlserver.data.host}
sqlserver.after_write.port=${sqlserver.data.port}
sqlserver.after_write.dbname=ch999oanew
sqlserver.after_write.username=devops
sqlserver.after_write.password=devops00
## sqlserver:ch999oanewHis
sqlserver.ch999oanewHis.host=${sqlserver.data.host}
sqlserver.ch999oanewHis.port=${sqlserver.data.port}
sqlserver.ch999oanewHis.dbname=ch999oahis
sqlserver.ch999oanewHis.username=devops
sqlserver.ch999oanewHis.password=devops00
## sqlserver:ipaddress
sqlserver.ipaddress.host=sqlserver.dev.ch999.cn
sqlserver.ipaddress.port=1433
sqlserver.ipaddress.dbname=ipaddress
sqlserver.ipaddress.username=oa
sqlserver.ipaddress.password=oa2019!@#
##  mysql:oa_nc
mysql.url=mysql.dev.ch999.cn:3306
mysql.oa_nc.url=${mysql.url}
mysql.oa_nc.dbname=oa_nc
mysql.oa_nc.username=oa_nc
mysql.oa_nc.password=oa_nc2020#
## midl
redis.oa.host=redis.dev.ch999.cn
redis.oa.port=6379
redis.oa.password=google00
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}

redis.oa2.clusterNode=************:7001,************:7002,************:7003,************:7001,************:7002,************:7003
redis.oa2.password=google00
## \u65E5\u5FD7
logging.config.path=classpath:log/log4j2-jiujitest.xml
## mongoDB
##mongodb.url1=*************************************************************************************************************
mongodb.url1=mongodb://ch999oa:<EMAIL>:27017/ch999oa
## spring.autoconfigure.exclude
autoconfigure.exclude=org.springframework.cloud.consul.serviceregistry.ConsulAutoServiceRegistrationAutoConfiguration,\
  org.springframework.cloud.consul.serviceregistry.ConsulServiceRegistryAutoConfiguration,\
  org.springframework.boot.actuate.autoconfigure.health.HealthEndpointAutoConfiguration
#\uFFFD\u2CBF\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u013C\uFFFD\uFFFD\uFFFD\u05B7
url.source.path=i18nTest/url,i18nTest/constants
# rabbitMq-oaAsync
rabbitmq.oaAsync.url=rabbitmq.dev.ch999.cn
rabbitmq.oaAsync.port=5672
rabbitmq.oaAsync.username=oaAsync
rabbitmq.oaAsync.password=oaAsyncpwd
rabbitmq.oaAsync.vhost=oaAsync
rabbitmq.oa.url=rabbitmq.dev.ch999.cn
rabbitmq.oa.port=5672
rabbitmq.oa.username=oa
rabbitmq.oa.password=ch999
rabbitmq.oa.vhost=oa
## mysql:oa_core
mysql.oa_core.url=${mysql.url}
mysql.oa_core.dbname=oa_core
mysql.oa_core.username=oa_core
mysql.oa_core.password=oa_core2020#
## mysql:oa_log
mysql.oa_log.url=************:9383
mysql.oa_log.dbname=oa_log
mysql.oa_log.username=oa_log
mysql.oa_log.password=oa_log!@#
## image
image.upload.url=http://weedfs.dev.ch999.cn:9333
image.del.url=http://weedfs.dev.ch999.cn:5083
image.select.url=https://img.dev.9ji.com/

consul.host=************
consul.port=8500
spring.cloud.consul.discovery.instance-zone=9ji
instance-zone=9ji
sms.platform-url=https://testwww.9xun.com


apollo.url=http://************:6000
apollo.file=application-after.yml
yi.dong.yi_xiu_ge.url=https://yn.10086.cn/service_test/openapi/opensource/api.json
yi.dong.yi_xiu_ge.app_id=87100001
yi.dong.yi_xiu_ge.secret=gx@Ga*9kGp^J@fH5!V2%3XPM7]Myi&FT

after.lmstfy.host=************
after.lmstfy.token=01GP2BFHTHASM3TSYV4ZGKVKKF
feign.logger.my.isFull=true




mqtt.host=tcp://iot.9xun.com:1883
mqtt.username=admin
mqtt.clientId=oa-afterservice-${random.value}
mqtt.password=ch999
mqtt.timeout=10000
mqtt.keepalive=20  
mqtt.topic=afterservice-default-topic

jiuji.sys.moa=https://moa.dev.9ji.com
jiuji.sys.pc=https://oa.dev.9ji.com
jiuji.sys.web=https://www.dev.9ji.com
jiuji.sys.inWcf=http://inwcf.dev.9ji.com
jiuji.sys.oaWcf=http://oawcf.dev.9ji.com
