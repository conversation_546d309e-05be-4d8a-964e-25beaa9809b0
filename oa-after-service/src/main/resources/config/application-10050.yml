consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://test01.img.saas.ch999.cn/
  upload:
    url: http://**************:9333
instance-zone: 10050
jiuji:
  sys:
    moa: https://test01.moa.saas.ch999.cn
    pc: https://test01.oa.saas.ch999.cn
    inWcf: http://test01.inwcf.saas.ch999.cn
    oaWcf: http://test01.inwcf2.saas.ch999.cn
    xtenant: 10050
  xtenant: 50000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10050:Y4MQUAQR@**************:27017,**************:27017,**************:27017/ch999oa__10050
  url1: ***************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  oa_core:
    dbname: oa_core__10050
    password: oa_coreMtd
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_core__10050
  oa_nc:
    dbname: oa_nc__10050
    password: oa_ncHsi
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_nc__10050
  oa_log:
    dbname: oa_log__10050
    password: '012qAUX6m5#l'
    url: main.tidb.ch999.cn:9383
    username: oa_log__10050
office:
  sys:
    xtenant: 10050
rabbitmq:
  master:
    password: ZHESa
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10050
    vhost: oaAsync__10050
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: FunWO
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oa__10050
    vhost: oa__10050
  oaAsync:
    password: ZHESa
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10050
    vhost: oaAsync__10050
  printer:
    password: NkQHX
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: printer__10050
    vhost: printer__10050
redis:
  oa:
    host: **************
    password: google99
    port: 6387
    url: google99@**************:6387
sms:
  send:
    email:
      url: http://test01.sms.saas.ch999.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://test01.sms.saas.ch999.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10050
sqlserver:
  after_write:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  ch999oanew:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  ch999oanewReport:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  ch999oanewHis:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: officeFYnpU
    port: 1433
    username: office__10050
  oanewWrite:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  office:
    dbname: office__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: officeFYnpU
    port: 1433
    username: office__10050
  officeWrite:
    dbname: office__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: officeFYnpU
    port: 1433
    username: office__10050
  smallpro_write:
    dbname: ch999oanew__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "ch999oanewbEdGR"
    port: 1433
    username: ch999oanew__10050
  web999:
    dbname: web999__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: web999izklc
    port: 1433
    username: web999__10050
  ipaddress:
    dbname: ipaddress__10050
    host: sqlserver.serv.xn.saas.ch999.cn
    password: ipaddressTYnwl
    port: 1433
    username: ipaddress__10050
url:
  delImgUrl: http://**************:5083
  oa-push-info: http://test01.inwcf.saas.ch999.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://test01.img.saas.ch999.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://**************:9333

after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'

apollo:
  url:
mqtt:
  host: tcp://iot.9xun.com:1883
  topic: afterservice-default-topic
  clientId: oa-afterservice-${random.value}
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
