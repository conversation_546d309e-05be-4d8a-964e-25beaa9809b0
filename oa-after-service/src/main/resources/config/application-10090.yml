consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://jsjz.img.saas.ch999.cn/
  upload:
    url: http://**************:9333
instance-zone: 10090
jiuji:
  sys:
    moa: https://jsjz.moa.saas.ch999.cn
    pc: https://jsjz.oa.saas.ch999.cn
    xtenant: 10090
  xtenant: 90000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10090:7sZNK09J8gOs@************:27017,************:27017,************:27017/ch999oa__10090
  url1: *************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10090
    password: Qhk5sbhuMvmt
    url: mysql.serv.hn.saas.ch999.cn:3306
    username: oa_core__10090
  oa_nc:
    dbname: oa_nc__10090
    password: 7#bKaFpE4iJO
    url: mysql.serv.hn.saas.ch999.cn:3306
    username: oa_nc__10090
office:
  sys:
    xtenant: 10090
rabbitmq:
  master:
    password: cghoj
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oaAsync__10090
    vhost: oaAsync__10090
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: EBANs
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oa__10090
    vhost: oa__10090
  oaAsync:
    password: cghoj
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oaAsync__10090
    vhost: oaAsync__10090
  printer:
    password: WrTFH
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: printer__10090
    vhost: printer__10090
redis:
  oa:
    host: ************
    password: google99
    port: 6381
    url: google99@************:6381
sms:
  send:
    email:
      url: http://jsjz.sms.saas.ch999.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://jsjz.sms.saas.ch999.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10090
sqlserver:
  after_write:
    dbname: ch999oanew__10090
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "1j17uNn^PqjM"
    port: 1433
    username: ch999oanew__10090
  ch999oanew:
    dbname: ch999oanew__10090
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "1j17uNn^PqjM"
    port: 1433
    username: ch999oanew__10090
  ch999oanewReport:
    dbname: ch999oanew__10090
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "1j17uNn^PqjM"
    port: 1433
    username: ch999oanew__10090
  ch999oanewHis:
    dbname: ch999oanew__10090
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "1j17uNn^PqjM"
    port: 1433
    username: ch999oanew__10090
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10090
    host: sqlserver.serv.hn.saas.ch999.cn
    password: BZShLrI6j#rj
    port: 1433
    username: office__10090
  oanewWrite:
    dbname: ch999oanew__10090
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "1j17uNn^PqjM"
    port: 1433
    username: ch999oanew__10090
  office:
    dbname: office__10090
    host: sqlserver.serv.hn.saas.ch999.cn
    password: BZShLrI6j#rj
    port: 1433
    username: office__10090
  officeWrite:
    dbname: office__10090
    host: sqlserver.serv.hn.saas.ch999.cn
    password: BZShLrI6j#rj
    port: 1433
    username: office__10090
  smallpro_write:
    dbname: ch999oanew__10090
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "1j17uNn^PqjM"
    port: 1433
    username: ch999oanew__10090
  web999:
    dbname: web999__10090
    host: sqlserver.serv.hn.saas.ch999.cn
    password: 2vfCi2UG1fL^
    port: 1433
    username: web999__10090
url:
  delImgUrl: http://data3:5083
  oa-push-info: http://jsjz.inwcf.saas.ch999.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://jsjz.img.saas.ch999.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://data3:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'