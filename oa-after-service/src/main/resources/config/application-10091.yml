consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.zfmall.com/
  upload:
    url: http://**************:9333
instance-zone: 10091
jiuji:
  sys:
    moa: https://moa.zfmall.com
    pc: https://oa.zfmall.com
    xtenant: 10091
  xtenant: 91000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10091:HtQSbdq75LrR@***********:27017,***********:27017,***********:27017/ch999oa__10091
  url1: **********************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname:
    password:
    url: :3306
    username:
  oa_core:
    dbname: oa_core__10091
    password: cee6u5Oi8mdm
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10091
  oa_nc:
    dbname: oa_nc__10091
    password: jHbly8CzhimM
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10091
office:
  sys:
    xtenant: 10091
rabbitmq:
  master:
    password: HMpXK
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10091
    vhost: oaAsync__10091
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: vMNYg
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10091
    vhost: oa__10091
  oaAsync:
    password: HMpXK
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10091
    vhost: oaAsync__10091
  printer:
    password: IMOGW
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10091
    vhost: printer__10091
redis:
  oa:
    host: ***********
    password: google99
    port: 6382
    url: google99@***********:6382
sms:
  send:
    email:
      url: http://sms.zfmall.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.zfmall.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10091
sqlserver:
  after_write:
    dbname: ch999oanew__10091
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "Zu95lHLLjG91"
    port: 1433
    username: ch999oanew__10091
  ch999oanew:
    dbname: ch999oanew__10091
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "Zu95lHLLjG91"
    port: 1433
    username: ch999oanew__10091
  ch999oanewReport:
    dbname: ch999oanew__10091
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "Zu95lHLLjG91"
    port: 1433
    username: ch999oanew__10091
  ch999oanewHis:
    dbname: ch999oanew__10091
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "Zu95lHLLjG91"
    port: 1433
    username: ch999oanew__10091
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10091
    host: sqlserver.serv.hd.saas.ch999.cn
    password: xBerBgVO4tNQ
    port: 1433
    username: office__10091
  oanewWrite:
    dbname: ch999oanew__10091
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "Zu95lHLLjG91"
    port: 1433
    username: ch999oanew__10091
  office:
    dbname: office__10091
    host: sqlserver.serv.hd.saas.ch999.cn
    password: xBerBgVO4tNQ
    port: 1433
    username: office__10091
  officeWrite:
    dbname: office__10091
    host: sqlserver.serv.hd.saas.ch999.cn
    password: xBerBgVO4tNQ
    port: 1433
    username: office__10091
  smallpro_write:
    dbname: ch999oanew__10091
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "Zu95lHLLjG91"
    port: 1433
    username: ch999oanew__10091
  web999:
    dbname: web999__10091
    host: sqlserver.serv.hd.saas.ch999.cn
    password: 8#rShmAIwWmE
    port: 1433
    username: web999__10091
url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.zfmall.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.zfmall.com/
  source:
      path: i18n/url
  uploadImgUrl: http://data3:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: afterservice-default-topic
  clientId: oa-afterservice-${random.value}
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
