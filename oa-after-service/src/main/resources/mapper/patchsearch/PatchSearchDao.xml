<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.patchsearch.dao.PatchSearchDao">
    <select id="getBasketPage" parameterType="com.jiuji.oa.afterservice.patchsearch.vo.req.PatchSearchReq"
            resultType="com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchRes">
        SELECT
        a.area_name as areaName,
        a.area area,
        s.sub_id subId,
        p.product_name + p.product_color product,
        s.sub_check subCheck,
        s.sub_date subDate,
        '销售订单' AS type
        FROM dbo.basket b with(nolock)
        LEFT JOIN dbo.sub s with(nolock) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.productinfo p with(nolock) ON b.ppriceid = p.ppriceid
        LEFT JOIN dbo.tiemoCardUserLog l with(nolock) ON l.basket_id = b.basket_id
        LEFT JOIN dbo.tiemoCard c with(nolock) ON l.cardId = c.id
	    LEFT JOIN dbo.areainfo a with(nolock) ON s.areaid = a.id
        WHERE
        c.basket_idBind =#{req.basketId}
        AND ISNULL( s.sub_check, 0 ) <![CDATA[<>]]> 4 UNION
        SELECT
        a.area_name as areaName,
        a.area area,
        s.id AS subId,
        s.Name AS product,
        s.Stats AS subCheck,
        s.Indate AS subDate,
        '售后小件订单' AS type
        FROM dbo.Smallpro s WITH(NOLOCK)
        LEFT JOIN dbo.SmallproBill b WITH(NOLOCK) ON b.smallproID= s.id
        LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON s.areaid = a.id
        WHERE
        b.basket_id = #{req.basketId}
        AND ISNULL( s.Stats, 0 ) <![CDATA[<>]]> 2
        AND s.Kind = 2
        and s.ServiceType = 4
    </select>

    <select id="getBasketSub" resultType="com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchRes">
        SELECT
        a.area_name as areaName,
        a.area area,
        s.sub_id subId,
        p.product_name + p.product_color product,
        s.sub_check subCheck,
        s.sub_date subDate,
        '销售订单' AS type
        FROM dbo.basket b with(nolock)
        LEFT JOIN dbo.sub s with(nolock) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.productinfo p with(nolock) ON b.ppriceid = p.ppriceid
        LEFT JOIN dbo.tiemoCardUserLog l with(nolock) ON l.basket_id = b.basket_id
        LEFT JOIN dbo.tiemoCard c with(nolock) ON l.cardId = c.id
        LEFT JOIN dbo.areainfo a with(nolock) ON s.areaid = a.id
        WHERE 1=1
        <if test="subIdList != null and subIdList.size>0">
            AND s.sub_id  IN
            <foreach collection="subIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="getBasketSmall" resultType="com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchRes">

        SELECT
        a.area_name as areaName,
        a.area area,
        s.id AS subId,
        s.Name AS product,
        s.Stats AS subCheck,
        s.Indate AS subDate,
        '售后小件订单' AS type
        FROM dbo.Smallpro s WITH(NOLOCK)
        LEFT JOIN dbo.SmallproBill b WITH(NOLOCK) ON b.smallproID= s.id
        LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON s.areaid = a.id
        WHERE 1=1
        <if test="smallProIdIdList != null and smallProIdIdList.size>0">
            AND s.id  IN
            <foreach collection="smallProIdIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listGive" resultType="com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchResV2$PatchGive">
        SELECT
        ypt.receiver_id as receiverId,
        ypt.status as statusV2,
        case  WHEN ypt.status = 0 THEN 0
        ELSE 1
        END AS status,
        case  WHEN ypt.status = 0 THEN '待领取'
        ELSE '已领取'
        END AS statusStr,
        ypt.create_time as sendTime,
        ypt.start_time as receiverTime,
        bu.mobile
        from year_package_transfer ypt with(nolock)
	    LEFT JOIN dbo.tiemoCard c with(nolock) ON ypt.origin_card_id = c.id
	    left join BBSXP_Users bu  with(nolock) on bu.ID = ypt.receiver_id
        WHERE 1=1 and isnull(c.isdel, 0) = 0 and ypt.status in (0,1,2)
        and c.basket_idBind =#{basketId}

        <if test="transferCodeList != null and transferCodeList.size>0">
            AND ypt.transfer_code  IN
            <foreach collection="transferCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
