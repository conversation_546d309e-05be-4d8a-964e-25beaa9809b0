<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.refund.dao.way.NetPayOriginWayMapper">
    <insert id="batchInsert">
        insert into shouhou_tuihuan_detail
        (refund_business_type,fk_tuihuan_id,refund_price,tui_way,tui_group,pay_kinds,record_id,create_time,create_user,update_time,is_del,third_refund_type)
        values
        <foreach collection="tuiWayDetails" item="tuiWayDetail" separator=",">
            (#{tuiWayDetail.refundBusinessType},#{tuihuanForm.tuihuanId},#{tuiWayDetail.refundPrice},#{tuiWayDetail.returnWayName},#{tuiWayDetail.groupCode},
             1,#{tuiWayDetail.netPayRecordId},getdate(),#{tuihuanForm.currUser.userName},getdate(),isnull(#{tuiWayDetail.isDel},0),
            <!--isDel true 其他方式退款的记录 2-->
            <choose>
                <when test="tuiWayDetail.isDel">2</when>
                <otherwise>null</otherwise>
            </choose>
             )
        </foreach>
    </insert>
    <insert id="batchInsertNetPayRefundInfo">
        insert into dbo.netPayRefundInfo(netRecordId ,price ,inuser ,dtime ,returnid,fk_tuihuan_detail_id)
        values
        <foreach collection="tuiWayDetails" separator="," item="tuiWayDetail">
            (#{tuiWayDetail.netPayRecordId},#{tuiWayDetail.refundPrice},#{tuihuanForm.currUser.userName},getdate(),#{tuihuanForm.tuihuanId},#{tuiWayDetail.id})
        </foreach>
    </insert>
    <update id="batchUpdateNetPayInfo">
        update dbo.netpay_record
        <set>
            <trim prefix="refundPrice=case" suffix=" end,">
                <foreach collection="tuiWayDetails" item="tuiWayDetail">
                    when  id = #{tuiWayDetail.netPayRecordId} then isnull(refundPrice,0)+#{tuiWayDetail.refundPrice}
                </foreach>
            </trim>
        </set>
        <where>
            <foreach collection="tuiWayDetails" separator=" or " item="tuiWayDetail">
                (id=#{tuiWayDetail.netPayRecordId}
                <if test="tuiWayDetail.tradeNo != null">
                    <!--只有手动传null才忽略判断-->
                    and trade_no=#{tuiWayDetail.tradeNo}
                </if>
                and money>=isnull(refundPrice,0)+#{tuiWayDetail.refundPrice}
                and isnull(refundPrice,0)+#{tuiWayDetail.refundPrice}>=0)
            </foreach>
        </where>


    </update>
    <update id="updateRefundSplitServiceFee">
        update dbo.netpay_record
        set refund_split_service_fee=case when refund_split_service_fee - #{refundSplitServiceFee} &lt; 0 then 0
                                        else refund_split_service_fee - #{refundSplitServiceFee} end
        where id=#{netRecordId}
    </update>
    <sql id="listNetPayRecordSql">
        select id netPayRecordId,payWay returnWayName,money-isnull(refundPrice,0) refundPrice,isnull(money,0) actualPayPrice,
               isnull(refundPrice,0) refundedPrice,trade_no tradeNo,sub_number subId,dtime,butie_kind buTieKind,
               butie_price buTiePrice
        from dbo.netpay_record with(nolock)
    </sql>
    <select id="listNetPayRecordInfo" resultType="com.jiuji.oa.afterservice.refund.vo.res.way.NetPayOriginRefundVo">
        <include refid="listNetPayRecordSql"></include>
        where type= #{type}
            and cast(dtime as date)>='2018-03-20'
            and isnull(payWay,'') != '小UPOS快速开单'
            <choose>
                <when test="tuiWays != null and !tuiWays.isEmpty()">
                    and payWay in
                    <foreach collection="tuiWays" open="(" close=")" separator="," item="tuiWay">
                        #{tuiWay}
                    </foreach>
                </when>
            </choose>
            and sub_number in
            <foreach collection="subIds" open="(" close=")" separator="," item="subId">
                '${subId}'
            </foreach>
    </select>
    <select id="listNetPayRecordInfoByIds" resultType="com.jiuji.oa.afterservice.refund.vo.res.way.NetPayOriginRefundVo">
        <include refid="listNetPayRecordSql"></include>
        where id in
        <foreach collection="recordIds" open="(" close=")" separator="," item="recordId">
            #{recordId}
        </foreach>
    </select>
    <select id="countAlipayYouHui" resultType="java.lang.Long">
        select count(1) from dbo.shouying y where sub_id=#{subId} and y.inuser='支付宝(pay1)'
            and exists(
                    select 1 from dbo.basket b where b.sub_id=y.sub_id and b.ppriceid in
                    <foreach collection="ppriceids" separator="," item="ppriceid" open="(" close=")">
                        #{ppriceid}
                    </foreach>
                )
    </select>
    <select id="getSubParentId" resultType="com.jiuji.oa.afterservice.refund.bo.ParentChildSubBo">
        select sub.subPID subId,1 idType,isnull(p.yifuM, 0) yifuM,isnull(p.sub_check,0) subCheck
        from dbo.sub with(nolock)
        left join dbo.sub p with(nolock) on p.sub_id = sub.subPID
        where sub.sub_id=#{subId} and sub.subPID is not null
    </select>
    <select id="listSubChildId" resultType="com.jiuji.oa.afterservice.refund.bo.ParentChildSubBo">
        select sub_id subId,2 idType, yifuM,sub_check subCheck from dbo.sub with(nolock)
        where subPID=#{subPId}
    </select>
    <select id="getLpSubParentId" resultType="com.jiuji.oa.afterservice.refund.bo.ParentChildSubBo">
        select rm.subPID subId,1 idType, p.yifuM,p.sub_check subCheck from dbo.recover_marketInfo rm with(nolock)
        inner join dbo.recover_marketInfo p with(nolock) on p.sub_id = rm.subPID
        where rm.sub_id=#{subId}
    </select>
    <select id="listLpSubChildId" resultType="com.jiuji.oa.afterservice.refund.bo.ParentChildSubBo">
        select sub_id subId,2 idType, yifuM,sub_check subCheck from dbo.recover_marketInfo with(nolock)
        where subPID=#{subPId}
    </select>

    <select id="getPayGatewayMch" resultType="com.jiuji.oa.afterservice.refund.bo.PayGatewayMchBo">
        SELECT TOP 1
               id,
               mch_no,
               mch_name,
               mch_private_key,
               mch_public_key,
               gateway_host,
               gateway_public_key,
               gateway_aes_key
        FROM t_pay_gateway_mch with(nolock)
    </select>
    <select id="getChannelConfigIdByNetPayRecordId" resultType="java.lang.String">
        SELECT
        TOP 1 a.channel_config_id
    FROM
        netpay_record nr with(nolock)
        LEFT JOIN alipayInfo a with(nolock) ON CAST(a.id AS nvarchar) = nr.trade_no
    WHERE
        a.channel_config_id IS NOT NULL
        AND nr.id = #{netPayRecordId}
    </select>
</mapper>
