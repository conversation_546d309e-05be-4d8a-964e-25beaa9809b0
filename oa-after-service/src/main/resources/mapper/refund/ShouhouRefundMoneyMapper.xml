<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.refund.dao.ShouhouRefundMoneyMapper">
    <sql id="getRefundSql">
        select isdel,sub_id,tuikuanM,id,dtime,inuser,comment,tuihuan_kind,tui_way,bankname,bankfuming,banknumber,shouhou_id,tuikuanM1,sub_idM,zhejiaM,
               check1,check2,check3,check1user,check2user,check3user,check1dtime,check2dtime,check3dtime,tuikuanlock,areaid,pzid,coinM,baitiaoM,kuBaiTiaoM,
               payOpenId,st.tui_kinds,smallproid,basket_id,buypriceM,faultType,checkType
        from shouhou_tuihuan st with(nolock)
    </sql>
    <sql id="andTuihuanMatchSubIdSql">
        <choose>
            <!-- 6 退订金 8 退订金(良品)-->
            <when test="tuihuanKind == 6 || tuihuanKind == 8 ">
                and st.sub_id=#{subId}
            </when>
            <!-- 7 退配件 9 小件换货 -->
            <when test="tuihuanKind == 7 || tuihuanKind == 9 || tuihuanKind == 13">
                and st.smallproid = #{subId}
            </when>
            <!-- 1 换机头 2 换主板 3 退款 4 换其它型号 5 退维修费  10 小件退维修费 11 退订金(维修费) -->
            <otherwise>
                and st.shouhou_id=#{subId}
            </otherwise>

        </choose>
    </sql>
    <insert id="insertTuihuan" keyColumn="id" useGeneratedKeys="true" keyProperty="tuihuanForm.tuihuanId">
        insert into shouhou_tuihuan(shouhou_id,tuihuan_kind,inuser,areaid,comment,tuikuanM,tuikuanM1,sub_id,inprice,coinM,dtime,tui_kinds,smallproid,zhejiaM)
        select #{tuihuanForm.shouhouId},#{tuihuanForm.tuihuanKind},#{tuihuanForm.currUser.userName},#{tuihuanForm.currUser.areaId},#{tuihuanForm.comment},
                #{tuihuanForm.refundPrice},#{tuihuanForm.tuikuanM1},#{tuihuanForm.subId},#{tuihuanForm.inPrice},#{tuihuanForm.coinM},getdate(),#{tuihuanForm.tuiKinds}
                <choose>
                    <!-- 7 退配件 9 小件换货 -->
                    <when test="tuihuanKind == 7 || tuihuanKind == 9 || tuihuanKind == 13">
                        ,#{subId}
                    </when>
                    <otherwise>
                        ,null
                    </otherwise>
                </choose>
                ,#{tuihuanForm.zhejiaM}
        where not exists(SELECT 1 FROM shouhou_tuihuan st where isnull(st.isdel,0) = 0 and st.tuihuan_kind = #{tuihuanForm.tuihuanKind}
                            <include refid="andTuihuanMatchSubIdSql"></include> and st.check3 is null);
    </insert>
    <update id="cancelTuihuanDetail">
        update shouhou_tuihuan_detail set is_del = 1,update_time = getdate() where isnull(is_del,0)=0 and fk_tuihuan_id = #{tuihuanId}
    </update>
    <select id="getLastNotCompleteRefund"
            resultType="com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo">
        <include refid="getRefundSql"></include>
        where isnull(st.isdel,0)=0 and st.tuihuan_kind = #{tuihuanKind}
        <include refid="andTuihuanMatchSubIdSql"></include>
        <choose>
            <!-- 6 退订金 8 退订金(良品)-->
            <when test="tuihuanKind == 6 || tuihuanKind == 8">
                and st.check3 is null
            </when>
            <!-- 7 退配件 9 小件换货 -->
            <when test="tuihuanKind == 7 || tuihuanKind == 9 || tuihuanKind == 13 ">

            </when>
            <!-- 1 换机头 2 换主板 3 退款 4 换其它型号 5 退维修费  10 小件退维修费 11 退订金(维修费) -->
            <otherwise>
                and st.check3 is null
            </otherwise>

        </choose>
    </select>

    <select id="getRefund" resultType="com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo">
        <include refid="getRefundSql"></include>
        where st.id = #{tuihuanId}
    </select>
    <select id="existOldTuihuan" resultType="java.lang.Boolean">
        select case when exists(select 1 from shouhou_tuihuan st with(nolock)
        <where>
            isnull(st.isdel,0)=0 and isnull(st.tui_kinds,0)=0 and st.tuihuan_kind = #{tuihuanKind}
            <include refid="andTuihuanMatchSubIdSql"></include>) then 1 else 0 end
        </where>
    </select>
    <select id="getShouhouMaxRefundPrice" resultType="com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo">
        select
               <choose>
                   <when test="tuihuanKind == 11">case when isnull(isquji,0) >0 or yifum is null or yifum &lt; 0 then 0.00 else yifum end as maxRefundPrice</when>
                   <otherwise>(case when yifum is null or yifum &lt; 0 then 0.00 else yifum end) as maxRefundPrice</otherwise>
               </choose>
               ,id orderId,4 businessType,isnull(toareaid,areaid) areaId,costPrice inPrice,yifum yifuM,feiyong yingfuM,userid userId,isnull(isquji,0) subCheck

        from shouhou with(nolock)
        where id=#{subId}
        <if test="tuihuanKind == 11">

        </if>
    </select>
    <select id="getSubMaxRefundPrice" resultType="com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo">
        select (case when s.sub_check &lt;&gt; 1 or s.yifuM is null or s.yifuM &lt; 0 then 0.00 else s.yifuM end) maxRefundPrice,
               s.sub_id orderId,1 businessType,s.areaid areaId, s.kcAreaid kcAreaId,s.yifuM yifuM,s.userid userId,s.sub_check subCheck,s.yingfuM
        from sub s with(nolock)
        where s.sub_id=#{subId}
    </select>
    <select id="getRecoverMaxRefundPrice" resultType="com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo">
        select (case when s.sub_check &lt;&gt; 1 or s.yifuM is null or s.yifuM &lt; 0 then 0.00 else s.yifuM end) maxRefundPrice,
               s.sub_id orderId,2 businessType,s.areaid areaId,s.yifuM yifuM,s.userid userId,s.sub_check subCheck,s.yingfuM
        from recover_marketInfo s with(nolock)
        where s.sub_id=#{subId}
    </select>
    <select id="existExchangedDiy" resultType="java.lang.Boolean">
        select case when exists(
                select 1 from DIYTimeCard dc with(nolock)
                where dc.purchase_basket_id in
                    <foreach collection="basketIds" open="(" close=")" item="basketId" separator=",">
                        #{basketId}
                    </foreach>
                    and isnull(dc.isdel,0) = 0 and dc.basket_id is not null
            ) then 1 else 0 end
    </select>
    <sql id="getGoodTuiHuanConfig">
        from dbo.shouhou_tuihuan_discount_cfg with (nolock)
         where getdate() between stime and etime
          and isnull(is_del, 0) = 0
          and business_kind = 2
          and user_id = #{userId}
    </sql>
    <select id="getGoodLeftCount" resultType="java.lang.Integer">
        select isnull(all_count, 0) - isnull(tui_count, 0) as leftCount
            <include refid="getGoodTuiHuanConfig"></include>
    </select>
    <select id="getRecoverReduce" resultType="com.jiuji.cloud.after.vo.refund.RecoverNoReasonReduceVo">
        select id configId, isnull(all_count, 0) allCount, isnull(all_count, 0) - isnull(tui_count, 0) as leftCount
             ,isnull(tui_count, 0) tuiCount,stime, etime, user_id userId
        <include refid="getGoodTuiHuanConfig"></include>
    </select>
    <select id="getGoodNoReasonTuihuanInfo" resultType="com.jiuji.oa.afterservice.refund.bo.NoReasonTuiHuanInfoVO">
        SELECT t.id, h.sub_id subId, t.check3 check3
        from shouhou_tuihuan t with (nolock)
         inner join shouhou h with (nolock) on t.shouhou_id = h.id
            inner join recover_marketInfo s with (nolock) on h.sub_id = s.sub_id
        where t.tuihuan_kind = 3
          and h.ishuishou = 1
          and isnull(t.isdel, 0) = 0
          and isnull(t.discount, 0) = 0
          and isnull(t.zhejiaM, 0) = 0
          and t.dtime between #{recoverReduce.stime} and #{recoverReduce.etime}
          and t.faultType = '无故障'
          and s.userid = #{recoverReduce.userId}
    </select>
</mapper>
