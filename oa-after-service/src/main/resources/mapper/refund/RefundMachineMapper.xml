<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.refund.dao.RefundMachineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="RefundDataResultMap" type="com.jiuji.oa.afterservice.refund.bo.RefundDataBo">
        <result column="price" property="price" />
        <result column="coinM" property="coinMoney" />
        <result column="yingfuM" property="yingfuMoney" />
        <result column="yifuM" property="yifuMoney" />
        <result column="basket_id" property="basketId" />
        <result column="sub_id" property="subId" />
        <result column="userid" property="userId" />
        <result column="tradeDate1" property="tradeDate1" />
        <result column="type" property="type" />
        <result column="product_peizhi" property="productPeizhi" />
        <result column="sub_check" property="subCheck" />
        <result column="areaid" property="areaId" />
        <result column="inprice" property="costPrice" />
        <result column="price1" property="price1" />
    </resultMap>

    <!-- listRefundDataBySubId -->
    <select id="listRefundDataBySubId" resultMap="RefundDataResultMap">
        <choose>
            <when test="isHuishou != null and isHuishou == 1">
                SELECT ISNULL(b.price2, b.price) AS price, isnull(s.coinM,0) coinM, isnull(s.yingfuM,0) yingfuM, isnull(s.yifuM,0) yifuM,
                       b.basket_id,b.sub_id, s.userid, s.tradeDate1, b.type, '' product_peizhi, s.sub_check, s.areaid, k.inprice, b.price1
                FROM recover_marketSubInfo b with(nolock)
                    INNER JOIN recover_marketInfo s with(nolock) ON b.sub_id = s.sub_id
                    INNER JOIN recover_mkc k with(nolock) ON b.basket_id = k.to_basket_id
                WHERE s.sub_check = 3
                    AND isnull(b.isdel, 0) = 0
                    AND mkc_check = 5
            </when>
            <otherwise>
                SELECT isnull(b.price_shouhou, b.price2) price, isnull(s.coinM,0) coinM, isnull(s.yingfuM,0) yingfuM, isnull(s.yifuM,0) yifuM,
                       b.basket_id,b.sub_id,s.userid, s.tradeDate1, b.type, b.product_peizhi, s.sub_check,s.areaid,
                       k.inbeihuoprice inprice, b.price1
                FROM basket b with(nolock)
                    INNER JOIN sub s with(nolock) ON b.sub_id = s.sub_id
                    INNER JOIN product_mkc k with(nolock) ON b.basket_id = k.basket_id
                WHERE s.sub_check = 3
                    AND isnull(b.isdel, 0) = 0
                    AND kc_check = 5
            </otherwise>
        </choose>

        <choose>
            <when test="mkcId != null and mkcId > 0">
                and k.id = #{mkcId}
            </when>
            <otherwise>
                and imei = #{imei}
            </otherwise>
        </choose>
        <if test="subId != null and subId > 0">
            and s.sub_id = #{subId}
        </if>
        <if test="basketId != null and basketId > 0">
            and b.basket_id = #{basketId}
        </if>
        <choose>
            <when test="isHuishou != null and isHuishou == 1">
                ORDER BY b.basket_id DESC
            </when>
            <otherwise>
                ORDER BY s.tradeDate1 DESC
            </otherwise>
        </choose>
    </select>

    <select id="getGiftBasketListByBasketId" resultType="com.jiuji.oa.afterservice.refund.vo.res.machine.TuiGiftBasket">
        SELECT TOP 10 b.ppriceid,b.basket_id,b.basket_count,p.product_name,p.product_color
        FROM dbo.basket b WITH(NOLOCK) LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON b.ppriceid=p.ppriceid
        WHERE b.giftid = #{basketId} AND b.[type]=1 AND ISNULL(b.isdel,0)=0
    </select>

    <select id="getShouhouById" resultType="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
        SELECT id, name, peizhi, problem, comment, username, mobile, tel, stats, baoxiu, inuser, imei, xianshi, contentcsdate, tradedate, modidate, feiyong, costprice, weixiuren, dyjid, offtime, area, shouyinglock, shouyingdate, shouyinguser, userid, kinds, isticheng, waiguan, result_dtime, issoft, modidtime, product_id, product_color, buyarea, pandian, pandiandate, toarea, istui, pandianinuser, ppriceid, mkc_id, isquick, wcount, weixiuzuid, weixiuzuid_jl, isweixiu, weixiudtime, weixiu_startdtime, orderid, isquji, isfan, pingjia, pingjia1, sub_id, webtype1, webtype2, webstats, ServiceType, basket_id, ishuishou, yuyueid, huiprint, weixiurentime, reweixiuren, sxname, sxmobile, sxsex, sxuserid, lockpwd, testuser, wxkind, wxConfig, noticetime, testtime, deviceid, devicepwd, youhuima, yuyueCheck, isXcMkc, isXcMkcInfo, wxTestTime, wxTestInfo, RepairLevel, areaid, toareaid, buyareaid, wxTestStats, gjUser, ProcessConfirmStats, oldshouhouid, isBakData, isjbanwxqq, yuyueCheckuser, qujitongzhitime, daojishi, codeMsg, result_user, smstime, teltime, EarnestMoneySubid, serversOutUser, serversOutDtime, youhuifeiyong, truename, iszy, wxAreaid, imeifid, yifum, kuaixiuFlag, kuaixiuSendTime, iszp, wuliyou, mobileServeiceType, lppeizhi, fromshouhouid, ServiceCostprice, question_type, pzid, brand_id, refund_remark, order_source
        FROM shouhou with(nolock) WHERE id = #{shouhouId} and userid &lt;&gt; 76783;
    </select>

    <select id="getShouhouTuihuanByShouhouId" resultType="com.jiuji.oa.afterservice.other.po.ShouhouTuihuan">
        SELECT id, shouhou_id, tuihuan_kind, basket_id, tuikuanM, tuikuanM1, sub_id, tui_way, bankname, bankfuming, banknumber, comment, dtime, inuser, check1, check2, check1dtime, check2dtime, check1user, check2user, area, isdel, sub_idM, zhejiaM, check3, check3dtime, check3user, basket_ids, tuikuanlock, buypriceM, inprice, currency_price, pzid, salenm, puhuim, piaoInfo, ctype, areaid, coinM, IncludeChecklist, IsSendWeixinInviteCode, smallproid, iszengpin, gzinfo, payOpenId, paymobilecode, ispayMoney, baitiaoM, kuBaiTiaoM, isValidt, peizhi, peizhiPrice, piaoPrice, piaoType, faultType, checkType, tradeType, tradeDate, fpOpenid, fpPayState, fpPayTime, delUser, delTime, netExceptionFlag, kemuTui, accountException
        FROM shouhou_tuihuan with(nolock)  WHERE tuihuan_kind in(1,2,3,4) and isnull(isdel,0) = 0 and shouhou_id = #{shouhouId}
    </select>

    <select id="getHuanByShouhouId" resultType="com.jiuji.oa.afterservice.refund.vo.res.machine.HuanInfoVo">
        select name ,product_color productColor,imei,stats,product_id,baoxiu,userid,sub_id
        from shouhou with(nolock) where ((areaid =  #{id} and toareaid is null) or (toareaid = #{id} and toareaid is not null)) and id = #{id}
    </select>

    <select id="getHuanBySubId" resultType="com.jiuji.oa.afterservice.refund.vo.res.machine.HuanInfoVo">
        SELECT sub_id,sub_pay,yingfuM,yifuM,(yingfuM-yifuM) moneym
        FROM sub with(nolock)
        WHERE sub_id = #{subId} AND userid = #{userId} AND areaid = #{areaId} AND sub_check IN (1,6)
    </select>

    <select id="getWeiXinHongBao" resultType="java.math.BigDecimal">
        SELECT GrandAmount
        FROM RedPacketsRecord with(nolock)
        WHERE MobilebasketID = #{basketId} AND ISNULL(isGrant,0) = 1
    </select>

    <select id="getHuanInfo1" resultType="com.jiuji.oa.afterservice.refund.bo.HuanInfoWithShouhouBo">
        select h.name,h.product_color productColor,imei,stats,h.product_id productId,
               baoxiu,p.ppriceid1 as ppriceid
        from shouhou h with(nolock)
            left join productinfo p with(nolock) on h.ppriceid=p.ppriceid
        where id = #{shouhouId} and ((areaid = #{areaId} and toareaid is null)
                   or (toareaid = #{areaId} and toareaid is not null))
    </select>

    <select id="getHuanInfo2" resultType="com.jiuji.oa.afterservice.refund.bo.HuanInfoWithShouhouBo">
        select id,product_name productName,product_color productColor,imei,k.ppriceid
        from product_mkc k with(nolock)
            left join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        where k.areaid = #{areaId}
          <if test="isIgnoreStatus == null || isIgnoreStatus == false">
              <!--3 库存 6 售后  13 内完成售后-->
              and k.kc_check in(3,6,13) and isnull(k.basket_id,0) = 0
          </if>
          and k.id = #{mkcId}
    </select>

    <select id="getHuanBySub1" resultType="com.jiuji.oa.afterservice.refund.bo.HuanInfoWithShouhouBo">
        select name,product_color productColor,imei,stats,product_id productId,baoxiu,userid userId,sub_id subId
        from shouhou with(nolock)
        where ((areaid = #{areaId} and toareaid is null) or (toareaid = #{areaId} and toareaid is not null)) and id = #{shouhouId}
    </select>

    <select id="getHuanBySub2" resultType="com.jiuji.oa.afterservice.refund.bo.HuanInfoWithSubBo">
        SELECT sub_id subId,sub_pay subPay,isnull(yingfuM,0) yingfuMoney,isnull(yifuM,0) yifuMoney,(isnull(yingfuM,0)-isnull(yifuM,0)) needPayMoney
        FROM sub with(nolock)
        WHERE sub_id=#{subId} AND userid=#{userId} AND areaid=#{areaId}
          <if test="isIgnoreStatus == null || isIgnoreStatus == false">
              AND sub_check IN (1,6)
          </if>
    </select>

    <!-- getHuanMkc -->
    <select id="getHuanMkc" resultType="com.jiuji.oa.afterservice.refund.bo.HuanMkcBo">
        <choose>
            <when test="isHuishou != null and isHuishou != 1">
                select b.price, k.inbeihuoprice as inPrice, price2 as buyPrice, b.sub_id as subId, b.basket_id as basketId,
                       s.userid as userId
                from basket b with(nolock)
                    left join product_mkc k with(nolock) on b.basket_id = k.basket_id
                    left join sub s with(nolock) on s.sub_id = b.sub_id
                where isnull(b.isdel,0) = 0
                    and k.kc_check = 5
                    and s.sub_check <![CDATA[<>]]> 9
                    and s.userid = #{userId}
            </when>
            <otherwise>
                SELECT b.price, k.inprice as inPrice, isnull(b.price2,b.price) as buyPrice, b.sub_id as subId, b.basket_id as basketId, s.userid AS userId
                FROM recover_marketSubInfo b with(nolock)
                    INNER JOIN recover_marketInfo s with(nolock) ON b.sub_id = s.sub_id
                    INNER JOIN recover_mkc k with(nolock) ON b.basket_id = k.to_basket_id
                WHERE isnull(b.isdel, 0) = 0
                    AND s.sub_check = 3
                    AND isnull(b.isdel, 0) = 0
                    AND mkc_check = 5
            </otherwise>
        </choose>
        <choose>
            <when test="mkcId != null and mkcId > 0">
                and k.id = #{mkcId}
            </when>
            <otherwise>
                and k.imei = #{imei}
            </otherwise>
        </choose>
        <if test="subId != null and subId > 0">
            and b.sub_id = #{subId}
        </if>
        order by k.id desc
    </select>

    <!-- listBasketMkcByBasketId -->
    <select id="listBasketMkcByBasketId" resultType="com.jiuji.oa.afterservice.refund.bo.BasketMkcBo">
        select k.ppriceid, k.kc_check as kcCheck, k.areaid as areaId, k.inBeihuoPrice as inbeihuoprice,
               k.frareaid as frAreaId
        from dbo.product_mkc k WITH(nolock)
        where k.id = #{basketId}
    </select>

    <!-- listBasketShouhouByShouhouId -->
    <select id="listBasketShouhouByShouhouId" resultType="com.jiuji.oa.afterservice.refund.bo.BasketShouhouBo">
        select mkc.ppriceid, s.areaid as areaId, isNull(s.toareaid, s.areaid) as newAreaId
        from dbo.shouhou s WITH(nolock)
            left join dbo.basket b WITH(nolock) on s.basket_id = b.basket_id
            left join dbo.product_mkc mkc with(nolock ) on mkc.basket_id = b.basket_id
        where s.id = #{shouhouId}
    </select>
    <select id="getYouHuiSmallSubId" resultType="java.lang.Integer">
        select top 1 b.sub_id from dbo.basket b with(nolock) left join dbo.basket_extend e with(nolock) on b.basket_id=e.basket_id
        where isnull(b.isdel,0)=0 and b.price>0
          and e.link_basket_id in(select mb.basket_id from dbo.basket mb with(nolock) where mb.youhuiPrice2>0
            and mb.basket_id = #{basketId} and mb.ismobile=1)
          and exists(select 1 from dbo.sub s with(nolock) where b.sub_id=s.sub_id and s.sub_check=3 )
    </select>
    <select id="selectHuiShouPriceByBasketId" resultType="com.jiuji.oa.afterservice.bigpro.po.RecoverSub">
    select c.prices, ss.sub_id from dbo.cardLogs c with(nolock) left join dbo.recover_sub ss with(nolock) on c.sub_id=ss.sub_id
        where c.link_basket_id is not null and isnull(c.liangpin,0)=2 and ss.sub_check=3 and c.link_basket_id = #{basketId}
    </select>

    <!-- insertTuihuan -->
    <insert id="insertTuihuan" keyColumn="id" useGeneratedKeys="true" keyProperty="id">
        insert into shouhou_tuihuan
        (shouhou_id, tuihuan_kind, inuser, areaid, comment, puhuim, tui_way, bankname, bankfuming, banknumber, tuikuanM,
         tuikuanM1, zhejiaM, buypriceM, inprice, salenm, piaoInfo, ctype, coinM, IncludeChecklist, baitiaoM, kuBaiTiaoM, isValidt, peizhi,
         peizhiPrice, piaoPrice, piaoType, faultType, checkType, tradeType, tradeDate, basket_id, sub_idM, sub_id, smallproid, iszengpin,
         check2user, check1user, check2, check1,tui_kinds)
        output inserted.id
        values(#{tuihuan.shouhouId}, #{tuihuan.tuihuanKind}, #{tuihuan.inuser}, #{tuihuan.areaid}, #{tuihuan.comment}, #{tuihuan.puhuim}, #{tuihuan.tuiWay},
            #{tuihuan.bankname}, #{tuihuan.bankfuming}, #{tuihuan.banknumber}, #{tuihuan.tuikuanM}, #{tuihuan.tuikuanM1}, #{tuihuan.zhejiaM}, #{tuihuan.buypriceM},
            #{tuihuan.inprice}, #{tuihuan.salenm}, #{tuihuan.piaoInfo}, #{tuihuan.ctype}, #{tuihuan.coinM}, #{tuihuan.IncludeChecklist}, #{tuihuan.baitiaoM}, #{tuihuan.kuBaiTiaoM},
            #{tuihuan.isValidt}, #{tuihuan.peizhi}, #{tuihuan.peizhiPrice}, #{tuihuan.piaoPrice}, #{tuihuan.piaoType}, #{tuihuan.faultType}, #{tuihuan.checkType},
            #{tuihuan.tradeType}, #{tuihuan.tradeDate}, #{tuihuan.basketId}, #{tuihuan.subIdm}, #{tuihuan.subId}, #{tuihuan.smallproid}, #{tuihuan.iszengpin},
            #{tuihuan.check2user}, #{tuihuan.check1user}, #{tuihuan.check2}, #{tuihuan.check1},#{tuihuan.tuiKinds})
    </insert>

    <!-- insertNetPayRefundInfo -->
    <insert id="insertNetPayRefundInfo">
        insert into dbo.netPayRefundInfo(netRecordId ,price ,inuser ,dtime ,returnid )
        values(#{netRecordId}, #{price}, #{userName}, getdate(), #{returnId})
    </insert>

    <!-- insertSmallPro -->
    <insert id="insertSmallPro" keyColumn="id" useGeneratedKeys="true" keyProperty="id">
        insert into smallpro(userid, name, sub_id, buydate, areaid, inuser, indate, groupid,
                             isbaoxiu, username, mobile, kind, problem, stats, codeMsg)
        output inserted.id
        values(#{sp.userId}, #{sp.name}, #{sp.subId}, #{sp.buyDate}, #{sp.areaId}, '系统', getdate(), 1,
               #{sp.isBaoxiu}, #{sp.userName}, #{sp.mobile}, #{sp.kind}, #{sp.problem}, 0, '系统授权')
    </insert>

    <!-- insertSmallproBill -->
    <insert id="insertSmallproBill">
        insert into smallproBill(smallproID, basket_id, ppriceid, count)
        values (#{spId}, #{basketId}, #{ppid}, #{count})
    </insert>

    <!-- insertReturnsDetail -->
    <insert id="insertReturnsDetail">
        INSERT INTO dbo.ReturnsDetail( SHTHID, BasketID, BasketCount )
        VALUES(#{thId}, #{basketId}, #{basketCount})
    </insert>

</mapper>
