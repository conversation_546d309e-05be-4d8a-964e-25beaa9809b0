<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.ShouhouTuihuanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.ShouhouTuihuan">
        <id column="id" property="id"/>
        <result column="shouhou_id" property="shouhouId"/>
        <result column="tuihuan_kind" property="tuihuanKind"/>
        <result column="basket_id" property="basketId"/>
        <result column="tuikuanM" property="tuikuanM"/>
        <result column="tuikuanM1" property="tuikuanM1"/>
        <result column="sub_id" property="subId"/>
        <result column="tui_way" property="tuiWay"/>
        <result column="bankname" property="bankname"/>
        <result column="bankfuming" property="bankfuming"/>
        <result column="banknumber" property="banknumber"/>
        <result column="comment" property="comment"/>
        <result column="dtime" property="dtime"/>
        <result column="inuser" property="inuser"/>
        <result column="check1" property="check1"/>
        <result column="check2" property="check2"/>
        <result column="check1dtime" property="check1dtime"/>
        <result column="check2dtime" property="check2dtime"/>
        <result column="check1user" property="check1user"/>
        <result column="check2user" property="check2user"/>
        <result column="area" property="area"/>
        <result column="isdel" property="isdel"/>
        <result column="sub_idM" property="subIdm"/>
        <result column="zhejiaM" property="zhejiaM"/>
        <result column="check3" property="check3"/>
        <result column="check3dtime" property="check3dtime"/>
        <result column="check3user" property="check3user"/>
        <result column="basket_ids" property="basketIds"/>
        <result column="tuikuanlock" property="tuikuanlock"/>
        <result column="buypriceM" property="buypriceM"/>
        <result column="inprice" property="inprice"/>
        <result column="pzid" property="pzid"/>
        <result column="salenm" property="salenm"/>
        <result column="puhuim" property="puhuim"/>
        <result column="piaoInfo" property="piaoInfo"/>
        <result column="ctype" property="ctype"/>
        <result column="areaid" property="areaid"/>
        <result column="coinM" property="coinM"/>
        <result column="IncludeChecklist" property="IncludeChecklist"/>
        <result column="IsSendWeixinInviteCode" property="IsSendWeixinInviteCode"/>
        <result column="smallproid" property="smallproid"/>
        <result column="iszengpin" property="iszengpin"/>
        <result column="gzinfo" property="gzinfo"/>
        <result column="payOpenId" property="payOpenId"/>
        <result column="paymobilecode" property="paymobilecode"/>
        <result column="ispayMoney" property="ispayMoney"/>
        <result column="baitiaoM" property="baitiaoM"/>
        <result column="kuBaiTiaoM" property="kuBaiTiaoM"/>
        <result column="isValidt" property="isValidt"/>
        <result column="peizhi" property="peizhi"/>
        <result column="peizhiPrice" property="peizhiPrice"/>
        <result column="piaoPrice" property="piaoPrice"/>
        <result column="piaoType" property="piaoType"/>
        <result column="faultType" property="faultType"/>
        <result column="checkType" property="checkType"/>
        <result column="tradeType" property="tradeType"/>
        <result column="tradeDate" property="tradeDate"/>
        <result column="fpOpenid" property="fpOpenid"/>
        <result column="fpPayState" property="fpPayState"/>
        <result column="fpPayTime" property="fpPayTime"/>
        <result column="delUser" property="delUser"/>
        <result column="delTime" property="delTime"/>
        <result column="netExceptionFlag" property="netExceptionFlag"/>
        <result column="accountException" property="accountException"/>
        <result column="kemuTui" property="kemuTui"/>
        <result column="voucherId" property="voucherId"/>
        <result column="hsjjfc_pzid" property="hsjjfcPzid"/>
    </resultMap>
    <delete id="deleteUserById" parameterType="com.jiuji.oa.afterservice.other.po.ShouhouTuihuan">
        update shouhou_tuihuan set isdel=#{shouhouTuihuan.isdel},delUser=#{shouhouTuihuan.delUser},delTime=#{shouhouTuihuan.delTime} where id=#{shouhouTuihuan.id}
    </delete>

    <select id="listShouhouByUserId" resultMap="BaseResultMap">
        select t.id,t.shouhou_id,t.check3dtime,t.tuikuanM
        from dbo.shouhou_tuihuan t with(nolock)
            join dbo.shouhou s with(nolock) on t.shouhou_id=s.id
        where datediff(month,t.check3dtime,getdate()) &lt;=6
            and t.check3 is not null
            and t.tuihuan_kind = 3
            and s.userid=#{userId}
    </select>

    <select id="listSmallProByUserId" resultMap="BaseResultMap">
        select  t.id,
                t.shouhou_id,
                t.check3dtime,
                t.tuikuanM,
                t.tuihuan_kind,
                t.sub_id,
                t.shouhou_id,
                t.smallproid
        from dbo.shouhou_tuihuan t with(nolock)
            join dbo.Smallpro s with(nolock) on t.shouhou_id=s.id
        where datediff(month,t.check3dtime,getdate()) &lt;=6
            and t.check3 is not null
            and t.tuihuan_kind=7
            and s.userid=#{userId}
    </select>

    <select id="listBankTuihuan" resultMap="BaseResultMap">
        select sub_id,shouhou_id,tuihuan_kind,check3dtime,tuikuanM,smallproid
        from dbo.shouhou_tuihuan with(nolock)
        where replace(banknumber,' ','')=#{num}
            and datediff(month,check3dtime,getdate()) &lt;=6
            and check3 is not null
            and tui_way='银行转账'
    </select>

    <select id="getShouhouTuihuanIdByBankNumber" resultType="java.lang.Integer">
        SELECT TOP
        1 id
        FROM
        dbo.shouhou_tuihuan with(nolock)
        WHERE
        replace( banknumber, ' ', '' ) =#{bankNumber}
        AND datediff( MONTH, check3dtime, getdate( ) ) &lt;= 6
        AND check3 IS NOT NULL
    </select>

    <select id="getPayWeixinIdByOpenId" resultType="java.lang.Integer">
        SELECT TOP
        1 id
        FROM
        dbo.Pay_WXEnterprise with(nolock)
        WHERE
        openid = #{openId}
        AND tradeResult = '1'
        AND datediff( MONTH, tradeDate, getdate( ) ) &lt;= 6
        AND ( subsystem IN ( 'huishou', 'shouhou_tuihuan' ) OR remark LIKE '退换机款项，售后单号%' )
    </select>

    <select id="getTuihuanInfo" resultType="com.jiuji.oa.afterservice.other.bo.TuihuanBuyPriceBo">
        <choose>
            <when test="ishuishou != null and ishuishou == 1">
                SELECT top 1 ISNULL(price2,price) AS price,s.coinM,s.yingfuM,s.yifuM,b.basket_id as basketId,b.sub_id as
                subId,s.userid,s.tradeDate1,b.type,'' productPeizhi
                FROM recover_marketSubInfo b with(nolock)
                INNER JOIN recover_marketInfo s with(nolock) ON b.sub_id=s.sub_id
                INNER JOIN recover_mkc k with(nolock) ON b.basket_id=k.to_basket_id
                WHERE s.sub_check=3 AND isnull(b.isdel,0)=0 AND mkc_check=5
            </when>
            <otherwise>
                SELECT top 1 price2 price,s.coinM,s.yingfuM,s.yifuM,b.basket_id as basketId,b.sub_id as
                subId,s.userid,s.tradeDate1,b.type,b.product_peizhi as productPeizhi
                FROM basket b with(nolock)
                INNER JOIN sub s with(nolock) ON b.sub_id=s.sub_id
                INNER JOIN product_mkc k with(nolock) ON b.basket_id=k.basket_id
                WHERE s.sub_check=3 AND isnull(b.isdel,0)=0 AND kc_check=5
            </otherwise>
        </choose>
        <if test="mkcId != null and mkcId > 0">
            and k.id = #{mkcId}
        </if>

        <if test="buySubId != null and buySubId > 0">
            and s.sub_id = #{buySubId}
        </if>
        <if test="imei != null and imei != '' ">
            and imei = #{imei}
        </if>

        <if test="ishuishou != null and ishuishou == 1">
            ORDER BY b.basket_id DESC
        </if>
    </select>

    <select id="getKcFqje" resultType="java.math.BigDecimal">
        select sum(money-isnull(refundPrice,0)) leftPrice from dbo.netpay_record with(nolock) where sub_number=#{subId} and type=1 and payWay='库分期'
    </select>

    <select id="getFapiaoKindInfo" resultType="java.lang.Integer">
        select top 1 kind from (
        select p.kind, p.id
        from dbo.tax_piao p with(nolock)
        where p.flag = 4
        <choose>
            <when test="isHuiShou == null || isHuiShou == 0">
                and isnull(p.type_, 0) not in (1, 2)
            </when>
            <when test="isHuiShou == 1">
                and isnull(p.type_, 0) = 2
            </when>
        </choose>
        and p.sub_id = #{subId}
        union
        select p.kind, p.id
        from dbo.tax_piao p with(nolock)
        where p.flag = 4
        <choose>
            <when test="isHuiShou == null || isHuiShou == 0">
                and isnull(p.type_, 0) not in (1, 2)
            </when>
            <when test="isHuiShou == 1">
                and isnull(p.type_, 0) = 2
            </when>
        </choose>
        and exists (
        select 1 from dbo.piaoProductInfo f with(nolock)
        where f.piaoid = p.id and f.sub_id = #{subId}
        )
        ) t
        order by t.id desc
    </select>


    <select id="getKoujianBaojia" resultType="java.math.BigDecimal">
        select top 1 price from dbo.subPriceProtect with(nolock) where isDone = 1  AND basket_id = #{basketId}
    </select>

    <select id="getSaveMoney" resultType="java.math.BigDecimal">
        select [money] from dbo.save_money with(nolock) where userid=#{userId} and sub_id=#{userId} and kind=20 and comment='OVG购买返现'
    </select>

    <select id="getSaveMoneyByUserId" resultType="java.math.BigDecimal">
        select save_money from dbo.BBSXP_Users with(nolock) where ID=#{userId}
    </select>

    <select id="getBasketIdByShouhouId" resultType="java.lang.Integer">
        SELECT TOP 1 b.basket_id FROM basket b WITH(nolock)
         LEFT JOIN dbo.product_mkc k with(nolock) ON k.basket_id = b.basket_id left join sub s with(nolock) on s.sub_id=b.sub_id
         WHERE s.sub_check = 3 and  b.ismobile = 1
         AND k.imei = (SELECT TOP 1 imei FROM dbo.shouhou WITH(nolock) WHERE id = #{shouhouId}) AND k.kc_check = 5
    </select>

    <select id="getSubIdByBasketId" resultType="java.lang.Long">
        select b.sub_id from dbo.basket b with(nolock)  where exists (select 1 from dbo.basket b1 with(nolock) left join dbo.Taocan t with(nolock) on t.basket_id = b1.basket_id where b.sub_id=b1.sub_id and t.flag in (2,5) and isnull(b1.isdel,0)=0 and exists(select 1 from dbo.discountProConfig d with(nolock)
        where b1.ppriceid=d.ppriceid and d.kind=1))
         and b.basket_id= #{basketId}
    </select>

    <select id="getShouyinOtherBySubId" resultType="com.jiuji.oa.afterservice.other.bo.ShouyinOtherSimpleBo">
        select top 1 o.id,s.sub_pay04 from dbo.shouying s with(nolock) left join dbo.shouyin_other o with(nolock) on o.shouyinid=s.id
        where o.type_=11
        and s.sub_id=#{subId}
    </select>

    <select id="getMemberInfoByUserId" resultType="com.jiuji.oa.afterservice.other.bo.BBSXPUsersSimpleBo">
        select id,saltPay,payPwd from dbo.BBSXP_Users  with(nolock)  where 1=1
        <if test="userId != null and userId != 0">
            and id=#{userId}
        </if>
    </select>

    <update id="updateShouhouTui">
        update shouhou set istui=0 where (istui is null or istui=0) and id=#{shouhouId}
    </update>

    <select id="checkZitidianAccount" resultType="java.lang.Integer">
        seLect count(1) from BBSXP_Users u with(nolock)
        left join zitidian z with(nolock) on u.id=z.userid
        left join sub s on with(nolock) s.zitidianid=z.id
        where z.id>0 and s.delivery=3
        <if test="subId != null">
            and s.sub_id=#{subId}
        </if>
    </select>

    <select id="bhjlCountBySubId" resultType="java.lang.Integer">
        select count(1) from dbo.product_mkc  with(nolock) where kc_check&lt;>4 and basket_id in (select basket_id from dbo.basket with(nolock) where sub_id=#{subId}
    </select>

    <select id="bhsdCountBySubId" resultType="java.lang.Integer">
        select top 1 basket_id from dbo.basket_other a  with(nolock) where exists(select 1 from dbo.basket b with(nolock) where isnull(b.isdel,0)=0 and a.basket_id=b.basket_id and b.sub_id=#{subId}
    </select>

    <select id="ckspCountBySubId" resultType="java.lang.Integer">
        select top  1 basket_id from dbo.basket b  with(nolock) where isnull(b.isdel,0)=0 and b.sub_id=#{subId}
        and isnull(ischu,0)=1 and ismobile=0
    </select>

    <select id="checkIsZfbyhm" resultType="java.lang.Integer">
        select top 1 y.sub_id from dbo.shouying y  with(nolock)
        where 1=1
        <if test="subId != null">
            and sub_id=#{subId}
        </if>
        and y.inuser='支付宝(pay1)' and exists(select 1 from dbo.basket b  with(nolock) where b.sub_id=y.sub_id
        <if test="ppids != null and ppids.size>0">
            and b.ppriceid in
            <foreach collection="ppids" item="ppid" close=")" open="(" index=",">
                #{ppid}
            </foreach>
        </if>
    </select>

    <select id="tjqxCount" resultType="java.lang.Integer">
        select count(1) from dbo.basket b with(nolock)  where 1=1
        <if test="basketIds != null and basketIds.size>0">
            and basket_id in
            <foreach collection="basketIds" item="basketId" open="(" close=")" separator=",">
                #{basketId}
            </foreach>
        </if>
        and ( (b.product_peizhi&lt;>'' and exists(select 1 from dbo.productinfo p  with(nolock) where p.ppriceid=b.ppriceid and
        p.cid=164 )) or ppriceid in (21073,21074,21075,31642,32342,52447,52475,52476))
    </select>

    <select id="getBaskets" resultType="com.jiuji.oa.afterservice.other.bo.BasketSimpleBo">
        select isnull(inprice,price) as inprice,s.areaid,s.userid FROM dbo.basket b  with(nolock) with(nolock)
        LEFT JOIN dbo.sub s with(nolock)
        ON s.sub_id = b.sub_id
        WHERE s.sub_check IN (3) AND b.ischu=1 AND isnull(b.isdel,0)=0
        <if test="subId != null and subId != 0">
            AND b.sub_id=#{subId}
        </if>
        <if test="basketIds != null and basketIds.size>0">
            AND basket_id IN
            <foreach collection="basketIds" item="basketId" open="(" close=")" separator=",">
                #{basketId}
            </foreach>
        </if>
    </select>

    <select id="getRecoverMarkInfoBySubId" resultType="com.jiuji.oa.afterservice.other.bo.RecoverMarketInfoBo">
        select top 1 yifuM as yifuM,areaid as areaId,userid as userId FROM dbo.recover_marketInfo with(nolock) WHERE sub_check IN (1)
         AND sub_id=#{subId}
    </select>

    <select id="getSubPaySum" resultType="java.math.BigDecimal">
        select sum(isnull(sub_pay03,0)) from shouying with(nolock) where sub_id=#{subId}
        <if test="types != null and types.size>0">
            and shouying_type in
            <foreach collection="types" item="type" open="(" close=")" separator=",">
                #{type}
            </foreach>
        </if>
        and isnull(sub_pay03,0)&lt;>0
    </select>

    <select id="getWechatTuikuanM" resultType="java.math.BigDecimal">
        select sum(tuikuanM) from dbo.shouhou_tuihuan with(nolock)
        where tuihuan_kind in (6,7)
        and tui_way='微信秒退'
        and isnull(isdel,0)=0 and sub_id=#{subId}
    </select>

    <select id="getNetPayRecordInfo" resultType="com.jiuji.oa.afterservice.other.bo.NetPayRecordBo">
        select top 1 id,money,refundPrice,payWay,trade_no as tradeNo from dbo.netpay_record with(nolock) where 1=1
        <if test="type != null">
            and type=#{type}
        </if>
        <if test="id != null">
            and id=#{id}
        </if>
    </select>
    <select id="getAfterServicesDiscount" resultType="com.jiuji.oa.afterservice.other.bo.ShouhouTuiHuanInfo">
        select s.id as shouhouId,s.name as productName,s.offtime as offTime from dbo.shouhou_tuihuan t with(nolock)
        join dbo.shouhou s with(nolock) on t.shouhou_id=s.id
        join dbo.sub s1 with(nolock) on s1.sub_id=s.sub_id
        where t.tuihuan_kind in (3,4) and t.check3 is not null and s1.userid=#{userId}
        and datediff(month,t.check3dtime,getdate())&lt;=12 and isnull(t.zhejiaM,0)&gt;0
    </select>
    <select id="countAfterServicesDiscount" resultType="java.lang.Integer">
        select count(1) from dbo.shouhou_tuihuan t with(nolock)
        join dbo.shouhou s with(nolock) on t.shouhou_id=s.id
            join dbo.sub s1 with(nolock) on s1.sub_id=s.sub_id
        where t.tuihuan_kind in (3,4) and t.check3 is not null and s1.userid=#{userId}
          <if test="fromDays != null">
              AND DATEDIFF(DAY, t.check3dtime, GETDATE()) &gt; #{fromDays}
          </if>
          and datediff(day,t.check3dtime,getdate())&lt;= #{toDays}
          <!--and isnull(t.zhejiaM,0)&gt;0-->
    </select>
    <select id="getJiuJiCoinSum" resultType="java.math.BigDecimal">
        select isnull(sum(isnull(s.sub_pay05 - isnull(o.refund_price, 0), 0)), 0) as jiuJiCoinSum
        from dbo.shouying s with(nolock) inner join dbo.shouyin_other o
        with (nolock)
        on s.id=o.shouyinid
        where s.sub_id=#{subId}
          and s.shouying_type in ('交易'
            , '订金')
          and o.type_=#{jiujiCoinValue}
    </select>

    <select id="getShouhouIdbyTuihuanId" resultType="java.lang.Integer">
        select shouhou_id from shouhou_tuihuan st with(nolock) where st.id = #{id}
    </select>

    <!-- getIsHuishouByShouhouId -->
    <select id="getIsHuishouByShouhouId" resultType="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
        select isnull(ishuishou,0) ishuishou, sub_id as subId
        from dbo.shouhou with(nolock)
        where id = #{shouhouId}
    </select>

    <!-- getShThBySubId -->
    <select id="getShThBySubId" resultType="java.lang.Integer">
        select top 1 id from dbo.shouhou_tuihuan where sub_id = #{subId} and tuihuan_kind = 7 and isnull(isdel, 0) = 0 and check3 is null
    </select>
    <select id="getDiscountSubIds" resultType="java.lang.Integer">
        SELECT s.sub_id
        FROM shouhou_tuihuan t WITH(NOLOCK)
                 INNER JOIN shouhou h WITH(NOLOCK) ON t.shouhou_id = h.id
                 INNER JOIN recover_marketInfo s WITH(NOLOCK) ON h.sub_id = s.sub_id
        WHERE t.tuihuan_kind = 3
          AND h.ishuishou = 1
          AND ISNULL(t.isdel, 0) = 0
          AND t.check3 = 1
          AND ISNULL(t.discount, 0) = 0
          AND ISNULL(t.zhejiaM, 0) = 0
          AND t.faultType = '无故障'
          AND t.dtime > '2023-03-23'
          AND s.userid = #{userId}
          AND YEAR(t.check3dtime) = #{year}
    </select>
</mapper>
