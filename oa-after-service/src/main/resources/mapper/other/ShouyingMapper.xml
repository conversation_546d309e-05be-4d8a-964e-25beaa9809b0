<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.ShouyingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.Shouying">
        <id column="id" property="id"/>
        <result column="sub_id" property="subId"/>
        <result column="basket_id" property="basketId"/>
        <result column="hejim" property="hejim"/>
        <result column="shouxum" property="shouxum"/>
        <result column="dtime" property="dtime"/>
        <result column="shouying_type" property="shouyingType"/>
        <result column="sub_pay01" property="subPay01"/>
        <result column="sub_pay02" property="subPay02"/>
        <result column="sub_pay03" property="subPay03"/>
        <result column="sub_pay04" property="subPay04"/>
        <result column="sub_pay05" property="subPay05"/>
        <result column="sub_pay06" property="subPay06"/>
        <result column="sub_pay07" property="subPay07"/>
        <result column="sub_pay08" property="subPay08"/>
        <result column="inuser" property="inuser"/>
        <result column="area" property="area"/>
        <result column="istui" property="istui"/>
        <result column="tui_id" property="tuiId"/>
        <result column="shouxum1" property="shouxum1"/>
        <result column="collectionArea" property="collectionArea"/>
        <result column="collectionAreaid" property="collectionAreaid"/>
        <result column="areaid" property="areaid"/>
        <result column="islock" property="islock"/>
        <result column="lockUser" property="lockUser"/>
    </resultMap>
    <select id="statisticsSaleShouYin"
            resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.SaleShouYinProjectVo">
        WITH CTE_NUM AS (
            SELECT 1 as num
            UNION ALL
            SELECT num+1 FROM CTE_NUM WHERE num &lt; 5
        )
        <!--销售 新机销售 新机分销 回收机销售-->
        <!--1 类别 2 退款 3 换其它型号 4 退维修费 5 退订金 6 退配件 7 退订金(良品) 8 退订金(维修费) 9 退订金(小件维修费)-->
        select sale.businessTypeName,sale.projectName,sale.projectDescription as projectDescription,isnull(sale.saleNumber,0.0) as saleNumber
             ,isnull(sale.showSaleNumber,0) showSaleNumber,isnull(sale.saleAmount,0.0) as saleAmount,isnull(sale.showSaleAmount,0) showSaleAmount
             ,isnull(shrf.refundNumber,0.0)+isnull(sprf.refundNumber,0.0) as refundNumber,isnull(shrf.showRefundNumber,0) showRefundNumber
             ,isnull(shrf.refundAmount,0.0)+isnull(sprf.refundAmount,0.0) as refundAmount,isnull(shrf.showRefundAmount,0) showRefundAmount
            ,isnull(sale.saleNumber,0.0)+isnull(shrf.refundNumber,0.0)+isnull(sprf.refundNumber,0.0) as sumNumber,sale.showSumNumber
            ,isnull(sale.saleAmount,0.0)+isnull(shrf.refundAmount,0.0)+isnull(sprf.refundAmount,0.0) as sumAmount,1 showSumAmount
            ,sale.sortFlag,sale.minusFlags
        from (
            select '销售' as businessTypeName,'新机零售' as projectName,'取零售新机销售单中为交易（打勾）收银的订单（含瑕疵机或优品）' projectDescription,count(distinct sy.sub_id) as saleNumber,1 as showSaleNumber
                ,sum(sy.hejim) as saleAmount,1 as showSaleAmount,1 showSumNumber,1 as sortFlag,null as minusFlags
            from dbo.shouying sy with(nolock)
            where sy.shouying_type='交易'
                and sy.dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
                and not exists(select 1 from dbo.sub s with(nolock) where s.sub_id = sy.sub_id and s.subtype in(13,14))
                <foreach collection="query.areaIds" item="areaId" open="and sy.areaid in (" close=")" separator=",">
                    #{areaId}
                </foreach>
            union all
            select '销售' as businessTypeName,'新机分销' as projectName,'取分销新机销售单中为交易（打勾）收银的订单（含瑕疵机或优品）' projectDescription,count(distinct sy.sub_id) as saleNumber,1 as showSaleNumber
                 ,sum(sy.hejim) as saleAmount,1 as showSaleAmount,1 showSumNumber,2 as sortFlag,null as minusFlags
            from dbo.shouying sy with(nolock)
            where sy.shouying_type='交易'
                and exists(select 1 from dbo.sub s with(nolock) where s.sub_id = sy.sub_id and s.subtype in(13,14))
                and sy.dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
                <foreach collection="query.areaIds" item="areaId" open="and sy.areaid in (" close=")" separator=",">
                    #{areaId}
                </foreach>
            union all
            select '销售' as businessTypeName,'回收机销售' as projectName,'取二手销售订单中为交易（打勾）收银的订单（包括转售和良品交易）' projectDescription,count(distinct sy.sub_id) as saleNumber,1 as showSaleNumber
                 ,sum(sy.hejim) as saleAmount,1 as showSaleAmount,1 showSumNumber,3 as sortFlag,null as minusFlags
            from dbo.shouying sy with(nolock)
            where sy.shouying_type='交易2'
                and sy.dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
            <foreach collection="query.areaIds" item="areaId" open="and sy.areaid in (" close=")" separator=",">
                #{areaId}
            </foreach>
            union all
            select '销售' as businessTypeName,'回收机入库支付' as projectName,'取已经交易完成的回收订单' projectDescription,0 as saleNumber,0 as showSaleNumber
                ,0.0 as saleAmount,0 as showSaleAmount,1 showSumNumber,4 as sortFlag,null as minusFlags
            union all
            select '定金' as businessTypeName,'已付定金' as projectName,'取自所有订单中（新机销售单、回收转售、良品单、维修单、小件单中）订金方式收银金额' projectDescription
                 ,count(distinct sy.sub_id) as saleNumber,1 as showSaleNumber,sum(sy.hejim) as saleAmount,1 as showSaleAmount,1 showSumNumber,6 as sortFlag,null as minusFlags
            from dbo.shouying sy with(nolock)
            where sy.shouying_type in ('订金','订金2','订金3','售后小件')
                and sy.dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
            <foreach collection="query.areaIds" item="areaId" open="and sy.areaid in (" close=")" separator=",">
                #{areaId}
            </foreach>
            union all
            select '定金' as businessTypeName,'退还定金' as projectName,'取自所有订单退订办理的金额（零售订单、分销订单、转售单、良品单、维修单、小件单退订）' projectDescription
            ,0 as saleNumber,0 as showSaleNumber,0.0 as saleAmount,0 as showSaleAmount,1 showSumNumber,7 as sortFlag,null as minusFlags
            union all

            select '收银金额' as businessTypeName,case cn.num when 1 then '现金支付' WHEN 2 THEN '刷卡支付' WHEN 3 THEN '网上支付'
                WHEN 4 THEN '余额支付' WHEN 5 THEN '其他' end projectName,null projectDescription,0 as saleNumber,0 as showSaleNumber
                 ,case cn.num
                     <!--现金支付需要减去找零费用-->
                     when 1 then isnull(sum(isnull(sy.sub_pay01,0.0)-(isnull(sy.sub_pay01,0.0)+isnull(sy.sub_pay02,0.0)+isnull(sy.sub_pay03,0.0)
                          +isnull(sy.sub_pay04,0.0)+isnull(sy.sub_pay05,0.0)+isnull(sy.sub_pay06,0.0)+isnull(sy.sub_pay07,0.0)+isnull(sy.sub_pay08,0.0)-isnull(sy.hejim,0.0))),0.0)
                     WHEN 2 THEN isnull(sum(sy.sub_pay07),0.0)
                     WHEN 3 THEN isnull(sum(sy.sub_pay03),0.0)
                     WHEN 4 THEN isnull(sum(sy.sub_pay08),0.0)
                     WHEN 5 THEN isnull(sum(isnull(sy.sub_pay02,0.0)+isnull(sy.sub_pay06,0.0)),0.0) end as saleAmount,1 as showSaleAmount
                 <!--预留10个给 网上支付拆分收银-->
                 ,0 showSumNumber,iif(cn.num>3,18,8)+cn.num as sortFlag,null as minusFlags
            from dbo.shouying sy with(nolock)
                inner join CTE_NUM cn on 1=1
            where cn.num &lt; 6
              and sy.shouying_type in ('订金','交易','交易2','订金2','订金3','售后小件')
              and sy.dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
            <foreach collection="query.areaIds" item="areaId" open="and sy.areaid in (" close=")" separator=",">
                #{areaId}
            </foreach>
            group by cn.num
            union all
            select '收银金额' as businessTypeName,'网上支付    '+case when nr.payWay like '%微信%' then '微信支付'
                when nr.payWay like '%支付宝%' then '支付宝支付' when nr.payWay like '%首信%' then '首信支付' else '其他支付'  end projectName
                 ,null projectDescription,0 as saleNumber,0 as showSaleNumber,isnull(sum(nr.money),0.0) as saleAmount
                ,1 as showSaleAmount,0 showSumNumber
                ,11+ROW_NUMBER() over(order by (select 0)) as sortFlag,null as minusFlags
            from netpay_record nr with(nolock)
            <where>
                <foreach collection="query.areaIds" item="areaId" open="and nr.payAreaid in (" close=")" separator=",">
                    #{areaId}
                </foreach>
                and nr.dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
                and nr.type in (1,2,3,6,10)
            </where>
            group by case when nr.payWay like '%微信%' then '微信支付' when nr.payWay like '%支付宝%' then '支付宝支付' when nr.payWay like '%首信%' then '首信支付' else '其他支付' end
            union all
            select '收银金额' as businessTypeName,iif(sy.code = 36,'分期支付','三方支付')+'    '+isnull(sy.name,iif(sy.code = 36,'其他分期','其他支付')) as projectName,null projectDescription
                ,0 as saleNumber,0 as showSaleNumber,isnull(sum(iif(sy.code = 36,sy.sub_pay04,sy.sub_pay05)),0.0) as saleAmount
                ,1 as showSaleAmount,0 showSumNumber
                ,100+row_number() OVER(ORDER BY sy.code desc,sy.name) as sortFlag,null as minusFlags
            from (
                  select distinct sy.id,isnull(sc.code,iif(isnull(sy.sub_pay04,0.0)>0,36,37)) code,sc.name name,sy.sub_pay04,sy.sub_pay05
                  from dbo.shouying sy with(nolock)
                    left join dbo.shouyin_other so with(nolock) on sy.id=so.shouyinid and EXISTS(SELECT 1 from dbo.sysConfig sc with(nolock)
                        where (sc.code = 36 and isnull(sy.sub_pay04,0.0)>0 or sc.code = 37 and isnull(sy.sub_pay05,0.0)>0) and so.type_ = sc.value)
                    left join dbo.sysConfig sc with(nolock) on (sc.code = 36 and isnull(sy.sub_pay04,0.0)>0 or sc.code = 37 and isnull(sy.sub_pay05,0.0)>0) and so.type_ = sc.value
                    where 1=1
                    and sy.shouying_type in ('订金','交易','交易2','订金2','订金3','售后小件')
                    and sy.dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
                    and isnull(sy.sub_pay04,0.0)+isnull(sy.sub_pay05,0.0)>0
                    <foreach collection="query.areaIds" item="areaId" open="and sy.areaid in (" close=")" separator=",">
                        #{areaId}
                    </foreach>
            ) sy
            group by sy.code,sy.name
        ) sale
        left join (
            <!--新机零售退款-->
            select -count(distinct sh.sub_id) as refundNumber,1 as showRefundNumber
                 ,-isnull(sum(isnull(shtk.tuikuanM,0.0)-isnull(shtk.peizhiPrice,0.0)-isnull(shtk.piaoPrice,0.0)),0.0) as refundAmount,1 as showRefundAmount,1 as sortFlag
            from dbo.shouhou_tuihuan shtk with(nolock)
            left join dbo.shouhou sh with(nolock) on sh.id =shtk.shouhou_id
            where shtk.tuihuan_kind in (3,4) and isnull(shtk.isdel,0)=0 and isnull(shtk.check3,0)=1
                and isnull(sh.ishuishou,0)=0 and shtk.check3dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
                and not exists(select 1 from dbo.recover_marketInfo r with(nolock) where sh.sub_id=r.sub_id)
                and not exists(select 1 from dbo.sub s with(nolock) where s.sub_id = sh.sub_id and s.subtype in(13,14))
                <foreach collection="query.areaIds" item="areaId" open="and shtk.areaid in (" close=")" separator=",">
                    #{areaId}
                </foreach>
            union all
            <!--新机分销退款-->
            select -count(distinct sh.sub_id) as refundNumber,1 as showRefundNumber
                 ,-isnull(sum(isnull(shtk.tuikuanM,0.0)-isnull(shtk.peizhiPrice,0.0)-isnull(shtk.piaoPrice,0.0)),0.0) as refundAmount,1 as showRefundAmount,2 as sortFlag
            from dbo.shouhou_tuihuan shtk with(nolock)
            left join dbo.shouhou sh with(nolock) on sh.id =shtk.shouhou_id
            where shtk.tuihuan_kind in (3,4) and isnull(shtk.isdel,0)=0 and isnull(shtk.check3,0)=1
                and exists(select 1 from dbo.sub s with(nolock) where s.sub_id = sh.sub_id and s.subtype in(13,14))
                and shtk.check3dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
                <foreach collection="query.areaIds" item="areaId" open="and shtk.areaid in (" close=")" separator=",">
                    #{areaId}
                </foreach>
            union all
            <!--回收机销售退款-->
            select -count(distinct sh.sub_id) as refundNumber,1 as showRefundNumber
                 ,-isnull(sum(isnull(shtk.tuikuanM,0.0)-isnull(shtk.peizhiPrice,0.0)-isnull(shtk.piaoPrice,0.0)),0.0) as refundAmount,1 as showRefundAmount,3 as sortFlag
            from dbo.shouhou_tuihuan shtk with(nolock)
            left join dbo.shouhou sh with(nolock) on sh.id=shtk.shouhou_id
            where shtk.tuihuan_kind in (3,4) and isnull(shtk.isdel,0)=0 and isnull(shtk.check3,0)=1 and isnull(sh.ishuishou,0)=1
                and exists(select 1 from dbo.recover_marketInfo r with(nolock) where sh.sub_id=r.sub_id)
                and shtk.check3dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
                <foreach collection="query.areaIds" item="areaId" open="and shtk.areaid in (" close=")" separator=",">
                    #{areaId}
                </foreach>
            union all
            <!--回收机入库支付-->
            select -COUNT(distinct rb.sub_id) as refundNumber,1 as showRefundNumber
            ,-isnull(SUM(rb.price),0.0) as refundAmount,1 as showRefundAmount,4 as sortFlag
            FROM dbo.recover_basket rb with(nolock)
            WHERE ISNULL(rb.isdel,0) = 0
                and exists(select 1 from dbo.recover_sub rs with(nolock)
                        where rb.sub_id=rs.sub_id and rs.sub_check = 3
                           AND rs.pay_time between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
                            <foreach collection="query.areaIds" item="areaId" open="and rs.areaid in (" close=")" separator=",">
                                #{areaId}
                            </foreach>
                           )
            union all
            <!--退还定金-->
            select -count(distinct iif(shtk.tuihuan_kind=6,shtk.sub_id,null))-count(distinct iif(shtk.tuihuan_kind in (8,10),shtk.smallproid,null))
                -count(distinct iif(shtk.tuihuan_kind=11,shtk.shouhou_id,null)) as refundNumber
                 ,1 as showRefundNumber,-isnull(sum(shtk.tuikuanM),0.0) as refundAmount,1 as showRefundAmount,7 as sortFlag
            from dbo.shouhou_tuihuan shtk with(nolock)
            where shtk.tuihuan_kind in (5,6,8,11,10) and isnull(shtk.isdel,0)=0 and isnull(shtk.check3,0)=1
                and shtk.check3dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
                <foreach collection="query.areaIds" item="areaId" open="and shtk.areaid in (" close=")" separator=",">
                    #{areaId}
                </foreach>
        ) shrf on shrf.sortFlag = sale.sortFlag
        left join (
            <!--新机零售小件退款-->
            select -count(distinct sp.sub_id) as refundNumber,-isnull(sum(sptk.tuikuanM),0.0) as refundAmount,1 as sortFlag
            from dbo.shouhou_tuihuan sptk with(nolock)
            left join dbo.Smallpro sp with(nolock) on sp.id =sptk.shouhou_id
            where sptk.tuihuan_kind = 7 and isnull(sptk.isdel,0)=0 and isnull(sptk.check3,0)=1
                and sptk.check3dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
                <foreach collection="query.areaIds" item="areaId" open="and sptk.areaid in (" close=")" separator=",">
                    #{areaId}
                </foreach>
            union all
            <!--新机分销小件退款-->
            select -count(distinct sp.sub_id) as refundNumber,-isnull(sum(sptk.tuikuanM),0.0) as refundAmount,2 as sortFlag
            from dbo.shouhou_tuihuan sptk with(nolock)
            left join dbo.Smallpro sp with(nolock) on sp.id =sptk.shouhou_id
            where sptk.tuihuan_kind = 7 and isnull(sptk.isdel,0)=0 and isnull(sptk.check3,0)=1
                and exists(select 1 from dbo.sub s with(nolock) where s.sub_id = sp.sub_id and s.subtype in(13,14))
                and sptk.check3dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
                <foreach collection="query.areaIds" item="areaId" open="and sptk.areaid in (" close=")" separator=",">
                    #{areaId}
                </foreach>
        ) sprf on sprf.sortFlag = sale.sortFlag
        union all
        select '退款金额' as businessTypeName,case when shtk.tui_kinds = 1 then isnull(std.tui_way,'其他') else isnull(shtk.tui_way,'其他') end projectName,
               null projectDescription,0 as saleNumber,0 as showSaleNumber,0.0 as saleAmount,0 as showSaleAmount,0 refundNumber,0 showRefundNumber
             ,isnull(sum(case when shtk.tui_kinds = 1 and std.id is not null then std.refund_price else isnull(shtk.tuikuanM,0.0)-isnull(shtk.peizhiPrice,0.0)-isnull(shtk.piaoPrice,0.0) end),0.0) as refundAmount
             ,1 showRefundAmount,0 as sumNumber,0 showSumNumber,isnull(sum(shtk.tuikuanM),0.0) as sumAmount
             ,1 showSumAmount,2000+row_number() OVER(ORDER BY case when shtk.tui_kinds = 1 then isnull(std.tui_way,'其他') else isnull(shtk.tui_way,'其他') end desc) as sortFlag,null as minusFlags
        from dbo.shouhou_tuihuan shtk with(nolock)
        left join dbo.shouhou_tuihuan_detail std with(nolock) on shtk.tui_kinds = 1 and isnull(std.is_del,0) = 0 and std.fk_tuihuan_id = shtk.id
        where isnull(shtk.isdel,0)=0 and isnull(shtk.check3,0)=1
            and shtk.check3dtime between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
            <foreach collection="query.areaIds" item="areaId" open="and shtk.areaid in (" close=")" separator=",">
                #{areaId}
            </foreach>
        group by case when shtk.tui_kinds = 1 then isnull(std.tui_way,'其他') else isnull(shtk.tui_way,'其他') end
        union all
        select '回收付款' as businessTypeName,case rs.sub_pay WHEN 1 THEN '充值到余额' WHEN 2 THEN '银行转账' WHEN 4 THEN '支付宝转账'
            WHEN 5 THEN '微信支付' WHEN 3 THEN '回购换其他' WHEN 6 THEN '支付宝支付' WHEN 7 THEN '现金' else '其他' end projectName
             ,null projectDescription,0 as saleNumber,0 as showSaleNumber,0.0 as saleAmount,0 as showSaleAmount,0 refundNumber
             ,0 showRefundNumber,isnull(SUM(rb.price),0.0) as refundAmount
            ,1 showRefundAmount,0 as sumNumber,0 showSumNumber,isnull(SUM(rb.price),0.0) as sumAmount
            ,1 showSumAmount,4000+row_number() OVER(ORDER BY rs.sub_pay desc) as sortFlag,null as minusFlags
        FROM dbo.recover_basket rb with(nolock)
        inner join dbo.recover_sub rs with(nolock) on rb.sub_id=rs.sub_id
        WHERE ISNULL(rb.isdel,0) = 0 and rs.sub_check = 3
          AND rs.pay_time between CONVERT(datetime,#{query.startDate},20) and CONVERT(datetime,#{query.endDate},20)
            <foreach collection="query.areaIds" item="areaId" open="and rs.areaid in (" close=")" separator=",">
                #{areaId}
            </foreach>
        GROUP BY rs.sub_pay
    </select>

    <select id="saleIncomeAndExpenditure"
            resultType="com.jiuji.oa.afterservice.statistics.vo.res.IncomeAndExpenditureVO">
        <include refid="amountReceived"></include>
        UNION ALL
        <include refid="refundAmount"></include>
        UNION ALL
        <include refid="payBack"></include>
     </select>

    <!--  收款金额 查询  -->
    <sql id="amountReceived">
        <!--现金查询-->
        SELECT '收款金额' AS businessTypeName, '现金' AS projectName, NULL AS projectDescription, areaid AS areaId ,
        100 AS sortFlag,
        isnull( SUM ( isnull( sy.sub_pay01, 0.0 ) - ( isnull( sy.sub_pay01, 0.0 ) + isnull( sy.sub_pay02, 0.0 ) + isnull( sy.sub_pay03, 0.0 ) + isnull( sy.sub_pay04, 0.0 ) +
        isnull( sy.sub_pay05, 0.0 ) + isnull( sy.sub_pay06, 0.0 ) + isnull( sy.sub_pay07, 0.0 ) + isnull( sy.sub_pay08, 0.0 ) - isnull( sy.hejim, 0.0 ) ) ), 0.0 )
        AS data
        FROM dbo.shouying sy
        WITH ( nolock )
        WHERE sy.shouying_type IN ( '订金', '交易', '交易2', '订金2', '订金3', '售后小件' )
        AND sy.areaid IN
        <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND sy.dtime
        BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
        AND CONVERT ( datetime,#{req.endTime}, 20 )
        GROUP BY sy.areaid
    UNION ALL
        <!--刷卡支付-->
        SELECT '收款金额' AS businessTypeName, '刷卡' AS projectName, NULL AS projectDescription, areaid AS areaId
             ,101 AS sortFlag , isnull( SUM ( sy.sub_pay07 ), 0.0 ) AS data
        FROM dbo.shouying sy
        WITH ( nolock )
        WHERE sy.shouying_type IN ( '订金', '交易', '交易2', '订金2', '订金3', '售后小件' )
        AND sy.areaid IN
        <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND sy.dtime
        BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
        AND CONVERT ( datetime,#{req.endTime}, 20 )
        GROUP BY  sy.areaid
    UNION ALL
        <!--余额支付-->
        SELECT '收款金额' AS businessTypeName, '余额支付' AS projectName, NULL AS projectDescription, areaid AS areaId,102 AS sortFlag, isnull( SUM ( sy.sub_pay08 ), 0.0 ) AS data
        FROM dbo.shouying sy
        WITH ( nolock )
        WHERE sy.shouying_type IN ( '订金', '交易', '交易2', '订金2', '订金3', '售后小件' )
        AND sy.areaid IN
        <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND sy.dtime
        BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
        AND CONVERT ( datetime,#{req.endTime}, 20 )
        GROUP BY  sy.areaid
    UNION ALL
        <!--其他-->
        SELECT '收款金额' AS businessTypeName, '其他' AS projectName, NULL AS projectDescription, areaid AS areaId
        ,103 AS sortFlag , isnull( SUM ( isnull( sy.sub_pay02, 0.0 ) + isnull( sy.sub_pay06, 0.0 ) ), 0.0 ) AS data
        FROM dbo.shouying sy
        WITH ( nolock )
        WHERE sy.shouying_type IN ( '订金', '交易', '交易2', '订金2', '订金3', '售后小件' )
        AND sy.areaid IN
        <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND sy.dtime
        BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
        AND CONVERT ( datetime,#{req.endTime}, 20 )
        GROUP BY sy.areaid
    UNION ALL
        <!--微信支付-->
        SELECT '收款金额' AS businessTypeName, '微信支付' AS projectName,NULL AS projectDescription, n.payAreaid AS areaId ,104 AS sortFlag , sum(n.money) AS data
        FROM dbo.netpay_record n with(nolock) WHERE 1=1
        AND n.payAreaid IN
        <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND n.dtime
        BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
        AND CONVERT ( datetime,#{req.endTime}, 20 )
        AND n.payWay LIKE '%微信%'
        AND n.type IN (1,2,3,6,10)
        GROUP BY n.payAreaid
    UNION ALL
        <!--支付宝支付-->
        SELECT '收款金额' AS businessTypeName, '支付宝支付' AS projectName,NULL AS projectDescription, n.payAreaid AS areaId ,105 AS sortFlag , sum(n.money) AS data
        FROM dbo.netpay_record n with(nolock) WHERE 1=1
        AND n.payAreaid IN
        <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND n.dtime
        BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
        AND CONVERT ( datetime,#{req.endTime}, 20 )
        AND n.payWay LIKE '%支付宝%'
        AND n.type IN (1,2,3,6,10)
        GROUP BY n.payAreaid
    UNION ALL
        <!--首信易支付-->
        SELECT '收款金额' AS businessTypeName, '首信易支付' AS projectName,NULL AS projectDescription, n.payAreaid AS areaId ,106 AS sortFlag , sum(n.money) AS data
        FROM dbo.netpay_record n with(nolock) WHERE 1=1
        AND n.payAreaid IN
        <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND n.dtime
        BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
        AND CONVERT ( datetime,#{req.endTime}, 20 )
        AND n.payWay LIKE '%首信%'
        AND n.type IN (1,2,3,6,10)
        GROUP BY n.payAreaid
    UNION ALL
        <!--其他网络支付-->
        SELECT '收款金额' AS businessTypeName, '其他网络支付' AS projectName,NULL AS projectDescription, n.payAreaid AS areaId ,107 AS sortFlag , sum(n.money) AS data
        FROM dbo.netpay_record n with(nolock) WHERE 1=1
        AND n.payAreaid IN
        <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND n.dtime
        BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
        AND CONVERT ( datetime,#{req.endTime}, 20 )
        AND n.type IN (1,2,3,6,10)
        AND n.payWay NOT LIKE '%微信%'
        AND n.payWay NOT LIKE '%支付宝%'
        AND n.payWay NOT LIKE '%首信%'
        GROUP BY n.payAreaid
    UNION ALL
        <!--网上支付-->
        SELECT '收款金额' AS businessTypeName, '网上支付合计' AS projectName, NULL AS projectDescription, areaid AS areaId ,108 AS sortFlag ,
        isnull( SUM ( sy.sub_pay03 ), 0.0 ) AS data
        FROM dbo.shouying sy
        WITH ( nolock )
        WHERE sy.shouying_type IN ( '订金', '交易', '交易2', '订金2', '订金3', '售后小件' )
        AND sy.areaid IN
        <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND sy.dtime
        BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
        AND CONVERT ( datetime,#{req.endTime}, 20 )
        GROUP BY sy.areaid
    UNION ALL
        <!--分期支付-->
        SELECT '收款金额' AS businessTypeName, isnull( sy.name,'其他分期') AS projectName ,NULL projectDescription , sy.areaid AS areaId,
        200 + dense_rank() OVER(ORDER BY sy.name desc) AS sortFlag, isnull( SUM(sy.sub_pay04), 0.0) AS data
        FROM
        (SELECT DISTINCT
        sy.id,sc.code,sc.value,sc.name name,sy.sub_pay04,sy.areaid
        FROM dbo.shouying sy with(nolock)
        LEFT JOIN dbo.shouyin_other so with(nolock)
        ON sy.id=so.shouyinid
        AND EXISTS
        (
        SELECT 1 FROM dbo.sysConfig sc WHERE (sc.code = 36 AND isnull(sy.sub_pay04,0.0)>0)
        AND CAST(so.type_ AS nvarchar(10)) = sc.value )
        LEFT JOIN dbo.sysConfig sc with(nolock)
        ON (sc.code = 36
        AND isnull(sy.sub_pay04,0.0)>0
        AND CAST(so.type_ AS nvarchar(10)) = sc.value)
        WHERE 1=1
        AND sy.shouying_type IN ('订金','交易','交易2','订金2','订金3','售后小件')
        AND sy.dtime
        BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
        AND CONVERT ( datetime,#{req.endTime}, 20 )
        AND isnull(sy.sub_pay04,0.0)>0 ) sy
        WHERE sy.areaid IN
        <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY  sy.areaid, sy.code, sy.name
    UNION ALL
        <!--三方支付-->
        SELECT '收款金额' AS businessTypeName, isnull( sy.name,'其他三方') AS projectName ,NULL projectDescription , sy.areaid AS areaId,
        500 + dense_rank() OVER(ORDER BY sy.name desc) AS sortFlag, isnull( SUM(sy.sub_pay05), 0.0) AS data
        FROM
        (SELECT DISTINCT sy.id,sc.code,sc.value,sc.name name, sy.sub_pay05,sy.areaid
        FROM dbo.shouying sy with(nolock)
        LEFT JOIN dbo.shouyin_other so with(nolock)
        ON sy.id=so.shouyinid
        AND EXISTS
            (
            SELECT 1 FROM dbo.sysConfig sc WHERE (sc.code = 37 AND isnull(sy.sub_pay05,0.0)>0)
        AND CAST(so.type_ AS nvarchar(10)) = sc.value )
        LEFT JOIN dbo.sysConfig sc with(nolock)
        ON (sc.code = 37 AND isnull(sy.sub_pay05,0.0)>0 AND CAST(so.type_ AS nvarchar(10)) = sc.value)
        WHERE 1=1
        AND sy.shouying_type IN ('订金','交易','交易2','订金2','订金3','售后小件')
        AND sy.dtime
        BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
        AND CONVERT ( datetime,#{req.endTime}, 20 )
        AND isnull(sy.sub_pay05,0.0)>0 ) sy
        WHERE sy.areaid IN
        <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY sy.areaid, sy.code, sy.name
    </sql>

    <!--  退款金额 查询  -->
    <sql id="refundAmount">
        SELECT '退款金额' AS businessTypeName,case when shtk.tui_kinds = 1 then isnull(std.tui_way,'其他') else isnull(shtk.tui_way,'其他') end AS projectName, NULL projectDescription, areaid AS areaId
        ,1000 + dense_rank() OVER(ORDER BY case when shtk.tui_kinds = 1 then isnull(std.tui_way,'其他') else isnull(shtk.tui_way,'其他') end desc) AS sortFlag
             , isnull(sum(case when shtk.tui_kinds = 1 and std.id is not null then std.refund_price else isnull(shtk.tuikuanM,0.0)-isnull(shtk.peizhiPrice,0.0)-isnull(shtk.piaoPrice,0.0) end),0.0) AS data
        FROM dbo.shouhou_tuihuan shtk with(nolock)
        left join dbo.shouhou_tuihuan_detail std with(nolock) on shtk.tui_kinds = 1 and isnull(std.is_del,0) = 0 and std.fk_tuihuan_id = shtk.id
        WHERE isnull(shtk.isdel,0)=0
          AND isnull(shtk.check3,0)=1
          AND shtk.check3dtime
        BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
        AND CONVERT ( datetime,#{req.endTime}, 20 )
        AND areaid IN
        <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY  shtk.areaid,case when shtk.tui_kinds = 1 then isnull(std.tui_way,'其他') else isnull(shtk.tui_way,'其他') end
    </sql>

    <!--  回收付款 查询  -->
    <sql id="payBack">
        SELECT '回收付款' AS businessTypeName ,case rs.sub_pay WHEN 1 THEN '充值到余额' WHEN 2 THEN '银行转账' WHEN 4 THEN '支付宝转账' WHEN 5 THEN '微信支付'
                                               WHEN 3 THEN '回购换其他' WHEN 6 THEN '支付宝支付' WHEN 7 THEN '现金' ELSE '其他' END projectName
             ,null projectDescription ,areaid AS areaId
            ,2000 +dense_rank() OVER(ORDER BY rs.sub_pay desc) AS sortFlag
             ,isnull(SUM(rb.price),0.0) AS data
        FROM dbo.recover_basket rb with(nolock)
    INNER JOIN dbo.recover_sub rs with(nolock)
        ON rb.sub_id=rs.sub_id
        WHERE ISNULL(rb.isdel,0) = 0
          AND rs.sub_check = 3
          AND rs.pay_time
            BETWEEN CONVERT ( datetime,#{req.startTime}, 20 )
            AND CONVERT ( datetime,#{req.endTime}, 20 )
            AND areaid IN
            <foreach collection='req.areaIdList' index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY rs.areaid,rs.sub_pay

    </sql>


    <select id="getInstallmentName" resultType="java.lang.String">
        select DISTINCT tui_way from dbo.shouhou_tuihuan  t with(nolock) where isnull(check3,0)=1
            and exists( select 1 from dbo.sysConfig s with(nolock) where s.code in (36) and s.name+'返回'=t.tui_way and isnull(isdel,0)=0)
    </select>

    <select id="getThreePartyName" resultType="java.lang.String">
        select DISTINCT tui_way from dbo.shouhou_tuihuan  t with(nolock) where isnull(check3,0)=1
            and exists( select 1 from dbo.sysConfig s with(nolock) where s.code in (37) and s.name+'返回'=t.tui_way and isnull(isdel,0)=0)
    </select>

    <select id="getPosName" resultType="java.lang.String">
        select DISTINCT tui_way from dbo.shouhou_tuihuan t with(nolock) WHERE isnull(check3,0)=1
            and exists( select 1 from dbo.posPayConfig p with(nolock) where p.name+'返回'=t.tui_way and isnull(isdel,0)=0)
    </select>
    <select id="listActivityShouYing" resultType="com.jiuji.oa.afterservice.other.po.Shouying">
        SELECT sy.* FROM shouying sy WITH(NOLOCK)
        INNER JOIN shouyin_other so WITH(NOLOCK) ON so.shouyinid = sy.id
        WHERE sy.sub_id=#{subId} and sy.shouying_type in
          <foreach collection="shouyingTypes" separator="," open="(" close=")" item="shouyingType">
              #{shouyingType}
          </foreach>
          AND so.type_ in (SELECT CAST(c.value as int) FROM dbo.sysConfig c WITH(NOLOCK)
              INNER JOIN dbo.payment_config pc WITH(NOLOCK) ON pc.sys_config_id=c.id WHERE code=37 AND pc.activity_type=#{activityType})
    </select>

</mapper>
