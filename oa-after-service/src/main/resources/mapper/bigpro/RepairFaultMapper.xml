<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.RepairFaultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.RepairFault">
        <id column="id" property="id" />
        <result column="master_id" property="masterId" />
        <result column="fault_name" property="faultName" />
        <result column="is_required" property="isRequired" />
        <result column="sort_no" property="sortNo" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_del" property="isDel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, master_id, fault_name, is_required, sort_no, create_user, create_time, update_user, update_time, is_del
    </sql>

</mapper> 