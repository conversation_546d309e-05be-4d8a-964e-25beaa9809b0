<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouExMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="peizhi" property="peizhi"/>
        <result column="problem" property="problem"/>
        <result column="comment" property="comment"/>
        <result column="username" property="username"/>
        <result column="mobile" property="mobile"/>
        <result column="tel" property="tel"/>
        <result column="stats" property="stats"/>
        <result column="baoxiu" property="baoxiu"/>
        <result column="inuser" property="inuser"/>
        <result column="imei" property="imei"/>
        <result column="xianshi" property="xianshi"/>
        <result column="contentcsdate" property="contentcsdate"/>
        <result column="tradedate" property="tradedate"/>
        <result column="modidate" property="modidate"/>
        <result column="feiyong" property="feiyong"/>
        <result column="costprice" property="costprice"/>
        <result column="weixiuren" property="weixiuren"/>
        <result column="dyjid" property="dyjid"/>
        <result column="offtime" property="offtime"/>
        <result column="area" property="area"/>
        <result column="shouyinglock" property="shouyinglock"/>
        <result column="shouyingdate" property="shouyingdate"/>
        <result column="shouyinguser" property="shouyinguser"/>
        <result column="userid" property="userid"/>
        <result column="kinds" property="kinds"/>
        <result column="isticheng" property="isticheng"/>
        <result column="waiguan" property="waiguan"/>
        <result column="result_dtime" property="resultDtime"/>
        <result column="issoft" property="issoft"/>
        <result column="modidtime" property="modidtime"/>
        <result column="product_id" property="productId"/>
        <result column="product_color" property="productColor"/>
        <result column="buyarea" property="buyarea"/>
        <result column="pandian" property="pandian"/>
        <result column="pandiandate" property="pandiandate"/>
        <result column="toarea" property="toarea"/>
        <result column="istui" property="istui"/>
        <result column="pandianinuser" property="pandianinuser"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="mkc_id" property="mkcId"/>
        <result column="isquick" property="isquick"/>
        <result column="wcount" property="wcount"/>
        <result column="weixiuzuid" property="weixiuzuid"/>
        <result column="weixiuzuid_jl" property="weixiuzuidJl"/>
        <result column="isweixiu" property="isweixiu"/>
        <result column="weixiudtime" property="weixiudtime"/>
        <result column="weixiu_startdtime" property="weixiuStartdtime"/>
        <result column="orderid" property="orderid"/>
        <result column="isquji" property="isquji"/>
        <result column="isfan" property="isfan"/>
        <result column="pingjia" property="pingjia"/>
        <result column="pingjia1" property="pingjia1"/>
        <result column="sub_id" property="subId"/>
        <result column="webtype1" property="webtype1"/>
        <result column="webtype2" property="webtype2"/>
        <result column="webstats" property="webstats"/>
        <result column="ServiceType" property="ServiceType"/>
        <result column="basket_id" property="basketId"/>
        <result column="ishuishou" property="ishuishou"/>
        <result column="yuyueid" property="yuyueid"/>
        <result column="huiprint" property="huiprint"/>
        <result column="weixiurentime" property="weixiurentime"/>
        <result column="reweixiuren" property="reweixiuren"/>
        <result column="sxname" property="sxname"/>
        <result column="sxmobile" property="sxmobile"/>
        <result column="sxsex" property="sxsex"/>
        <result column="sxuserid" property="sxuserid"/>
        <result column="lockpwd" property="lockpwd"/>
        <result column="testuser" property="testuser"/>
        <result column="wxkind" property="wxkind"/>
        <result column="wxConfig" property="wxConfig"/>
        <result column="noticetime" property="noticetime"/>
        <result column="testtime" property="testtime"/>
        <result column="deviceid" property="deviceid"/>
        <result column="devicepwd" property="devicepwd"/>
        <result column="youhuima" property="youhuima"/>
        <result column="yuyueCheck" property="yuyueCheck"/>
        <result column="isXcMkc" property="isXcMkc"/>
        <result column="isXcMkcInfo" property="isXcMkcInfo"/>
        <result column="wxTestTime" property="wxTestTime"/>
        <result column="wxTestInfo" property="wxTestInfo"/>
        <result column="RepairLevel" property="repairLevel"/>
        <result column="areaid" property="areaid"/>
        <result column="toareaid" property="toareaid"/>
        <result column="buyareaid" property="buyareaid"/>
        <result column="wxTestStats" property="wxTestStats"/>
        <result column="gjUser" property="gjUser"/>
        <result column="ProcessConfirmStats" property="ProcessConfirmStats"/>
        <result column="oldshouhouid" property="oldshouhouid"/>
        <result column="isBakData" property="isBakData"/>
        <result column="isjbanwxqq" property="isjbanwxqq"/>
        <result column="yuyueCheckuser" property="yuyueCheckuser"/>
        <result column="qujitongzhitime" property="qujitongzhitime"/>
        <result column="daojishi" property="daojishi"/>
        <result column="codeMsg" property="codeMsg"/>
        <result column="result_user" property="resultUser"/>
        <result column="smstime" property="smstime"/>
        <result column="teltime" property="teltime"/>
        <result column="EarnestMoneySubid" property="EarnestMoneySubid"/>
        <result column="serversOutUser" property="serversOutUser"/>
        <result column="serversOutDtime" property="serversOutDtime"/>
        <result column="youhuifeiyong" property="youhuifeiyong"/>
        <result column="truename" property="truename"/>
        <result column="iszy" property="iszy"/>
        <result column="wxAreaid" property="wxAreaid"/>
        <result column="imeifid" property="imeifid"/>
        <result column="yifum" property="yifum"/>
        <result column="kuaixiuFlag" property="kuaixiuFlag"/>
        <result column="kuaixiuSendTime" property="kuaixiuSendTime"/>
        <result column="iszp" property="iszp"/>
        <result column="wuliyou" property="wuliyou"/>
        <result column="mobileServeiceType" property="mobileServeiceType"/>
        <result column="lppeizhi" property="lppeizhi"/>
        <result column="fromshouhouid" property="fromshouhouid"/>
        <result column="ServiceCostprice" property="ServiceCostprice"/>
        <result column="question_type" property="questionType"/>
        <result column="pzid" property="pzid"/>
    </resultMap>

    <sql id="getIndex">
        shouhou h with(nolock)
        LEFT JOIN shouhou_tuihuan t with(nolock) on ISNULL( isdel, 0 ) = 0 AND tuihuan_kind IN ( 1, 2, 3, 4, 5 ) and t.shouhou_id= h.id
        LEFT JOIN ( SELECT DISTINCT shouhou_id FROM shouhou_huishou with(nolock) ) AS hs ON hs.shouhou_id= h.id
        LEFT JOIN BBSXP_Users u with(nolock) ON u.id= h.userid
        WHERE
        1 = 1
        <choose>
            <when test="shouhou.key!=null and shouhou.key.length()>0">
                <choose>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="mobile"'>
                        AND (
                        h.sxmobile LIKE CONCAT('%',#{shouhou.key},'%')
                        OR h.mobile LIKE CONCAT('%',#{shouhou.key},'%')
                        OR h.tel LIKE CONCAT('%',#{shouhou.key},'%')
                        )
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="imei"'>
                        and h.imei like CONCAT('%',#{shouhou.key},'%')
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="orderid"'>
                        and h.orderid = #{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="id" and shouhou.shouHouIdList!=null and shouhou.shouHouIdList.size()>0'>
                        and h.id  in
                        <foreach collection="shouhou.shouHouIdList" item="id" separator="," open="(" close=")">
                            #{id}
                        </foreach>
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="name"'>
                        and h.name like CONCAT('%',#{shouhou.key},'%')
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="inuser"'>
                        and h.inuser = #{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="weixiuren"'>
                        and h.weixiuren = #{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="pandianinuser"'>
                        and h.pandianinuser = #{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="userid" and shouhou.keyIsNum'>
                        and h.userid = #{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="mkc_id" and shouhou.keyIsNum'>
                        and h.mkc_id = #{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="minprice" and shouhou.keyIsNum'>
                        and exists(select basket_id from basket with(nolock) where price>=#{shouhou.key} and
                        basket_id=h.basket_id) and
                        h.baoxiu !=2
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="day" and shouhou.keyIsNum'>
                        and DateDiff(d,h.modidate,isnull(h.qujitongzhitime,isnull(h.offtime,getdate())))&gt;=#{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="minday" and shouhou.keyIsNum'>
                        and DateDiff(d,h.modidate,isnull(h.qujitongzhitime,isnull(h.offtime,getdate())))&lt;=#{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="minhour" and shouhou.keyIsNum'>
                        and DateDiff(hour,h.modidate,isnull(h.qujitongzhitime,isnull(h.offtime,getdate())))&lt;=#{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="minminute" and shouhou.keyIsNum'>
                        and DateDiff(minute,h.modidate,isnull(h.qujitongzhitime,isnull(h.offtime,getdate())))&lt;=#{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="syday"'>
                        and DateDiff(d,h.tradedate,modidate)&lt;=#{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="feiyong"'>
                        and h.feiyong&gt;=#{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="costprice" and shouhou.keyIsNum'>
                        and h.costprice&gt;=#{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="wxlirun" and shouhou.keyIsNum'>
                        and (h.feiyong-h.costprice) &gt;=#{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="wxlirunf" and shouhou.keyIsNum'>
                        and (h.feiyong-h.costprice) &lt;=#{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="wxcount" and shouhou.keyIsNum'>
                        and wcount &gt;=#{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="gjuser"'>
                        and h.gjUser like CONCAT('%',#{shouhou.key},'%')
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="productid" and shouhou.keyIsNum'>
                        and h.product_id = #{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="qujizhouqi"'>
                        AND ISNULL(isquji,0)=0 AND qujitongzhitime IS NOT NULL AND DATEDIFF(HOUR,
                        qujitongzhitime,GETDATE())&gt; #{shouhou.key}*24
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="testuser"'>
                        and h.testuser= #{shouhou.key}
                    </when>
                    <when test='shouhou.shou_kind1!=null and shouhou.shou_kind1=="comment"'>
                        and h.comment like CONCAT('%',#{shouhou.key},'%')
                    </when>
                </choose>
            </when>
            <when test='(shouhou.key==null or shouhou.key=="") and shouhou.shou_kind1=="weixiuren"'>
                and isnull(h.weixiuren,'')=''
            </when>
            <when test='shouhou.shou_kind1=="wxkindShouyin"'>
                and wxkind = 6 AND ISNULL(shouyinglock,0)=0
            </when>
        </choose>
        <if test="shouhou.sourceType!=null and shouhou.sourceType ==1">
            and (
             <if test="shouhou.isNewMachine!=null and shouhou.isNewMachine ==1">
                  (isnull(h.ishuishou,0) = 0
                  and not exists(select 1 from dbo.basket b with(nolock ) where b.basket_id = h.basket_id and b.type = 22)
                  and ISNULL(h.sub_id,0) != 0
                  and h.userid != 76783) or
              </if>
              <if test="shouhou.isExcellentProduct!=null and shouhou.isExcellentProduct ==1">
                  (exists(select 1 from dbo.basket b with(nolock ) where b.basket_id = h.basket_id and b.type = 22)
                  and h.userid != 76783 )or
              </if>
              <if test="shouhou.isGoodProduct!=null and shouhou.isGoodProduct ==1">
                  (isnull(h.ishuishou,0) = 1
                  and h.userid != 76783) or
              </if>
              <if test="shouhou.isValueAddedRecycling!=null and shouhou.isValueAddedRecycling ==1">
                  (isnull(h.ishuishou,0) = 2
                  and h.userid != 76783) or
              </if>
              <if test="shouhou.isExternalRepairMachine!=null and shouhou.isExternalRepairMachine ==1">
                  (ISNULL(h.sub_id,0) = 0
                  and h.userid != 76783) or
              </if>
              <choose>
                  <when test="shouhou.isGoodsInStock!=null and shouhou.isGoodsInStock ==1">
                      h.userid = 76783
                  </when>
                  <otherwise>
                      1=2
                  </otherwise>
              </choose>
               )
        </if>


        <if test="shouhou.fromSource !=null">
            and h.from_source=#{shouhou.fromSource}
        </if>
        <if test="shouhou.isyouhui">
            AND ISNULL( h.ServiceType, 0 ) = 0
            AND ISNULL( h.baoxiu, 0 )&lt;&gt; 1
            AND ISNULL( h.wxkind, 0 ) &lt;&gt;5
            AND NOT EXISTS (
            SELECT
            id
            FROM
            shouhou_huishou sh with(nolock)
            WHERE
            ISNULL( ishuanhuo, 0 ) = 1
            AND sh.shouhou_id = h.id
            )
        </if>
        <choose>
            <when test='shouhou.isXianhuo!=null and shouhou.isXianhuo == "1"'>and h.userid = 76783</when>
            <when test='shouhou.isXianhuo!=null and shouhou.isXianhuo == "0"'>and h.userid != 76783</when>
        </choose>
        <if test='shouhou.isshouyinlock!=null and shouhou.isshouyinlock!=""'>
            <choose>
                <when test='shouhou.isshouyinlock=="1"'>
                    and isnull(h.feiyong,0) > 0 and h.feiyong=h.yifum
                </when>
                <when test='shouhou.isshouyinlock=="0"'>
                    and isnull(h.feiyong,0) > 0 and h.feiyong &lt;&gt;h.yifum
                </when>
            </choose>
        </if>
        <if test='shouhou.authid!=null and shouhou.authid!="" and shouhou.area_kind1!=1'>
            AND EXISTS(SELECT 1 FROM areainfo a with(nolock) where h.areaid=a.id and a.authorizeid= #{shouhou.authid})
        </if>
        <if test="shouhou.areaIds!=null and shouhou.areaIds.size>0">
            <choose>
                <when test='shouhou.istoarea=="1"'>and h.toareaid in
                    <foreach collection="shouhou.areaIds" item="areaId" separator="," open="(" close=")">
                        #{areaId}
                    </foreach>
                </when>
                <when test='shouhou.istoarea=="0"'>and h.toareaid is null and h.areaid in
                    <foreach collection="shouhou.areaIds" item="areaId" separator="," open="(" close=")">
                        #{areaId}
                    </foreach>
                </when>
                <when test='shouhou.istoarea=="2"'>and h.toareaid is not null and h.areaid in
                    <foreach collection="shouhou.areaIds" item="areaId" separator="," open="(" close=")">
                        #{areaId}
                    </foreach>
                </when>
                <when test='shouhou.istoarea=="3"'>and isnull(h.toareaid,h.areaid) in
                    <foreach collection="shouhou.areaIds" item="areaId" separator="," open="(" close=")">
                        #{areaId}
                    </foreach>
                </when>
                <otherwise>
                    and (h.areaid in
                    <foreach collection="shouhou.areaIds" item="areaId" separator="," open="(" close=")">
                        #{areaId}
                    </foreach>
                    or h.toareaid in
                    <foreach collection="shouhou.areaIds" item="areaId" separator="," open="(" close=")">
                        #{areaId}
                    </foreach>)
                </otherwise>
            </choose>
        </if>
        <if test='shouhou.weixiuzuid!=null and shouhou.weixiuzuid!=""'>
            <choose>
                <when test='shouhou.weixiuzuid.toLowerCase()=="null"'>and h.weixiuzuid is null</when>
                <otherwise>
                    <choose>
                        <when test='shouhou.isweixiu == "1" or shouhou.isweixiu == "0"'>
                            and h.weixiuzuid_jl=#{shouhou.weixiuzuid}
                        </when>
                        <otherwise>
                            and h.weixiuzuid=#{shouhou.weixiuzuid}
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test='shouhou.isweixiu == "1"'>and h.isweixiu=1</when>
            <when test='shouhou.isweixiu == "0"'>and h.isweixiu=0</when>
            <when test='shouhou.isweixiu.toLowerCase() == "null"'>and h.isweixiu is null</when>
        </choose>
        <if test="shouhou.youPinMachine != null">
            <choose>
                <when test="shouhou.youPinMachine == 1">
                    and EXISTS(select 1 from basket bk with(nolock) where bk.[type]=22 and bk.basket_id = h.basket_id)
                </when>
                <otherwise>
                    and not EXISTS(select 1 from basket bk with(nolock) where bk.[type]=22 and bk.basket_id = h.basket_id)
                </otherwise>
            </choose>
        </if>
        <if test='shouhou.date1!=null and shouhou.date2!=null and shouhou.date_kind!=null and shouhou.date_kind!="" and shouhou.date_kind!="testtime"'>
            <choose>
                <when test='shouhou.date_kind=="modidate"'>
                    AND h.modidate  BETWEEN #{shouhou.date1} AND #{shouhou.date2}
                </when>
                <when test='shouhou.date_kind=="tradedate"'>
                    AND h.tradedate  BETWEEN #{shouhou.date1} AND #{shouhou.date2}
                </when>
                <when test='shouhou.date_kind=="result_dtime"'>
                    AND h.result_dtime  BETWEEN #{shouhou.date1} AND #{shouhou.date2}
                </when>
                <when test='shouhou.date_kind=="modidtime"'>
                    AND((h.modidtime  BETWEEN #{shouhou.date1} AND #{shouhou.date2} and isnull(h.stats,0)  = 1)   or (h.weixiudtime  BETWEEN #{shouhou.date1} AND #{shouhou.date2} and isnull(h.stats,0)  = 3))
                </when>
                <when test='shouhou.date_kind=="offtime"'>
                    AND h.offtime  BETWEEN #{shouhou.date1} AND #{shouhou.date2}
                </when>
                <when test='shouhou.date_kind=="weixiu_startdtime"'>
                    AND h.weixiu_startdtime  BETWEEN #{shouhou.date1} AND #{shouhou.date2}
                </when>
                <when test='shouhou.date_kind=="weixiudtime"'>
                    AND h.weixiudtime  BETWEEN #{shouhou.date1} AND #{shouhou.date2}
                </when>
                <when test='shouhou.date_kind=="testtime"'>
                    AND h.testtime  BETWEEN #{shouhou.date1} AND #{shouhou.date2}
                </when>
            </choose>
        </if>
        <if test="shouhou.ishuan == 1">AND h.wxkind= 5</if>
        <if test="shouhou.ceshijieguo!=null and shouhou.ceshijieguo == 1">
            and exists(select id from shouhoutestinfo with(nolock) where isnull(testResult,0) = 0 and
            h.id=shouhoutestinfo.shouhou_id)
        </if>
        <choose>
            <when test='shouhou.issoft!=null and shouhou.issoft == "1"'>and h.issoft=1</when>
            <when test='shouhou.issoft!=null and shouhou.issoft == "0"'>and h.issoft=0</when>
        </choose>
        <choose>
            <when test='shouhou.pandian == "1"'>and h.pandian=1</when>
            <when test='shouhou.pandian == "0"'>and isnull(h.pandian,0)=0</when>
        </choose>
        <choose>
            <when test='shouhou.isquji == "1"'>and h.isquji=1</when>
            <when test='shouhou.isquji == "0"'>AND isnull( h.isquji, 0 ) = 0</when>
        </choose>
        <if test='shouhou.isticheng == "0" or shouhou.isticheng == "1"'>AND isticheng = #{shouhou.isticheng}
        </if>
        <if test="shouhou.noCheck">and h.baoxiu&lt;&gt;4</if>
        <if test='shouhou.feibaoKind!=null and shouhou.feibaoKind!=""'>
            <choose>
                <when test='shouhou.feibaoKind == "feibaozixiu"'>
                    AND isnull(h.baoxiu,0)=0 AND NOT EXISTS(SELECT 1 FROM shouhou_qudao with(nolock) WHERE
                    shouhouid=h.id ) AND h.feiyong>0 AND h.shouyinglock IN(1,2) AND ISNULL(h.weixiuzuid,0)!=0
                </when>
                <when test='shouhou.feibaoKind == "feibaojiaoyi"'>AND isnull(h.baoxiu,0)=0 AND h.feiyong>0 AND
                    h.shouyinglock IN(1,2)
                </when>
            </choose>
        </if>
        <if test='shouhou.baoxiu!=null and shouhou.baoxiu!=""'>
            and isnull(h.baoxiu,0)=#{shouhou.baoxiu}
        </if>
        <if test='shouhou.baoxiuList!=null and shouhou.baoxiuList.size > 0'>
            and isnull(h.baoxiu,0) in
            <foreach collection="shouhou.baoxiuList" index="index" item="baoxiu" separator="," open="(" close=")">
                #{baoxiu}
            </foreach>
        </if>
        <if test='shouhou.waisong!=null'>
            <choose>
                <when test='shouhou.waisong == 1'>
                    <choose>
                        <when test='shouhou.waisongname==null or shouhou.waisongname==""'>
                            and exists(select id from shouhou_qudao sq with(nolock) where sq.shouhouid=h.id)
                        </when>
                        <otherwise>
                            <choose>
                                <when test="shouhou.waisongNameIsNumber">
                                    and exists( select id from shouhou_qudao sq with(nolock) where sq.shouhouid=h.id and
                                    shqd2id=#{shouhou.waisongname} )
                                </when>
                                <otherwise>
                                    and exists(select id from shouhou_qudao sq with(nolock) where sq.shouhouid=h.id and
                                    shqd2name LIKE CONCAT('%',#{shouhou.waisongname},'%') )
                                </otherwise>
                            </choose>
                        </otherwise>
                    </choose>
                </when>
                <when test='shouhou.waisong == 2'>
                    and not exists(select id from shouhou_qudao sq with(nolock) where sq.shouhouid=h.id)
                </when>
            </choose>
        </if>
        <if test='shouhou.shou_kind1 != "inuser"'>
            <choose>
                <when test='shouhou.newj == "1"'>and h.userid=76783</when>
                <when test='shouhou.newj == "0"'>and h.userid&lt;&gt;76783</when>
            </choose>
        </if>
        <if test='shouhou.weixiuren != null and shouhou.weixiuren != ""'>AND h.weixiuren=#{shouhou.weixiuren}</if>
        <if test='shouhou.isfan != null and shouhou.isfan!="" and shouhou.isfan != "-1"'>AND isnull( h.isfan, 0 )
            =#{shouhou.isfan}
        </if>
        <choose>
            <when test='shouhou.shou_kind2!=null and shouhou.shou_kind2=="clz"'>and h.xianshi=1 and h.stats=0
            </when>
            <when test='shouhou.shou_kind2!=null and shouhou.shou_kind2=="yxh"'>and h.xianshi=1 and h.stats=1
            </when>
            <when test='shouhou.shou_kind2!=null and shouhou.shou_kind2=="xbh"'>and h.xianshi=1 and h.stats=3
            </when>
            <when test='shouhou.shou_kind2!=null and shouhou.shou_kind2=="hsz"'>and h.xianshi=0</when>
            <otherwise>and h.xianshi=1</otherwise>
        </choose>
        <choose>
            <when test='shouhou.shou_kinds == "bd"'>and h.kinds='bd'</when>
            <when test='shouhou.shou_kinds == "dz"'>and h.kinds='dz'</when>
        </choose>
        <choose>
            <when test='shouhou.ishanghuo == "0"'>and h.product_color not like '%行货%'</when>
            <when test='shouhou.shou_kinds == "1"'>and h.product_color like '%行货%'</when>
        </choose>
        <if test='shouhou.webstats!=null and shouhou.webstats!=""'>
            and h.webstats=#{shouhou.webstats}
        </if>
        <choose>
            <when test='shouhou.isyuyue!=null and shouhou.isyuyue=="0"'>AND ISNULL(yuyueid,0)=0</when>
            <when test='shouhou.isyuyue!=null and shouhou.isyuyue=="1"'>AND ISNULL(yuyueid,0)&gt;0</when>
        </choose>
        <choose>
            <when test='shouhou.serviceType!=null and shouhou.serviceType=="3"'>
                and h.ServiceType Is null
            </when>
            <when test='shouhou.serviceType!=null and shouhou.serviceType!="3"'>
                and h.ServiceType=#{shouhou.serviceType}
            </when>
        </choose>
        <if test='shouhou.serviceTypeList!=null and shouhou.serviceTypeList.size > 0'>
            and isnull(h.ServiceType,3) in
            <foreach collection="shouhou.serviceTypeList" index="index" item="serviceType" separator="," open="(" close=")">
                #{serviceType}
            </foreach>
        </if>
        <choose>
            <when test='shouhou.shouhouServicesSale!=null and shouhou.shouhouServicesSale=="1"'>AND
                exists(SELECT 1 FROM
                dbo.wxkcoutput wxk WITH(NOLOCK) WHERE wxk.wxid=h.id AND wxk.ppriceid = 81683)
            </when>
            <when test='shouhou.shouhouServicesSale!=null and shouhou.shouhouServicesSale=="2"'>
                AND exists(SELECT 1 FROM dbo.wxkcoutput wxk WITH(NOLOCK) WHERE wxk.wxid=h.id AND wxk.ppriceid =
                81682)
            </when>
            <when test='shouhou.shouhouServicesSale!=null and shouhou.shouhouServicesSale=="3"'>
                AND exists(SELECT 1 FROM dbo.wxkcoutput wxk WITH(NOLOCK) WHERE wxk.wxid=h.id AND wxk.ppriceid =
                110939)
            </when>
            <when test='shouhou.shouhouServicesSale!=null and shouhou.shouhouServicesSale!="1"
            and shouhou.shouhouServicesSale!="2"
            and shouhou.shouhouServicesSale!="3"'>
                AND exists(SELECT 1 FROM dbo.ServiceRecord sr WITH(NOLOCK) WHERE sr.sub_id=h.id AND sr.ServiceType
                = #{shouhou.shouhouServicesSale} and sr.servicesTypeBindId is null)
            </when>
        </choose>
        <if test='shouhou.shouhouServicesSaleList!=null and shouhou.shouhouServicesSaleList.size > 0'>
            AND exists(SELECT 1 FROM (
                select wxk.wxid,case when wxk.ppriceid = 81683 then 1 when wxk.ppriceid = 81682 then 2 when wxk.ppriceid = 110939 then 3 else 0 end shouhouServicesSale from wxkcoutput wxk WITH(NOLOCK) WHERE wxk.ppriceid in (81683,81682,110939) and wxk.wxid=h.id
                union
                select sr.sub_id wxid,sr.ServiceType shouhouServicesSale from dbo.ServiceRecord sr WITH(NOLOCK) where sr.ServiceType > 3 and sr.sub_id=h.id and sr.servicesTypeBindId is null
                ) t
            WHERE t.shouhouServicesSale in
            <foreach collection="shouhou.shouhouServicesSaleList" index="index" item="shouhouServicesSale" separator="," open="(" close=")">
                #{shouhouServicesSale}
            </foreach>
            )
        </if>
        <if test="shouhou.printFlag != null and shouhou.printFlag == true">
            and exists (select 1 from dbo.order_record_flag f where f.flag_type=1 and f.del_flag=0 and f.sub_type=4 and f.sub_id = h.id)
        </if>
        <if test="shouhou.printFlag != null and shouhou.printFlag == false">
            and not exists (select 1 from dbo.order_record_flag f where f.flag_type=1 and f.del_flag=0 and f.sub_type=4 and f.sub_id = h.id)
        </if>
        <if test="shouhou.ishuishou!=null">
            and isnull(h.ishuishou,0)=#{shouhou.ishuishou}
        </if>
        <choose>
            <when test='shouhou.jiujian!=null and shouhou.jiujian==1'>and exists(select id from shouhou_huishou
                with(nolock) where shouhou_id=h.id  AND ISNULL(isdel,0)=0 )
            </when>
            <when test='shouhou.jiujian!=null and shouhou.jiujian==2'>
                and not exists(select id from shouhou_huishou with(nolock) where shouhou_id=h.id AND ISNULL(isdel,0)=0)
                and exists(select id from wxkcoutput with(nolock) where wxid=h.id and ppriceid &gt; 0)
            </when>
        </choose>
        <choose>
            <when test='shouhou.gift!=null and shouhou.gift==1'>
                AND EXISTS(select top 1 1 from  basket b with(nolock)
                inner join basket b1 with(nolock) on b1.giftid=b.basket_id and b1.type=1
                inner join SmallproBill pb with(nolock) on pb.basket_id=b1.basket_id
                inner join Smallpro sp with(nolock) on sp.id =pb.smallproID
                where b.basket_id=h.basket_id and isnull(b.isdel,0)=0 and  isnull(b1.isdel,0)=0 and sp.Stats&lt;&gt;2
                and t.tuihuan_kind in(3,4) and t.check3=1)
            </when>
            <when test='shouhou.gift!=null and shouhou.gift==0'>
                AND EXISTS(select top 1 1 from   basket b with(nolock)
                inner join basket b1 with(nolock) on b1.giftid=b.basket_id and b1.type=1
                left join SmallproBill pb with(nolock) on pb.basket_id=b1.basket_id
                left join Smallpro sp with(nolock) on sp.id =pb.smallproID
                where b.basket_id=h.basket_id and isnull(b.isdel,0)=0 and  isnull(b1.isdel,0)=0
                and t.tuihuan_kind in(3,4) and t.check3=1 and pb.id is null and sp.id is null)
            </when>
        </choose>
        <if test='shouhou.NeedFahuo!=null and shouhou.NeedFahuo'>
            AND EXISTS(SELECT TOP 1 ShouhouId FROM CaigouBasketRefShouhou with(nolock)
            JOIN caigou_basket with(nolock) ON caigou_basket.id=CaigouBasketRefShouhou.CaigouBasketId
            JOIN caigou_sub with(nolock) ON caigou_basket.sub_id=caigou_sub.id
            WHERE IsFahuo=0 and caigou_sub.stats=3 AND ShouhouId=h.id)
        </if>
        <if test='shouhou.repairLevel!=null'>
            <choose>
                <when test='shouhou.repairLevel==10'>AND (SELECT count(id) FROM dbo.wxkcoutput wxk WITH(NOLOCK)
                    INNER
                    JOIN dbo.productinfo wxp WITH(NOLOCK) ON wxk.ppriceid=wxp.ppriceid WHERE wxk.wxid=h.id AND
                    wxk.stats!=3 AND wxk.ppriceid &lt;&gt; 36679 AND wxk.ppriceid &lt;&gt; 81683 AND wxp.cid &lt;&gt;
                    393) = 1
                </when>
                <when test='shouhou.repairLevel==11'>AND EXISTS(SELECT 1 FROM dbo.wxkcoutput wxk WITH(NOLOCK)
                    INNER JOIN
                    dbo.productinfo wxp WITH(NOLOCK) ON wxk.ppriceid=wxp.ppriceid WHERE wxk.wxid=h.id AND
                    wxp.cid
                    IN(393,410) AND wxk.stats &lt;&gt; 3)
                    AND (EXISTS(SELECT 1 FROM dbo.wxkcoutput wxk WITH(NOLOCK) LEFT JOIN dbo.productinfo wxp
                    WITH(NOLOCK)
                    ON wxk.ppriceid=wxp.ppriceid WHERE wxk.wxid=h.id AND wxk.stats &lt;&gt; 3
                    AND ((wxk.ppriceid=0 AND wxk.price>30) OR (wxk.ppriceid>0 AND wxk.ppriceid NOT
                    IN(36678,81683,71287)
                    AND wxp.cid NOT IN(393,410) )))
                    OR ( EXISTS(SELECT 1 FROM dbo.wxkcoutput wxk WITH(NOLOCK) LEFT JOIN dbo.productinfo wxp
                    WITH(NOLOCK)
                    ON wxk.ppriceid=wxp.ppriceid WHERE wxk.wxid=h.id AND wxk.stats &lt;&gt; 3 AND wxp.cid=393)
                    AND EXISTS(SELECT 1 FROM dbo.wxkcoutput wxk WITH(NOLOCK) LEFT JOIN dbo.productinfo wxp
                    WITH(NOLOCK)
                    ON wxk.ppriceid=wxp.ppriceid WHERE wxk.wxid=h.id AND wxk.stats &lt;&gt; 3 AND wxp.cid=410) )
                    )
                </when>
                <otherwise>and h.RepairLevel=#{shouhou.repairLevel}</otherwise>
            </choose>
        </if>
        <if test='shouhou.webType2!=null and shouhou.webType2!=0'>
            and webstats IS NOT NULL and ISNULL(h.webtype2,0)= #{shouhou.webType2}
        </if>
        <if test='shouhou.tuihuan_kind!=null and shouhou.tuihuan_kind!=""'>
            and tuihuan_kind=#{shouhou.tuihuan_kind}
        </if>
        <if test='shouhou.tuihuanKindList!=null and shouhou.tuihuanKindList.size > 0'>
            and tuihuan_kind in
            <foreach collection="shouhou.tuihuanKindList" index="index" item="tuihuanKind" separator="," open="(" close=")">
                #{tuihuanKind}
            </foreach>
        </if>
        <choose>
            <when test='shouhou.wxcltype!=null and shouhou.wxcltype=="waisong"'>
                AND EXISTS( SELECT 1 FROM dbo.shouhou_qudao with(nolock) WHERE shouhouid=h.id
                <if test='shouhou.waisongqidao!=null and shouhou.waisongqidao>0'>
                    AND shqd2id=#{shouhou.waisongqidao}
                </if>
            </when>
            <when test='shouhou.wxcltype!=null and shouhou.wxcltype=="zixiu"'>
                AND NOT EXISTS( SELECT 1 FROM dbo.shouhou_qudao with(nolock) WHERE shouhouid=h.id)
            </when>
        </choose>
        <if test='shouhou.wxpjKind!=null and shouhou.wxpjKind !="" and shouhou.shou_kind1!=null and shouhou.shou_kind1 !="" and shouhou.key!=null and shouhou.key !="" and shouhou.shou_kind1 == "userid"'>
            <choose>
                <when test='shouhou.wxpjKind == "ping"'>
                    and EXISTS(SELECT 1 FROM dbo.wxkcoutput WITH(NOLOCK) INNER JOIN dbo.productinfo p
                    with(nolock) ON
                    p.ppriceid=wxkcoutput.ppriceid WHERE p.cid IN(31,24,25) AND wxkcoutput.wxid=h.id)
                </when>
                <when test='shouhou.wxpjKind=="dianchi"'>
                    and EXISTS(SELECT 1 FROM dbo.wxkcoutput WITH(NOLOCK) INNER JOIN dbo.productinfo p
                    with(nolock) ON
                    p.ppriceid=wxkcoutput.ppriceid WHERE p.cid=393 AND wxkcoutput.wxid=h.id)
                </when>
            </choose>
        </if>
        <if test="shouhou.isAuthPart != null and shouhou.isAuthPart == true">
            <!--租户隔离-->
            AND EXISTS(SELECT id from dbo.areainfo area WITH(NOLOCK) where area.id=ISNULL(h.toareaid,h.areaid) and area.xtenant = #{shouhou.xtenant})
            AND EXISTS(SELECT 1 FROM dbo.areainfo area WITH(NOLOCK) WHERE authorizeid= #{shouhou.authorizeId} AND area.id=ISNULL(h.toareaid,h.areaid))
        </if>
        <if test='shouhou.tongjitype!=null and shouhou.tongjitype !=""'>
            <choose>
                <when test='shouhou.tongjitype == "1"'>
                    AND DATEDIFF(DAY,h.modidate,GETDATE()) &gt; 5 AND NOT EXISTS( SELECT 1
                    FROM dbo.shouhou_qudao _qd WITH(NOLOCK) WHERE _qd.shouhouid=h.id)
                </when>
                <when test='shouhou.tongjitype == "2"'>
                    AND DATEDIFF(DAY,h.modidate,GETDATE()) &gt; 30 AND EXISTS( SELECT 1 FROM dbo.shouhou_qudao
                    _qd
                    WITH(NOLOCK) WHERE _qd.shouhouid=h.id)
                </when>
                <when test='shouhou.tongjitype == "4"'>
                    AND DATEDIFF(DAY,h.modidate,GETDATE()) &gt; 7
                </when>
                <when test='shouhou.tongjitype == "5"'>
                    AND DATEDIFF(DAY,h.modidate,GETDATE()) &gt; 5
                </when>
                <when test='shouhou.tongjitype == "6"'>
                    AND DATEDIFF(DAY,h.modidate,GETDATE()) &gt; 30
                </when>
            </choose>
        </if>
        <if test="shouhou.isCodeSub != null">
            and
            <if test="!shouhou.isCodeSub">
                not
            </if>
            exists(SELECT 1 FROM dbo.userCodeSubRecord ucsr with(nolock) where ucsr.subType=4 and ucsr.sub_id = h.id and isnull(check_type,0)=0)
        </if>
        <if test="shouhou.couponKinds != null and shouhou.couponKinds.size > 0">
            and exists(SELECT 1 from douyin_coupon_log dcl with(nolock) where dcl.sub_kinds = 3 and status = 1 and sub_id = h.id and is_del = 0
            and dcl.coupon_kinds in
            <foreach collection="shouhou.couponKinds" item="couponKinds" separator="," open="(" close=")" index="index">
                #{couponKinds}
            </foreach>
            )
        </if>
    </sql>
    <insert id="insertShouHou" parameterType="com.jiuji.oa.afterservice.bigpro.po.Shouhou" useGeneratedKeys="true"
            keyProperty="id" keyColumn="id">
        insert into shouhou(sxmobile,sxname,sxsex,sxuserid,lockpwd,wxkind,wxConfig,trueName,name,peizhi,problem,comment,
        username,mobile,tel,stats,baoxiu,inuser,imei,xianshi,contentcsdate,tradedate,modidate,feiyong,costprice,weixiuren,
        dyjid,offtime,areaid,shouyinglock,shouyingdate,shouyinguser,userid,kinds,isticheng,waiguan,result_dtime,issoft,
        modidtime,product_id,product_color,buyareaid,pandian,pandiandate,toareaid,istui,pandianinuser,ppriceid,mkc_id,
        isquick,wcount,weixiuzuid,weixiuzuid_jl,isweixiu,weixiudtime,weixiu_startdtime,orderid,isquji,isfan,pingjia,
        pingjia1,sub_id,webtype1,webtype2,webstats,ServiceType,basket_id,ishuishou,yuyueid,deviceid,devicepwd,
        isXcMkc,isXcMkcInfo,isBakDATA,isjbanwxqq,iszy,imeifid,kuaixiuFlag,iszp,wuliyou,lppeizhi,mobileServeiceType)
        values (#{sxmobile},#{sxname},#{sxsex},#{sxuserid},#{lockpwd},#{wxkind},#{wxConfig},#{trueName},#{name},#{peizhi},#{problem},#{comment},
        #{username},#{mobile},#{tel},#{stats},#{baoxiu},#{inuser},#{imei},#{xianshi},#{contentcsdate},#{tradedate},#{modidate},#{feiyong},#{costprice},#{weixiuren},
        #{dyjid},#{offtime},#{areaid},#{shouyinglock},#{shouyingdate},#{shouyinguser},#{userid},#{kinds},#{isticheng},#{waiguan},#{result_dtime},#{issoft},
        #{modidtime},#{product_id},#{product_color},#{buyareaid},#{pandian},#{pandiandate},#{toareaid},#{istui},#{pandianinuser},#{ppriceid},#{mkc_id},
        #{isquick},#{wcount},#{weixiuzuid},#{weixiuzuid_jl},#{isweixiu},#{weixiudtime},#{weixiu_startdtime},#{orderid},#{isquji},#{isfan},#{pingjia},
        #{pingjia1},#{sub_id},#{webtype1},#{webtype2},#{webstats},#{ServiceType},#{basket_id},#{ishuishou},#{yuyueid},#{deviceid},#{devicepwd},
        #{isXcMkc},#{isXcMkcInfo},#{isBakDATA},#{isjbanwxqq},#{iszy},#{imeifid},#{kuaixiuFlag},#{iszp},#{wuliyou},#{lppeizhi},#{mobileServeiceType})
    </insert>

    <select id="getZengpinByShouhouid" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouHouZengpinBo">
        select sc.ppriceid,p.product_name as productName,p.memberprice,sc.inuser,sc.dtime,sc.zCount from shouhou_chaoshizengpin sc WITH(NOLOCK)
        left join productinfo p WITH(NOLOCK) on sc.ppriceid = p.ppriceid
        where isnull(_type,0) = #{type} and sc.shouhouid =#{shouhouId}
    </select>

    <select id="getServersList" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouServiceOutBo">
        SELECT
        id,
        ServiceType,
        serversOutUser,
        serversOutDtime
        FROM
        dbo.shouhou WITH(NOLOCK)
        WHERE
        xianshi = 1
        AND ISNULL( isquji, 0 )= 1
        AND stats = 1
        AND ISNULL( ServiceType, 0 )!= 0
        <if test="imei != null and imei !=''">
            AND imei = #{imei}
        </if>
        <if test="wxId != null">
            AND id != #{wxId}
        </if>
    </select>

    <select id="getPpidBy" resultType="integer">
        select top 1 ppriceid from dbo.productinfo  with(nolock)
        where ppriceid=#{ppriceid}
        and brandID=1
    </select>

    <select id="getPriceAndTradeDate" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouHouPriceAndTradeDateBo">
      select top 1 b.price,s.tradeDate1 from dbo.basket b with(nolock)
      left join dbo.sub s with(nolock)
      on s.sub_id = b.sub_id
      where isnull(b.isdel,0)= 0 and b.basket_id =#{basketId}
      and s.sub_check = 3
    </select>

    <select id="getPriceBySubIdAndPpid" resultType="decimal">
        select top 1 b.price from dbo.basket b with(nolock)
        left join dbo.sub s with(nolock)
        on s.sub_id=b.sub_id where isnull(b.isdel,0)=0
        and b.sub_id=#{subId} and s.sub_check=3 and b.ppriceid=#{ppriceid}
    </select>

    <select id="getTroubleList" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTroubleListRes">
        select t.Id as id,t.Name as name,t.Description as description from trouble t with(nolock)
    </select>

    <select id="getShouhouTroubles" resultType="java.lang.Integer">
        select t.id from trouble t WITH(nolock) INNER JOIN shouhou_trouble st WITH(nolock) on t.id = st.TroubleId
        where st.ShouhouId = #{shouhouId} and st.IsValid = 1
    </select>

    <update id="updateBuyAreaIdBySubInfo">
        UPDATE shouhou SET buyareaid = (SELECT isnull(areaid, null) FROM sub with(nolock) WHERE sub_id = #{subId} ) WHERE id = #{shouhouId}
    </update>

    <update id="updateBuyAreaIdByRecoverMarketInfo">
        UPDATE shouhou SET buyareaid = (SELECT isnull(areaid, null) FROM recover_marketInfo with(nolock) WHERE sub_id = #{subId} ) WHERE id = #{shouhouId}
    </update>

    <select id="getHardwareHistory" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouHardwareHistoryRes">
        select top 1000 offtime,inuser,areaid from shouhou with(nolock)
        where xianshi=1 AND userid!=76783 and isnull(isquji,0)=1 and issoft = 0 and offtime is not null  and imei=#{imei} order by offtime desc
    </select>

    <select id="getBuyAreaIdBySubId" resultType="java.lang.Integer">
        SELECT areaid FROM sub with(nolock) WHERE sub_id = #{subId}
    </select>

    <select id="getBuyAreaIdByRecoverMarketInfo" resultType="java.lang.Integer">
        SELECT top 1 areaid FROM recover_marketInfo with(nolock) WHERE sub_id = #{subId}
    </select>

    <select id="getUserHistory" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouUserHistoryRes">
        select top 1000 name,product_color as productColor,h.areaid as areaId,modidate,t.tuihuan_kind as tuihuanKind,h.id shouhouId,
                     h.problem problem,h.baoxiu,h.feiyong,h.wxkind,h.ServiceType serviceType
        from shouhou h with(nolock)
        left join (select tuihuan_kind,shouhou_id from shouhou_tuihuan with(nolock) where (isdel is null or isdel=0) and tuihuan_kind in(1,2,3,4)) t on h.id=t.shouhou_id
        where xianshi=1 and userid=#{userId} AND userid!=76783 and isnull(isquji,0)=1 and issoft = 0
        <if test="isAuthPart != null and isAuthPart == true">
            <!--租户隔离-->
            AND EXISTS(SELECT id from dbo.areainfo area WITH(NOLOCK) where area.id=ISNULL(h.toareaid,h.areaid) and area.xtenant = #{xtenant})
            AND EXISTS(SELECT 1 FROM dbo.areainfo area WITH(NOLOCK) WHERE authorizeid= #{authorizeId} AND area.id=ISNULL(h.toareaid,h.areaid))
        </if>
        order by h.id desc
    </select>

    <select id="queryAlipayYouhuiSubId" resultType="java.lang.Integer">
        select top 1 y.sub_id from dbo.shouhou s with(nolock)  join dbo.shouying y with(nolock)  on s.sub_id=y.sub_id
        where s.id = #{shouhouId} and y.inuser = '支付宝(pay1)'
        and exists(select * from dbo.basket b with(nolock) where b.sub_id=y.sub_id
        and b.ppriceid in
        <foreach collection="aliPayYouhuiPpids" index="index" item="ppid" separator="," open="(" close=")">
            #{ppid}
        </foreach>
    </select>

    <select id="getImeiInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouImeiInfoBo">
        select imei,sh.tradedate,userid,ISNULL(th.tuihuan_kind,0) tuihuanKind from shouhou sh  with(nolock)
        left join shouhou_tuihuan th  with(nolock)  on th.shouhou_id=sh.id
        where sh.id=#{id} and ISNULL(th.isdel,0)=0
    </select>

    <select id="getImei1" resultType="java.lang.String">
        select imei1 from shouhou_imeichange  with(nolock)
        where imei2=#{imei2}
        and checkdtime is not null order by checkdtime desc
    </select>

    <select id="getImeiChangeCount" resultType="java.lang.Integer">
        select count(1) from shouhou_imeichange  with(nolock)  where shouhou_id=#{id}
    </select>

    <select id="loadSubInfoByShouhouid" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouSubDetailBo">
        select b.sub_id as subId,b.basket_id as basketId,s.userid as userId,h.areaid as areaId,s.sub_to as subTo,s.sub_mobile as subMobile,s.tradedate as tradeDate from dbo.basket b  with(nolock)
        left join dbo.sub s  with(nolock)  on b.sub_id=s.sub_id
        left join dbo.product_mkc k  with(nolock)  on b.basket_id=k.basket_id
        left join dbo.shouhou h  with(nolock)  on h.imei=k.imei and h.sub_id=s.sub_id
        where k.kc_check=5 and s.sub_check=3 and isnull(b.isdel,0)=0
        and h.id=#{shouhouId}
    </select>

    <select id="queryBBsXpUserMoneyInfo" resultType="com.jiuji.oa.afterservice.other.bo.BBSXPUsersMoneyBo">
        select save_money as saveMoney,mobile,erdu,erdu1,erdu2,isnull(save_money,0) as saveMoneyE,timingdate,timingday from bbsXP_users with(nolock) where id=#{userId}
    </select>

    <select id="getFueduByUserId" resultType="java.math.BigDecimal">
        SELECT fuedu FROM AuctionUserMoney with(nolock) WHERE userid=#{userId}
    </select>

    <update id="updateCostPriceById">
        UPDATE shouhou SET costprice=ISNULL((SELECT SUM(inprice) FROM wxkcoutput with(nolock) WHERE wxid=#{shouhouId} AND ISNULL(stats,0)!=3),0) WHERE id = #{shouhouId}
    </update>

    <select id="getShouyinAreaId" resultType="java.lang.Integer">
        select areaid from shouhou with(nolock)  where isnull(yifum,0) = isnull(feiyong,0) and feiyong > 0 and id = #{shouhouId}
    </select>

    <update id="updateShouyingLock">
        update shouhou set shouyinglock = 1 where shouyinglock =0 and isnull(yifum,0) = isnull(feiyong,0) and feiyong > 0 and id =#{shouhouId}
    </update>

    <select id="getZengpingPageList" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ChaoshiZengPinRes">
        select top 2  scp.id,scp.ppriceid,p.product_name as productName,k.leftCount,k.areaid as areaId,a.area,p.memberprice as memberPrice,scp.startDay,scp.endDay from
        shouhou_chaoshiPeifu scp with(nolock)
        left join productinfo p with(nolock) on scp.ppriceid = p.ppriceid
        left join product_kc k with(nolock) on k.ppriceid = scp.ppriceid
        left join areainfo a with(nolock) on a.id = k.areaid
        where areaid = #{req.areaId}  and k.leftCount > 0 and #{req.chaoshiDay} &gt; scp.startDay and #{req.chaoshiDay} &lt;= scp.endDay
    </select>
    <select id="getList" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouIndexRes">
        SELECT
        ISNULL( u.userclass, 0 ) AS userclass,
        u.username AS username1,
        u.realname,
        h.id,
        h.username,
        h.areaid,
        h.toareaid,
        h.tradedate,
        h.ServiceType,
        h.feiyong,
        h.youhuifeiyong,
        h.yifum,
        h.costprice,
        h.modidate,
        h.name,
        h.product_color,
        h.orderid,
        h.modidtime,
        h.ishuishou,
        h.userid,
        h.imei,
        h.offtime,
        h.result_dtime,
        h.daojishi,
        h.ppriceid,
        h.qujitongzhitime,
        h.product_id,
        h.wxkind,
        h.stats,
        h.baoxiu,
        h.gjUser,
        h.weixiuren,
        h.inuser,
        h.iszy,
        h.problem,
        h.isquji,
        h.peizhi,
        isnull( h.isquji, 0 ) AS isquji_,
        isnull( h.isquji, 0 ) AS isQuJi,
        t.tuihuan_kind tuihuanKind,
        h.weixiudtime resultTime,
        h.weixiuzuid,
        CASE
        WHEN hs.shouhou_id IS NULL THEN
        0 ELSE 1
        END AS jiujian
        <if test="shouhou.Export!=null and shouhou.Export==1">
            ,(select (SELECT wxpj.name + ';' FROM dbo.wxkcoutput wxpj WITH (NOLOCK) WHERE wxpj.wxid =  h.id FOR XML PATH (''),type).value('.', 'NVARCHAR(MAX)')) AS
            wxpj_names
        </if>,
        case when h.userid = 76783 then '现货'
             when t.tuihuan_kind is null then ''
             when t.tuihuan_kind = 3 then '退货'
             else '换货' end tuiKindName,
        case when isnull(h.ishuishou,0) = 1 then 2
             when EXISTS(select 1 from basket bk with(nolock) where bk.[type]=22 and bk.basket_id = h.basket_id) then 1
            end youLiangPin
        FROM
        <include refid="getIndex"></include>
        <choose>
            <when test="shouhou.getPagination().getField()==null">
                ORDER BY h.id desc
            </when>
            <otherwise>
                <choose>
                    <when test='shouhou.getPagination().getField() == "modidate"'>
                        ORDER BY h.modidate
                    </when>
                    <when test='shouhou.getPagination().getField() == "tradedate"'>
                        ORDER BY h.tradedate
                    </when>
                    <when test='shouhou.getPagination().getField() == "result_dtime"'>
                        ORDER BY h.result_dtime
                    </when>
                </choose>
                <if test='shouhou.getPagination().getOrder() != null and shouhou.getPagination().getOrder() == "descend"'>
                    DESC
                </if>
            </otherwise>
        </choose>
        OFFSET #{startIndex} ROWS FETCH NEXT #{pageSize} ROWS ONLY
    </select>

    <select id="getListCount" resultType="java.lang.Long">
        SELECT COUNT(1) FROM
        <include refid="getIndex"></include>
    </select>
    <select id="listBaskets" resultType="com.jiuji.oa.afterservice.bigpro.bo.BasketsQueryBO">
        select cs.areaid,c.id as basketId,cs.id as subId,c.ppriceid, c.lcount, c.inprice from caigou_basket c WITH(NOLOCK)
        join CaigouBasketRefShouhou cr WITH(NOLOCK) on c.id=cr.CaigouBasketId
        join caigou_sub cs WITH(NOLOCK) on c.sub_id = cs.id
        where cr.ShouhouId = #{shouhouId} and cr.IsFahuo=0 and cs.stats = 3
    </select>

    <select id="selectLastRepair" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.LastRepairRes">
        select top 1 id
        FROM dbo.shouhou s with (nolock)
        where (isnull(feiyong, 0) > 0 or isnull(costprice, 0) > 0)
          and userid &lt;&gt; 76783
        <if test='lastRepairReq.imei!=null and lastRepairReq.imei!=""'>
            and s.imei=#{lastRepairReq.imei}
            <if test="lastRepairReq.tradingHours != null ">
                and s.modidate>=#{lastRepairReq.tradingHours}
            </if>
        </if>
        order by id desc
    </select>
    <select id="selectRepairImeiHis" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.PageRepairImeiHisVo">

     select * from(
        select s.id, s.feiyong, s.costprice, s.baoxiu, t.wxName as repairParts,s.offtime,s.ServiceType as serviceType,s.repair_order_id
        from dbo.shouhou s with (nolock)
        left join (select distinct STUFF((select ',' + convert(nvarchar(200), w.name)
        FROM dbo.wxkcoutput w with (nolock)
        where w.wxid = wx.wxid
        FOR XML Path('')), 1, 1, '') as wxName, wx.wxid
        from dbo.wxkcoutput wx  group by wx.wxid) t on t.wxid = s.id
        where s.userid &lt;&gt; 76783
        <if test="req.repairOrderId!=null and req.repairOrderId!=0">
            and s.id=#{req.repairOrderId}
        </if>
        <if test="req.id!=null and req.id!=0">
            and s.id&lt;&gt;#{req.id}
        </if>
        <if test="req.imei!=null and req.imei!=''">
            and s.imei=#{req.imei}
        </if>) f
    </select>
    <sql id="repairHistorieFromSql">
        FROM dbo.shouhou s WITH(NOLOCK) LEFT JOIN dbo.shouhou_qudao q WITH(NOLOCK) ON s.id=q.shouhouid
        LEFT JOIN dbo.shouhou_tuihuan t WITH(NOLOCK) ON t.shouhou_id=s.id AND ISNULL(t.isdel,0)=0
        WHERE userid &lt;&gt; 76783 and
        <trim prefix="(" suffix=")" prefixOverrides="or">
            <if test='imei!=null and imei!=""'>
                or (s.imei=#{imei}
                <if test='tradingHours != null and tradingHours != "null"' >
                    and s.modidate>=#{tradingHours}
                </if>)
            </if>
            <if test='userId!=null and userId!=0'>
                or userid=#{userId}
            </if>
            <if test='mobile!=null and mobile!=""'>
                or mobile=#{mobile}
            </if>
        </trim>
    </sql>
    <select id="repairHistoriesCount" resultType="integer">
        SELECT count(s.id)
        <include refid="repairHistorieFromSql"></include>
    </select>
    <select id="repairHistories" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.RepairHistoryRes">
        SELECT top 500 s.id,s.userid,s.username,s.truename,s.mobile,s.name,s.imei,problem,CASE WHEN ISNULL(q.id,0)=0 THEN '自修'
        ELSE '外送' END wxqudao,
        CASE WHEN s.baoxiu=0 THEN '非保' WHEN s.baoxiu=1 THEN '在保' WHEN s.baoxiu=2 THEN '外修'  ELSE '' END baoxiu,
        s.feiyong,s.offtime,
        CASE WHEN t.tuihuan_kind=4 THEN '换其它型号' WHEN t.tuihuan_kind=3 THEN '退款' WHEN t.tuihuan_kind=1 THEN '换机头' WHEN
        t.tuihuan_kind=2 THEN '换主板' ELSE '维修' END weixiutype,s.ServiceType serviceType
        <include refid="repairHistorieFromSql"></include>
        ORDER BY
        <if test='imei!=null and imei!=""'>
            case when s.imei=#{imei} then isnull(s.offtime,CONVERT(datetime, '1970-01-02', 120))   else CONVERT(datetime, '1970-01-01', 120) end desc,
        </if>
        s.id DESC

    </select>
    <select id="exchangeCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM dbo.shouhou_tuihuan t WITH(NOLOCK)
            INNER JOIN dbo.shouhou s WITH(NOLOCK) ON s.id=t.shouhou_id
            WHERE s.userid=#{userId} AND t.tuihuan_kind=3 AND ISNULL(t.isdel,0)=0 AND ISNULL(s.ishuishou,0)=1
            AND t.dtime >#{startDate} AND s.wuliyou='15天无理由退货'
    </select>
    <select id="hasShouhou" resultType="java.lang.Integer">
        select top 1 id from shouhou with(nolock) where xianshi=1 and isnull(isquji,0)=0 and (imei=#{imei}
        <if test='mkcId!=null and mkcId>0'>
            or mkc_id= #{mkcId}
        </if>)
    </select>
    <select id="has4GjiaTaocan" resultType="java.lang.Integer">
        select 1 from dbo.Taocan t with(nolock) left join dbo.basket b with(nolock) on t.basket_id=b.basket_id
                left join dbo.sub s with(nolock) on b.sub_id=s.sub_id
                WHERE PackageType = 5 and isptype = 3 and flag in (2,5) and b.ppriceid in (56768,66467,70809,70810,70811,70808,70812,66473,56769,73840,73841)
                AND t.imei = #{imei}
    </select>
    <select id="getShohouIstichengSoft" resultType="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
        select top 1 id,modidate from shouhou with(nolock)  where xianshi=1 and isnull(isquji,0)=1 and inuser &lt;&gt;'系统' and imei=#{imei}
        and imei is not null and imei&lt;&gt;'' and isnull(iszp,0) = 0 and isnull(issoft,0) = 1 order by modidate desc
    </select>
    <select id="getWxCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM dbo.shouhou WITH(NOLOCK) WHERE imei=#{imei} AND userid &lt;&gt; 76783 AND xianshi=1 AND issoft=0
        AND ISNULL(isquji,0)=1
    </select>
    <select id="checkFanxiu" resultType="java.lang.Integer">
        SELECT TOP 1 id from shouhou with(nolock) where xianshi=1 and isnull(isquji,0)=1 AND imei=#{imei} AND GETDATE() &lt; DATEADD(DAY,30,offtime)
    </select>


    <update id="pingJia">
        <if test="pjkind != null and pjkind == 1">
            update shouhou set pingjia=#{pingjia} where pingjia is null and DATEDIFF(n,modidate,getdate()) &lt;= 30 and
            id=#{shouhouId}
        </if>
        <if test="pjkind != null and pjkind == 2">
            update shouhou set pingjia1=#{pingjia} where pingjia1 is null and id=#{shouhouId}
        </if>
    </update>
    <update id="handleUpdateDeFaultLabel">
        update product_xtenant_info set product_label = 25 where ppriceid &gt; #{maxPpirceId} and product_label is null
        and ppriceid in (select ppriceid from productinfo p with(nolock) where p.ppriceid &gt; #{maxPpirceId} and exists( select 1 from f_category_children(23) f where f.id=p.cid ))
    </update>
    <insert id="handleInsertDeFaultLabel">
        insert into product_xtenant_info (ppriceid,product_label,xtenant,create_user,create_time,is_del)
        select p.ppriceid,25,0,'系统',getdate(),0 from productinfo p with(nolock)
        LEFT JOIN product_xtenant_info pxi with(nolock) on pxi.ppriceid = p.ppriceid
        where pxi.ppriceid is null and p.ppriceid &gt; #{maxPpirceId}
        and exists( select 1 from f_category_children(23)  f where f.id=p.cid )
    </insert>

    <select id="getUserBlackListTypeByRecoverId" resultType="java.lang.Integer">
        SELECT top 1 u.blackListType FROM BBSXP_Users u WITH(NOLOCK)
        left join recover_marketInfo r WITH(NOLOCK) on u.id = r.userid
        where r.sub_id = #{subId} and r.saleType = 0
    </select>

    <select id="getSmallProId" resultType="java.lang.Integer">
        SELECT distinct(sm.id)  FROM smallproBill bi with(nolock)
        INNER JOIN basket b with(nolock) ON bi.basket_id=b.basket_id
        INNER JOIN shouhou s with(nolock) ON s.basket_id=b.giftid
        INNER JOIN Smallpro sm with(nolock) on sm.sub_id = s.sub_id
        WHERE s.id=#{shouhouId} AND b.type=1 and sm.Stats != 2
        AND isnull(sm.isdel,0)=0
    </select>

    <select id="getOfficialInsurance" resultType="com.jiuji.oa.afterservice.bigpro.po.OfficialInsurance">
        select o.id, o.ppid, o.insurance_name, o.is_deleted AS deleted
        from official_insurance o with(nolock)
        left join basket b with(nolock) on b.ppriceid = o.ppid
        where sub_id = #{subId} and is_deleted = 0;
    </select>

    <select id="getBigProId" resultType="java.lang.Integer">
        SELECT top 1 s.id from shouhou s with(nolock)
        INNER JOIN basket b with(nolock) ON s.basket_id=b.giftid
        INNER JOIN smallproBill sb with(nolock) on sb.basket_id = b.basket_id
        INNER JOIN Smallpro sm with(nolock) on sm.id = sb.smallproID
        where sm.id = #{smallId}
    </select>

    <select id="getYuYueListByMobile" resultType="com.jiuji.oa.afterservice.bigpro.bo.yuyue.ShouhouYuYueBasicInfo">
        SELECT id,name,concat(name,' ',color) as productName,imei,problem,dtime as dTime from shouhou_yuyue with(nolock)
        where ISNULL(ismobile, 0) = 1
        and mobile = #{mobile}
        and stats not in (3,5,10)
    </select>

    <select id="getProductKcStatusInfoByImei" resultType="com.jiuji.oa.afterservice.bigpro.m.bo.ProductKcStatusInfo">
        select top 1 areaText,dTime,dDays,product_name,product_color,imei,kcCheck,kcType,kcTypeText,kcStatusText
        from (SELECT concat(a.area,'(',a.area_name,')') as areaText,mkc.imeidate dTime,datediff(day, mkc.imeidate, GETDATE()) as dDays,p.product_name,p.product_color,mkc.imei,mkc.kc_check as kcCheck
	    ,CASE WHEN d.id IS NOT NULL THEN 3 WHEN x.mkc_id IS NOT NULL THEN 2 ELSE 1 END kcType
	    ,CASE WHEN d.id IS NOT NULL THEN '优品' WHEN x.mkc_id IS NOT NULL THEN '瑕疵机' ELSE '新机' END kcTypeText
	    ,CASE WHEN mkc.kc_check = 3 THEN '库存' ELSE '在途' END kcStatusText
        from product_mkc mkc with(nolock)
        left join productinfo p with(nolock) on mkc.ppriceid = p.ppriceid
        left join areainfo a with(nolock) on mkc.areaid = a.id
        LEFT JOIN dbo.xc_mkc x with(nolock) ON x.mkc_id = mkc.id
        LEFT JOIN dbo.displayProductInfo d WITH(nolock) ON d.mkc_id = mkc.id AND d.stats_ != 2
        WHERE mkc.imei = #{imei}
        and mkc.kc_check in (3,10)
        union all
        SELECT concat(a.area,'(',a.area_name,')') as areaText,mkc.intime as dTime,datediff(day, mkc.intime, GETDATE()) as dDays,p.product_name,p.product_color,mkc.imei,mkc.mkc_check as kcCheck
        ,CASE WHEN mkc.issalf = 1 THEN 5 ELSE 4 END kcType
        ,CASE WHEN mkc.issalf = 1 THEN '良品机' ELSE '回收机' END kcTypeText
        ,CASE WHEN mkc.mkc_check = 3 THEN '库存' ELSE '在途' END kcStatusText
        from recover_mkc mkc with(nolock)
        left join productinfo p with(nolock) on mkc.ppriceid = p.ppriceid
        left join areainfo a with(nolock) on mkc.areaid = a.id
        WHERE mkc.imei = #{imei}
        and mkc.mkc_check in (3,10)) mm
        order by dTime desc
    </select>

    <select id="getShouHouIdByServiceType" resultType="java.lang.Integer">
        select top 1 id from shouhou with(nolock) where ServiceType = #{serviceType} and imei = #{imei}
        and isnull(isquji,0) = 1;
    </select>

    <select id="getRepairRecordsByImeiOrMobile" resultType="com.jiuji.oa.afterservice.bigpro.m.bo.RepairRecords">
        select s.id,
        s.userid as userId,
        s.imei,
        s.baoxiu as gurantee,
        CONCAT(s.name , ' ' ,s.product_color) as productName,
        s.username as realName,
        s.mobile,
        CONCAT(s.mobile,'(',s.username,')') as customerText,
        isnull(s.baoxiu,0) gurantee,
        s.wxKind
        from shouhou s with(nolock) where s.xianshi = 1
        <if test="imei != null and imei != '' ">
            and s.imei = #{imei}
        </if>
        <if test="mobile != null and mobile != '' ">
            and s.mobile = #{mobile}
        </if>
    </select>

    <select id="getAfterServiceTimeConfigList" resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.AfterServiceTimeConfigBo">
        select category_id as cid,brand_id,product_id,ppid as ppids,refund_day,replace_day,repair_day from after_service_time_cfg with(nolock)
        where is_delete = 0 and is_use = 1
    </select>
    <select id="getCategoryByShouIdList"
            resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouCategoryRes">
        SELECT
        wxid,p.cid,c.name as wxpj
        FROM wxkcoutput x with(nolock)
        LEFT JOIN productinfo p with(nolock) ON x.ppriceid= p.ppriceid
        LEFT JOIN category c with(nolock) ON p.cid = c.id
        WHERE wxid in
        <foreach collection="shouhouIds" item="shouhouId" separator="," open="(" close=")">
            #{shouhouId}
        </foreach>
    </select>
    <select id="listBargainPprice" resultType="java.lang.Integer">
        select w.ppriceid
        from wxkcoutput w with(nolock) where w.stats &lt;&gt; 3 and w.ppriceid >0
                                         and exists(select 1 from productprice pp with(nolock) where isnull(pp.isdel,0)=0 and pp.noPromotion = 1 and pp.ppriceid = w.ppriceid)
                                         and w.wxid = #{shouhouId}
    </select>
    <select id="getPpriceIdByShouhouInfo" resultType="java.lang.Integer">
        <choose>
            <when test="shouhou.ishuishou != null and shouhou.ishuishou>0">
                SELECT top 1 rmsi.ppriceid FROM recover_marketSubInfo rmsi with(nolock) WHERE rmsi.basket_id = #{shouhou.basketId} and rmsi.sub_id = #{shouhou.subId}
            </when>
            <otherwise>
                SELECT top 1 b.ppriceid FROM basket b with(nolock) WHERE b.basket_id = #{shouhou.basketId} and b.sub_id = #{shouhou.subId}
            </otherwise>
        </choose>
    </select>
    <select id="selectUserByMobile" resultType="com.jiuji.oa.afterservice.api.bo.Ch999UserBasicBO">
        select * from dbo.ch999_user with(nolock) where iszaizhi =1 and mobile=#{mobile}
    </select>
    <select id="selectDirectSuperiorLeaders" resultType="com.jiuji.oa.afterservice.api.bo.Ch999UserBasicBO">
        select ch999_id,ch999_name
        from ch999_user
        where ch999_name = (
            select case when d.curAdmin is null then (case when d.preAdmin is null then d.preAdminF else d.preAdmin end)
                        when d.curAdmin = ch999_name then d.preAdmin
                        else d.curAdmin
                       end
            from ch999_user c with (nolock)
            left join departInfo d
        with (nolock)
        on c.depart_id = d.id
        where c.iszaizhi = 1 and ch999_id = #{userId}
            );

    </select>
    <select id="selectUserById" resultType="com.jiuji.oa.afterservice.api.bo.Ch999UserBasicBO">
        select *
        from dbo.ch999_user u with (nolock)
         left join dbo.BBSXP_Users b WITH (NOLOCK) on b.mobile = u.mobile
        where u.iszaizhi =1 and b.ID=#{userId}
    </select>
    <select id="selectUserByUserName" resultType="com.jiuji.oa.afterservice.api.bo.Ch999UserBasicBO">
        select *
        from dbo.ch999_user u with (nolock)
     where u.iszaizhi =1 and u.ch999_name=#{userName}

    </select>
    <select id="hasRecoveryShouhou" resultType="java.lang.Integer">
        select top 1 id from shouhou with(nolock) where xianshi=1 and isnull(isquji,0)=0 and imei=#{imei} and  mkc_id= #{mkcId}

    </select>


</mapper>
