<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.RepairPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.RepairPlan">
        <id column="id" property="id" />
        <result column="fault_id" property="faultId" />
        <result column="plan_name" property="planName" />
        <result column="accessory_type" property="accessoryType" />
        <result column="accessory_id" property="accessoryId" />
        <result column="price" property="price" />
        <result column="sort_no" property="sortNo" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_del" property="isDel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fault_id, plan_name, accessory_type, accessory_id, price, sort_no, create_user, create_time, update_user, update_time, is_del
    </sql>

</mapper>