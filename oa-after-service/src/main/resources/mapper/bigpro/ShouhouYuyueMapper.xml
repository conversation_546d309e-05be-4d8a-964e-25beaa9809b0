<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouYuyueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyue">
        <id column="id" property="id" />
        <result column="stype" property="stype" />
        <result column="kind" property="kind" />
        <result column="stats" property="stats" />
        <result column="username" property="username" />
        <result column="mobile" property="mobile" />
        <result column="stime" property="stime" />
        <result column="etime" property="etime" />
        <result column="delivery" property="delivery" />
        <result column="sub_id" property="subId" />
        <result column="basket_id" property="basketId" />
        <result column="ppriceid" property="ppriceid" />
        <result column="imei" property="imei" />
        <result column="ismobile" property="ismobile" />
        <result column="comment" property="comment" />
        <result column="dtime" property="dtime" />
        <result column="problem" property="problem" />
        <result column="inuser" property="inuser" />
        <result column="shouhou_id" property="shouhouId" />
        <result column="name" property="name" />
        <result column="color" property="color" />
        <result column="area" property="area" />
        <result column="isdel" property="isdel" />
        <result column="wuliuid" property="wuliuid" />
        <result column="tuidata" property="tuidata" />
        <result column="userid" property="userid" />
        <result column="check_user" property="checkUser" />
        <result column="y_ppriceid" property="yPpriceid" />
        <result column="y_mkcArea" property="yMkcarea" />
        <result column="smallproid" property="smallproid" />
        <result column="areaid" property="areaid" />
        <result column="y_mkcAreaId" property="yMkcareaid" />
        <result column="oldyuyueid" property="oldyuyueid" />
        <result column="isBakData" property="isBakData" />
        <result column="issend" property="issend" />
        <result column="xunitui" property="xunitui" />
        <result column="orderType" property="orderType" />
        <result column="yuyuePPids" property="yuyuePPids" />
        <result column="enterUser" property="enterUser" />
        <result column="enterTime" property="enterTime" />
        <result column="productId" property="productId" />
        <result column="fchecktime" property="fchecktime" />
        <result column="iszy" property="iszy" />
        <result column="zyptype" property="zyptype" />
        <result column="zypnum" property="zypnum" />
        <result column="zyShouhouIDs" property="zyShouhouIDs" />
        <result column="kdtime" property="kdtime" />
        <result column="kdtype" property="kdtype" />
        <result column="kuaididan" property="kuaididan" />
        <result column="wuliyou" property="wuliyou" />
        <result column="onfree" property="onfree" />
        <result column="kuaidigongsi" property="kuaidigongsi" />
        <result column="record" property="record" />
        <result column="fuwuma" property="fuwuma" />
        <result column="cancelRemark" property="cancelRemark" />
        <result column="ychecktime" property="ychecktime" />
        <result column="checkTime" property="checkTime" />
        <result column="guideStaffId" property="guideStaffId" />
        <result column="cancelKind" property="cancelKind" />
    </resultMap>

    <sql id="yuyueQueryBasicWhere">
        <if test="param != null">
            <choose>
                <when test="param.key != null and param.keyVal != null and param.key =='id' and param.keyVal !='' ">
                    AND s.id = #{param.keyVal}
                </when>
                <otherwise>
                    <if test="param.isOvertime != null and param.isOvertime == 1">
                       and s.stats in (1,4) and GETDATE() > s.etime
                    </if>
                    <!--超时类别-->
                    <if test="param.overtimeType != null">
                        <choose>
                            <!--所有-->
                            <when test="param.overtimeType == 0">
                                and
                                (
                                ((fchecktime is null and datediff(minute,dbo.getWorkDate(dtime),getdate())>30 and s.stats=1) or (fchecktime is not null and datediff(minute,dbo.getWorkDate(dtime),fchecktime)>30))
                                or
                                ((ychecktime is null and datediff(minute,dbo.getWorkDate(fchecktime),getdate())>15 and s.stats=2) or (ychecktime is not null and datediff(minute,dbo.getWorkDate(fchecktime),ychecktime)>30))
                                or
                                (stype=2 and ((checkTime is null and datediff(hour,dbo.getWorkDate(fchecktime),getdate())>2 and s.stats=4) or (ychecktime is not null and checkTime is not null and datediff(hour,dbo.getWorkDate(fchecktime),checkTime)>2)) )
                                )
                                and s.stats not in (5)
                            </when>
                            <!--客服确认-->
                            <when test="param.overtimeType == 1">
                                and
                                (
                                ((fchecktime is null and datediff(minute,dbo.getWorkDate(dtime),getdate())>30 and s.stats=1) or (fchecktime is not null and datediff(minute,dbo.getWorkDate(dtime),fchecktime)>30))
                                )
                                and s.stats not in (5)
                            </when>
                            <!--业务确认-->
                            <when test="param.overtimeType == 2">
                                and
                                (
                                ((ychecktime is null and datediff(minute,dbo.getWorkDate(fchecktime),getdate())>15 and s.stats=2) or (ychecktime is not null and datediff(minute,dbo.getWorkDate(fchecktime),ychecktime)>30))
                                )
                                and s.stats not in (5)
                            </when>
                            <when test="param.overtimeType == 3">
                                and
                                (
                                (stype=2 and ((checkTime is null and datediff(hour,dbo.getWorkDate(fchecktime),getdate())>2 and s.stats=4) or (ychecktime is not null and checkTime is not null and datediff(hour,dbo.getWorkDate(fchecktime),checkTime)>2)) )
                                )
                                and s.stats not in (5)
                            </when>
                        </choose>
                        <if test="param.isUseNewYuYue != null and param.isUseNewYuYue==0">
                            <if test="param.status == null || param.status != 10">
                                and isnull(s.isdel,0)=0
                            </if>
                        </if>
                     </if>

                    <choose>
                        <when test="param.key == null || param.key == '' || param.keyVal == null || param.keyVal == ''">
                            <!--啥也不做-->
                        </when>
                        <when test="param.key != null and param.keyVal != null and param.key =='name'">
                            and s.name like CONCAT('%',#{param.keyVal},'%')
                        </when>
                        <when test="param.key != null  and param.keyVal != null and param.key =='mobile'">
                            and s.mobile like CONCAT('%',#{param.keyVal},'%')
                        </when>
                        <when test="param.key != null  and param.keyVal != null and param.key =='imei'">
                            and s.imei like CONCAT('%',#{param.keyVal},'%')
                        </when>
                        <when test="param.key != null  and param.keyVal != null and param.key =='checkuser'">
                            and s.check_User = #{param.keyVal}
                        </when>
                        <when test="param.key != null  and param.keyVal != null and param.key =='realname'">
                            and b.realname like CONCAT('%',#{param.keyVal},'%')
                        </when>
                        <when test="param.key != null  and param.keyVal != null and param.key =='kuaididan'">
                            and s.kuaididan like CONCAT('%',#{param.keyVal},'%')
                        </when>
                        <when test="param.key != null  and param.keyVal != null and param.key =='userid'">
                            and s.userid = #{param.keyVal}
                        </when>
                        <when test="param.key != null  and param.keyVal != null and param.key =='inuser'">
                            and s.inuser = #{param.keyVal}
                        </when>
                        <when test="param.key != null  and param.keyVal != null and param.key =='consignee1'">
                            AND EXISTS(SELECT 1 FROM dbo.Addinfops with(nolock) WHERE type=3 AND BindId=s.id AND consignee = #{param.keyVal})
                        </when>
                    </choose>
                    <if test="param.isMobile != null">
                        <choose>
                            <when test="param.isMobile == true">
                                and isnull(s.ismobile,0)=1
                            </when>
                            <otherwise>
                                and isnull(s.ismobile,0)=0
                            </otherwise>
                        </choose>
                    </if>
                    <if test="param.areaIds != null and param.areaIds.size > 0">
                        and s.areaid in
                        <foreach collection="param.areaIds" index="index" item="areaId" open="(" close=")" separator=",">
                            #{areaId}
                        </foreach>
                    </if>
                    <!--服务方式-->
                    <if test="param.servicesWay != null and param.servicesWay != 0">
                        and s.stype = #{param.servicesWay}
                    </if>
                    <!--处理方式-->
                    <if test="param.dealWay != null and param.dealWay != 0">
                        and s.kind = #{param.dealWay}
                    </if>
                    <!--状态-->
                    <if test="param.status != null and param.status != 0">
                        <choose>
                            <when test="param.status == 10">
                                <!--仅查询已取消数据-->
                                and s.stats &lt;> 5 and isnull(s.isdel,0)=1
                            </when>
                            <otherwise>
                                <choose>
                                    <when test="test=param.isUseNewYuYue != null and param.isUseNewYuYue==0">
                                        and s.stats = #{param.status} and isnull(s.isdel,0)=0
                                    </when>
                                    <otherwise>
                                        <choose>
                                            <when test="param.status != null and param.status == 5">
                                                and (s.stats = #{param.status} or isnull(s.isdel,0)=1)
                                            </when>
                                            <otherwise>
                                                and (s.stats = #{param.status} and isnull(s.isdel,0)=0)
                                            </otherwise>
                                        </choose>
                                    </otherwise>
                                </choose>
                            </otherwise>
                        </choose>
                    </if>
                    <!--高级-->
                    <if test="param.gaoji != null and param.gaoji == 1">
<!--                        <choose>-->
<!--                            <when test="param.timeType != null and param.timeType == 1 and param.startTime != null and param.endTime != null">-->
<!--                                and s.stime >= #{param.startTime} and s.etime &lt;= #{param.endTime}-->
<!--                            </when>-->
<!--                            <when test="param.timeType != null and param.timeType == 2 and param.startTime != null and param.endTime != null">-->
<!--                                and s.dtime between #{param.startTime} and #{param.endTime}-->
<!--                            </when>-->
<!--                            <when test="param.cancelRemark != null and param.cancelRemark != ''">-->
<!--                                and s.cancelRemark IS NOT NULL and s.cancelRemark like CONCAT('%',#{param.cancelRemark},'%')-->
<!--                            </when>-->
<!--                            <when test="param.cancelReason != null and param.cancelReason != ''">-->
<!--                                and s.cancelKind IS NOT NULL and s.cancelKind like CONCAT('%',#{param.cancelReason},'%')-->
<!--                            </when>-->
<!--                        </choose>-->
                        <if test="param.timeType != null and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
                            <choose>
                                <when test="param.timeType == 1">
                                    and s.stime >= #{param.startTime} and s.etime &lt;= #{param.endTime}
                                </when>

                                <when test="param.timeType == 2">
                                    and s.dtime between #{param.startTime} and #{param.endTime}
                                </when>
                            </choose>
                        </if>

                        <if test="param.cancelRemark != null and param.cancelRemark != ''">
                            and s.cancelRemark IS NOT NULL and s.cancelRemark like CONCAT('%',#{param.cancelRemark},'%')
                        </if>

                        <if test="param.cancelReason != null and param.cancelReason != ''">
                            and s.cancelKind IS NOT NULL and s.cancelKind like CONCAT('%',#{param.cancelReason},'%')
                        </if>
                    </if>

                    <!--授权隔离-->
                    <if test="areaKind != 1">
                        and exists(select id from areainfo a with(nolock) where a.authorizeid = #{authorizeId} and a.kind1=#{areaKind} and a.id = s.areaid)
                    </if>
                    <if test="param.fromSource != null">
                        and s.from_source = #{param.fromSource}
                    </if>
                </otherwise>
            </choose>
        </if>
    </sql>
    <insert id="insertUserCodeSubBySource">
        INSERT INTO dbo.userCodeSubRecord
        (sub_id, addAreaid, dtime, ch999_id, ch999_name, subType, addType, check_type)
        select top 1 #{targetSubId}, ucsr.addAreaid, getdate(), ucsr.ch999_id, ucsr.ch999_name, #{targetSubType}, ucsr.addType, ucsr.check_type
        from dbo.userCodeSubRecord ucsr with(nolock)
        where ucsr.sub_id = #{sourceSubId} and ucsr.subType = #{sourceSubType}

    </insert>

    <!--售后预约列表总数量-->
    <select id="getYuyueListCount" resultType="java.lang.Integer">
        select count(1) from shouhou_yuyue s with(nolock) left join BBSXP_Users b with(nolock) on s.userid = b.id left join areainfo a with(nolock) on a.id = s.areaid  where 1=1
        <if test="xtenant != null">
            and isnull(a.xtenant,0) = #{xtenant}
        </if>
        <include refid="yuyueQueryBasicWhere"></include>
    </select>

    <select id="getYuyueList" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouYuyueListRes">
        select s.id,
        s.dtime as submitTime,
        CONCAT(s.name,' ',s.color) productName,
        s.stype as servicesWay,
        s.kind as dealWay,
        s.stime as startTime,
        s.etime as endTime,
        s.mobile as customTel,
        s.username as userName,
        s.area,
        s.areaid as areaId,
        s.problem,
        s.cancelRemark,
        s.cancelKind,
        s.stats as status,
        b.realname,
        s.isdel as isDel
        from shouhou_yuyue s with(nolock) left join BBSXP_Users b with(nolock) on s.userid = b.id left join areainfo a with(nolock) on a.id = s.areaid  where 1=1
        <if test="xtenant != null">
            and isnull(a.xtenant,0) = #{xtenant}
        </if>
        <include refid="yuyueQueryBasicWhere"></include>
        order by id desc
        OFFSET #{param.startRow} ROWS FETCH NEXT #{param.size} ROWS ONLY
    </select>

    <!--获取预约数量（中邮需求）-->
    <select id="getYuyueCont" resultType="java.lang.Integer">
        select COUNT(1) from shouhou_yuyue s with(nolock) INNER JOIN dbo.shouhou_yuyueproductinfo yp with(nolock) ON s.id=yp.yuyueid left join BBSXP_Users b with(nolock) on s.userid = b.id  where 1=1
        <include refid="yuyueQueryBasicWhere"></include>
    </select>

    <!--通过预约id获取锁定的维修配件-->
    <select id="getLockWxpj" resultType="com.jiuji.oa.afterservice.bigpro.bo.LockWxpjBo">
         select product_name as productName, isnull(product_color,'') as productColor,
         p.product_id as pid,p.ppriceid as ppid,ISNULL(ypj.areaid,0) lockAreaId
        FROM productinfo p with(nolock)
        LEFT JOIN dbo.shouhou_yuyuelockppids ypj WITH(NOLOCK) ON ypj.ppid=p.ppriceid AND ypj.yyid=#{yuyueId}
        <where>
            (
                <if test="ppids != null and ppids.size > 0">
                    ppriceid IN
                    <foreach collection="ppids" item="ppid" index="index" open="(" separator="," close=")">
                        #{ppid}
                    </foreach>
                    or
                </if>
                p.ppriceid in(select ylp.ppid from dbo.shouhou_yuyuelockppids ylp WITH(NOLOCK) where ylp.yyid = #{yuyueId})
            )
        </where>

    </select>

    <select id="getShouhouYuyueById" resultMap="BaseResultMap">
        select * from shouhou_yuyue with(nolock) where id=#{yyid}
    </select>

    <select id="getYuyueProduct" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.YuyueProductInfoRes">
        select basket_id basketId,s.userid userId,s.areaid areaId,u.username as userName,u.mobile mobile,u.realname realName,b.ismobile isMobile,p.product_name productName, basket_count basketCount,b.price,b.ppriceid ppid
        from basket b with(nolock) inner join sub s with(nolock) on b.sub_id=s.sub_id
        LEFT JOIN productinfo p with(nolock) ON b.ppriceid=p.ppriceid LEFT JOIN BBSXP_Users u with(nolock) on u.id=s.userid
        where 1=1
        <if  test="subId != null">
            and  s.sub_id=#{subId}
        </if>
        and ismobile=0 and s.sub_check=3 and isnull(b.isdel,0)=0 and b.ischu=1
        order by basketId asc
    </select>

    <select id="getTuiProductInfoBySubId" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.YuyueProductInfoRes">
        SELECT * from (
        SELECT
        b.basket_id basketId,
        1 as type,
        s.userid userId,
        s.areaid areaId,
        s.sub_date subDate,
        u.username AS userName,
        u.mobile mobile,
        u.realname realName,
        b.ismobile isMobile,
        p.product_name productName,
        p.product_color productColor,
        b.basket_count basketCount,
        b.price,
        b.ppriceid ppid,
        p.product_id pid,
        k.imei
    FROM
        basket b WITH ( nolock )
        INNER JOIN sub s WITH ( nolock ) ON b.sub_id= s.sub_id
        LEFT JOIN productinfo p WITH ( nolock ) ON b.ppriceid= p.ppriceid
        LEFT JOIN BBSXP_Users u WITH ( nolock ) ON u.id= s.userid
        LEFT JOIN product_mkc k WITH ( nolock ) ON k.basket_id= b.basket_id
    WHERE
        s.sub_id=#{subId}
        AND b.ismobile = #{isMobile}
        AND s.sub_check= 3
        AND isnull( b.isdel, 0 ) = 0
        AND b.ischu= 1

        union all
        SELECT
        b.basket_id basketId,
        2 as type,
        s.userid userId,
        s.areaid areaId,
        s.sub_date subDate,
        u.username AS userName,
        u.mobile mobile,
        u.realname realName,
        b.ismobile isMobile,
        p.product_name productName,
        p.product_color productColor,
        b.basket_count basketCount,
        b.price,
        b.ppriceid ppid,
        p.product_id pid,
        k.imei
    FROM
        recover_marketSubInfo b WITH ( nolock )
        INNER JOIN recover_marketInfo s WITH ( nolock ) ON b.sub_id= s.sub_id
        LEFT JOIN productinfo p WITH ( nolock ) ON b.ppriceid= p.ppriceid
        LEFT JOIN BBSXP_Users u WITH ( nolock ) ON u.id= s.userid
        LEFT JOIN recover_mkc k WITH ( nolock ) ON k.to_basket_id= b.basket_id
    WHERE
        s.sub_id=#{subId}
        AND isnull(b.ismobile,0) = #{isMobile}
        AND s.sub_check= 3
        AND isnull( b.isdel, 0 ) = 0
        AND b.ischu= 1
        ) t where 1=1
        ORDER BY t.subDate desc, t.basketId asc
    </select>

    <select id="getShouHouYuYueWuLiuInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouHouYuYueWuLiuBo">
        select top 1 w.wutype as type,w.com as wuCompanyType,w.nu from wuliu w with(nolock) left join  shouhou_yuyue sh with(nolock)
        on w.danhaobind=sh.sub_id
        where sh.sub_id>0 and sh.stype=3
        <if  test="id != null">
            and  sh.id=#{id}
        </if>
    </select>

    <select id="getShouhouYuyueTimeOutSendMsgInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouYuyueTimeOutSendMsgBo">
        select id,areaid,enterUser,check_user as checkuser,stype,s.stats,0 mins,'' curAdmin from dbo.shouhou_yuyue with(nolock) where datediff(minute,dtime,getdate()) between 30 and 70 and s.stats=1 and isdel=0 AND stype!=5
        union
        select s.id,s.areaid,s.enterUser,check_user as checkuser,s.stype,s.s.stats,datediff(minute,fchecktime,getdate()),d.curAdmin from dbo.shouhou_yuyue s with(nolock)
        left join dbo.ch999_user u with(nolock) on u.ch999_name=s.enterUser
        left join (select a.parentCode,a.curAdmin from dbo.departInfo a with(nolock) where a.name='售后部' and isnull(a.isdel,0)=0) d on d.parentCode=left(u.departCode,6)
        where datediff(minute,fchecktime,getdate()) between 15 and 70 and s.stats=2 and isdel=0 and enterUser is not null and s.stype not in(3,5)
    </select>

    <select id="getShouhouYuyueTimeOutSendMsgInfoNew" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouYuyueTimeOutSendMsgBo">
        SELECT y.id,y.stype,y.areaid,y.check_user as checkUser,y.enterUser,y.fchecktime as fCheckTime,y.stype,datediff(DAY,y.fchecktime,getdate()) fday as fDay,u.ch999_id as ch999Id,u.ch999_name as ch999Name,LEFT(a.areaCode,6) code
        FROM shouhou_yuyue y WITH(NOLOCK) LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON a.id=y.areaid
        OUTER APPLY ( SELECT TOP 1 ch999_id,ch999_name FROM dbo.ch999_user WITH(NOLOCK)
        WHERE iszaizhi=1 AND area1id=y.areaid AND( mainRole IN(20,577) OR CHARINDEX(',20,', ','+Roles+',')>0) ORDER BY mainRole  ) u
        WHERE s.stats=2 AND stype=5 AND enterUser is not NULL AND isdel=0 AND a.xtenant=0
    </select>

    <select id="getCh999Id" resultType="java.lang.String">
        select ch999_id from dbo.ch999_user WITH(NOLOCK) where departCode=10070508 and zhiwuid=4 and iszaizhi=1
    </select>
    <select id="getShouhouYuyueList" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouYuyueListRes">
        select s.id,
        s.dtime as submitTime,
        CONCAT(s.name,' ',s.color) productName,
        s.stype as servicesWay,
        s.kind as dealWay,
        s.stime as startTime,
        s.etime as endTime,
        s.mobile as customTel,
        s.username as userName,
        s.area,
        s.areaid as areaId,
        s.problem,
        s.cancelRemark,
        s.cancelKind,
        s.stats as status,
        b.realname,
        s.isdel as isDel,
        s.shouhou_id as shouHouId,
        s.fchecktime as fCheckTime,
        s.ychecktime as yCheckTime,
        s.imei as iMei,
        shouhou.id as wxId,
        shouhou.stats as wxStatus,
        shouhou.feiyong as feiyong,
        shouhou.costprice as costPrice,
        shouhou.offtime as offTime
        from shouhou_yuyue s with(nolock)
        left join BBSXP_Users b with(nolock) on s.userid = b.id
        left join areainfo a with(nolock) on a.id = s.areaid
        left join  shouhou shouhou with(nolock) on shouhou.id=s.shouhou_id
          where 1=1
        <if test="xtenant != null">
            and isnull(a.xtenant,0) = #{xtenant}
        </if>
        <include refid="yuyueQueryBasicWhere"></include>
        order by id desc
    </select>
    <select id="getPeijianByShouHouIds" resultType="com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput">
        SELECT DISTINCT wxid, name from wxkcoutput WITH(NOLOCK) where
        <if test="shouHouIds != null">
            wxid in
        <foreach collection="shouHouIds" index="index" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        </if>
    </select>

    <select id="getCh999UserIdByAreaId" resultType="java.lang.Integer">
        select ch999_id from  dbo.ch999_user WITH(NOLOCK)  where iszaizhi = 1 and area1id = #{areaId}
    </select>

    <select id="getWorkingCh999IdByAreaId" resultType="java.lang.Integer">
        SELECT top 1 ch999_id from ch999_user u with(nolock) where isnull(u.iszaizhi,0) = 1 and u.zhiwu = '店长' and u.area1id = #{areaId}
        and EXISTS(SELECT 1 from kaoqin with(nolock)  WHERE  DATEDIFF(day,dtime,GETDATE())=0 and ch999_id = u.ch999_id and kind1 = 1)
        and not EXISTS(SELECT 1 from kaoqin with(nolock) WHERE  DATEDIFF(day,dtime,GETDATE())=0 and ch999_id = u.ch999_id and kind1 = 2)
    </select>

    <select id="getWorkingCh999UserInfoByAreaId" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.UserSimpleInfoRes">
        SELECT u.ch999_id as userId,u.ch999_name as userName,a.area,a.area_name,u.zhiwu from ch999_user u with(nolock)
        left join areainfo a with(nolock) on u.area1id = a.id
        where isnull(u.iszaizhi,0) = 1 and u.area1id = #{areaId}
        and EXISTS(SELECT 1 from kaoqin with(nolock)  WHERE  DATEDIFF(day,dtime,GETDATE())=0 and ch999_id = u.ch999_id and kind1 = 1)
        and not EXISTS(SELECT 1 from kaoqin with(nolock)  WHERE  DATEDIFF(day,dtime,GETDATE())=0 and ch999_id = u.ch999_id and kind1 = 2)
    </select>

    <select id="getYjsxAddressByAuthId" resultType="com.jiuji.oa.afterservice.bigpro.bo.yuyue.YjsxAddress">
        SELECT al.name as provinceName,company_address as address,a.contact as receiver,a.contact_tel as mobile,a.cityid addressCityId
        from areainfo a  with(nolock)
        left join AreaList al with(nolock) on al.code = left(cast(a.cityid as varchar),2)
        LEFT JOIN authorize t with(nolock) on a.id = t.H1AreaId
        where t.id = #{authId}
    </select>
    <select id="getFromsourceById" resultType="java.lang.Integer">
        select sy.from_source from shouhou_yuyue sy with(nolock) where id = #{id}
    </select>
    <select id="getUserMobileByYuyueId" resultType="java.lang.String">
        select BU.mobile from shouhou_yuyue sy with(nolock)
        inner join BBSXP_Users BU on sy.userid = BU.ID
        where sy.id = #{yuyueId}
    </select>
    <select id="selectAddSubDelCollect" resultType="java.lang.Integer">
        select yu.id
        from dbo.shouhou_yuyue yu with (nolock)
        where stats = 5
          and not exists(select * from dbo.sub_delCollect del where subType = 3 and del.sub_id = yu.id)
        order by id

    </select>

    <insert id="batchInsertSubDelCollect" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
        INSERT INTO sub_delCollect (sub_id, dtime, subType,del_type)
        VALUES
        <foreach collection="subDelCollectList" item="item" index="index" separator=",">
            (#{item.subId}, #{item.dtime}, #{item.subType}, #{item.delType})
        </foreach>
    </insert>
</mapper>
