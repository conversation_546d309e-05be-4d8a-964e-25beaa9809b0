<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.RepairPlanMasterMapper">



    <!-- 根据故障ID查询售后单ID -->
    <select id="getShouHouIdByFaultId" resultType="java.lang.Integer">
        select m.shouHouId
        from dbo.repair_plan_master m with (nolock)
                 left join dbo.repair_fault f with (nolock) on f.master_id = m.id
        where m.is_del = 0 and f.is_del = 0 and f.id = #{faultId}
    </select>

</mapper>