<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.RepairAccessoriesMapper">


    <select id="selectPartsAssociation" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.PartsAssociationRes">
        select distinct p.product_id as productId, p.product_name as productName,p.product_color as productColor,p.ppriceid as ppid
        from dbo.productinfo p with (nolock)
        <where>
            ISNULL(p.que,0)!= 2
            and exists(select 1 from f_category_children(23) f where f.id = p.cid)
            <if test="keyType.type == 1 and keyType.value!=null ">
                and p.ppriceid =#{keyType.value}
            </if>
            <if test="keyType.type == 2 and keyType.value!=null ">
                and p.product_id =#{keyType.value}
            </if>
            <if test="keyType.type == 3 and keyType.value!=null and keyType.value!=''">
                and p.product_name like CONCAT('%',#{keyType.value},'%')
            </if>
            <if test="keyType.type == 4 and keyType.value!=null">
                and (p.ppriceid =#{keyType.value} or p.product_id =#{keyType.value})
            </if>
        </where>
           order by p.product_id desc
    </select>
    <select id="searchProductKC" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.RepairAccessoriesDetailRes">
         SELECT TOP
            30 p.product_name as productName,
            p.pLabel,
            isnull( p.product_color, '' ) AS productColor,
            p.ppriceid as ppid,
            p.product_id as productId,
            p.memberprice as price,
            p.memberprice as memberPrice,
            isnull( k.lcount, 0 ) AS lcount,
            isnull( k.inprice, p.costprice ) AS inprice,
            p.cid
            FROM
            productinfo p WITH (nolock)
            LEFT JOIN (SELECT ppriceid, inprice, leftCount AS lcount FROM product_kc WITH (nolock)
            WHERE 1=1
            <if test="req.currentInventoryStores != null and req.currentInventoryStores != 0">
                and areaid = #{req.currentInventoryStores}
            </if>
            ) k ON k.ppriceid= p.ppriceid1
            WHERE
            ISNULL(p.que,0)!= 2
            <if test="req.isHaveLcount != null and req.isHaveLcount == 1">
                and k.lcount>0
            </if>
            and exists(select 1 from f_category_children(23) f where f.id = p.cid)
            <if test="req.xtenant != null and req.xtenant == 0">
                AND p.ppriceid NOT IN ( 81683, 81682 )
            </if>

            <if test="req.keyType.getType() == 1 and req.keyType.getValue()!=null ">
                and p.ppriceid =#{req.keyType.value}
            </if>
            <if test="req.keyType.getType() == 2 and req.keyType.getValue()!=null ">
                and p.product_id =#{req.keyType.value}
            </if>
            <if test="req.keyType.getType() == 3 and req.keyType.getValue()!=null and req.keyType.getValue()!=''">
                and p.product_name like CONCAT('%',#{req.keyType.value},'%')
            </if>
            <if test="req.keyType.getType() == 4 and req.keyType.getValue()!=null ">
                and (p.ppriceid =#{req.keyType.value} or p.product_id =#{req.keyType.value})
            </if>
            <if test="req.maintenanceAccessoriesProductIdList.size > 0">
                and p.productid in
                <foreach collection="req.maintenanceAccessoriesProductIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.cidList.size > 0">
                and p.cid in
                <foreach collection="req.cidList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            ORDER BY
            lcount DESC
     </select>
    <select id="selectProductColorInfo" resultType="com.jiuji.oa.afterservice.bigpro.po.ProductColorInfo">
        SELECT
            A.ppriceid ,
            D.productid,
            C.name,
            B.value,
            C.orderID AS FIRST_ORDER,
            B.orderID AS SECOND_ORDER,
            C.showType,
            B.id as detailid,
            D.bpic,
            C.id AS standId,
            D.memberprice,
            C.cid,
            D.que,
            D.sale_channel
        FROM
            dbo.productStInfo AS A WITH ( NOLOCK )
        INNER JOIN dbo.productStDetail AS B WITH ( NOLOCK ) ON A.standardDetailID = B.id
            INNER JOIN dbo.productStandard AS C WITH ( NOLOCK ) ON B.standID = C.id
            INNER JOIN dbo.productprice AS D WITH ( NOLOCK ) ON D.ppriceid = A.ppriceid
        WHERE d.isdel = 0 and D.productid=#{productId} order by FIRST_ORDER,SECOND_ORDER
    </select>
    <select id="selectProductByKey" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.PartsAssociationRes">
        select distinct p.product_id as productId, p.product_name as productName,p.ppriceid as ppid,p.product_color as productColor
        from dbo.productinfo p with (nolock)
        <where>
            ISNULL(p.que,0)!= 2
            <if test="req.isMobile != null ">
               and p.ismobile1 = #{req.isMobile}
            </if>
            <if test="req.type == 1 and req.value!=null ">
                and p.ppriceid =#{req.value}
            </if>
            <if test="req.type == 2 and req.value!=null ">
                and p.product_id =#{req.value}
            </if>
            <if test="req.type == 3 and req.value!=null and req.value!=''">
                and p.product_name like CONCAT('%',#{req.value},'%')
            </if>
            <if test="req.type == 4 and req.value!=null">
                and (p.ppriceid =#{req.value} or p.product_id =#{req.value})
            </if>
        </where>
        order by p.product_id desc
    </select>


</mapper>